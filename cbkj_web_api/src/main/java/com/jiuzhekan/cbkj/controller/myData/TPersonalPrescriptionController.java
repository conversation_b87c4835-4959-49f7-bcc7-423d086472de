package com.jiuzhekan.cbkj.controller.myData;

import com.jiuzhekan.cbkj.beans.business.prescription.THisXdf;
import com.jiuzhekan.cbkj.beans.myData.TPersonalPrescription;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.service.myData.TPersonalPrescriptionFolderService;
import com.jiuzhekan.cbkj.service.myData.TPersonalPrescriptionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@Api(value = "我的协定方", tags = "我的协定方")
public class TPersonalPrescriptionController {

    @Autowired
    private TPersonalPrescriptionService tPersonalPrescriptionService;
    @Autowired
    private TPersonalPrescriptionFolderService tPersonalPrescriptionFolderService;


    @RequestMapping(value = "my/tPersonalPrescription/getShareType", method = RequestMethod.GET)
    @ApiOperation(value = "协定方分享权限")
    @ResponseBody
    @LogAnnotation("协定方分享权限")
    public Object getShareType() {
        return ResEntity.entity(true, Constant.SUCCESS_DX, tPersonalPrescriptionFolderService.getShareType());
    }


    @RequestMapping(value = "my/tPersonalPrescription/getPages", method = RequestMethod.GET)
    @ApiOperation(value = "分页查询协定方主表", notes = "根据方名,功效与适应症,协定方类型,共享级别shareType,preOwner 拥有者 分页查询协定方主表"
            , response = TPersonalPrescription.class)
    @ResponseBody
    @LogAnnotation("分页查询协定方主表")
    public Object getPages(TPersonalPrescription tPersonalPrescription, Page page) {
        return tPersonalPrescriptionService.getPageDatas(tPersonalPrescription, page);
    }


    @RequestMapping(value = "my/tPersonalPrescription/getPageByFolder", method = RequestMethod.GET)
    @ApiOperation(value = "根据文件夹ID分页查询协定方主表", notes = "根据文件夹ID分页查询协定方主表"
            , response = TPersonalPrescription.class)
    @ResponseBody
    @LogAnnotation("分页查询协定方主表")
    public Object getPersonalPresByFolder(TPersonalPrescription tPersonalPrescription, Page page) {
        return tPersonalPrescriptionService.getPersonalPresByFolder(tPersonalPrescription, page);
    }

    @RequestMapping(value = "my/tPersonalPrescription/search", method = RequestMethod.GET)
    @ApiOperation(value = "搜索协定方", notes = "查询类别shareType 0我的协定方 1科室协定方 2机构协定方 3院内方 多个逗号拼接"
            , response = TPersonalPrescription.class)
    @ResponseBody
    @LogAnnotation("搜索协定方")
    public Object searchList(TPersonalPrescription tPersonalPrescription, Page page) {
        return tPersonalPrescriptionService.searchList(tPersonalPrescription, page);
    }

    @RequestMapping(value = "tPersonalPrescription/getPerItemById", method = RequestMethod.GET)
    @ApiOperation(value = "加载单个协定方详情", notes = "加载协定方详情,明细也会当做一个集合返回", response = TPersonalPrescription.class)
    @ApiImplicitParam(name = "persPreId", value = "协定方主表的协定方id", dataType = "String", paramType = "query")
    @LogAnnotation("加载协定方主表详情")
    @ResponseBody
    public Object getPerItemById(String persPreId) {
        return tPersonalPrescriptionService.findObj(persPreId);
    }

    @RequestMapping(value = "tPersonalPrescription/insert", method = RequestMethod.POST)
    @LogAnnotation(value = "新增协定方", isWrite = true)
    @ApiOperation(value = "新增协定方", notes = "新增协定方，录入方名、功效适应症，选择协定方类型（1为内服中药，2为外用中药）" +
            "，选择共享级别（0私有 1本科室 2本医疗机构）。勾选允许开方时修改，IS_MODIFY填1。输入拼音码或五笔码，选择药品名称规格，" +
            "填上剂量、单位、用法。协定方维护到药品的规格级")
    @ResponseBody
    public Object insert(@RequestBody TPersonalPrescription tPersonalPrescription) {
        return tPersonalPrescriptionService.insert(tPersonalPrescription);
    }

    @RequestMapping(value = "tPersonalPrescription/update", method = RequestMethod.POST)
    @LogAnnotation(value = "修改协定方", isWrite = true)
    @ApiOperation(value = "修改协定方", notes = "修改协定方")
    @ResponseBody
    public Object update(@RequestBody TPersonalPrescription tPersonalPrescription) {
        return tPersonalPrescriptionService.update(tPersonalPrescription);
    }

    @RequestMapping(value = "tPersonalPrescription/updateOrder", method = RequestMethod.POST)
    @LogAnnotation(value = "修改协定方序号", isWrite = true)
    @ApiOperation(value = "修改协定方序号", notes = "修改协定方序号(persPreId:协定方ID,preOrder:序号)")
    @ResponseBody
    public Object updateOrder(@RequestBody TPersonalPrescription tPersonalPrescription) {
        return tPersonalPrescriptionService.updateOrder(tPersonalPrescription);
    }

    @RequestMapping(value = "tPersonalPrescription/deleteLis", method = RequestMethod.GET)
    @LogAnnotation(value = "删除协定方", isWrite = true)
    @ApiOperation(value = "删除协定方", notes = "删除协定方")
    @ApiImplicitParam(name = "ids", value = "协定方ids,如果要删除多个就将所有协定方id用英文逗号拼接起来"
            , dataType = "String", paramType = "query", required = true)
    @ResponseBody
    public Object deleteLis(String ids) throws Exception {
        return tPersonalPrescriptionService.deleteLis(ids);
    }

    @RequestMapping(value = "tPersonalPrescription/getHISList", method = RequestMethod.GET)
    @ApiOperation(value = "获取his临床路径列表", notes = "获取his临床路径列表", response = THisXdf.class)
    @ApiImplicitParams(
            {
                    @ApiImplicitParam(name = "presPreId", value = "协定方号id", paramType = "query", required = false),
                    @ApiImplicitParam(name = "searchStr", value = "搜索条件（his的协定方序号或者是his的协定方名）", paramType = "query", required = false)
            }
    )
    @LogAnnotation("获取his临床路径列表")
    @ResponseBody
    public Object getHISList(String presPreId,String searchStr,Page page) {
        return tPersonalPrescriptionService.getHISList(presPreId,searchStr,page);
    }

}