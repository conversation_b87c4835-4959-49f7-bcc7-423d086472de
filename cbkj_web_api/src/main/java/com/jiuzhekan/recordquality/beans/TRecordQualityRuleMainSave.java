package com.jiuzhekan.recordquality.beans;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@ApiModel
public class TRecordQualityRuleMainSave implements Serializable{

    @ApiModelProperty(value = "规则主表id")
    private String recordQualityRuleId;

    @NotBlank(message = "规则类型不能为空")
    @ApiModelProperty(value = "规则类型:名称")
    private String recordQualityRuleCalssValue;

    @NotBlank(message = "规则类型不能为空")
    @ApiModelProperty(value = "规则类型:代码")
    private String recordQualityRuleCalssName;

    @NotBlank(message = "规则名不能为空")
    @ApiModelProperty(value = "规则名")
    private String recordQualityRuleName;

    @NotNull(message = "状态不能为空，0启用1不启用")
    @ApiModelProperty(value = "0启用1不启用")
    private Integer recordQualityRuleStatus;

    @NotBlank(message = "规则描述不能为空")
    @ApiModelProperty(value = "规则描述")
    private String recordQualityRuleDesc;


    @NotEmpty(message = "ruleInfoList不能为空")
    @ApiModelProperty(value = "规则条件分组List")
    private List<TRecordQualityRuleItemsSave> ruleInfoList;

}
