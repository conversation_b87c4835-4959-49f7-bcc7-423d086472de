package com.jiuzhekan.appraisal.bean.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/27 16:29
 * @Version 1.0
 */
@Data
@ApiModel
public class PerformanceAppraisalZhuRe {
//    private String appId;

    @ApiModelProperty(value = "机构代码：多个用逗号分隔")
    private String insCode;

    @ApiModelProperty(value = "主指标代码：多个用逗号分隔")
    private String zibiaodm;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date yeWuRQStart;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date yeWuRQEnd;

//    private Integer page;
//    private Integer limit;
}
