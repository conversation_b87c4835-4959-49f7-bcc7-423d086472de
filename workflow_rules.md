
# 📘 6A 工作流项目规则

本项目遵循 **6A 工作流** 管理规范，确保团队协作高效、透明、可追溯。  
6A 包含：**目标明确（Aim）、分工清晰（Assign）、过程可见（Aware）、及时行动（Act）、严格验收（Audit）、持续改进（Advance）**。  

---

## 1. 🎯 Aim - 目标明确
- 所有任务需在创建时说明 **背景、目标、预期产出**。  
- 使用统一格式：  
  ```
  【目标】简述任务目的  
  【背景】说明上下文和需求来源  
  【产出】明确交付物和完成标准  
  ```

---

## 2. 👥 Assign - 分工清晰
- 每个任务必须指定 **责任人**，可有协作人。  
- 使用 `@成员` 标记责任人，避免出现无人负责的任务。  
- 责任人需保证任务推进直至关闭。

---

## 3. 👀 Aware - 过程可见
- 所有任务状态必须在看板 / issue 中 **实时更新**。  
- 状态流转规范：  
  - `待办` → `进行中` → `待验收` → `已完成`  
- 关键进展需在任务评论中同步，保证团队成员知情。

---

## 4. ⚡ Act - 及时行动
- 任务需按计划推进，避免长时间停滞。  
- 如遇 **阻塞**，责任人必须在 24 小时内反馈并寻求帮助。  
- 临时新增任务需评估优先级，避免打乱整体节奏。

---

## 5. ✅ Audit - 严格验收
- 所有任务完成后需经过 **验收人** 审核。  
- 验收标准：  
  - 是否符合目标与产出要求  
  - 是否符合项目规范（代码规范、文档规范等）  
- 验收通过后，任务方可关闭。

---

## 6. 🔄 Advance - 持续改进
- 项目每个迭代结束后需进行 **复盘**：  
  - 哪些做得好，可以沉淀为流程  
  - 哪些存在问题，需要优化改进  
- 持续优化工作流，提高团队效率和交付质量。

---

## 📌 附录
- **任务优先级定义**  
  - P0：紧急 & 重要，必须立即处理  
  - P1：重要，但可排期处理  
  - P2：一般任务，按计划推进  
  - P3：优化 / 调研类，可灵活安排  

- **任务模板（示例）**
  ```
  【目标】实现登录功能
  【背景】用户反馈无法登录，需要开发新接口
  【产出】提供登录 API + 前端接入
  【责任人】@张三
  【协作人】@李四
  【优先级】P1
  【截止时间】2025-08-31
  ```

---

✍️ 本规则适用于本项目所有成员，请严格遵守并积极反馈改进建议。
