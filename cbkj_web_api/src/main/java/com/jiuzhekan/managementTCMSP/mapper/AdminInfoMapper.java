package com.jiuzhekan.managementTCMSP.mapper;

import com.jiuzhekan.cbkj.beans.sysBeans.AdminInfo;
import com.jiuzhekan.managementTCMSP.bean.AppsInfo;
import com.jiuzhekan.managementTCMSP.bean.TCMSPAdminInfoReq;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/8/13 13:58
 * @Version 1.0
 */
@Component
public interface AdminInfoMapper {
    List<AppsInfo> getPageDatas(TCMSPAdminInfoReq admin);
    Integer getPageDatasNum(TCMSPAdminInfoReq admin);

    @Select("select app_id as appId,app_name as appName from cbkj_web_parameter.sys_app where status = '0'")
    List<HashMap<String, String>> getAppList();

    @Select("select ins_code as insCode,ins_name as insName,ins_parent_code as insParentCode from cbkj_web_parameter.sys_institution where app_id = #{appId} and status = '0'")
    List<HashMap<String, String>> getInsList(String appId);
}
