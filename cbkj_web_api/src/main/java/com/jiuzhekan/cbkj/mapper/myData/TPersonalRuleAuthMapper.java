package com.jiuzhekan.cbkj.mapper.myData;

import com.jiuzhekan.cbkj.beans.myData.TPersonalPrescription;
import com.jiuzhekan.cbkj.beans.myData.TPersonalRuleAuth;
import com.jiuzhekan.cbkj.beans.sysBeans.AdminInfo;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public interface TPersonalRuleAuthMapper extends BaseMapper<TPersonalRuleAuth> {

    /**
     * 根据处方ID获取权限
     *
     * @param t t
     * @return TPersonalRuleAuth
     * <AUTHOR>
     * @date 2021/05/20
     */
    List<TPersonalRuleAuth> getListByPreId(TPersonalRuleAuth t);

//    /**
//     * 根据用户ID获取权限
//     *
//     * @param t t
//     * @return TPersonalRuleAuth
//     * <AUTHOR>
//     * @date 2021/05/20
//     */
//    List<TPersonalRuleAuth> getListByUserId(TPersonalRuleAuth t);

    /**
     * 根据处方ID和角色ID删除权限
     *
     * @param persPreId 处方ID
     * @param ruleId    角色ID
     * <AUTHOR>
     * @date 2021/05/20
     */
    void deleteByPreAndRule(String persPreId, String ruleId);

    /**
     * 根据处方ID删除权限
     *
     * @param persPreId 处方ID
     * <AUTHOR>
     * @date 2021/05/20
     */
    void deleteByPreId(String persPreId);

    /**
     * 根据用户ID删除权限
     *
     * @param userId 用户ID
     * <AUTHOR>
     * @date 2021/05/20
     */
    void deleteByUserId(String userId);


    /**
     * 维护用户时，同步协定方权限
     *
     * @param adminInfo 用户
     * @return long
     * <AUTHOR>
     * @date 2021/05/20
     */
    long saveUserAuth(String depId, String adminId);


}