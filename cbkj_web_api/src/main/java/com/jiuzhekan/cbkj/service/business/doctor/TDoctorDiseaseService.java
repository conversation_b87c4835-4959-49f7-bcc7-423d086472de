package com.jiuzhekan.cbkj.service.business.doctor;

import com.jiuzhekan.cbkj.beans.business.doctor.TDoctorDisease;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.mapper.business.doctor.TDoctorDiseaseMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class TDoctorDiseaseService {

    @Autowired
    private TDoctorDiseaseMapper tDoctorDiseaseMapper;

    /**
     * 根据医生ID历史常用疾病
     *
     * @param docId
     * @return ResEntity
     * <AUTHOR>
     * @date 2020/8/24
     */
    public ResEntity getDoctorDiseaseList(String docId) {
        TDoctorDisease tDoctorDisease = new TDoctorDisease();
        tDoctorDisease.setDocId(docId);
        List<TDoctorDisease> list = tDoctorDiseaseMapper.getPageListByObj(tDoctorDisease);
        return ResEntity.entity(true, Constant.SUCCESS_DX, list);
    }

    /**
     * 当前医生30天内的常用疾病
     *
     * @return List
     * <AUTHOR>
     * @date 2020/8/24
     */
    public List<TDoctorDisease> getCurrentDoctorDiseaseListInThirtyDay() {
        TDoctorDisease tDoctorDisease = new TDoctorDisease();
        tDoctorDisease.setDocId(AdminUtils.getCurrentHr().getId());
        return tDoctorDiseaseMapper.getDoctorDiseaseInThirtyDay(tDoctorDisease);
    }
}