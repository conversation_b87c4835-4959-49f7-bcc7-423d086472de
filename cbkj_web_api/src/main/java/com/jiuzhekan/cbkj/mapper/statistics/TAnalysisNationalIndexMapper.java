package com.jiuzhekan.cbkj.mapper.statistics;
//
//import com.jiuzhekan.cbkj.beans.statistics.TAnalysisNationalIndexVo;
import org.springframework.stereotype.Component;
//
//import java.util.List;
//
@Component
public interface TAnalysisNationalIndexMapper {
//
//    List<TAnalysisNationalIndexVo> getTAnalysisNationalIndexVo(TAnalysisNationalIndexVo tAnalysisNationalIndexVo);
//
//    Integer insertNationalGoal(List<TAnalysisNationalIndexVo> list);
//
//    Integer deleteNationalGoal(String appId , String insCode , String depId);
//
//    TAnalysisNationalIndexVo getDeptGoal(TAnalysisNationalIndexVo tAnalysisNationalIndexVo);
//
//    List<TAnalysisNationalIndexVo> getDeps(TAnalysisNationalIndexVo tAnalysisNationalIndexVo);
//
//    List<TAnalysisNationalIndexVo> getDoctor(TAnalysisNationalIndexVo tAnalysisNationalIndexVo);
//
}
