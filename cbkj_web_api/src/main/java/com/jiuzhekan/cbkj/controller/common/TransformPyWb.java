package com.jiuzhekan.cbkj.controller.common;

import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.Dist;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TransformPyWb.java
 * @Description 根据中文获取拼音和五笔
 * @createTime 2019年08月01日 14:25:00
 */
@Controller
@RequestMapping("public")
@Api(value = "根据中文获取拼音和五笔", tags = "根据中文获取拼音和五笔")
public class TransformPyWb {

    @RequestMapping(value = "common/transformPyWb", method = RequestMethod.GET)
    @ApiOperation(value = "根据中文获取拼音和五笔", notes = "根据中文获取拼音和五笔")
    @LogAnnotation(value = "根据中文获取拼音和五笔")
    @ResponseBody
    public Object transformPyWb(String name) {
        Map<String, String> map = new HashMap<>();
        map.put("py", Dist.getFirstPinYins(name));
        map.put("wb", Dist.getFives(name));

        return ResEntity.entity(true, Constant.SUCCESS_DX, map);
    }
}