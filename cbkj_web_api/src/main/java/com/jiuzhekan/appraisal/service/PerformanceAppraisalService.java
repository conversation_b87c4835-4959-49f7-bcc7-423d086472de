package com.jiuzhekan.appraisal.service;

import com.jiuzhekan.appraisal.bean.*;
import com.jiuzhekan.appraisal.bean.bo.JiGouInfo;
import com.jiuzhekan.appraisal.bean.bo.KeShiInfo;
import com.jiuzhekan.appraisal.bean.dao.PerformanceAppraisalDao;
import com.jiuzhekan.appraisal.bean.dto.*;
import com.jiuzhekan.appraisal.mapper.JxkhZibiaoMapper;
import com.jiuzhekan.appraisal.mapper.JxkhZibiaoZjMapper;
import com.jiuzhekan.appraisal.mapper.JxkhZibiaoZjzbtjMapper;
import com.jiuzhekan.appraisal.utils.FormulaVariableExtractor;
import com.jiuzhekan.cbkj.common.http.PlatformRestTemplate;
import com.jiuzhekan.cbkj.common.utils.Constant;
import org.apache.commons.lang.StringUtils;
import org.nfunk.jep.JEP;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/16 13:46
 * @Version 1.0
 */
@Service
public class PerformanceAppraisalService {
    private final PlatformRestTemplate platformRestTemplate;
    private final PerformanceInsService performanceInsService;
    private final JxkhZibiaoZjzbtjMapper jxkhZibiaoZjzbtjMapper;
    private final JxkhZibiaoZjMapper jxkhZibiaoZjMapper;
    private final JxkhZibiaoMapper zibiaoMapper;

    public PerformanceAppraisalService(PlatformRestTemplate platformRestTemplate, PerformanceInsService performanceInsService, JxkhZibiaoZjzbtjMapper jxkhZibiaoZjzbtjMapper, JxkhZibiaoZjMapper jxkhZibiaoZjMapper, JxkhZibiaoMapper zibiaoMapper) {
        this.platformRestTemplate = platformRestTemplate;
        this.performanceInsService = performanceInsService;
        this.jxkhZibiaoZjzbtjMapper = jxkhZibiaoZjzbtjMapper;
        this.jxkhZibiaoZjMapper = jxkhZibiaoZjMapper;
        this.zibiaoMapper = zibiaoMapper;
    }


    public static Map<String, String> extractAppInfo(List<App> apps) {
        Map<String, String> insMap = new HashMap<>();
        for (App app : apps) {
            insMap.putAll(extractInsInfo(app.getInsList()));
        }
        return insMap;
    }

    private static Map<String, String> extractInsInfo(List<Ins> insList) {
        Map<String, String> insMap = new HashMap<>();
        if (insList == null) {
            return insMap;
        }

        for (Ins ins : insList) {
            insMap.put(ins.getInsCode(), ins.getInsName());
            insMap.putAll(extractInsInfo(ins.getInsList()));
        }

        return insMap;
    }


    public Object getZhiBiaoList(PerformanceZhiBiaoDiclRe performanceZhiBiaoDiclRe) {
        if (Constant.BASIC_STRING_ONE.equals(performanceZhiBiaoDiclRe.getZhiBiaoType())) {
            List<JxkhZibiao> pageListByObj = zibiaoMapper.getPageListByObj(new JxkhZibiao());
            HashMap<String, String> stringStringHashMap = new HashMap<>();
            for (JxkhZibiao jxkhZibiaoZj : pageListByObj) {
                stringStringHashMap.put(jxkhZibiaoZj.getZibiaodm(), jxkhZibiaoZj.getZibiaomc());
            }
            return stringStringHashMap;

        } else {
            List<JxkhZibiaoZj> pageListByObj = jxkhZibiaoZjMapper.getPageListByObj(new JxkhZibiaoZj());
            LinkedHashMap<String, String> stringStringHashMap = new LinkedHashMap<>();
            for (JxkhZibiaoZj jxkhZibiaoZj : pageListByObj) {
                stringStringHashMap.put(jxkhZibiaoZj.getZibiaodm(), jxkhZibiaoZj.getZibiaomc());
            }
            return stringStringHashMap;
        }
    }

    public ArrayList<JxkhZibiaoSubClassRes> getZZBPageDatas(PerformanceAppraisalRe performanceAppraisalRe) {
        String insCode = performanceAppraisalRe.getInsCode();
        String zibiaodm = performanceAppraisalRe.getZibiaodm();
        List<String> insCodeSplit;
        List<String> zibiaodmSplit;
        if (StringUtils.isNotBlank(insCode)) {
            insCodeSplit = Arrays.asList(insCode.split(","));
        } else {
            insCodeSplit = new ArrayList<String>();
            insCodeSplit.add("000000");
        }
        if (StringUtils.isNotBlank(zibiaodm)) {
            zibiaodmSplit = Arrays.asList(zibiaodm.split(","));
        } else {
            zibiaodmSplit = new ArrayList<String>();
            zibiaodmSplit.add("000000");
        }
        Map<String, String> ins = performanceInsService.getIns();
        ArrayList<PerformanceSubClassIns> performanceInsList = new ArrayList<>();
        ArrayList<JxkhZibiaoSubClassRes> performanceInsListre = new ArrayList<>();
        JxkhZibiaoZj jxkhZibiaoZj = new JxkhZibiaoZj();
        jxkhZibiaoZj.setZibiaodms((StringUtils.isBlank(performanceAppraisalRe.getZibiaodm())) ? null : performanceAppraisalRe.getZibiaodm().split(","));

        List<JxkhZibiaoZj> pageListByObj = jxkhZibiaoZjMapper.getPageListByObj(jxkhZibiaoZj);
        ins.forEach((k, v) -> {
            //如果insCodeSplit不为空，就需要k的值在insCodeSplit集合内
            if (insCodeSplit.contains(k) || insCodeSplit.contains("000000")) {
                PerformanceSubClassIns performanceIns1 = new PerformanceSubClassIns();
                performanceIns1.setJiGouDM(k);
                performanceIns1.setJiGouMC(v);
                ArrayList<JxkhZibiaoSubClassRes> jxkhZibiaoRes1 = new ArrayList<>();
                pageListByObj.forEach(jxkhZibiao -> {
                    if (zibiaodmSplit.contains(jxkhZibiao.getZibiaodm()) || zibiaodmSplit.contains("000000")) {
                        JxkhZibiaoSubClassRes jxkhZibiaoRes = new JxkhZibiaoSubClassRes();
                        BeanUtils.copyProperties(jxkhZibiao, jxkhZibiaoRes);
                        //计算公式中提取出所有的变量
//                        jxkhZibiaoRes.setJisuangsSets(FormulaVariableExtractor.extractVariables(jxkhZibiaoRes.getJisuangs()));
                        jxkhZibiaoRes1.add(jxkhZibiaoRes);
                    }
                });
                performanceIns1.setZibiaoList(jxkhZibiaoRes1);
                performanceInsList.add(performanceIns1);
            }
        });
        /**
         * 查出符合条件的所有数据，用于后边计算使用
         */
        PerformanceAppraisalDao dao = new PerformanceAppraisalDao();
        dao.setInsCodes((StringUtils.isBlank(performanceAppraisalRe.getInsCode())) ? null : performanceAppraisalRe.getInsCode().split(","));
//        dao.setZibiaodms((StringUtils.isBlank(performanceAppraisalRe.getZibiaodm())) ? null : performanceAppraisalRe.getZibiaodm().split(","));
        dao.setYeWuRQEnd(performanceAppraisalRe.getYeWuRQEnd());
        dao.setYeWuRQStart(performanceAppraisalRe.getYeWuRQStart());
        //查询需要累加的指标
        List<JxkhZibiaoZjzbtj> pageListByPerformanceAppraisalRe = jxkhZibiaoZjzbtjMapper.getMZBPageDatas(dao);
        //查询不需要累加的指标
        List<JxkhZibiaoZjzbtj> pageListByPerformanceAppraisalReINS = jxkhZibiaoZjzbtjMapper.getMZBPageDatasOnlyINS(dao);

        //整理数据按照 机构（指标代码）-科室（指标代码）分组展示数据
        Map<String, JiGouInfo> stringJiGouInfoMap = computerDataAndGroupSet(pageListByPerformanceAppraisalRe);

        performanceInsList.forEach(performanceIns -> {


                    performanceIns.getZibiaoList().forEach(jxkhZibiaoRes -> {
                        if (jxkhZibiaoRes.getZibiaolb() == 1) {

                            JxkhZibiaoZjzbtj finaljxkhZibiaoZjzbtj = pageListByPerformanceAppraisalReINS.stream()
                                    .filter(innerjxkhZibiaoZjzbtj ->
                                            innerjxkhZibiaoZjzbtj.getJigoudm().equals(performanceIns.getJiGouDM()) &&
                                                    innerjxkhZibiaoZjzbtj.getZibiaodm().equals(jxkhZibiaoRes.getZibiaodm()))
                                    .findFirst().orElse(null);

                            if (finaljxkhZibiaoZjzbtj != null) {
                                jxkhZibiaoRes.setJisuangsValue(finaljxkhZibiaoZjzbtj.getZibiaoz().toString());
                            } else {
                                jxkhZibiaoRes.setJisuangsValue("0");
                            }
                        } else {
                            JiGouInfo jiGouInfo = stringJiGouInfoMap.get(performanceIns.getJiGouDM());
                            if (jiGouInfo == null) {
                                jxkhZibiaoRes.setJisuangsValue("0");
                                return;
                            }
                            Map<String, BigDecimal> zongZhiBiaoValueMap = jiGouInfo.getZongZhiBiaoValueMap();
                            if (zongZhiBiaoValueMap == null || zongZhiBiaoValueMap.isEmpty()) {
                                jxkhZibiaoRes.setJisuangsValue("0");
                                return;
                            }
                            BigDecimal v = zongZhiBiaoValueMap.get(jxkhZibiaoRes.getZibiaodm());
                            if (v == null) {
                                jxkhZibiaoRes.setJisuangsValue("0");
                                return;
                            }
                            jxkhZibiaoRes.setJisuangsValue(v.toString());
                        }


                    });

                }

        );
        performanceInsList.forEach(performanceIns -> {
            String jiGouMC = performanceIns.getJiGouMC();
            performanceIns.getZibiaoList().forEach(jxkhZibiaoRes -> {
                jxkhZibiaoRes.setJigoudmmc(jiGouMC);
                jxkhZibiaoRes.setJigoudm(performanceIns.getJiGouDM());
            });
            performanceInsListre.addAll(performanceIns.getZibiaoList());
        });
        return performanceInsListre;
    }

    public ArrayList<JxkhZibiaoSubClassRes> getZZBDeptPageDatas(PerformanceAppraisalDeptRe performanceAppraisalRe) {
        String[] deptList = (StringUtils.isBlank(performanceAppraisalRe.getDeptId())) ? null : performanceAppraisalRe.getDeptId().split(",");
        PerformanceAppraisalDao dao = new PerformanceAppraisalDao();
        dao.setInsCodes((StringUtils.isBlank(performanceAppraisalRe.getInsCode())) ? null : performanceAppraisalRe.getInsCode().split(","));
        dao.setDeptIds(deptList);
//        dao.setZibiaodms((StringUtils.isBlank(performanceAppraisalRe.getZibiaodm())) ? null : performanceAppraisalRe.getZibiaodm().split(","));
        dao.setYeWuRQEnd(performanceAppraisalRe.getYeWuRQEnd());
        dao.setYeWuRQStart(performanceAppraisalRe.getYeWuRQStart());

        //查询需要累加的指标
        List<JxkhZibiaoZjzbtj> pageListByPerformanceAppraisalRe = jxkhZibiaoZjzbtjMapper.getMZBPageDatas(dao);

        //查询不需要累加的指标
        List<JxkhZibiaoZjzbtj> pageListByPerformanceAppraisalReINS = jxkhZibiaoZjzbtjMapper.getMZBPageDatasOnlyINS(dao);

        //整理数据按照 机构（指标代码）-科室（指标代码）分组展示数据
        Map<String, JiGouInfo> stringJiGouInfoMap = computerDataAndGroupSet(pageListByPerformanceAppraisalRe);
        Map<String, String> dept = performanceInsService.getDept2(performanceAppraisalRe.getInsCode(), performanceAppraisalRe.getDeptId());
        ArrayList<PerformanceSubClassDept> performanceDeptsList = new ArrayList<>();
        ArrayList<JxkhZibiaoSubClassRes> performanceDeptsListre = new ArrayList<>();
//        JxkhZibiao jxkhZibiao1 = new JxkhZibiao();
//        jxkhZibiao1.setZibiaodms((StringUtils.isBlank(performanceAppraisalRe.getZibiaodm())) ? null : performanceAppraisalRe.getZibiaodm().split(","));
//        jxkhZibiao1.setZibiaolb(0);
//        List<JxkhZibiao> pageListByObj = zibiaoMapper.getPageListByObj(jxkhZibiao1);
        JxkhZibiaoZj jxkhZibiaoZj = new JxkhZibiaoZj();
        jxkhZibiaoZj.setZibiaodms((StringUtils.isBlank(performanceAppraisalRe.getZibiaodm())) ? null : performanceAppraisalRe.getZibiaodm().split(","));

        List<JxkhZibiaoZj> pageListByObj = jxkhZibiaoZjMapper.getPageListByObj(jxkhZibiaoZj);

        dept.forEach((deptDM, deptMC) -> {
            PerformanceSubClassDept performanceDept = new PerformanceSubClassDept();
            performanceDept.setKeShiDM(deptDM.substring(deptDM.indexOf("-") + 1));
            performanceDept.setKeShiMC(deptMC.substring(deptMC.indexOf("-") + 1));
            performanceDept.setJiGouDM(deptDM.substring(0, deptDM.indexOf("-")));
            performanceDept.setJiGouMC(deptMC.substring(0, deptMC.indexOf("-")));
            if (deptList != null && deptList.length > 0) {

                //boolean contains = performanceAppraisalRe.getDeptId().contains(performanceDept.getKeShiDM());
                //改成使用deptList里边判断
//                boolean contains = Arrays.stream(deptList).findAny().equals(performanceDept.getKeShiDM());
boolean contains = Arrays.stream(deptList).anyMatch(performanceDept.getKeShiDM()::equals);
//                Optional<String> any = Arrays.stream(deptList).findAny();
//                any.filter(performanceDept.getKeShiDM()::equals).ifPresent(s -> return;);
                if (!contains) {
                    return;
                }
            }
            ArrayList<JxkhZibiaoSubClassRes> jxkhZibiaoRes1 = new ArrayList<>();
            pageListByObj.forEach(jxkhZibiao -> {
                JxkhZibiaoSubClassRes jxkhZibiaoRes = new JxkhZibiaoSubClassRes();
                BeanUtils.copyProperties(jxkhZibiao, jxkhZibiaoRes);

                //计算公式中提取出所有的变量
//                jxkhZibiaoRes.setJisuangsSets(FormulaVariableExtractor.extractVariables(jxkhZibiaoRes.getJisuangs()));
                jxkhZibiaoRes1.add(jxkhZibiaoRes);
            });
            performanceDept.setZibiaoList(jxkhZibiaoRes1);
            performanceDeptsList.add(performanceDept);
        });


        performanceDeptsList.forEach(performanceDept -> {

            performanceDept.getZibiaoList().forEach(jxkhZibiaoRes -> {


                if (jxkhZibiaoRes.getZibiaolb() == 1 && "0".equals(performanceDept.getKeShiDM())) {
                    JxkhZibiaoZjzbtj finaljxkhZibiaoZjzbtj = pageListByPerformanceAppraisalReINS.stream().filter(innerjxkhZibiaoZjzbtj ->
                                    innerjxkhZibiaoZjzbtj.getKeshidm().equals(performanceDept.getKeShiDM()) &&
                                            innerjxkhZibiaoZjzbtj.getJigoudm().equals(performanceAppraisalRe.getInsCode()) &&
                                            innerjxkhZibiaoZjzbtj.getZibiaodm().equals(jxkhZibiaoRes.getZibiaodm()))
                            .findFirst().orElse(null);
                    if (finaljxkhZibiaoZjzbtj != null) {
                        jxkhZibiaoRes.setJisuangsValue(finaljxkhZibiaoZjzbtj.getZibiaoz().toString());
                    } else {
                        jxkhZibiaoRes.setJisuangsValue("0");
                    }
                } else {
                    JiGouInfo jiGouInfo = stringJiGouInfoMap.get(performanceAppraisalRe.getInsCode());
                    if (jiGouInfo == null) {
                        jxkhZibiaoRes.setJisuangsValue("0");
                        return;
                    }
                    Map<String, KeShiInfo> keShiMap = jiGouInfo.getKeShiMap();
                    if (keShiMap == null || keShiMap.isEmpty()) {
                        jxkhZibiaoRes.setJisuangsValue("0");
                        return;
                    }
                    KeShiInfo keShiInfo = keShiMap.get(performanceDept.getKeShiDM());
                    if (keShiInfo == null) {
                        jxkhZibiaoRes.setJisuangsValue("0");
                        return;
                    }
                    Map<String, BigDecimal> zhiBiaoValueMap = keShiInfo.getZhiBiaoValueMap();
                    if (zhiBiaoValueMap == null || zhiBiaoValueMap.isEmpty()) {
                        jxkhZibiaoRes.setJisuangsValue("0");
                        return;
                    }
                    BigDecimal v = zhiBiaoValueMap.get(jxkhZibiaoRes.getZibiaodm());
                    if (v == null) {
                        jxkhZibiaoRes.setJisuangsValue("0");
                        return;
                    }
                    jxkhZibiaoRes.setJisuangsValue(v.toString());
                }
            });
        });
        performanceDeptsList.forEach(performanceDept -> {
            performanceDept.getZibiaoList().forEach(jxkhZibiaoRes -> {
                jxkhZibiaoRes.setJigoudm(performanceDept.getJiGouDM());
                jxkhZibiaoRes.setJigoudmmc(performanceDept.getJiGouMC());
                jxkhZibiaoRes.setKeShidm(performanceDept.getKeShiDM());
                jxkhZibiaoRes.setKeshidmmc(performanceDept.getKeShiMC());
            });
            performanceDeptsListre.addAll(performanceDept.getZibiaoList());
        });
        return performanceDeptsListre;
    }

    public ArrayList<JxkhZibiaoRes> getMZBPageDatas(PerformanceAppraisalZhuRe performanceAppraisalRe) {
        String insCode = performanceAppraisalRe.getInsCode();
        String zibiaodm = performanceAppraisalRe.getZibiaodm();
        List<String> insCodeSplit;
        List<String> zibiaodmSplit;
        if (StringUtils.isNotBlank(insCode)) {
            insCodeSplit = Arrays.asList(insCode.split(","));
        } else {
            insCodeSplit = new ArrayList<String>();
            insCodeSplit.add("000000");
        }
        if (StringUtils.isNotBlank(zibiaodm)) {
            zibiaodmSplit = Arrays.asList(zibiaodm.split(","));
        } else {
            zibiaodmSplit = new ArrayList<String>();
            zibiaodmSplit.add("000000");
        }

        Map<String, String> ins = performanceInsService.getIns();
        ArrayList<PerformanceIns> performanceInsList = new ArrayList<>();
        ArrayList<JxkhZibiaoRes> performanceInsListRe = new ArrayList<>();
        JxkhZibiao jxkhZibiao1 = new JxkhZibiao();
        jxkhZibiao1.setZibiaodms((StringUtils.isBlank(performanceAppraisalRe.getZibiaodm())) ? null : performanceAppraisalRe.getZibiaodm().split(","));
        List<JxkhZibiao> pageListByObj = zibiaoMapper.getPageListByObj(jxkhZibiao1);
        ins.forEach((k, v) -> {
            //如果insCodeSplit不为空，就需要k的值在insCodeSplit集合内
            if (insCodeSplit.contains(k) || insCodeSplit.contains("000000")) {
                PerformanceIns performanceIns1 = new PerformanceIns();
                performanceIns1.setJiGouDM(k);
                performanceIns1.setJiGouMC(v);
                ArrayList<JxkhZibiaoRes> jxkhZibiaoRes1 = new ArrayList<>();
                pageListByObj.forEach(jxkhZibiao -> {
                    if (zibiaodmSplit.contains(jxkhZibiao.getZibiaodm()) || zibiaodmSplit.contains("000000")) {
                        JxkhZibiaoRes jxkhZibiaoRes = new JxkhZibiaoRes();
                        BeanUtils.copyProperties(jxkhZibiao, jxkhZibiaoRes);
                        //计算公式中提取出所有的变量
                        jxkhZibiaoRes.setJisuangsSets(FormulaVariableExtractor.extractVariables(jxkhZibiaoRes.getJisuangs()));
                        jxkhZibiaoRes1.add(jxkhZibiaoRes);
                    }
                });
                performanceIns1.setZibiaoList(jxkhZibiaoRes1);
                performanceInsList.add(performanceIns1);
            }
        });
        /**
         * 查出符合条件的所有数据，用于后边计算使用
         */
        PerformanceAppraisalDao dao = new PerformanceAppraisalDao();
        dao.setInsCodes((StringUtils.isBlank(performanceAppraisalRe.getInsCode())) ? null : performanceAppraisalRe.getInsCode().split(","));
//        dao.setZibiaodms((StringUtils.isBlank(performanceAppraisalRe.getZibiaodm())) ? null : performanceAppraisalRe.getZibiaodm().split(","));
        dao.setYeWuRQEnd(performanceAppraisalRe.getYeWuRQEnd());
        dao.setYeWuRQStart(performanceAppraisalRe.getYeWuRQStart());
        //查询需要全部科室累加的指标
        List<JxkhZibiaoZjzbtj> pageListByPerformanceAppraisalRe = jxkhZibiaoZjzbtjMapper.getMZBPageDatas(dao);
        //查询科室代码为0的不累加的指标
        List<JxkhZibiaoZjzbtj> pageListByPerformanceAppraisalReINS = jxkhZibiaoZjzbtjMapper.getMZBPageDatasOnlyINS(dao);
        //整理数据按照 机构（指标代码）-科室（指标代码）分组展示数据
        Map<String, JiGouInfo> stringJiGouInfoMap = computerDataAndGroupSet(pageListByPerformanceAppraisalRe);

        performanceInsList.forEach(performanceIns -> {
                    JEP jep = new JEP();
                    jep.addStandardFunctions();
                    AtomicBoolean flag = new AtomicBoolean(false);
                    performanceIns.getZibiaoList().forEach(jxkhZibiaoRes -> {
                        //1 是增幅指标，需要用科室代码是0的叠加
                        if (jxkhZibiaoRes.getZibiaolb() == 1) {
                            //不累加
                            jxkhZibiaoRes.getJisuangsSets().forEach(jisuang -> {
                                //pageListByPerformanceAppraisalReINS 中过滤获取JiGouDM和ZiBiaodm符合的一条数据
                                JxkhZibiaoZjzbtj finaljxkhZibiaoZjzbtj = pageListByPerformanceAppraisalReINS.stream().
                                        filter(innerjxkhZibiaoZjzbtj ->

                                                innerjxkhZibiaoZjzbtj.getJigoudm().equals(performanceIns.getJiGouDM()) &&

                                                        innerjxkhZibiaoZjzbtj.getZibiaodm().equals(jisuang)


                                        )
                                        .findFirst().orElse(null);
                                if (finaljxkhZibiaoZjzbtj == null) {
                                    flag.set(true);
                                    return;
                                }
                                jep.addVariable(jisuang, finaljxkhZibiaoZjzbtj.getZibiaoz());
                            });
                        } else {
                            //这里是除了科室代码是0的普通指标叠加
                            jxkhZibiaoRes.getJisuangsSets().forEach(jisuang -> {

                                JiGouInfo jiGouInfo = stringJiGouInfoMap.get(performanceIns.getJiGouDM());
                                if (jiGouInfo == null) {
                                    flag.set(true);
                                    return;
                                }
                                Map<String, BigDecimal> zongZhiBiaoValueMap = jiGouInfo.getZongZhiBiaoValueMap();
                                if (zongZhiBiaoValueMap == null || zongZhiBiaoValueMap.isEmpty()) {
                                    flag.set(true);
                                    return;
                                }
                                BigDecimal v = zongZhiBiaoValueMap.get(jisuang);
                                if (v == null) {
                                    flag.set(true);
                                    return;
                                }
                                jep.addVariable(jisuang, v.doubleValue());
                            });
                        }

                        if (flag.get()) {
                            flag.set(false);
                            jxkhZibiaoRes.setJisuangsValue("0");
                        } else {
                            jep.parseExpression(jxkhZibiaoRes.getJisuangs().replaceAll("%", ""));
                            double value = jep.getValue();
                            if ( Double.isNaN( value) || Double.isInfinite( value) || value == 0){
                                jxkhZibiaoRes.setJisuangsValue("0");
                            }else {
                                BigDecimal bd = new BigDecimal((value + "")).setScale(2, RoundingMode.HALF_UP);
                                jxkhZibiaoRes.setJisuangsValue(bd.toString());
                            }
                        }
                    });

                }

        );
        performanceInsList.forEach(performanceIns -> {
            String jiGouMC = performanceIns.getJiGouMC();
            performanceIns.getZibiaoList().forEach(jxkhZibiaoRes -> {
                jxkhZibiaoRes.setJigoudmmc(jiGouMC);
                jxkhZibiaoRes.setJigoudm(performanceIns.getJiGouDM());
            });

            performanceInsListRe.addAll(performanceIns.getZibiaoList());
        });
        return performanceInsListRe;
    }

    public Map<String, JiGouInfo> computerDataAndGroupSet(List<JxkhZibiaoZjzbtj> pageListByPerformanceAppraisalRe) {
        Map<String, JiGouInfo> result = new HashMap<>();

        for (JxkhZibiaoZjzbtj item : pageListByPerformanceAppraisalRe) {
            String jiGouDM = item.getJigoudm();
            String keShiDM = item.getKeshidm();
            String ziBiaoDM = item.getZibiaodm();
            Double ziBiaoZ = item.getZibiaoz();

            // 获取或创建机构信息
            JiGouInfo jiGouInfo = result.computeIfAbsent(jiGouDM, k -> new JiGouInfo());
            if (jiGouInfo.getZongZhiBiaoValueMap() == null) {
                jiGouInfo.setZongZhiBiaoValueMap(new HashMap<String, BigDecimal>());
            }
            // 累加机构级别的指标值
            jiGouInfo.getZongZhiBiaoValueMap().merge(ziBiaoDM, BigDecimal.valueOf(ziBiaoZ).setScale(2, RoundingMode.HALF_UP), BigDecimal::add);
            if (jiGouInfo.getKeShiMap() == null) {
                jiGouInfo.setKeShiMap(new HashMap<String, KeShiInfo>());
            }
            // 获取或创建科室信息
            KeShiInfo keShiInfo = jiGouInfo.getKeShiMap().computeIfAbsent(keShiDM, k -> new KeShiInfo());
            if (keShiInfo.getZhiBiaoValueMap() == null) {
                keShiInfo.setZhiBiaoValueMap(new HashMap<String, BigDecimal>());
            }
            // 累加科室级别的指标值
            keShiInfo.getZhiBiaoValueMap().merge(ziBiaoDM, BigDecimal.valueOf(ziBiaoZ).setScale(2, RoundingMode.HALF_UP), BigDecimal::add);
        }
        return result;
    }


    public ArrayList<JxkhZibiaoRes> getMZBDeptPageDatas(PerformanceAppraisalDeptZhuRe performanceAppraisalRe) {
        String[] deptList = (StringUtils.isBlank(performanceAppraisalRe.getDeptId())) ? null : performanceAppraisalRe.getDeptId().split(",");
        PerformanceAppraisalDao dao = new PerformanceAppraisalDao();
        dao.setInsCodes((StringUtils.isBlank(performanceAppraisalRe.getInsCode())) ? null : performanceAppraisalRe.getInsCode().split(","));
        dao.setDeptIds(deptList);
//        dao.setZibiaodms((StringUtils.isBlank(performanceAppraisalRe.getZibiaodm())) ? null : performanceAppraisalRe.getZibiaodm().split(","));
        dao.setYeWuRQEnd(performanceAppraisalRe.getYeWuRQEnd());
        dao.setYeWuRQStart(performanceAppraisalRe.getYeWuRQStart());

        //查询需要累加的指标
        List<JxkhZibiaoZjzbtj> pageListByPerformanceAppraisalRe = jxkhZibiaoZjzbtjMapper.getMZBPageDatas(dao);

        //查询不需要累加的指标
        List<JxkhZibiaoZjzbtj> pageListByPerformanceAppraisalReINS = jxkhZibiaoZjzbtjMapper.getMZBPageDatasOnlyINS(dao);

        //整理数据按照 机构（指标代码）-科室（指标代码）分组展示数据
        Map<String, JiGouInfo> stringJiGouInfoMap = computerDataAndGroupSet(pageListByPerformanceAppraisalRe);
        Map<String, String> dept = performanceInsService.getDept2(performanceAppraisalRe.getInsCode(), performanceAppraisalRe.getDeptId());
        ArrayList<PerformanceDept> performanceDeptsList = new ArrayList<>();
        ArrayList<JxkhZibiaoRes> performanceDeptsListre = new ArrayList<>();
        JxkhZibiao jxkhZibiao1 = new JxkhZibiao();
        jxkhZibiao1.setZibiaodms((StringUtils.isBlank(performanceAppraisalRe.getZibiaodm())) ? null : performanceAppraisalRe.getZibiaodm().split(","));
        jxkhZibiao1.setZibiaolb(0);
        List<JxkhZibiao> pageListByObj = zibiaoMapper.getPageListByObj(jxkhZibiao1);
        if (dept == null) {
            return performanceDeptsListre;
        }
        dept.forEach((deptDM, deptMC) -> {
            PerformanceDept performanceDept = new PerformanceDept();
//            performanceDept.setKeShiDM(deptDM);
//            performanceDept.setKeShiMC(deptMC);

            performanceDept.setKeShiDM(deptDM.substring(deptDM.indexOf("-") + 1));
            performanceDept.setKeShiMC(deptMC.substring(deptMC.indexOf("-") + 1));
            performanceDept.setJiGouDM(deptDM.substring(0, deptDM.indexOf("-")));
            performanceDept.setJiGouMC(deptMC.substring(0, deptMC.indexOf("-")));
            if (deptList != null && deptList.length > 0) {
//                boolean contains = performanceAppraisalRe.getDeptId().contains(performanceDept.getKeShiDM());
                boolean contains = Arrays.stream(deptList).anyMatch(performanceDept.getKeShiDM()::equals);
                if (!contains) {
                    return;
                }
//                if (Arrays.stream(deptList).anyMatch(x -> !x.equals(performanceDept.getKeShiDM()))){
//                    //跳过当前数据
//                    return;
//                }
            }
            ArrayList<JxkhZibiaoRes> jxkhZibiaoRes1 = new ArrayList<>();
            pageListByObj.forEach(jxkhZibiao -> {
                JxkhZibiaoRes jxkhZibiaoRes = new JxkhZibiaoRes();
                BeanUtils.copyProperties(jxkhZibiao, jxkhZibiaoRes);

                //计算公式中提取出所有的变量
                jxkhZibiaoRes.setJisuangsSets(FormulaVariableExtractor.extractVariables(jxkhZibiaoRes.getJisuangs()));
                jxkhZibiaoRes1.add(jxkhZibiaoRes);
            });
            performanceDept.setZibiaoList(jxkhZibiaoRes1);
            performanceDeptsList.add(performanceDept);
        });

//        DecimalFormat df = new DecimalFormat("#.##");
        performanceDeptsList.forEach(performanceDept -> {

            performanceDept.getZibiaoList().forEach(jxkhZibiaoRes -> {
                JEP jep = new JEP();
                jep.addStandardFunctions();
                AtomicBoolean flag = new AtomicBoolean(false);
                jxkhZibiaoRes.getJisuangsSets().forEach(jisuang -> {
                    //指标类别：0普通指标：全院指标是各科室相加，1增幅指标：全院指标单独数据，科室代码为0，科室名称“全院”的记录为全院指标
                    //并且科室代码是 0
                    if (jxkhZibiaoRes.getZibiaolb() == 1 && "0".equals(performanceDept.getKeShiDM())) {
                        JxkhZibiaoZjzbtj finaljxkhZibiaoZjzbtj = pageListByPerformanceAppraisalReINS.stream()
                                .filter(innerjxkhZibiaoZjzbtj ->
                                        innerjxkhZibiaoZjzbtj.getKeshidm().equals(performanceDept.getKeShiDM()) &&
                                                innerjxkhZibiaoZjzbtj.getJigoudm().equals(performanceAppraisalRe.getInsCode()) &&
                                                innerjxkhZibiaoZjzbtj.getZibiaodm().equals(jisuang))
                                .findFirst().orElse(null);
                        if (finaljxkhZibiaoZjzbtj == null) {
                            flag.set(true);
                            //return;
                            jep.addVariable(jisuang, 0);
                        } else {
                            jep.addVariable(jisuang, finaljxkhZibiaoZjzbtj.getZibiaoz());

                        }
                    } else {
                        JiGouInfo jiGouInfo = stringJiGouInfoMap.get(performanceAppraisalRe.getInsCode());
                        if (jiGouInfo == null) {
//                            flag.set(true);
                            // return;
                            jep.addVariable(jisuang, 0);
                            return;
                        }
                        Map<String, KeShiInfo> keShiMap = jiGouInfo.getKeShiMap();
                        if (keShiMap == null || keShiMap.isEmpty()) {
//                            flag.set(true);
//                            return;
                            jep.addVariable(jisuang, 0);
                            return;
                        }
                        KeShiInfo keShiInfo = keShiMap.get(performanceDept.getKeShiDM());
                        if (keShiInfo == null) {
//                            flag.set(true);
                            jep.addVariable(jisuang, 0);
                            return;
                        }
                        Map<String, BigDecimal> zhiBiaoValueMap = keShiInfo.getZhiBiaoValueMap();
                        if (zhiBiaoValueMap == null || zhiBiaoValueMap.isEmpty()) {
//                            flag.set(true);
                            jep.addVariable(jisuang, 0);
                            return;
                        }
                        BigDecimal v = zhiBiaoValueMap.get(jisuang);
                        if (v == null) {
//                            flag.set(true);
                            jep.addVariable(jisuang, 0);
                            return;
                        }
                        jep.addVariable(jisuang, v.doubleValue());
                    }
                });
                //  if (flag.get()) {
                //       flag.set(false);
                //      jxkhZibiaoRes.setJisuangsValue("0");
                //  } else {
                jep.parseExpression(jxkhZibiaoRes.getJisuangs().replaceAll("%", ""));
                double value = jep.getValue();
                if (Double.isNaN(value) || Double.isInfinite(value) || value == 0 ) {
                    jxkhZibiaoRes.setJisuangsValue("0");
                } else {
                    BigDecimal bd = new BigDecimal((value + "")).setScale(2, RoundingMode.HALF_UP);
                    jxkhZibiaoRes.setJisuangsValue(bd.toString());
                }
                //  }
            });
        });
        performanceDeptsList.forEach(performanceDept -> {
            performanceDept.getZibiaoList().forEach(jxkhZibiaoRes -> {


                jxkhZibiaoRes.setJigoudm(performanceDept.getJiGouDM());
                jxkhZibiaoRes.setJigoudmmc(performanceDept.getJiGouMC());
                jxkhZibiaoRes.setKeshidm(performanceDept.getKeShiDM());
                jxkhZibiaoRes.setKeshidmmc(performanceDept.getKeShiMC());
            });
            performanceDeptsListre.addAll(performanceDept.getZibiaoList());
        });
        return performanceDeptsListre;
    }

    public Object getZhiBiaoInfo(String zibiaodm) {
        return zibiaoMapper.getObjectById(zibiaodm);
    }

    public static void main(String[] args) {
        double value = 0.00000000;
        BigDecimal bd = new BigDecimal((value + "")).setScale(2, RoundingMode.HALF_UP);
        String[] deptList = {"123", "456"};
//        Optional<String> any = Arrays.stream(deptList).findAny();
//        AtomicBoolean temp = new AtomicBoolean(false);
//        any.filter("123"::equals).ifPresent(s -> System.out.println(s));
////        System.out.println(temp);
//        any.filter("1234"::equals).ifPresent(s -> System.out.println(s));
//        System.out.println();
        System.out.println(Arrays.stream(deptList).anyMatch("123"::equals));
        System.out.println(Arrays.stream(deptList).anyMatch("1234"::equals));
        System.out.println(Arrays.stream(deptList).anyMatch("456"::equals));
    }
}
