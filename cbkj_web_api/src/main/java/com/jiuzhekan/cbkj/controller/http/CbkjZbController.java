package com.jiuzhekan.cbkj.controller.http;

import com.jiuzhekan.cbkj.beans.http.ZbReqVO;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.service.http.CbkjZbService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName CbkjZbController.java
 */

@Controller
@Api(value = "国医大师接口",tags = {"国医大师相关接口"})
@RequestMapping(value = "/api")
public class CbkjZbController {

    @Autowired
    private CbkjZbService cbkjZbService;

    @RequestMapping(value = "/getListByApiToken" ,method = RequestMethod.GET)
    @ApiOperation(value = "2.1获取专病列表,不分页", notes = "由国医大师系统分配给第三方系统接入的唯一标识获取专病列表")
    @ApiImplicitParam(name = "apiToken", value = "由国医大师系统分配给第三方系统接入的唯一标识,前台不传的话,后台获取常量中的token"
            , dataType = "String", paramType = "query")
    @LogAnnotation("由国医大师系统分配给第三方系统接入的唯一标识获取专病列表")
    @ResponseBody
    public Object getListByApiToken(String apiToken, String type) {

        return cbkjZbService.getListByApiToken(apiToken, type);
    }

    @RequestMapping(value = "/getDialectical" ,method = RequestMethod.GET)
    @ApiOperation(value = "经方辩证URL", notes = "由国医大师系统分配给第三方系统接入的唯一标识获取经方辩证UR")
    @LogAnnotation("由国医大师系统分配给第三方系统接入的唯一标识获取经方辩证URL")
    @ResponseBody
    public Object getDialectical() {
        return cbkjZbService.getDialectical();
    }


    @RequestMapping(value = "/getDiseaseTemplate" ,method = RequestMethod.GET)
    @ApiOperation(value = "2.2获取专病模板", notes = "获取专病模板")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "apiToken", value = "由国医大师系统分配给第三方系统接入的唯一标识,前台不传的话,后台获取常量中的token"
                    , dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "diseaseCode", value = "专病代码，通过1号接口获取", dataType = "String", paramType = "query")
    })
    @LogAnnotation("获取专病模板")
    @ResponseBody
    public Object getDiseaseTemplate(String apiToken,String diseaseCode) {
        return cbkjZbService.getDiseaseTemplate(apiToken,diseaseCode);
    }


    @RequestMapping(value = "/getDiseaseResult" ,method = RequestMethod.POST)
    @ApiOperation(value = "2.3提交专病数据计算并返回处方信息", notes = "2.3提交专病数据计算并返回处方信息")
    @LogAnnotation("2.3提交专病数据计算并返回处方信息")
    @ResponseBody
    public Object getDiseaseResult(@RequestBody ZbReqVO zbReqVO) {
        return cbkjZbService.getDiseaseResult(zbReqVO);
    }
}