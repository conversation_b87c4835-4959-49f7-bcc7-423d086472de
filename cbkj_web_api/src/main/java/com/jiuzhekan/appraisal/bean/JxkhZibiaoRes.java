package com.jiuzhekan.appraisal.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.Set;

@Data
@NoArgsConstructor
@ApiModel
public class JxkhZibiaoRes implements Serializable {

    @ApiModelProperty(value = "指标代码")
    private String zibiaodm;

    @ApiModelProperty(value = "指标名称")
    private String zibiaomc;

    @ApiModelProperty(value = "指标值")
    private String jisuangsValue;

    @ApiModelProperty(value = "机构代码")
    private String jigoudm;
    private String keshidm;

    @ApiModelProperty(value = "机构名称")
    private String jigoudmmc;
    private String keshidmmc;

    @ApiModelProperty(value = "指标属性")
    private String zibiaosx;

    @ApiModelProperty(value = "计量单位")
    private String jiliangdw;

    @ApiModelProperty(value = "指标定义")
    private String zibiaody;

    @ApiModelProperty(value = "计算方法")
    private String jisuanff;

    @ApiModelProperty(value = "计算公式")
    private String jisuangs;
    @ApiModelProperty(value = "0累加 1 不累加",hidden = true)
    private Integer zibiaolb;

    @ApiModelProperty(value = "计算公式",hidden = true)
    private Set<String> jisuangsSets;


}
