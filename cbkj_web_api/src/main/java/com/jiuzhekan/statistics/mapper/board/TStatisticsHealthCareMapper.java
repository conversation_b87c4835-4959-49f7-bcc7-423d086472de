package com.jiuzhekan.statistics.mapper.board;

import com.jiuzhekan.statistics.beans.board.TStatisticsHealthCare;
import com.jiuzhekan.statistics.beans.register.QueryParam;
import org.springframework.stereotype.Component;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;

@Component
public interface TStatisticsHealthCareMapper extends BaseMapper<TStatisticsHealthCare>{
    /**
     * 查询医保控费提醒
     * @param queryParam
     * @return
     */
    public Integer sumStatistics(QueryParam queryParam);

    TStatisticsHealthCare getOneByObject(TStatisticsHealthCare tStatisticsHealthCare);
}