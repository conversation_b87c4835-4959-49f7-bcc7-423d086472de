package com.jiuzhekan.cbkj.service.statistics;

import com.jiuzhekan.cbkj.beans.statistics.TStatistics;
import com.jiuzhekan.cbkj.beans.sysBeans.AdminInfo;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.DateUtil;
import com.jiuzhekan.cbkj.mapper.statistics.TStatisticsMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * 描述  临床业务监管模块
 *
 * <AUTHOR>
 * @Date 2020/7/28
 */
@Service
public class TStatisticsService {

    @Autowired
    private TStatisticsMapper tStatisticsMapper;

    /**
     * 根据时间，查询前5种高发疾病
     * （每天统计timeType四种类型的前5的疾病数据）
     *
     * @param timeType 时间类型：1昨天,2近半月,3近一个季度,4近一年
     * @return
     * <AUTHOR>
     */
    public List<TStatistics> getTop5DisName(Integer timeType) {
        TStatistics tStatistics = new TStatistics();
        tStatistics.setAppId(AdminUtils.getCurrentAppIdIgnoreBasic());
        //tStatistics.setInsCode(AdminUtils.getCurrentInsCode());
        tStatistics.setTimeType(timeType);
        tStatistics.setNowDate(DateUtil.getDateFormats(DateUtil.YYYY_MM_DD, new Date()));
        return tStatisticsMapper.getTop5DisName(tStatistics);
    }

    public List<TStatistics> processData(Integer timeType,String recTreType,String appId,String insCode){
        TStatistics tStatistics = new TStatistics();
        tStatistics.setAppId(appId);
        tStatistics.setInsCode(insCode);
        tStatistics.setTimeType(timeType);
        tStatistics.setRecTreType(recTreType);
        tStatistics.setNowDate(DateUtil.getDateFormats(DateUtil.YYYY_MM_DD, new Date()));
        return tStatisticsMapper.getTop5DisName(tStatistics);
    }

    /**
     * 根据时间，查询前5种高发证型
     *
     * @param timeType 时间类型：1昨天,2近半月,3近一个季度,4近一年
     * @return
     * <AUTHOR>
     */
    public List<TStatistics> getTop5SymName(Integer timeType) {
        TStatistics tStatistics = new TStatistics();
        tStatistics.setAppId(AdminUtils.getCurrentAppIdIgnoreBasic());
        //tStatistics.setInsCode(AdminUtils.getCurrentInsCode());
        tStatistics.setTimeType(timeType);
        tStatistics.setNowDate(DateUtil.getDateFormats(DateUtil.YYYY_MM_DD, new Date()));
        return tStatisticsMapper.getTop5SymName(tStatistics);
    }

    /**
     * 查询每个年龄段前5种高发疾病
     *
     * @param timeType 时间类型：1昨天,2近半月,3近一个季度,4近一年
     * @return List
     * <AUTHOR>
     */
    public List<TStatistics> getTop5DisNameByAge(Integer timeType) {

        List<TStatistics> list = new ArrayList<>();

        TStatistics tStatistics = new TStatistics();
        tStatistics.setAppId(AdminUtils.getCurrentAppIdIgnoreBasic());
        //tStatistics.setInsCode(AdminUtils.getCurrentInsCode());
        tStatistics.setTimeType(timeType);
        tStatistics.setNowDate(DateUtil.getDateFormats(DateUtil.YYYY_MM_DD, new Date()));
        List<TStatistics> disTop5list = tStatisticsMapper.getTop5DisNameByAge(tStatistics);

        if (null != disTop5list && !disTop5list.isEmpty()) {

            LinkedHashMap<String, TStatistics> map = new LinkedHashMap<>();

            for (TStatistics statistics : disTop5list) {
                TStatistics ts = map.get(statistics.getGroupName());
                if (ts == null) {
                    ts = new TStatistics();
                    ts.setGroupName(statistics.getGroupName());
                    ts.setStatisList(new ArrayList<>());
                    map.put(statistics.getGroupName(), ts);
                }
                ts.getStatisList().add(statistics);
            }

            map.forEach((key, value) -> list.add(value));

            disTop5list.clear();
            map.clear();
        }
        return list;
    }

    /**
     * 查询发病量上升趋势前5的疾病
     *
     * @param timeType 时间类型：1昨天,2近半月,3近一个季度,4近一年
     * @return List
     * <AUTHOR>
     */
    public List<TStatistics> getTop5DisNameByGoUp(Integer timeType) {

        List<TStatistics> list = new ArrayList<>();

        int splitDay = 1;
        int pointNum = 7;
        if (timeType == 2) {
            splitDay = 2;
            pointNum = 7;
        } else if (timeType == 3) {
            splitDay = 10;
            pointNum = 9;
        } else if (timeType == 4) {
            splitDay = 30;
            pointNum = 12;
        }

        List<String> dateList = new ArrayList<>();
        Calendar cal = Calendar.getInstance();
        for (int i = 0; i < pointNum; i++) {
            cal.add(Calendar.DAY_OF_YEAR, splitDay * -1);
            dateList.add(DateUtil.getDateFormats(DateUtil.YYYY_MM_DD, cal.getTime()));
        }

        TStatistics tStatistics = new TStatistics();
        tStatistics.setAppId(AdminUtils.getCurrentAppId());
        tStatistics.setInsCode(AdminUtils.getCurrentInsCode());
        tStatistics.setTimeType(timeType);
        tStatistics.setDateList(dateList);
        List<TStatistics> disTop5list = tStatisticsMapper.getTop5DisNameByGoUp(tStatistics);

        if (disTop5list != null && disTop5list.size() > 0) {

            LinkedHashMap<String, TStatistics> map = new LinkedHashMap<>();

            for (TStatistics statistics : disTop5list) {
                String key = DateUtil.getDateFormats(DateUtil.M_D, statistics.getCreateTime());
                TStatistics ts = map.get(key);
                if (ts == null) {
                    ts = new TStatistics();
                    ts.setGroupName(key);
                    ts.setStatisList(new ArrayList<>());
                    map.put(key, ts);
                }
                ts.getStatisList().add(statistics);
            }

            map.forEach((key, value) -> list.add(value));

            disTop5list.clear();
            map.clear();
        }
        return list;
    }

    public List<TStatistics> getTop5DisNameByGoUp2(Integer timeType) {

        List<TStatistics> list = new ArrayList<>();

        TStatistics tStatistics = new TStatistics();
        tStatistics.setAppId(AdminUtils.getCurrentAppIdIgnoreBasic());
//        tStatistics.setInsCode(AdminUtils.getCurrentInsCode());
        tStatistics.setTimeType(timeType);
        tStatistics.setNowDate(DateUtil.getDateFormats(DateUtil.YYYY_MM_DD, new Date()));
        List<TStatistics> disTop5list = tStatisticsMapper.getTop5DisNameByGoUp(tStatistics);

        if (disTop5list != null && disTop5list.size() > 0) {

            LinkedHashMap<String, TStatistics> map = new LinkedHashMap<>();

            for (TStatistics statistics : disTop5list) {
                String key = statistics.getGroupName();
                TStatistics ts = map.get(key);
                if (ts == null) {
                    ts = new TStatistics();
                    ts.setGroupName(key);
                    ts.setStatisList(new ArrayList<>());
                    map.put(key, ts);
                }
                ts.getStatisList().add(statistics);
            }

            map.forEach((key, value) -> list.add(value));

            disTop5list.clear();
            map.clear();
        }
        return list;
    }


    /**
     * 查询每一种性别发病率前5的疾病
     *
     * @param timeType 时间类型：1昨天,2近半月,3近一个季度,4近一年
     * @return List
     * <AUTHOR>
     */
    public List<TStatistics> getTop5DisNameByGender(Integer timeType) {
        TStatistics tStatistics = new TStatistics();
        tStatistics.setAppId(AdminUtils.getCurrentAppIdIgnoreBasic());
        //tStatistics.setInsCode(AdminUtils.getCurrentInsCode());
        tStatistics.setTimeType(timeType);
        tStatistics.setNowDate(DateUtil.getDateFormats(DateUtil.YYYY_MM_DD, new Date()));
        return tStatisticsMapper.getTop5DisNameByGender(tStatistics);
    }

    /**
     * 查询二十四节气中，每个节气发病率前5的疾病
     *
     * @param timeType 时间类型：1昨天,2近半月,3近一个季度,4近一年
     * @return List
     * <AUTHOR>
     */
    public Map<String, Object> getTop5DisNameBy24(Integer timeType) {
        Map<String, Object> map = new HashMap<>();
        List<TStatistics> termsList = tStatisticsMapper.getTermsName();//获取最近的24个节气
        if (null != termsList && !termsList.isEmpty()) {
            map.put("beginTime", DateUtil.getDateAddOfDay(termsList.get(0).getBeginTime(), -5).getTime());
            map.put("endTime", DateUtil.getDateAddOfDay(termsList.get(termsList.size() - 1).getEndTime(), -5).getTime());
            Date beginTime;
            int step = 1;//步长
            for (int i = 0; i < termsList.size(); i++) {
                List<TStatistics> termList = tStatisticsMapper.getTop5DisNameByTermName(termsList.get(i));
                for (int j = 0; j < termList.size(); j++) {
                    beginTime = DateUtil.getDateAddOfDay(termList.get(0).getBeginTime(), j * step);
                    System.out.println(DateUtil.getDateFormats(DateUtil.YYYY_MM_DD_HH_MM_SS, beginTime));
                    termList.get(j).setGroupName(String.valueOf(beginTime.getTime()));
                    termList.get(j).setBeginTime(beginTime);
                }
                termsList.get(i).setStatisList(termList);
            }
        }
        map.put("termsList", termsList);
        return map;
    }

    /**
     * 获取当前医生今天的月均贴金额
     *
     * @return java.util.Map<java.lang.String, java.lang.Object>
     * <AUTHOR>
     * @date 2021/3/24
     */
    public BigDecimal getDoctorMonthlyAvgAmount() {
        AdminInfo admin = AdminUtils.getCurrentHr();
        return tStatisticsMapper.getDoctorMonthlyAvgAmount(admin);
    }
}
