package com.jiuzhekan.cbkj.service.statistics;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.github.pagehelper.PageHelper;
import com.jiuzhekan.cbkj.beans.statistics.*;
import com.jiuzhekan.cbkj.beans.sysApp.SysInstitution;
import com.jiuzhekan.cbkj.common.utils.DateUtil;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.mapper.statistics.TPrescriptionStatisticsMapper;
import com.jiuzhekan.cbkj.mapper.statistics.TStatisticsPrescriptionItemMapper;
import com.jiuzhekan.cbkj.service.sysService.AdminService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.IntStream;


/**
 * 描述  临床业务监管模块
 *
 * <AUTHOR>
 * @Date 2020/2/18
 */
@Service
@Slf4j
public class TPrescriptionStatisticsService {




    private final TPrescriptionStatisticsMapper tPrescriptionStatisticsMapper;
    private final TRegisterStatisticsService tRegisterStatisticsService;
    private final AdminService adminService;

    public TPrescriptionStatisticsService(TPrescriptionStatisticsMapper tPrescriptionStatisticsMapper,
                                          TRegisterStatisticsService tRegisterStatisticsService,
                                          AdminService adminService) {
        this.tPrescriptionStatisticsMapper = tPrescriptionStatisticsMapper;
        this.tRegisterStatisticsService = tRegisterStatisticsService;
        this.adminService = adminService;
    }


    /**
     * 查询单次就诊金额在某范围内的次数
     * appId
     * insCode
     * preTimeBegin 开方开始日期
     * preTimeEnd  开方截止日期
     * preTolMoneyBegin 起始金额
     * preTolMoneyEnd   截止金额
     *
     * @return 次数
     * <AUTHOR>
     */
    public HashMap<String, Object> getCountGroupByPreTolMoney(TPrescriptionVO prescriptionVO, String lower) {
        SysInstitution sysInstitution = new SysInstitution();
        sysInstitution.setAppId(prescriptionVO.getAppId());
        sysInstitution.setInsCode(prescriptionVO.getInsCode());
        prescriptionVO.setInsCodeList(tRegisterStatisticsService.getSubInsCodeList(sysInstitution, lower));
//        prescriptionVO.setInsCode(null);
        return tPrescriptionStatisticsMapper.getCountGroupByPreTolMoney(prescriptionVO);
    }

    /**
     * 根据不同的处方类型，查看处方金额在某个范围内的次数
     * appId
     * insCode
     * preTimeBegin 开方起始日期
     * preTimeEnd  开方截止日期
     * preTolMoneyBegin 起始金额
     * preTolMoneyEnd   截止金额
     * preType 药房类型
     *
     * @return count总数，preType处方类型
     * <AUTHOR>
     */
    public List<HashMap<String, Object>> getCountByPreTolMoneyGroupByType(TPrescriptionVO prescriptionVO) {
        return tPrescriptionStatisticsMapper.getCountByPreTolMoneyGroupByType(prescriptionVO);
    }

    /**
     * 查询每个月某个处方类型的开方数量
     * appId
     * insCode
     * preTimeBegin 开方起始日期
     * preTimeEnd  开方截止日期
     * preType 药方类型
     *
     * @return preTime 月份 count数量
     * <AUTHOR>
     */
    public List<InstitutionStatsResponse> getCountByMonthAndType(Map<String, Object> paramMap) {

        List<InstitutionStatsResponse> countByMonthAndType = tPrescriptionStatisticsMapper.getCountByMonthAndType(paramMap);
        return countByMonthAndType;
    }

    /**
     * 根据开方时间，获取某一时间段内开方总量
     * "中药处方分析"模块使用
     *
     * @param prescriptionVO
     * @return
     * <AUTHOR>
     */
    public Integer getCountByPreTime(TPrescriptionVO prescriptionVO) {
        return tPrescriptionStatisticsMapper.getCountByPreTime(prescriptionVO);
    }


    /**
     * 根据开方时间，获取某一时间段内使用率最高的50种药材
     * "中药处方分析"模块使用
     *
     * @param prescriptionVO
     * @return matName 药品名称，count 使用次数
     * <AUTHOR>
     */
    public List<HashMap> getCountByPreTimeGroupMatName(TPrescriptionVO prescriptionVO) {
        return tPrescriptionStatisticsMapper.getCountByPreTimeGroupMatName(prescriptionVO);
    }


        /**
         * 根据开方时间，获取所有开方途径的数量
         *
         * @param prescriptionVO
         * @return count 数量，preOrigin开方途径
         * <AUTHOR>
         */
    public List<HashMap> getCountByPreTimeGroupPreOrigin(TPrescriptionVO prescriptionVO) {
        return tPrescriptionStatisticsMapper.getCountByPreTimeGroupPreOrigin(prescriptionVO);
    }

    /**
     * 统计医生工作量
     *
     * @param map appId医联体id,
     *            insCode医疗机构ID，
     *            year年份，
     *            month月份
     * @return id 医生ID
     * name 医生姓名
     * regCount, 月就诊人次
     * nfCount,月内服药方数量
     * wyCount,月外用药方数量
     * zcCount,月中成药方数量
     * syCount,月适宜技术药方量
     * pCount,月总开方量
     * qregCount,全年就诊人次
     * qnfCount,全年内服药方数量
     * qwyCount,全年外用药方量
     * qzcCount,全年中成药方量
     * qsyCount,全年适宜技术药方量
     * qpCount，全年开方量
     * <AUTHOR>
     */
    public Object getDocutorWorkload(HashMap<String, Object> map, Page page, String lower) {

        SysInstitution sysInstitution = new SysInstitution();
        if (map.get("appId") != null) {
            sysInstitution.setAppId(map.get("appId").toString());
        }
        if (map.get("insCode") != null) {
            sysInstitution.setInsCode(map.get("insCode").toString());
        }
        List<String> insCodeList = tRegisterStatisticsService.getSubInsCodeList(sysInstitution, lower);
        map.put("insCodeList", insCodeList);
        map.put("insCode", null);
        PageHelper.startPage(page.getPage(), page.getLimit() - 1);
        List<HashMap<String, Object>> list = tPrescriptionStatisticsMapper.getDocutorWorkload(map);
        List<HashMap<String, Object>> totalList = tPrescriptionStatisticsMapper.getInstitutionWorkloadTotal(map);
        HashMap<String, Object> total = getMapByList(totalList, "name", "所有医生合计");
        if (null != list && list.size() > 0) {
            list.add(total);
        } else {
            list = new ArrayList<>();
            list.add(total);
        }
        Object result = Page.getLayUiTablePageData(list);
        return result;
    }

    /**
     * 统计机构工作量
     *
     * @param map appId医联体id,
     *            insCode医疗机构ID，
     *            year年份，
     *            month月份
     * @return insCode 机构编码
     * insName 机构名称
     * regCount, 月就诊人次
     * nfCount,月内服药方数量
     * wyCount,月外用药方数量
     * zcCount,月中成药方数量
     * syCount,月适宜技术药方量
     * pCount,月总开方量
     * qregCount,全年就诊人次
     * qnfCount,全年内服药方数量
     * qwyCount,全年外用药方量
     * qzcCount,全年中成药方量
     * qsyCount,全年适宜技术药方量
     * qpCount，全年开方量
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public Object getInstitutionWorkload(HashMap<String, Object> map, Page page, String lower) {
        SysInstitution sysInstitution = new SysInstitution();
        if (map.get("appId") != null) {
            sysInstitution.setAppId(map.get("appId").toString());
        }
        if (map.get("insCode") != null) {
            sysInstitution.setInsCode(map.get("insCode").toString());
        }
        List<String> insCodeList = tRegisterStatisticsService.getSubInsCodeList(sysInstitution, lower);
        map.put("insCodeList", insCodeList);
        map.put("insCode", null);

        timeConversion(map);
//        PageHelper.startPage(page.getPage(), page.getLimit() - 1);

        List<InstitutionStatsVO> list = tPrescriptionStatisticsMapper.getInstitutionWorkload(map);

        for (InstitutionStatsVO institutionStatsVO : list) {
            institutionStatsVO.setInsName(adminService.getInsName(institutionStatsVO.getInsCode()));
        }
        List<HashMap<String, Object>> totalList = tPrescriptionStatisticsMapper.getInstitutionWorkloadTotal(map);

        HashMap<String, Object> total = getMapByList(totalList, "insName", "所有医院合计");
        InstitutionStatsVO institutionStatsVO = this.mapToInstitutionStats(total);
        list.add(institutionStatsVO);
        return Page.getLayUiTablePageData(list);
    }


    public InstitutionStatsVO mapToInstitutionStats(HashMap<String, Object> statsMap) {
        InstitutionStatsVO institutionStats = new InstitutionStatsVO();

        institutionStats.setInsCode(null!=statsMap.get("insCode")?statsMap.get("insCode").toString():null);
        institutionStats.setInsName(null!=statsMap.get("insName")?statsMap.get("insName").toString():null);
        institutionStats.setRegCount(statsMap.get("regCount").toString());

        institutionStats.setNfCount(statsMap.get("nfCount").toString());
        institutionStats.setPCount(statsMap.get("pCount").toString());
        institutionStats.setSyCount(statsMap.get("syCount").toString());
        institutionStats.setWyCount(statsMap.get("wyCount").toString());
        institutionStats.setZcCount(statsMap.get("zcCount").toString());

        institutionStats.setQregCount(statsMap.get("qregCount").toString());
        institutionStats.setQsyCount(statsMap.get("qsyCount").toString());
        institutionStats.setQpCount(statsMap.get("qpCount").toString());
        institutionStats.setQnfCount(statsMap.get("qnfCount").toString());
        institutionStats.setQzcCount(statsMap.get("qzcCount").toString());
        institutionStats.setQwyCount(statsMap.get("qwyCount").toString());

        return institutionStats;
    }


    public  void timeConversion(HashMap<String, Object> map){

        String monthStr = map.get("month").toString();
        String startYear = DateUtil.getBeginningOfTheYear(monthStr);
        String endYear = DateUtil.getEndOfTheYear(monthStr);
        String startMonth = DateUtil.getBeginningOfTheMonth(monthStr);
        String endMonth = DateUtil.getEndOfTheMonth(monthStr);

        map.put("startYear", startYear);
        map.put("endYear", endYear);
        map.put("startMonth", startMonth);
        map.put("endMonth", endMonth);
    }

    public HashMap<String, Object> getMapByList(List<HashMap<String, Object>> list, String lineKey, String lineName) {
        if (null != list && list.size() > 0) {
            HashMap<String, Object> map = new HashMap<>();
            map.put(lineKey, lineName);
            for (HashMap m : list) {
                if(m.get("TYPE")!=null){
                    m.put("type",m.get("TYPE"));
                }
                if ("0".equals(m.get("type").toString())) {//该月就诊人次
                    map.put("regCount", m.get("totalCount"));
                } else if ("10".equals(m.get("type").toString())) {//全年就诊人次
                    map.put("qregCount", m.get("totalCount"));
                } else if ("1".equals(m.get("type").toString())) {//该月内服药方数
                    map.put("nfCount", m.get("totalCount"));
                } else if ("2".equals(m.get("type").toString())) {//该月外用药方数
                    map.put("wyCount", m.get("totalCount"));
                } else if ("3".equals(m.get("type").toString())) {//该月中药药方数
                    map.put("zcCount", m.get("totalCount"));
                } else if ("4".equals(m.get("type").toString())) {//该月适宜技术药方数
                    map.put("syCount", m.get("totalCount"));
                } else if ("11".equals(m.get("type").toString())) {//全年内服
                    map.put("qnfCount", m.get("totalCount"));
                } else if ("12".equals(m.get("type").toString())) {//全年外用
                    map.put("qwyCount", m.get("totalCount"));
                } else if ("13".equals(m.get("type").toString())) {//全年中草
                    map.put("qzcCount", m.get("totalCount"));
                } else if ("14".equals(m.get("type").toString())) {//全年适宜技术
                    map.put("qsyCount", m.get("totalCount"));
                }
            }
            if (null == map.get("regCount")) {
                map.put("regCount", 0);
            }
            if (null == map.get("qregCount")) {
                map.put("qregCount", 0);
            }
            if (null == map.get("nfCount")) {
                map.put("nfCount", 0);
            }
            if (null == map.get("wyCount")) {
                map.put("wyCount", 0);
            }
            if (null == map.get("zcCount")) {
                map.put("zcCount", 0);
            }
            if (null == map.get("syCount")) {
                map.put("syCount", 0);
            }
            if (null == map.get("qnfCount")) {
                map.put("qnfCount", 0);
            }
            if (null == map.get("qwyCount")) {
                map.put("qwyCount", 0);
            }
            if (null == map.get("qzcCount")) {
                map.put("qzcCount", 0);
            }
            if (null == map.get("qsyCount")) {
                map.put("qsyCount", 0);
            }
            map.put("pCount", Integer.parseInt(map.get("nfCount").toString()) + Integer.parseInt(map.get("wyCount").toString()) + Integer.parseInt(map.get("zcCount").toString()) + Integer.parseInt(map.get("syCount").toString()));
            map.put("qpCount", Integer.parseInt(map.get("qnfCount").toString()) + Integer.parseInt(map.get("qwyCount").toString()) + Integer.parseInt(map.get("qzcCount").toString()) + Integer.parseInt(map.get("qsyCount").toString()));
            return map;
        }
        return null;
    }

    /**
     * 拼装统计列表标题
     *
     * @param insCode 机构编号
     * @param year    年
     * @param lower   是否包含下级 1：包含，0：不包含
     * @return
     * <AUTHOR>
     */
    public String getTitle(String insCode, String year, String lower) {
        StringBuffer sb = new StringBuffer(year);
        sb.append("年");
        if (StringUtils.isNotBlank(insCode)) {

            String insName = adminService.getInsName(insCode);
            sb.append(insName);
        }
        if ("1".equals(lower)) {
            sb.append("(包含下级)");
        }
        sb.append("工作量");
        return sb.toString();
    }

}
