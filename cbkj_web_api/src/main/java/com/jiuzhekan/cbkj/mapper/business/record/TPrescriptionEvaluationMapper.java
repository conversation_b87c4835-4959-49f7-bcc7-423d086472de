package com.jiuzhekan.cbkj.mapper.business.record;

import com.jiuzhekan.cbkj.beans.business.record.TPrescriptionEvaluation;
import com.jiuzhekan.cbkj.beans.business.record.TPrescriptionEvaluationSDK;
import com.jiuzhekan.cbkj.beans.business.record.TPrescriptionEvaluationSDKOther;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public interface TPrescriptionEvaluationMapper extends BaseMapper<TPrescriptionEvaluation> {

    /**
     * 物理删除
     */
    long physicalDeleteByPreId(List<String> draftPreIds);

    void insertListSDKOther(List<TPrescriptionEvaluationSDKOther> preEvaluationListSDKOther);
    void insertListSDK(List<TPrescriptionEvaluationSDK> preEvaluationListSDK);

    String getSDKOtherJsonByPreId(String preId);
}