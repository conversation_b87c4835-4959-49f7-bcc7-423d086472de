package com.jiuzhekan.cbkj.mapper.sysMapper;

import com.jiuzhekan.cbkj.beans.sysBeans.OperationLog;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;
import org.apache.commons.collections4.map.HashedMap;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public interface OperationLogMapper extends BaseMapper<OperationLog> {

    /**
     * 返回分页数据 List<Map>
     * @param t
     * @return
     */
    List<Map<String,Object>> getPageDatas(OperationLog t);

    /**
     * 创建时间后缀表
     * @param tableSuffix 表后缀
     */
    void createTimeTable(OperationLog t);

    OperationLog getObjectByMap(HashMap<String,String> map);
}