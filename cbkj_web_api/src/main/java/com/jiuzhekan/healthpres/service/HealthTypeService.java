package com.jiuzhekan.healthpres.service;

import com.jiuzhekan.healthpres.mapper.TUserQuestionnaireMainMapper;
import lombok.Data;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/6/5 17:18
 * @Version 1.0
 */
@Service
public class HealthTypeService {

    private final TUserQuestionnaireMainMapper tUserQuestionnaireMainMapper;

    public HealthTypeService(TUserQuestionnaireMainMapper tUserQuestionnaireMainMapper) {
        this.tUserQuestionnaireMainMapper = tUserQuestionnaireMainMapper;
    }

    public LinkedHashMap<String, String> getHealthType() {
        List<Map<String, Object>> commonDicList = tUserQuestionnaireMainMapper.getCommonDicList();
        LinkedHashMap<String, String> healthType = new LinkedHashMap<>();
        commonDicList.forEach(item -> {
            healthType.put(item.get("commonDicCode").toString(), item.get("commonDicName").toString());
        });
        return healthType;
    }
}
