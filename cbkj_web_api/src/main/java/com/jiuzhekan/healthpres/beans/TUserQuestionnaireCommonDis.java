package com.jiuzhekan.healthpres.beans;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class TUserQuestionnaireCommonDis implements Serializable{

    @ApiModelProperty(value = "疾病id")
    private String commonDisId;

    @ApiModelProperty(value = "疾病名称")
    private String commonDisName;

    @ApiModelProperty(value = "0正常1删除")
    private Integer status;

    @ApiModelProperty(value = "排序（升序排序）")
    private Integer sort;

    @ApiModelProperty(value = "疾病代码")
    private String commonDisCode;


}
