package com.jiuzhekan.cbkj.common.exception;

import lombok.Getter;

/**
 * ExceptionEnum
 * <p>
 * 杭州聪宝科技有限公司
 *
 * <AUTHOR>
 * @date 2021/7/8
 */
@Getter
public enum ExceptionEnum {

    /**
     * 错误
     */
    ERROR(-1, "错误"),

    /**
     * 无权限
     */
    ACCESS_DENIED(1000, "Access Denied"),

    /**
     * appId is not exist
     */
    APP_NOT_DEFAULT(1002, "appId is not exist"),

    /**
     * 缺少签名
     */
    UNABLE_SIGN(1003, "缺少签名！"),

    /**
     * 缺少签名
     */
    UNABLE_TIMESTAMP(1004, "缺少时间戳!"),

    /**
     * 缺少appId
     */
    UNABLE_APP_ID(1005, "缺少appId!"),

    /**
     * 缺少appId
     */
    EXPIRED_URI(1006, "uri过期!"),

    /**
     * 签名错误
     */
    ERROR_SIGN(1007, "签名错误！"),

    /**
     * 用户名或者密码错误
     */
    ERROR_PASSWORD(1008, "用户名或者密码错误"),

    /**
     * 用户名或者密码错误
     */
    ERROR_ACCOUNT(1009, "账户已被锁"),

    /**
     * 返回数据为空
     */
    DATA_EMPTY(1010, "返回数据为空！"),


    /**
     * 缺少file参数或者参数为空！
     */
    UNABLE_FILE(1011, "缺少file参数或者参数为空！"),

    /**
     * 消息缺少id参数或者参数为空
     */
    UNABLE_ID(1012, "缺少id参数或者参数为空！"),

    /**
     * 登录用户不存在
     */
    LOGIN_NAME(1013, "登录用户不存在!"),

    /**
     * 文件上传到OSS异常
     */
    ERROR_UPLOAD_OSS(1014, "文件上传到OSS异常!");



    private int code;

    private String message;


    ExceptionEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }
}
