package com.jiuzhekan.statistics.controller.personal;

import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.statistics.beans.personal.TStatisticsPersonalPrescription;
import com.jiuzhekan.statistics.service.personal.TStatisticsPersonalPrescriptionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@Api(value = "协定方统计表接口", tags = "协定方统计表接口")
@RequestMapping("tStatisticsPersonalPrescription")
public class TStatisticsPersonalPrescriptionController {

    @Autowired
    private TStatisticsPersonalPrescriptionService tStatisticsPersonalPrescriptionService;


    @RequestMapping(value = "getPages", method = RequestMethod.GET)
    @ApiOperation(value = "分页查询协定方统计表", notes = "分页查询协定方统计表", response = TStatisticsPersonalPrescription.class)
    @LogAnnotation("分页查询协定方统计表")
    @ResponseBody
    public Object getApps(TStatisticsPersonalPrescription tStatisticsPersonalPrescription, Page page){
        return tStatisticsPersonalPrescriptionService.getPageDatas(tStatisticsPersonalPrescription,page);
    }


}