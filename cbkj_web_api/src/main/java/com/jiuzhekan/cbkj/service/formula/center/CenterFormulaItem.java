package com.jiuzhekan.cbkj.service.formula.center;

import com.jiuzhekan.cbkj.beans.formula.TFormulaItem;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * CenterFormulaItem
 * <p>
 * 杭州聪宝科技有限公司
 *
 * <AUTHOR>
 * @date 2021/8/11
 */
@Data
@NoArgsConstructor
public class CenterFormulaItem {
    /**
     * 配方明细id
     */
    private String persPreItemId;
    /**
     * 配方id
     */
    private String persPreId;
    /**
     * Appid
     */
    private String appId;
    /**
     * 医疗机构id
     */
    private String insId;
    /**
     * 药品价格id
     */
    private String appMatCode;
    /**
     * 药品名称
     */
    private String appMatName;
    /**
     * 产地id
     */
    private String appMatOricode;
    /**
     * 产地名称
     */
    private String appMatOrigin;
    /**
     * 数量
     */
    private BigDecimal dose;
    /**
     * 单位
     */
    private String unitName;
    /**
     * 用法
     */
    private String useageName;
    /**
     * 顺序号
     */
    private String seqn;
    /**
     * 销售价
     */
    private BigDecimal matXsj;

    public CenterFormulaItem(TFormulaItem formulaItem) {
        this.persPreItemId = formulaItem.getItemId();
        this.persPreId = formulaItem.getFormulaId();
        this.appMatCode = formulaItem.getYpdmCenter();
        this.appMatName = formulaItem.getYpmcCenter();
        this.dose = formulaItem.getMatDose();
        this.unitName = formulaItem.getBzdwHis();
        this.useageName = formulaItem.getYfmcHis();
        this.seqn = String.valueOf(formulaItem.getMatSeqn() + 1);
        this.matXsj = formulaItem.getMatXsj();
    }
}
