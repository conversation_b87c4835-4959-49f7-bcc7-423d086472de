package com.jiuzhekan.healthpres.mapper;

import com.jiuzhekan.healthpres.beans.TUserQuestionnaireMain;
import com.jiuzhekan.healthpres.beans.healthPrescribingControllerVo.GetTypesHealthPrescribingContent;
import com.jiuzhekan.healthpres.beans.heathPresControllerVo.GetHealthPreModel;
import com.jiuzhekan.healthpres.beans.heathPresControllerVo.HealthPreModelMainResponse;
import org.springframework.stereotype.Component;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Component
public interface TUserQuestionnaireMainMapper extends BaseMapper<TUserQuestionnaireMain>{


    HealthPreModelMainResponse getHealthPreModel(GetHealthPreModel getHealthPreModel);

    List<HealthPreModelMainResponse> getHealthPreModelAllList();

    List<Map<String,Object>> getCommonDicList();
}