package com.jiuzhekan.cbkj.common.config.security;

import com.alibaba.fastjson.JSONObject;
import com.jiuzhekan.cbkj.beans.sysBeans.AdminInfo;
import com.jiuzhekan.cbkj.beans.sysBeans.SysAdminPractice;
import com.jiuzhekan.cbkj.service.redis.ClientRedisService;
import com.jiuzhekan.cbkj.service.redis.RedisService;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Clock;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.impl.DefaultClock;
import io.jsonwebtoken.impl.TextCodec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2019年12月13日 14:35:00
 */
@Component
public class TokenUtil implements Serializable {
    private static final long serialVersionUID = -3301605591108950415L;

    @Autowired
    private RedisService redisService;
    @Autowired
    private ClientRedisService clientRedisService;

    @Value("${jwt.secret}")
    private String secret;

    @Value("${jwt.expiration}")
    private Long expiration;

    private Clock clock = DefaultClock.INSTANCE;

    public static final String TOKEN_PREFIX = "PRE_AI_TOKEN_";

    @Value("${login.one}")
    private boolean loginOne;

    public String createTokenKey(String adminId, String appId, String insCode) {
        return TOKEN_PREFIX + adminId + "_" + appId + "_" + insCode;
    }


    /**
     * 创建Token
     *
     * @param sessionId sso的sessionId
     * @param admin     admin
     * @param practice  practice
     * @return com.jiuzhekan.cbkj.common.config.security.TokenBo
     * <AUTHOR>
     * @date 2021/7/12
     */
    public TokenBo createTokenBo(String sessionId, AdminInfo admin, SysAdminPractice practice) {

        return new TokenBo(sessionId, sessionId, admin.getId(), practice.getAppId(), practice.getInsCode(), admin, practice);
    }

    /**
     * 创建Token
     *
     * @param admin    admin
     * @param practice practice
     * @return com.jiuzhekan.cbkj.common.config.security.TokenBo
     * <AUTHOR>
     * @date 2021/7/12
     */
    public TokenBo createTokenBo(AdminInfo admin, SysAdminPractice practice) {

        String tokenKey = createTokenKey(admin.getId(), practice.getAppId(), practice.getInsCode());
        String authorization = generateToken(tokenKey);
        TokenBo tokenBo = new TokenBo(tokenKey, authorization, admin.getId(), practice.getAppId(), practice.getInsCode(), admin, practice);

        clientRedisService.putToken(tokenKey, tokenBo);
        return tokenBo;
    }

    /**
     * 更新Token
     *
     * @param tokenBo tokenBo
     * <AUTHOR>
     * @date 2021/7/12
     */
    public void updateTokenBo(TokenBo tokenBo) {

        clientRedisService.putToken(tokenBo.getTokenKey(), tokenBo);
    }

    /**
     * 更新Token
     *
     * @param tokenBo    tokenBo
     * @param registerId registerId
     * <AUTHOR>
     * @date 2021/7/12
     */
    public void updateTokenBo(TokenBo tokenBo, String registerId) {

        tokenBo.setRegister(clientRedisService.getRegisterById(registerId));

        clientRedisService.putToken(tokenBo.getTokenKey(), tokenBo);
    }

    /**
     * 获取Token
     *
     * @param tokenKey tokenKey
     * @return com.jiuzhekan.cbkj.common.config.security.TokenBo
     * <AUTHOR>
     * @date 2021/7/12
     */
    public TokenBo getTokenBo(String tokenKey) {
        return clientRedisService.getToken(tokenKey);
    }

    public void deleteTokens(String tokenKey) {
        redisService.clearRedisCache("client::token::" + tokenKey, null);
    }

    public void deleteToken(String tokenKey) {
        redisService.clearRedisCache(null, "client::token::" + tokenKey);
    }


    /**
     * 登录信息交给Security管理
     *
     * @param tokenBo tokenBo
     * <AUTHOR>
     * @date 2021/7/12
     */
    public void setSecurityContext(TokenBo tokenBo) {
        if (tokenBo != null && SecurityContextHolder.getContext().getAuthentication() == null) {

            UsernamePasswordAuthenticationToken auth = new UsernamePasswordAuthenticationToken(tokenBo, null, new ArrayList<>());

            SecurityContextHolder.getContext().setAuthentication(auth);
        }
    }


    public String generateToken(String key) {
        return doGenerateToken(new HashMap<>(), key);
    }

    private String doGenerateToken(Map<String, Object> claims, String subject) {

        final Date createdDate = clock.now();
        final Date expirationDate = calculateExpirationDate(createdDate);
        return Jwts.builder()
                .setClaims(claims)
                .setSubject(subject)
                .setIssuedAt(createdDate)
                .setExpiration(expirationDate)
                .signWith(SignatureAlgorithm.HS512, secret)
                .compact();
    }

    private Date calculateExpirationDate(Date createdDate) {
        return new Date(createdDate.getTime() + expiration);
    }

    public String getKeyFromToken(String token) {
        return getClaimFromToken(token, Claims::getSubject);
    }

    public <T> T getClaimFromToken(String token, Function<Claims, T> claimsResolver) {
        final Claims claims = getAllClaimsFromToken(token);
        return claimsResolver.apply(claims);
    }

    private Claims getAllClaimsFromToken(String token) {
        return Jwts.parser()
                .setSigningKey(secret).setAllowedClockSkewSeconds(0)
                .parseClaimsJws(token)
                .getBody();
    }

    private Boolean isTokenExpired(String token) {
        final Date expiration = getExpirationDateFromToken(token);
        return expiration.before(clock.now());
    }

    public Date getExpirationDateFromToken(String token) {
        return getClaimFromToken(token, Claims::getExpiration);
    }

    public Map<String, Object> getKeyAndExp(String token) {
        Map<String, Object> map = new HashMap<>();
        String base64UrlEncodedPayload = token.split("\\.")[1];
        String payload = TextCodec.BASE64URL.decodeToString(base64UrlEncodedPayload);
        if (payload.charAt(0) == '{' && payload.charAt(payload.length() - 1) == '}') {
            JSONObject object = JSONObject.parseObject(payload);
            Object sub = object.get("sub");
            Object exp = object.get("exp");
            Object iat = object.get("iat");
            map.put("key", sub);
            map.put("expirationTime", ((Number) exp).longValue());
            map.put("loginTime", ((Number) iat).longValue());

        }
        return map;
    }

    public boolean getLoginOne() {
        return loginOne;
    }
}
