package com.jiuzhekan.healthpres.beans.heathPresControllerVo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/5/27 13:53
 * @Version 1.0
 */
@ApiModel
@Data
public class HealthPreModelMainResponse {

    @ApiModelProperty(value = "模板内容id")
    private String questionnaireMainId;

    @ApiModelProperty(value = "标题")
    private String questionnaireMainTitle;

    @ApiModelProperty(value = "内容首行文本")
    private String questionnaireMainFirstLine;

    @ApiModelProperty(value = "内容第二行提示。")
    private String questionnaireMainTips;

    @ApiModelProperty(value = "1.儿童保健2.慢病调理")
    private Integer questionnaireMainType;

    @ApiModelProperty(value = "子分类 1-1(6、12月龄儿童)1-2(18、24月龄儿童)1-3(30、36月龄儿童) 等")
    private String questionnaireMainSecondTypeCode;

    @ApiModelProperty(value = "")
    private String questionnaireMainSecondTypeName;

    @ApiModelProperty(value = "模板明细")
    private List<HealthPreModelMainItemResponse> questionnaireMainItemList;

}
