package com.jiuzhekan.cbkj.controller.myData;

import com.jiuzhekan.cbkj.beans.myData.TPersonalPrescriptionFolder;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.service.myData.TPersonalPrescriptionFolderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

@Controller
@RequestMapping("tPersonalPrescriptionFolder")
@Api(value = "协定方文件夹", tags = "协定方文件夹")
public class TPersonalPrescriptionFolderController {

    @Autowired
    private TPersonalPrescriptionFolderService tPersonalPrescriptionFolderService;


    @RequestMapping(value = "getPages", method = RequestMethod.GET)
    @ApiOperation(value = "分页查询协定方文件夹", response = TPersonalPrescriptionFolder.class)
    @LogAnnotation("分页查询协定方文件夹")
    @ResponseBody
    public Object getApps(TPersonalPrescriptionFolder tPersonalPrescriptionFolder, Page page) {
        return tPersonalPrescriptionFolderService.getPageDatas(tPersonalPrescriptionFolder, page);
    }

    @RequestMapping(value = "/tree/show", method = RequestMethod.GET)
    @LogAnnotation("获取医生可以查看的文件夹树")
    @ApiOperation(value = "获取医生可以查看的文件夹树", notes = "获取医生可以查看的文件夹树")
    @ResponseBody
    public Object getShowFolderTree() {
        return ResEntity.entity(true, Constant.SUCCESS_DX, tPersonalPrescriptionFolderService.getShowFolderTree());
    }

    @RequestMapping(value = "/tree/know", method = RequestMethod.GET)
    @LogAnnotation("获取包含方剂的医生可以查看的文件夹树")
    @ApiOperation(value = "获取包含方剂的医生可以查看的文件夹树", notes = "获取包含方剂的医生可以查看的文件夹树")
    @ResponseBody
    public Object getKnowFolderTree(String preType) {
        return ResEntity.entity(true, Constant.SUCCESS_DX, tPersonalPrescriptionFolderService.getKnowFolderTree(preType));
    }

    @RequestMapping(value = "/tree/share", method = RequestMethod.GET)
    @LogAnnotation("获取医生可以共享的文件夹树")
    @ApiOperation(value = "获取医生可以共享的文件夹树", notes = "获取医生可以共享的文件夹树")
    @ResponseBody
    public Object canUpdateFolderTree() {
        return ResEntity.entity(true, Constant.SUCCESS_DX, tPersonalPrescriptionFolderService.getShareFolderTree());
    }

    @RequestMapping(value = "findObj", method = RequestMethod.GET)
    @LogAnnotation("加载协定方文件夹详情")
    @ApiOperation(value = "加载协定方文件夹详情", notes = "加载协定方文件夹详情")
    @ApiImplicitParam(name = "id", value = "协定方文件夹id", dataType = "String", paramType = "query")
    @ResponseBody
    public Object getObj(String id) {
        return tPersonalPrescriptionFolderService.findObj(id);
    }

    @RequestMapping(value = "insert", method = RequestMethod.POST)
    @LogAnnotation(value = "新增协定方文件夹", isWrite = true)
    @ApiOperation(value = "新增协定方文件夹", notes = "新增协定方文件夹")
    @ResponseBody
    public Object insert(@RequestBody TPersonalPrescriptionFolder tPersonalPrescriptionFolder) {
        return tPersonalPrescriptionFolderService.insert(tPersonalPrescriptionFolder);
    }

    @RequestMapping(value = "update", method = RequestMethod.POST)
    @LogAnnotation(value = "修改协定方文件夹", isWrite = true)
    @ApiOperation(value = "修改协定方文件夹", notes = "修改协定方文件夹")
    @ResponseBody
    public Object update(@RequestBody TPersonalPrescriptionFolder tPersonalPrescriptionFolder) {
        return tPersonalPrescriptionFolderService.update(tPersonalPrescriptionFolder);
    }

    @RequestMapping(value = "moveNum", method = RequestMethod.POST)
    @LogAnnotation(value = "移动协定方文件夹", isWrite = true)
    @ApiOperation(value = "移动协定方文件夹", notes = "移动协定方文件夹")
    @ResponseBody
    public Object moveNum(@RequestBody List<TPersonalPrescriptionFolder> list) {
        return tPersonalPrescriptionFolderService.moveNum(list);
    }

    @RequestMapping(value = "delete", method = RequestMethod.GET)
    @LogAnnotation(value = "删除协定方文件夹", isWrite = true)
    @ApiOperation(value = "删除协定方文件夹", notes = "删除协定方文件夹")
    @ApiImplicitParam(name = "id", value = "协定方文件夹id", dataType = "String", paramType = "query", required = true)
    @ResponseBody
    public Object deleteLis(String id) throws Exception {
        return tPersonalPrescriptionFolderService.delete(id);
    }

}