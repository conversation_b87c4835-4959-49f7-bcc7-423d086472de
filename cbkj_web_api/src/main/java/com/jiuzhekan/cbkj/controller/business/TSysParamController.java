package com.jiuzhekan.cbkj.controller.business;

import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.service.business.TSysParamService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

@Api(value = "参数管理接口", tags = "参数管理接口")
@Controller
@RequestMapping("business/")
public class TSysParamController {

    @Autowired
    private TSysParamService tSysParamService;


    @RequestMapping(value = "tSysParam/getMap", method = RequestMethod.GET)
    @ApiOperation(value = "获取业务参数管理", notes = "获取业务参数管理", response = String.class)
    @LogAnnotation(value = "获取业务参数管理")
    @ResponseBody
    public Object getApps(String registerId) {
        return tSysParamService.getPageDatas(registerId);
    }

//    @RequestMapping(value = "tSysParam/getPages", method = RequestMethod.GET)
//    @ApiOperation(value = "分页查询参数管理", notes = "分页查询参数管理", response = TSysParam.class)
//    @LogAnnotation(value = "分页查询参数管理")
//    @ResponseBody
//    public Object getApps(TSysParam tSysParam, Page page) {
//        return tSysParamService.getPageDatas(tSysParam, page);
//    }
//
//    @RequestMapping(value = "tSysParam/findObj", method = RequestMethod.GET)
//    @ApiOperation(value = "加载参数管理详情", notes = "加载参数管理详情", response = TSysParam.class)
//    @LogAnnotation(value = "加载参数管理详情")
//    @ResponseBody
//    public Object getObj(TSysParam tSysParam) {
//        return tSysParamService.findObj(tSysParam);
//    }
//
//    @RequestMapping(value = "tSysParam/save", method = RequestMethod.POST)
//    @ApiOperation(value = "保存参数配置", notes = "保存参数配置")
//    @LogAnnotation(value = "保存参数配置", isWrite = true)
//    @ResponseBody
//    public Object save(@RequestBody TSysParamVO params) {
//        return tSysParamService.save(params);
//    }
//
//    @RequestMapping(value = "tSysParam/delete", method = RequestMethod.GET)
//    @ApiOperation(value = "删除参数管理", notes = "删除参数管理")
//    @LogAnnotation(value = "删除参数管理", isWrite = true)
//    @ResponseBody
//    public Object delete(TSysParam tSysParam) {
//        return tSysParamService.deleteByParCode(tSysParam);
//    }
//
//    @RequestMapping(value = "tSysParam/classifyAll", method = RequestMethod.GET)
//    @ApiOperation(value = "获取所有参数分类", notes = "删除参数管理")
//    @LogAnnotation(value = "获取所有参数分类", isWrite = true)
//    @ResponseBody
//    public Object getAllParamClassifyList(){
//        return tSysParamService.getAllParamClassifyList();
//    }
}