package com.jiuzhekan.healthpres.beans;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class TUserQuestionnaireMain implements Serializable{

    @ApiModelProperty(value = "")
    private String questionnaireMainId;

    @ApiModelProperty(value = "标题")
    private String questionnaireMainTitle;

    @ApiModelProperty(value = "内容首行文本")
    private String questionnaireMainFirstLine;

    @ApiModelProperty(value = "内容第二行提示。")
    private String questionnaireMainTips;

    @ApiModelProperty(value = "1.儿童保健2.慢病调理")
    private Integer questionnaireMainType;

    @ApiModelProperty(value = "子分类1-1(6、12月龄儿童)1-2(18、24月龄儿童)1-3(30、36月龄儿童) ")
    private String questionnaireMainSecondTypeCode;

    @ApiModelProperty(value = "")
    private String questionnaireMainSecondTypeName;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建人名")
    private String createUserName;

    @ApiModelProperty(value = "创建人id")
    private String createUserId;

    @ApiModelProperty(value = "0.正常1删除")
    private Integer status;


}
