package com.jiuzhekan.statistics.beans.function;

import com.jiuzhekan.statistics.beans.register.ChangeStaticsFunction2;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date $ $
 **/
@Data
@ApiModel("系统学习统计")
public class FunctionStaticsResult {
    @ApiModelProperty(value = "统计所有数据总和")
    List<SimpleP> function2List;
    @ApiModelProperty(value = "医共体-机构的统计数据")
    List<ChangeStaticsFunction2> changeMaps;
}
