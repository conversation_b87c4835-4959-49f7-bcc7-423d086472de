package com.jiuzhekan.cbkj.mapper.formula;

import com.jiuzhekan.cbkj.beans.formula.TFormulaAuth;
import com.jiuzhekan.cbkj.service.formula.center.CenterFormulaStock;
import org.springframework.stereotype.Component;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;

import java.util.List;

@Component
public interface TFormulaAuthMapper extends BaseMapper<TFormulaAuth> {

    /**
     * 删除配方库存
     *
     * @param formulaIds formulaIds
     * <AUTHOR>
     * @date 2021/9/24
     */
    void deleteByFormulaIds(List<String> formulaIds);

    /**
     * 删除配方库存
     *
     * <AUTHOR>
     * @date 2021/9/24
     */
    void deleteFormulaIdNotExists();

    /**
     * 删除配方库存
     *
     * @param stockList stockList
     * <AUTHOR>
     * @date 2021/9/24
     */
    void deleteByCenterFormulaStocks(List<CenterFormulaStock> stockList);

    /**
     * 保存配方库存
     *
     * @param stockList stockList
     * <AUTHOR>
     * @date 2021/9/24
     */
    void insertListByCenterFormulaStocks(List<CenterFormulaStock> stockList);


}