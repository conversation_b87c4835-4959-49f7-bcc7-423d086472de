package com.jiuzhekan.cbkj.controller.business.doctor;


import com.jiuzhekan.cbkj.beans.business.doctor.TDoctorNote;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.common.annotaionUtil.StatisticsFunction;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.service.business.doctor.TDoctorNoteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("tDoctorNote")
@Api(value = "医生笔记相关接口", tags = {"医生笔记相关接口"})
public class TDoctorNoteController {

    @Autowired
    private TDoctorNoteService tDoctorNoteService;

    @RequestMapping(value = "/getPages", method = RequestMethod.GET)
    @ApiOperation(value = "常规分页查询笔记", notes = "常规分页查询笔记,按时间顺序排序", response = TDoctorNote.class)
    @ResponseBody
    @LogAnnotation("常规分页查询笔记")
    public Object getApps(TDoctorNote tDoctorNote, Page page) {
        Object obj = tDoctorNoteService.getPageDatas(tDoctorNote, page);
        return obj;
    }

    @RequestMapping(value = "/getNotes", method = RequestMethod.GET)
    @ApiOperation(value = "分页查询笔记,本人在前", notes = "分页查询笔记,自己的笔记放在最前面，其他人的笔记按时间倒序"
            , response = TDoctorNote.class)
    @ResponseBody
    @LogAnnotation("分页查询笔记,本人在前")
    public Object getNotes(TDoctorNote tDoctorNote, Page page) {
        return tDoctorNoteService.getNotes(tDoctorNote, page);
    }

    @RequestMapping(value = "/getNoteByUser", method = RequestMethod.GET)
    @ApiOperation(value = "根据关联id查询笔记", notes = "根据关联id查询笔记,后台获取登录人"
            , response = TDoctorNote.class)
    @ResponseBody
    @LogAnnotation("根据关联id和登录人查询笔记")
    public Object getNoteByUser(TDoctorNote tDoctorNote) {
        return tDoctorNoteService.getNoteByUser(tDoctorNote);
    }


    @RequestMapping(value = "/update/findObj", method = RequestMethod.GET)
    @ApiOperation(value = "加载医生笔记表详情", notes = "加载医生笔记表详情", response = TDoctorNote.class)
    @ApiImplicitParam(name = "id", value = "笔记id", dataType = "String", paramType = "query")
    @LogAnnotation("加载医生笔记表详情")
    @ResponseBody
    public Object getObj(String id) {
        ResEntity result = tDoctorNoteService.findObj(id);
        return result;
    }

    @RequestMapping(value = "/insert", method = RequestMethod.POST)
    @ApiOperation(value = "新增医生笔记表", notes = "新增医生笔记表,新增笔记默认收藏")
    @LogAnnotation(value = "新增医生笔记表,新增笔记默认收藏", isWrite = true)
    @StatisticsFunction(source = "名医验案", value = "笔记数")
    @ResponseBody
    public Object insert(@RequestBody TDoctorNote tDoctorNote) {
        ResEntity result = tDoctorNoteService.insert(tDoctorNote);
        return result;
    }

    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @ApiOperation(value = "修改医生笔记表", notes = "修改医生笔记表,不影响收藏,某个字段不传值就是不修改那个字段,sql做了if判断")
    @LogAnnotation(value = "修改医生笔记表", isWrite = true)
    @ResponseBody
    public Object update(@RequestBody TDoctorNote tDoctorNote) {
        ResEntity result = tDoctorNoteService.update(tDoctorNote);
        return result;
    }

    @RequestMapping(value = "/deleteLis", method = RequestMethod.GET)
    @LogAnnotation(value = "删除医生笔记表", isWrite = true)
    @ApiOperation(value = "物理删除医生笔记表", notes = "物理删除医生笔记表,不影响收藏")
    @ApiImplicitParam(name = "ids", value = "笔记id用英文逗号分隔,物理删除", dataType = "String", paramType = "query")
    @ResponseBody
    public Object deleteLis(String ids) throws Exception {
        ResEntity result = tDoctorNoteService.deleteLis(ids);
        return result;
    }
}