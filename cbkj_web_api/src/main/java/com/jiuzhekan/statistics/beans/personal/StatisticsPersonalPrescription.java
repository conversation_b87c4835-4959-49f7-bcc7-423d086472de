package com.jiuzhekan.statistics.beans.personal;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 **/
@Data
public class StatisticsPersonalPrescription {
    @ApiModelProperty(value = "自增主键")
    private Integer id;

    @ApiModelProperty(value = "医联体ID")
    private String appId;
    @ApiModelProperty(value = "医疗机构代码")
    private String insCode;
    @ApiModelProperty(value = "用户ID")
    private String userId;
    private String userName;
    @ApiModelProperty(value = "协定方总数")
    private Integer totalNum;

    @ApiModelProperty(value = "个人协定方数量")
    private Integer selfNum;

    @ApiModelProperty(value = "科室协定方数量")
    private Integer deptNum;

    @ApiModelProperty(value = "全院协定方数量")
    private Integer insNum;

    @ApiModelProperty(value = "专家经验共享数量")
    private Integer appNum;

    private Date dataDate;

    //最新统计次数
    private int number;
}
