package com.jiuzhekan.healthpres.beans.healthPrescribingControllerVo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/5/27 13:53
 * @Version 1.0
 */
@ApiModel
@Data
public class HealthPrescribingRequestItemVO {

    @NotNull(message = "itemId不能为空")
    @ApiModelProperty(value = "模板明细id")
    private String itemId;

    @NotNull(message = "itemType不能为空")
    @ApiModelProperty(value = "1.纯文本2.下拉选择")
    private Integer itemType;

    @NotBlank(message = "标题不能为空")
    @ApiModelProperty(value = "标题")
    private String itemTitle;

    @NotBlank(message = "itemContent不能为空")
    @ApiModelProperty(value = "内容")
    private String itemContent;

    @ApiModelProperty(value = "穴位选择的id。")
    private String itemSelectId;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @NotNull(message = "健康处方中是否展示：0前端展示1前端不展示不能空")
    @ApiModelProperty(value = "健康处方中是否展示：0前端展示1前端不展示")
    private Integer showType;

    @ApiModelProperty(value = "穴位定位")
    private List<HealthSavePreResultAcu> healthSavePreResultAcuList;

}
