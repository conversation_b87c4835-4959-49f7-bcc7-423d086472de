package com.jiuzhekan.cbkj.service.business.his;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jiuzhekan.cbkj.beans.business.store.HTTPDisPlay;
import com.jiuzhekan.cbkj.beans.business.store.TDisplay;
import com.jiuzhekan.cbkj.beans.drug.CenterHisMappingVO;
import com.jiuzhekan.cbkj.beans.drug.MatVo;
import com.jiuzhekan.cbkj.beans.drug.SpecialPreMat;
import com.jiuzhekan.cbkj.beans.drug.TransCenterHisMappingVoToMatVo;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.http.DrugsRestTemplate;
import com.jiuzhekan.cbkj.common.http.InterfaceRestTemplate;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.DateUtil;
import com.jiuzhekan.cbkj.controller.common.vo.TransSpecialMatVo;
import com.jiuzhekan.cbkj.mapper.drug.TCenterYpkcMapper;
import com.jiuzhekan.cbkj.service.business.TSysParamService;
import com.jiuzhekan.cbkj.service.business.treatment.SearchMatService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.jiuzhekan.cbkj.beans.drug.TransCenterHisMappingVoToMatVo.transBeanToDisplay;

/**
 * HisSpecialPreService
 * <p>
 * 杭州聪宝科技有限公司
 * <p>
 *
 * <AUTHOR>
 * @date 2021/5/25 10:42
 */
@Service
public class HisSpecialPreService {


    @Autowired
    private TSysParamService tSysParamService;
    @Autowired
    private SearchMatService searchMatService;
    @Autowired
    private TCenterYpkcMapper tCenterYpkcMapper;
    @Autowired
    private InterfaceRestTemplate interfaceRestTemplate;
    @Autowired
    private DrugsRestTemplate drugsRestTemplate;

    /**
     * 获取HIS特病治疗方案
     *
     * @param registerId 挂号ID
     * <AUTHOR>
     * @date 2021/1/25
     */
    public ResEntity hisSpecialPres(String registerId) {
        if (StringUtils.isBlank(registerId)) {
            return ResEntity.entity(false, "挂号ID不能为空", null);
        }

        String specialDisPresSource = tSysParamService.getSysParam(Constant.SPECIAL_DIS_PRES_SOURCE).getParValues();
        if ("his".equals(specialDisPresSource)) {
            //接口调用HIS特病治疗方案
            ResEntity sRes = interfaceRestTemplate.getSpecialDis(registerId);

//            List<Map<String, String>> sData = new ArrayList<>();
//            sData.add(createHisMat("39037", "艾叶", "3", "g"));
//            sData.add(createHisMat("7153", "艾叶", "3", "g"));
//            sData.add(createHisMat("42226", "P艾叶", "5", "g"));
//            sData.add(createHisMat("36262", "P艾叶", "5", "g"));
//
//            sData.add(createHisMat("38875", "当归", "10", "g"));
//            sData.add(createHisMat("6791", "当归", "10", "g"));
//            sData.add(createHisMat("6792", "当归", "10", "g"));
//            sData.add(createHisMat("36013", "P当归", "10", "g"));
//            sData.add(createHisMat("36014", "P当归", "10", "g"));
//            sData.add(createHisMat("42521", "P当归", "10", "g"));
//
//            sData.add(createHisMat("36056", "P羌活", "10", "g"));
//            sData.add(createHisMat("42658", "P羌活", "10", "g"));
//            sData.add(createHisMat("39783", "羌活", "10", "g"));
//            sData.add(createHisMat("6943", "羌活", "10", "g"));
//            sData.add(createHisMat("39783", "羌活", "10", "g"));
//
//            sData.add(createHisMat("36305", "P佩兰", "10", "克"));
//            sData.add(createHisMat("CY000187", "红曲*", "10", "g"));
//            sData.add(createHisMat("bc21102", "大腹皮", "6", "克"));
//            ResEntity sRes = ResEntity.entity(true, "", sData);

            if (sRes.getStatus()) {

                ResEntity res = searchMatService.getHisYpmlId();
                if (!res.getStatus()) {
                    return null;
                }

                String ypmlId = (String) res.getData();

                Map<String, Object> pre = new HashMap<>();
                //List<String> ypdmList = new ArrayList<>();
                JSONArray jsonArray = new JSONArray();
                List<SpecialPreMat> fromMats = new ArrayList<>();

                List<LinkedHashMap<String, String>> data = (List<LinkedHashMap<String, String>>) sRes.getData();

                for (LinkedHashMap<String, String> preMat : data) {

                    String matCode = preMat.get("qordDR");
                    String matName = preMat.get("qorderDesc");
                    String matDose = preMat.get("qordQty");
                    String matUnit = preMat.get("qordUOM");
                    String matUnitId = preMat.get("quomdr");
                    String preFreq = preMat.get("qordFreq");
                    String preFreqId = preMat.get("qfreqDR");
                    String specialDis = preMat.get("qdiag");
                    String hsaStartDate = preMat.get("hsastartDate");
                    String hsaEndDate = preMat.get("hsaendDate");

                    SpecialPreMat mat = new SpecialPreMat();
                    mat.setHisYpdm(matCode);
                    mat.setHisYpmc(matName);
                    mat.setHisYpdw(matUnit);
                    mat.setHasStartDate(hsaStartDate);
                    mat.setHasEndDate(hsaEndDate);
                    if (StringUtils.isNotBlank(matDose)) {
                        mat.setDose(new BigDecimal(matDose));
                    }

                    fromMats.add(mat);
                    //ypdmList.add(matCode);
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("matPriceId", matCode);
                    jsonArray.add(jsonObject);
                    pre.put("specialDis", specialDis);
                }

                List<TDisplay> displayList = new ArrayList<>();
                List<TDisplay> displayInCurrent = searchMatService.getDisplayList(AdminUtils.getCurrentRegister());
//                List<TDisplay> displayInMat = tCenterYpkcMapper.getDisplayInMatList(ypmlId, ypdmList);
                List<TDisplay> displayInMat = new ArrayList<>();
//2022-06-21 改为接口调用
                ResEntity post = drugsRestTemplate.post("get/displayByMatPriceIds", jsonArray);
                if (post.getStatus() && 200 == post.getCode()) {
                    //药品查询服务返回药品列表
                    List<HTTPDisPlay> matVos = JSON.parseArray(JSON.toJSONString(post.getData()), HTTPDisPlay.class);
                    //转换
                    displayInMat = matVos.stream().map(m -> transBeanToDisplay(m, new TDisplay())).collect(Collectors.toList());

                }
                Set<String> displaySet = new HashSet<>();
                if (displayInMat.size() > 0) {
                    for (TDisplay display : displayInMat) {
                        displaySet.add(display.getStoreId() + "-" + display.getPreMatType());
                    }

                    for (TDisplay display : displayInCurrent) {
                        if (displaySet.contains(display.getStoreId() + "-" + display.getPreMatType())) {
                            displayList.add(display);
                        }
                    }
                }


                String specialDisModifyColumn = tSysParamService.getSysParam(Constant.SPECIAL_DIS_MODIFY_COLUMN).getParValues();
                pre.put("specialDisModifyColumn", specialDisModifyColumn);
                pre.put("ismodify", "0");
                pre.put("items", fromMats);
                pre.put("displayList", displayList);
                sRes.setData(pre);

            }
            return sRes;
        }
        return ResEntity.entity(true, "", null);
    }


    /**
     * 特病药品转换
     *
     * @param vo 特病药品
     * <AUTHOR>
     * @date 2021/6/17
     */
    public List<SpecialPreMat> transSpecialMat(TransSpecialMatVo vo) {

        Date currentDate = new Date();
        List<SpecialPreMat> toMats = new ArrayList<>();
        LinkedList<SpecialPreMat> specialMatList = new LinkedList<>();

        List<CenterHisMappingVO> mxList = new ArrayList<CenterHisMappingVO>();
        //调用接口获取转换药
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("matType", vo.getCenterYplx());//转换后的中药类型（规格表中）
        jsonObject.put("phaId", vo.getCenterStoreId());//转换后的药房id
        //jsonObject.put("fromKnowleage", request.isFromKnow()?1:0);//是否来自知识库 0 否 1是
        jsonObject.put("drugIdHis", vo.getDrugId());//his药品目录id
        JSONArray objects = new JSONArray();
        for (int i = 0; i < vo.getFromMats().size(); i++) {
            MatVo matVo1 = TransCenterHisMappingVoToMatVo.transCenterHisMappingVoToMatVo(new MatVo(), vo.getFromMats().get(i));
            objects.add(JSONObject.toJSON(matVo1));
        }
        jsonObject.put("matList", objects);//原药品列表
        ResEntity post = drugsRestTemplate.post("mat/getChangeMat", jsonObject);
        List<LinkedHashMap> data = (List<LinkedHashMap>) post.getData();
        if (post.getStatus() && 200 == post.getCode()) {
            String parValues = tSysParamService.getSysParam(Constant.MAT_SPECIAL_USAGE_SOURCE).getParValues();
            List<MatVo> matVos = JSON.parseArray(JSON.toJSONString(data), MatVo.class);
            for (MatVo matVo : matVos) {
                CenterHisMappingVO vo2 = new CenterHisMappingVO();
                CenterHisMappingVO centerHisMappingVO = TransCenterHisMappingVoToMatVo.transferMatBean(matVo, vo2,parValues);
                mxList.add(centerHisMappingVO);
            }
        }
        for (int i = 0; i < mxList.size(); i++) {
            CenterHisMappingVO toMat = mxList.get(i);
            SpecialPreMat mat = vo.getFromMats().get(i);
            if (vo.getCenterYplx() == null) {
                vo.setCenterYplx(toMat.getCenterYplx());
            }
            if (vo.getCenterStoreId() == null) {
                vo.setCenterStoreId(toMat.getCenterStoreId());
            }
            toMat.setDose(mat.getDose());
            SpecialPreMat specialPreMat = new SpecialPreMat(toMat);

            //能否使用
            boolean canUsage = true;
            StringBuilder specialTip = new StringBuilder();
            if (toMat.getCenterKucunsl() == null || toMat.getCenterKucunsl().compareTo(Constant.BIG_DECIMAL_ZERO) <= 0) {
                specialTip.append("库存为0");
                canUsage = false;
            }

            specialPreMat.setHasStartDate(mat.getHasStartDate());
            specialPreMat.setHasEndDate(mat.getHasEndDate());

            Date hasStartDate = DateUtil.getDateFormatd(mat.getHasStartDate(), DateUtil.YYYY_MM_DD);
            Date hasEndDate = DateUtil.getDateFormatd(mat.getHasEndDate(), DateUtil.YYYY_MM_DD);

            if (hasStartDate != null && hasStartDate.compareTo(currentDate) > 0 && hasEndDate != null && hasEndDate.compareTo(currentDate) < 0) {
                if (specialTip.length() > 0) {
                    specialTip.append("，");
                }
                specialTip.append("超出方案时间");
                canUsage = false;
            }

            specialPreMat.setCanUsage(canUsage);
            specialPreMat.setCannotUsageTip(specialTip.toString());

            toMats.add(specialPreMat);
        }
//        for (SpecialPreMat mat : vo.getFromMats()) {
//
//            mat.setHisYpmlId(ypmlId);
//            mat.setCenterYplx(vo.getCenterYplx());
//            mat.setCenterStoreId(vo.getCenterStoreId());
//            mat.setContainNoStock(true);
//            CenterHisMappingVO toMat = searchMatService.transHisMat(mat);
//
//            if (vo.getCenterYplx() == null) {
//                vo.setCenterYplx(toMat.getCenterYplx());
//            }
//            if (vo.getCenterStoreId() == null) {
//                vo.setCenterStoreId(toMat.getCenterStoreId());
//            }
//
//            toMat.setDose(mat.getDose());
//
//            searchMatService.transDefaultUsage(1, mat, toMat);
//
//            SpecialPreMat specialPreMat = new SpecialPreMat(toMat);
//
//            //能否使用
//            boolean canUsage = true;
//            StringBuilder specialTip = new StringBuilder();
//            if (toMat.getCenterKucunsl() == null || toMat.getCenterKucunsl().compareTo(Constant.BIG_DECIMAL_ZERO) <= 0) {
//                specialTip.append("库存为0");
//                canUsage = false;
//            }
//
//            specialPreMat.setHasStartDate(mat.getHasStartDate());
//            specialPreMat.setHasEndDate(mat.getHasEndDate());
//
//            Date hasStartDate = DateUtil.getDateFormatd(mat.getHasStartDate(), DateUtil.date2);
//            Date hasEndDate = DateUtil.getDateFormatd(mat.getHasEndDate(), DateUtil.date2);
//
//            if (hasStartDate != null && hasStartDate.compareTo(currentDate) > 0 && hasEndDate != null && hasEndDate.compareTo(currentDate) < 0) {
//                if (specialTip.length() > 0) {
//                    specialTip.append("，");
//                }
//                specialTip.append("超出方案时间");
//                canUsage = false;
//            }
//
//            specialPreMat.setCanUsage(canUsage);
//            specialPreMat.setCannotUsageTip(specialTip.toString());
//
//            toMats.add(specialPreMat);
//        }


        LinkedHashMap<String, List<SpecialPreMat>> specialMatMap = new LinkedHashMap<>();

        for (SpecialPreMat toMat : toMats) {
            String key = StringUtils.isBlank(toMat.getMatId()) ? toMat.getHisYpdm() : toMat.getMatId();
            List<SpecialPreMat> smatList = specialMatMap.computeIfAbsent(key, k -> new ArrayList<>());
            smatList.add(toMat);
        }

        specialMatMap.forEach((key, smatList) -> {
            SpecialPreMat mat = null;
            if (smatList.size() > 1) {
                //合并同一个药（知识库映射一致）
                for (SpecialPreMat preMat : smatList) {
                    //随机显示一个有库存的
                    if (preMat.getCenterKucunsl() != null && preMat.getCenterKucunsl().compareTo(Constant.BIG_DECIMAL_ZERO) > 0) {
                        mat = new SpecialPreMat(preMat);
                        break;
                    }
                }
                if (mat == null) {
                    mat = new SpecialPreMat(smatList.get(0));
                }
                mat.setMatList(smatList);
            } else {
                mat = new SpecialPreMat(smatList.get(0));
            }
            specialMatList.add(mat);
        });

        return specialMatList;
    }

    private Map<String, String> createHisMat(String QOrdDR, String QOrderDesc, String QOrdQty, String qordUOM) {
        Map<String, String> sMat = new LinkedHashMap<>();
        sMat.put("qordDR", QOrdDR);
        sMat.put("qorderDesc", QOrderDesc);
        sMat.put("qordQty", QOrdQty);
        sMat.put("qordUOM", qordUOM);
        sMat.put("quomdr", "1");
        sMat.put("qordFreq", "Bid");
        sMat.put("qfreqDR", "9");
        sMat.put("qordCycle", "90");
        sMat.put("qdiag", "2型糖尿病");
        sMat.put("hsastartDate", "2021-04-15");
        Calendar ca = Calendar.getInstance();
        ca.add(Calendar.DATE, 1);
        sMat.put("hsaendDate", "2021-07-15");
        sMat.put("sum", "0.00");
        return sMat;
    }

}
