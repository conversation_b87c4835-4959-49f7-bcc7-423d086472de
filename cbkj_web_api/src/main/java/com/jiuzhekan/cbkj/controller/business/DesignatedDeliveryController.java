package com.jiuzhekan.cbkj.controller.business;

import com.jiuzhekan.cbkj.beans.bs.BsSelfPickupPoint;
import com.jiuzhekan.cbkj.beans.bs.designateddelivery.DesignatedDeliveryListRe;
import com.jiuzhekan.cbkj.beans.business.patients.TDcAddress;
import com.jiuzhekan.cbkj.beans.business.record.TRegister;
import com.jiuzhekan.cbkj.beans.sysApp.SysInstitution;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.service.common.BsSelfPickupPointService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/31 14:31
 * @Version 1.0
 */
@Api(value = "自提点接口", tags = "自提点接口")
@RestController
public class DesignatedDeliveryController {

//    @RequestMapping(value = "delivery/street/list", method = RequestMethod.GET)
//    @ApiOperation(value = "获取定点配送乡镇", notes = "获取定点配送乡镇")
//    @LogAnnotation(value = "获取定点配送乡镇")
//    @ResponseBody
//    public Object getStreets(String registerId) {
////        TRegister register = clientRedisService.getRegisterById(registerId);
////        TDcAddress address = tDcAddressMapper.getLastAddressByPatientId(patientId);
////
////        SysInstitution ins = orgRedisService.getSysInstitution(AdminUtils.getCurrentAppId(), AdminUtils.getCurrentInsCode());
//        return null;
//    }

//    @RequestMapping(value = "delivery/village/list", method = RequestMethod.GET)
//    @ApiOperation(value = "获取行政村", notes = "获取行政村")
//    @LogAnnotation(value = "获取行政村")
//    @ResponseBody
//    public Object getVillages(String streetCode) {
////        TRegister register = clientRedisService.getRegisterById(registerId);
////        TDcAddress address = tDcAddressMapper.getLastAddressByPatientId(patientId);
////
////        SysInstitution ins = orgRedisService.getSysInstitution(AdminUtils.getCurrentAppId(), AdminUtils.getCurrentInsCode());
//        return null;
//    }


//    @RequestMapping(value = "delivery/type/list", method = RequestMethod.GET)
//    @ApiOperation(value = "获取自提点类型", notes = "获取自提点类型")
//    @LogAnnotation(value = "获取自提点类型")
//    @ResponseBody
//    public Object getDeliveryTypeList() {
////        TRegister register = clientRedisService.getRegisterById(registerId);
////        TDcAddress address = tDcAddressMapper.getLastAddressByPatientId(patientId);
////
////        SysInstitution ins = orgRedisService.getSysInstitution(AdminUtils.getCurrentAppId(), AdminUtils.getCurrentInsCode());
//        return null;
//    }

private final BsSelfPickupPointService bsSelfPickupPointService;

    public DesignatedDeliveryController(BsSelfPickupPointService bsSelfPickupPointService) {
        this.bsSelfPickupPointService = bsSelfPickupPointService;
    }

    @RequestMapping(value = "delivery/data/list", method = RequestMethod.GET)
    @ApiOperation(value = "获取可选自提点列表", notes = "获取可选自提点列表", response = BsSelfPickupPoint.class)
    @LogAnnotation(value = "获取可选自提点列表")
    @ResponseBody
    public Object getDeliveryDataList(DesignatedDeliveryListRe designatedDeliveryListRe, Page page) {
//        TRegister register = clientRedisService.getRegisterById(registerId);
//        TDcAddress address = tDcAddressMapper.getLastAddressByPatientId(patientId);
//
//        SysInstitution ins = orgRedisService.getSysInstitution(AdminUtils.getCurrentAppId(), AdminUtils.getCurrentInsCode());
        return bsSelfPickupPointService.getDeliveryDataList(designatedDeliveryListRe,page);

    }


}
