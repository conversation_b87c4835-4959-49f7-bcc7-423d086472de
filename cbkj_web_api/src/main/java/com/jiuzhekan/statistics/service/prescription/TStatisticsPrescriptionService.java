package com.jiuzhekan.statistics.service.prescription;

import com.github.pagehelper.PageHelper;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.statistics.beans.analysis.StatisticsResult;
import com.jiuzhekan.statistics.beans.prescription.StatisticsPrescription;
import com.jiuzhekan.statistics.beans.prescription.TStatisticsPrescription;
import com.jiuzhekan.statistics.beans.register.QueryParam;
import com.jiuzhekan.statistics.beans.register.StatisticsRegister;
import com.jiuzhekan.statistics.beans.register.TStatisticsRegister;
import com.jiuzhekan.statistics.beans.statistics.TStatisticsPrescriptionMzZy;
import com.jiuzhekan.statistics.mapper.prescription.TStatisticsPrescriptionMapper;
import com.jiuzhekan.statistics.mapper.statistics.TStatisticsPrescriptionMzZyMapper;
import com.jiuzhekan.statistics.service.analysis.TStatisticsAnalysisService;
import com.jiuzhekan.statistics.service.personal.TStatisticsPersonalPrescriptionService;
import com.jiuzhekan.statistics.service.register.TStatisticsRegisterService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
public class TStatisticsPrescriptionService {


    private final TStatisticsPrescriptionMapper tStatisticsPrescriptionMapper;
    private final TStatisticsRegisterService tStatisticsRegisterService;
    /**
     * 体制辨识报告
     */
    private final TStatisticsAnalysisService tStatisticsAnalysisService;
    /**
     * 协定方
     */
    private final TStatisticsPersonalPrescriptionService tStatisticsPersonalPrescriptionService;
    private final TStatisticsPrescriptionMzZyMapper tStatisticsPrescriptionMzZyMapper;

    @Autowired
    public TStatisticsPrescriptionService(TStatisticsPrescriptionMapper tStatisticsPrescriptionMapper,
                                          TStatisticsRegisterService tStatisticsRegisterService,
                                          TStatisticsAnalysisService tStatisticsAnalysisService,
                                          TStatisticsPersonalPrescriptionService tStatisticsPersonalPrescriptionService, TStatisticsPrescriptionMzZyMapper tStatisticsPrescriptionMzZyMapper
    ) {
        this.tStatisticsPrescriptionMapper = tStatisticsPrescriptionMapper;
        this.tStatisticsRegisterService = tStatisticsRegisterService;
        this.tStatisticsAnalysisService = tStatisticsAnalysisService;
        this.tStatisticsPersonalPrescriptionService = tStatisticsPersonalPrescriptionService;

        this.tStatisticsPrescriptionMzZyMapper = tStatisticsPrescriptionMzZyMapper;
    }

    /**
     * 加载分页数据
     *
     * @param tStatisticsPrescription 处方统计表
     * @param page                    分页
     * @return Object
     * <AUTHOR>
     * @date 2022-05-23
     */
    public Object getPageDatas(TStatisticsPrescription tStatisticsPrescription, Page page) {

        PageHelper.startPage(page.getPage(), page.getLimit());
        List<TStatisticsPrescription> list = tStatisticsPrescriptionMapper.getPageListByObj(tStatisticsPrescription);
        return Page.getLayUiTablePageData(list);
    }

    /**
     * 加载某条数据
     *
     * @param id 处方统计表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-05-23
     */
    public ResEntity findObj(String id) {

        if (StringUtils.isBlank(id)) {
            return ResEntity.entity(false, "参数不能为空哦", null);
        }
        TStatisticsPrescription tStatisticsPrescription = tStatisticsPrescriptionMapper.getObjectById(id);
        return ResEntity.entity(true, Constant.SUCCESS_DX, tStatisticsPrescription);
    }


    /**
     * 插入新数据
     *
     * @param tStatisticsPrescription 处方统计表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-05-23
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insert(TStatisticsPrescription tStatisticsPrescription) {

        long rows = tStatisticsPrescriptionMapper.insert(tStatisticsPrescription);

        return ResEntity.success(tStatisticsPrescription);
    }


    /**
     * 修改
     *
     * @param tStatisticsPrescription 处方统计表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-05-23
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity update(TStatisticsPrescription tStatisticsPrescription) {

        long rows = tStatisticsPrescriptionMapper.updateByPrimaryKey(tStatisticsPrescription);

        return ResEntity.success(tStatisticsPrescription);
    }

    /**
     * 删除
     *
     * @param ids ids
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-05-23
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity deleteLis(String ids) {
        if (StringUtils.isBlank(ids)) {
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }
        long rowsR = tStatisticsPrescriptionMapper.deleteBylist(ids.split(","));

        return ResEntity.success(rowsR);
    }

    /**
     * 统计处方
     *
     * @param timeDiff 1是昨天。2前天。0今天。-1明天
     * @param preType  1内服中药方 2外用中药方 3中成药方 4适宜技术方 5制剂）
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insertDate(String curDate, String timeDiff, String preType) {
        Map<String, Object> param = new HashMap<>(16);
        //传1是昨天。2前天。0今天。-1明天
        if (StringUtils.isBlank(timeDiff)) {
            timeDiff = "1";
        }
        param.put("timeDiff", timeDiff);
        //1内服中药方 2外用中药方 3中成药方 4适宜技术方 5制剂）
        if (StringUtils.isBlank(preType)) {
            preType = "4";
        }
        if (StringUtils.isBlank(curDate)) {
            param.put("curDate", new SimpleDateFormat("yyyy-MM-dd").format(new Date()).toString());
        } else {
            param.put("curDate", curDate);
        }
        List<StatisticsPrescription> insertRe = new ArrayList<StatisticsPrescription>();
        List<StatisticsPrescription> updateRe = new ArrayList<StatisticsPrescription>();
        param.put("preType", preType);
        List<StatisticsPrescription> list = tStatisticsPrescriptionMapper.selectStatisticsPrescriptionResult(param);
        for (StatisticsPrescription prescription : list) {
            if (Constant.BASIC_STRING_ONE.equals(preType)) {
                prescription.setInnerNum(prescription.getNumber());
            }
            if (Constant.BASIC_STRING_TWO.equals(preType)) {
                prescription.setExterNum(prescription.getNumber());
            }
            if (Constant.BASIC_STRING_THREE.equals(preType)) {
                prescription.setPatentNum(prescription.getNumber());
            }
            if (Constant.BASIC_STRING_FOUR.equals(preType)) {
                prescription.setAcuNum(prescription.getNumber());
            }
            if (Constant.BASIC_STRING_FIVE.equals(preType)) {
                prescription.setPrepareNum(prescription.getNumber());
            }
            if (null == prescription.getId()) {
                insertRe.add(prescription);
            } else {
                updateRe.add(prescription);
            }
            prescription.setTotalNum(prescription.getInnerNum() + prescription.getExterNum() + prescription.getPatentNum() + prescription.getAcuNum() + prescription.getPrepareNum());
        }
        if (insertRe.size() > 0) {
            tStatisticsPrescriptionMapper.insertStatisticsPrescription(insertRe);
        }
        if (updateRe.size() > 0) {
            for (StatisticsPrescription prescription : updateRe) {
                TStatisticsPrescription tStatisticsPrescription = new TStatisticsPrescription();
                BeanUtils.copyProperties(prescription, tStatisticsPrescription);
                tStatisticsPrescriptionMapper.updateByPrimaryKey(tStatisticsPrescription);
            }
        }
        return ResEntity.success(null);
    }

    /**
     *
     * @param curDate
     * @param timeDiff
     * @param preType 1内服中药方 2外用中药方 3中成药方 4适宜技术方 5制剂）
     * @param preMzZy 1门诊 2住院
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insertDateMzZy(String curDate, String timeDiff, String preType,Integer preMzZy) {
        Map<String, Object> param = new HashMap<>(16);
        //传1是昨天。2前天。0今天。-1明天
        if (StringUtils.isBlank(timeDiff)) {
            timeDiff = "1";
        }
        param.put("timeDiff", timeDiff);
        if (StringUtils.isBlank(curDate)) {
            param.put("curDate", new SimpleDateFormat("yyyy-MM-dd").format(new Date()).toString());
        } else {
            param.put("curDate", curDate);
        }
        param.put("preType", preType);
        param.put("preMzZy", preMzZy);
        List<TStatisticsPrescriptionMzZy> insertRe = new ArrayList<TStatisticsPrescriptionMzZy>();
        List<TStatisticsPrescriptionMzZy> updateRe = new ArrayList<TStatisticsPrescriptionMzZy>();
        List<TStatisticsPrescriptionMzZy> list = tStatisticsPrescriptionMzZyMapper.selectStatisticsPrescriptionMzZyResult(param);
        for (int i = 0; i < list.size(); i++) {
            TStatisticsPrescriptionMzZy tStatisticsPrescriptionMzZy = list.get(i);
            tStatisticsPrescriptionMzZy.setInsertDate(new Date());
            if (Constant.BASIC_STRING_ONE.equals(preType)) {
                if (1  == (preMzZy)){
                    //内药+门诊
                    tStatisticsPrescriptionMzZy.setInnerMzNum(tStatisticsPrescriptionMzZy.getNumber());
                }
                if (2 == (preMzZy)){
                    //内药+住院
                    tStatisticsPrescriptionMzZy.setInnerZyNum(tStatisticsPrescriptionMzZy.getNumber());
                }

            }
            if (Constant.BASIC_STRING_TWO.equals(preType)) {
                if (1 == (preMzZy)){
                    //外药+门诊
                    tStatisticsPrescriptionMzZy.setExterMzNum(tStatisticsPrescriptionMzZy.getNumber());
                }
                if (2 == (preMzZy)){
                    //外药+住院
                    tStatisticsPrescriptionMzZy.setExterZyNum(tStatisticsPrescriptionMzZy.getNumber());
                }
            }
            tStatisticsPrescriptionMzZy.setInnerNum(tStatisticsPrescriptionMzZy.getInnerMzNum()+tStatisticsPrescriptionMzZy.getInnerZyNum());
            tStatisticsPrescriptionMzZy.setExterNum(tStatisticsPrescriptionMzZy.getExterMzNum()+tStatisticsPrescriptionMzZy.getExterZyNum());
            tStatisticsPrescriptionMzZy.setTotalNum(tStatisticsPrescriptionMzZy.getExterNum()+tStatisticsPrescriptionMzZy.getInnerNum());
            if (null == tStatisticsPrescriptionMzZy.getId()) {
                insertRe.add(tStatisticsPrescriptionMzZy);
            } else {
                updateRe.add(tStatisticsPrescriptionMzZy);
            }
        }
        if (!insertRe.isEmpty()) {
            tStatisticsPrescriptionMzZyMapper.insertList(insertRe);
        }
        if (!updateRe.isEmpty()) {
            for (TStatisticsPrescriptionMzZy prescription : updateRe) {
                TStatisticsPrescriptionMzZy tStatisticsPrescription = new TStatisticsPrescriptionMzZy();
                BeanUtils.copyProperties(prescription, tStatisticsPrescription);
                tStatisticsPrescriptionMzZyMapper.updateByPrimaryKey(tStatisticsPrescription);
            }
        }
        return ResEntity.success();
    }
    /**
     * 统计处方
     *
     * @param timeDiff 1是昨天。2前天。0今天。-1明天
     * @param preType  1内服中药方 2外用中药方 3中成药方 4适宜技术方 5制剂）
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insertDateCDSS(String curDate, String timeDiff, String preType) {
        Map<String, Object> param = new HashMap<>(16);
        //传1是昨天。2前天。0今天。-1明天
        if (StringUtils.isBlank(timeDiff)) {
            timeDiff = "1";
        }
        param.put("timeDiff", timeDiff);
        //1内服中药方 2外用中药方 3中成药方 4适宜技术方 5制剂）
        if (StringUtils.isBlank(preType)) {
            preType = "4";
        }
        if (StringUtils.isBlank(curDate)) {
            param.put("curDate", new SimpleDateFormat("yyyy-MM-dd").format(new Date()).toString());
        } else {
            param.put("curDate", curDate);
        }
        List<StatisticsPrescription> insertRe = new ArrayList<StatisticsPrescription>();
        List<StatisticsPrescription> updateRe = new ArrayList<StatisticsPrescription>();
        param.put("preType", preType);
        List<StatisticsPrescription> list = tStatisticsPrescriptionMapper.selectStatisticsPrescriptionResultCDSS(param);
        for (StatisticsPrescription prescription : list) {
            if (Constant.BASIC_STRING_ONE.equals(preType)) {
                prescription.setInnerNum(prescription.getNumber());
            }
            if (Constant.BASIC_STRING_TWO.equals(preType)) {
                prescription.setExterNum(prescription.getNumber());
            }
            if (Constant.BASIC_STRING_THREE.equals(preType)) {
                prescription.setPatentNum(prescription.getNumber());
            }
            if (Constant.BASIC_STRING_FOUR.equals(preType)) {
                prescription.setAcuNum(prescription.getNumber());
            }
            if (Constant.BASIC_STRING_FIVE.equals(preType)) {
                prescription.setPrepareNum(prescription.getNumber());
            }
            if (null == prescription.getId()) {
                insertRe.add(prescription);
            } else {
                updateRe.add(prescription);
            }
            prescription.setTotalNum(prescription.getInnerNum() + prescription.getExterNum() + prescription.getPatentNum() + prescription.getAcuNum() + prescription.getPrepareNum());
        }
        if (insertRe.size() > 0) {
            tStatisticsPrescriptionMapper.insertStatisticsPrescriptionCDSS(insertRe);
        }
        if (updateRe.size() > 0) {
            for (StatisticsPrescription prescription : updateRe) {
                TStatisticsPrescription tStatisticsPrescription = new TStatisticsPrescription();
                BeanUtils.copyProperties(prescription, tStatisticsPrescription);
                tStatisticsPrescriptionMapper.updateByPrimaryKeyCDSS(tStatisticsPrescription);
            }
        }
        return ResEntity.success(null);
    }

    public TStatisticsPrescription getLastData() {
        return tStatisticsPrescriptionMapper.getLastData();
    }

    /**
     * 退费减数量。
     *
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity changeNumber(String timeDiff) {
        Map<String, Object> param = new HashMap<>(16);
        //timeDiff
        param.put("timeDiff", timeDiff);
        List<StatisticsPrescription> statisticsPrescriptions = tStatisticsPrescriptionMapper.selectStatisticsRegisterSplit(param);
        statisticsPrescriptions.forEach(a -> {
            TStatisticsPrescription tStatisticsRegister = new TStatisticsPrescription();
            tStatisticsRegister.setAppId(a.getAppId());
            tStatisticsRegister.setInsCode(a.getInsCode());
            tStatisticsRegister.setUserId(a.getUserId());
            tStatisticsRegister.setCreateDate(a.getDataDate());
            List<TStatisticsPrescription> pageListByObj = tStatisticsPrescriptionMapper.getPageListByObj(tStatisticsRegister);
            if (pageListByObj.size() >= 1) {
                TStatisticsPrescription tStatisticsPrescription = pageListByObj.get(0);
                String preType = a.getPreType();
                if (Constant.BASIC_STRING_ONE.equals(preType)) {
                    tStatisticsPrescription.setInnerNum(tStatisticsPrescription.getInnerNum() - a.getNumber());
                }
                if (Constant.BASIC_STRING_TWO.equals(preType)) {
                    tStatisticsPrescription.setExterNum(tStatisticsPrescription.getExterNum() - a.getNumber());
                }
                if (Constant.BASIC_STRING_THREE.equals(preType)) {
                    tStatisticsPrescription.setPatentNum(tStatisticsPrescription.getPatentNum() - a.getNumber());
                }
                if (Constant.BASIC_STRING_FOUR.equals(preType)) {
                    tStatisticsPrescription.setAcuNum(tStatisticsPrescription.getAcuNum() - a.getNumber());
                }
                if (Constant.BASIC_STRING_FIVE.equals(preType)) {
                    tStatisticsPrescription.setPrepareNum(tStatisticsPrescription.getPrepareNum() - a.getNumber());
                }
                tStatisticsPrescription.setTotalNum(tStatisticsPrescription.getTotalNum() - a.getNumber());
                tStatisticsPrescriptionMapper.updateByPrimaryKey(tStatisticsPrescription);
            }
        });
        return ResEntity.success(null);
    }

//    public void changeNumber(String appId,String insCode,String userId,Date createDate,String preType){
//        TStatisticsPrescription tStatisticsPrescription = new TStatisticsPrescription();
//        tStatisticsPrescription.setAppId(appId);
//        tStatisticsPrescription.setInsCode(insCode);
//        tStatisticsPrescription.setUserId(userId);
//        TStatisticsPrescription a = tStatisticsPrescriptionMapper.getChangeNumber(tStatisticsPrescription);
//
//    }

    /**
     * @param startDate
     * @param endDate
     * @return
     */
    public ResEntity statisticsHistoryData(String startDate, String endDate) {
        String timeDiff = "0";
        try {
            //计算差值
            Date end = new SimpleDateFormat("yyyy-MM-dd").parse(endDate);
            Date start = new SimpleDateFormat("yyyy-MM-dd").parse(startDate);
            int days = (int) ((end.getTime() - start.getTime()) / (1000 * 3600 * 24));
            timeDiff = days + "";
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }

        //1内服中药方 2外用中药方 3中成药方 4适宜技术方 5制剂）
        ResEntity resEntity2 = insertDate(endDate, timeDiff, "1");
        ResEntity resEntity3 = insertDate(endDate, timeDiff, "2");
        ResEntity resEntity4 = insertDate(endDate, timeDiff, "3");
        ResEntity resEntity5 = insertDate(endDate, timeDiff, "4");
        ResEntity resEntity6 = insertDate(endDate, timeDiff, "5");


        return ResEntity.success(null);
    }

    /**
     * /就诊人次-电子病历
     *
     * @param startDate
     * @param endDate
     * @return
     */
    public ResEntity tStatisticsRegister(String startDate, String endDate) {
        String timeDiff = "0";
        try {
            //计算差值
            Date end = new SimpleDateFormat("yyyy-MM-dd").parse(endDate);
            Date start = new SimpleDateFormat("yyyy-MM-dd").parse(startDate);
            int days = (int) ((end.getTime() - start.getTime()) / (1000 * 3600 * 24));
            timeDiff = days + "";
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        //就诊人次-电子病历
        ResEntity resEntity = tStatisticsRegisterService.insertDate(endDate, timeDiff, "1");
        ResEntity resEntity1 = tStatisticsRegisterService.insertDate(endDate, timeDiff, "2");
        return ResEntity.success(null);
    }

    /**
     * 统计-体制辨识
     *
     * @param startDate
     * @param endDate
     * @return
     */
    public ResEntity settStatisticsAnalysis(String startDate, String endDate) {
        String timeDiff = "0";
        try {
            //计算差值
            Date end = new SimpleDateFormat("yyyy-MM-dd").parse(endDate);
            Date start = new SimpleDateFormat("yyyy-MM-dd").parse(startDate);
            int days = (int) ((end.getTime() - start.getTime()) / (1000 * 3600 * 24));
            timeDiff = days + "";
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        tStatisticsAnalysisService.settStatisticsAnalysis(endDate, timeDiff);
        return ResEntity.success(null);
    }

    /**
     * 协定方统计
     * @param startDate
     * @param endDate
     * @return
     */
    public ResEntity tStatisticsPersonalPrescription(String startDate, String endDate) {
        String timeDiff = "0";
        try {
            //计算差值
            Date end = new SimpleDateFormat(" ").parse(endDate);
            Date start = new SimpleDateFormat("yyyy-MM-dd").parse(startDate);
            int days = (int) ((end.getTime() - start.getTime()) / (1000 * 3600 * 24));
            timeDiff = days + "";
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        //        （0私有 1科室 2全院 3专家经验）
        ResEntity resEntity7 = tStatisticsPersonalPrescriptionService.setPersonalPrescriptionResult(endDate, timeDiff, "0");
        ResEntity resEntity8 = tStatisticsPersonalPrescriptionService.setPersonalPrescriptionResult(endDate, timeDiff, "1");
        ResEntity resEntity9 = tStatisticsPersonalPrescriptionService.setPersonalPrescriptionResult(endDate, timeDiff, "2");
        ResEntity resEntity10 = tStatisticsPersonalPrescriptionService.setPersonalPrescriptionResult(endDate, timeDiff, "3");
        return ResEntity.success(null);
    }
    /**
     * 获取数据
     * @param queryParam
     * @return
     */
    public int getInnerAndExterData(QueryParam queryParam){
        Integer innerAndExterData = tStatisticsPrescriptionMapper.getInnerAndExterData(queryParam);
        return null == innerAndExterData ? 0 : innerAndExterData;
    }
}
