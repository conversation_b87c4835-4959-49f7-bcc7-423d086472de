package com.jiuzhekan.appraisal.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class JxkhZibiaoZj implements Serializable{

    @ApiModelProperty(value = "中间指标代码")
    private String zibiaodm;

    @ApiModelProperty(value = "中间指标名称")
    private String zibiaomc;

    @ApiModelProperty(value = "计量单位")
    private String jiliangdw;

    @ApiModelProperty(value = "指标说明")
    private String zibiaosm;

    @ApiModelProperty(value = "指标类别：0累加指标，1一次性指标（取结束日期最近一条")
    private Integer zibiaolb;

    @ApiModelProperty(value = "修改时间")
    private Date xiugaisj;
    @ApiModelProperty(value = "指标代码：多个用逗号分隔",hidden = true)
    private String[] zibiaodms;

}
