package com.jiuzhekan.statistics.mapper.personal;

import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;
import com.jiuzhekan.statistics.beans.analysis.StatisticsResult;
import com.jiuzhekan.statistics.beans.personal.StatisticsPersonalPrescription;
import com.jiuzhekan.statistics.beans.personal.TStatisticsPersonalPrescription;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public interface TStatisticsPersonalPrescriptionMapper extends BaseMapper<TStatisticsPersonalPrescription>{
    /**
     * 统计协定方
     * @param param
     * @return
     */
    List<StatisticsPersonalPrescription> selectPersonalPrescriptionResult(Map<String, Object> param);
    /**
     * 新增统计
     * @param list
     * @return
     */
    int insertStatistics(List<StatisticsPersonalPrescription> list);

    /**
     * 获取最新一条
     * @return
     */
    TStatisticsPersonalPrescription getLastData();
}