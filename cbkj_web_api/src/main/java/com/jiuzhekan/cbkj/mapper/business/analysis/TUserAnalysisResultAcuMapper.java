package com.jiuzhekan.cbkj.mapper.business.analysis;

import com.jiuzhekan.cbkj.beans.business.analysis.TUserAnalysisResultAcu;
import org.springframework.stereotype.Component;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;

import java.util.List;

@Component
public interface TUserAnalysisResultAcuMapper extends BaseMapper<TUserAnalysisResultAcu>{

    void deleteByAnalyId(String analyId);

    List<TUserAnalysisResultAcu> getAcuListByAnalyId(String analyId);
}