package com.jiuzhekan.cbkj.mapper.statistics;

import com.jiuzhekan.cbkj.beans.statistics.TStatistics;
import com.jiuzhekan.cbkj.beans.sysBeans.AdminInfo;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


@Component
public interface TStatisticsMapper extends BaseMapper<TStatistics> {

    /**
     * 查询前5种高发疾病
     *
     * @param tStatistics
     * @return
     */
    List<TStatistics> getTop5DisName(TStatistics tStatistics);

    /**
     * 查询前5种高发证型
     *
     * @param tStatistics
     * @return
     */
    List<TStatistics> getTop5SymName(TStatistics tStatistics);

    /**
     * 按年龄段统计每个年龄段前5种高发疾病
     *
     * @param tStatistics
     * @return
     */
    List<TStatistics> getTop5DisNameByAge(TStatistics tStatistics);

    /**
     * 按时间段统计每个时间段前5种高发疾病
     *
     * @param tStatistics
     * @return
     */
    List<TStatistics> getTop5DisNameByGoUp(TStatistics tStatistics);


    /**
     * 根据性别统计每种性别前5种高发疾病
     *
     * @param tStatistics
     * @return
     */
    List<TStatistics> getTop5DisNameByGender(TStatistics tStatistics);

    /**
     * 24节气
     *
     * @param tStatistics
     * @return
     */
    List<TStatistics> getTop5DisNameBy24(TStatistics tStatistics);

    /**
     * 获取最近的二十四个节气名称
     *
     * @return
     */
    List<TStatistics> getTermsName();

    /**
     * 根据节气名称获取前5个疾病名称
     *
     * @param tStatistics
     * @return
     */
    List<TStatistics> getTop5DisNameByTermName(TStatistics tStatistics);

    /**
     * 获取今天某医生的月均贴金额
     *
     * @param adminInfo 医生
     * @return java.util.Map<java.lang.String, java.lang.Object>
     * <AUTHOR>
     * @date 2021/1/6
     */
    BigDecimal getDoctorMonthlyAvgAmount(AdminInfo adminInfo);


    /**
     * 统计医生月均贴金额
     *
     * <AUTHOR>
     * @date 2021/1/4
     */
    void statisticsAvgAmount();


    /**
     * 统计高发疾病
     *
     * @param param timeType 时间类型:1昨天,2近半月,3近一个季度,4近一年
     *              timeDiff 统计天数
     * @return void
     * <AUTHOR>
     * @date 2021/2/25
     */
    void statisticsTop5DisName(Map<String, Object> param);

    /**
     * 统计证型分布
     *
     * @param param timeType 时间类型:1昨天,2近半月,3近一个季度,4近一年
     *              timeDiff 统计天数
     * @return void
     * <AUTHOR>
     * @date 2021/2/25
     */
    void statisticsTop5SymName(Map<String, Object> param);

    /**
     * 统计疾病年龄分布
     *
     * @param param timeType 时间类型:1昨天,2近半月,3近一个季度,4近一年
     *              timeDiff 统计天数
     * @return void
     * <AUTHOR>
     * @date 2021/2/25
     */
    void statisticsTop5DisNameByAge(Map<String, Object> param);

    /**
     * 统计上升趋势前五疾病
     *
     * @param param timeType 时间类型：1昨天,2近半月,3近一个季度,4近一年
     *              timeSplit 节点个数: 昨天7,近半月7,近一个季度6 ,近一年12
     *              timeDiff  统计天数：昨天1,近半月2,近一个季度15,近一年30
     * @return void
     * <AUTHOR>
     * @date 2021/2/25
     */
    List<Map<String, Object>> statisticsTop5DisNameByGoUp(Map<String, Object> param);
    List<Map<String, Object>> top5DisNameCompensate(Map<String, Object> param);

    void saveStatisticsTop5DisNameByGoUp(List<Map<String, Object>> list);

    void saveTop5DisNameCompensate(List<Map<String, Object>> list, @Param("createTime")String createTime);

    /**
     * 统计性别分布
     *
     * @param param timeType 时间类型：1昨天,2近半月,3近一个季度,4近一年
     *              timeDiff 统计天数
     * @return void
     * <AUTHOR>
     * @date 2021/2/25
     */
    void statisticsTop5DisNameByGender(Map<String, Object> param);

    /**
     * 统计节气分布
     *
     * @param param terms 节气
     *              beginTime 开始时间
     *              endTime   结束时间
     * @return void
     * <AUTHOR>
     * @date 2021/2/25
     */
    void statisticsTop5DisNameByTermName(Map<String, Object> param);
}
