package com.jiuzhekan.cbkj.mapper.myData;

import com.jiuzhekan.cbkj.beans.myData.TPersonalPrescription;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public interface TPersonalPrescriptionMapper extends BaseMapper<TPersonalPrescription> {

    /**
     * 查询协定方，默认查询我的协定方和机构协定方
     * shareType: 0我的协定方(必需：preOwner)  2机构协定方(必需：insCode,deptId)  3院内方(必需：appId)
     * appId:    当前医联体
     * insCode:  当前医疗机构
     * deptId:   当前科室
     * preOwner: 当前用户
     *
     * @param tPersonalPrescription
     * @return java.util.List<com.jiuzhekan.cbkj.beans.myData.TPersonalPrescription>
     * <AUTHOR>
     * @date 2020/6/17
     */
    List<TPersonalPrescription> getPageListByPer(TPersonalPrescription tPersonalPrescription);

    /**
     * 开放页面搜索协定方
     *
     * @param tPersonalPrescription
     * @return java.util.List<com.jiuzhekan.cbkj.beans.myData.TPersonalPrescription>
     * <AUTHOR>
     * @date 2020/11/27
     */
    List<TPersonalPrescription> getSearchList(TPersonalPrescription tPersonalPrescription);

    /**
     * 推导院内方
     *
     * @param tPersonalPrescription
     * @return java.util.List<java.util.LinkedHashMap>
     * <AUTHOR>
     * @date 2020/7/2
     */
    List<Map> getAppPres(TPersonalPrescription tPersonalPrescription);

    int getNumByPreNameOnOwner(TPersonalPrescription tPersonalPrescription);

    /**
     * 根据类型搜索协定方
     *
     * @param tPersonalPrescription 协定方
     *                              shareType：类型（0个人 1本科室 2全院）
     *                              isOther：是否未分类
     * @return java.util.List<com.jiuzhekan.cbkj.beans.myData.TPersonalPrescription>
     * <AUTHOR>
     * @date 2021/4/1
     */
    //List<TPersonalPrescription> getPersonalPresByType(TPersonalPrescription tPersonalPrescription);
    /**
     * 根据类型搜索协定方-分页用
     *
     * @param tPersonalPrescription 协定方
     *                              shareType：类型（0个人 1本科室 2全院）
     *                              isOther：是否未分类
     * @return java.util.List<com.jiuzhekan.cbkj.beans.myData.TPersonalPrescription>
     * <AUTHOR>
     * @date 2021/4/1
     */
    //Integer countGetPersonalPresByType(TPersonalPrescription tPersonalPrescription);
    /**
     * 根据类型搜索协定方,左关联了协定方病症法的关联表，根据权重排序。
     *
     * @param tPersonalPrescription 协定方
     *                              shareType：类型（0个人 1本科室 2全院）
     *                              isOther：是否未分类
     * @return java.util.List<com.jiuzhekan.cbkj.beans.myData.TPersonalPrescription>
     * <AUTHOR>
     * @date 2023/11/20
     */
    List<TPersonalPrescription> getPersonalPresByTypeDisMapping(TPersonalPrescription tPersonalPrescription);
    Long countGetPersonalPresByTypeDisMapping(TPersonalPrescription tPersonalPrescription);


    /**
     * 根据文件夹搜索协定方
     *
     * @param tPersonalPrescription tPersonalPrescription
     * @return java.util.List<com.jiuzhekan.cbkj.beans.myData.TPersonalPrescription>
     * <AUTHOR>
     * @date 2021/4/1
     */
    List<TPersonalPrescription> getPersonalPresByFolder(TPersonalPrescription tPersonalPrescription);
    Long countGetPersonalPresByFolder(TPersonalPrescription tPersonalPrescription);

    /**
     * 修改排序
     *
     * @param tPersonalPrescription persPreId,preOrder必传
     * @return long
     * <AUTHOR>
     * @date 2021/4/6
     */
    long updateOrder(TPersonalPrescription tPersonalPrescription);

    /**
     * 协定方能否修改
     *
     * @param id id
     * @return java.lang.Integer
     * <AUTHOR>
     * @date 2021/5/11
     */
    Integer personalPrescriptionisModify(String id);


    /**
     * 获取所有科研编号
     * @param infoScientificPerparation
     * @return
     */
    @Select("select INFO_SCIENTIFIC_PREPARATION from t_personal_prescription where IS_SCIENTIFIC_PREPARATION = 1")
    List<String> exportSciencegetAllNo(String infoScientificPerparation);
}