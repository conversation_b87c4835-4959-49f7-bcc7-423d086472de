<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.statistics.mapper.board.TStatisticsPrescriptionTypeMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.statistics.beans.board.TStatisticsPrescriptionType">
        <id column="id" jdbcType="INTEGER"  property="id" />
        <result column="app_id" jdbcType="VARCHAR" property="appId" />
        <result column="app_name" jdbcType="VARCHAR" property="appName" />
        <result column="ins_code" jdbcType="VARCHAR" property="insCode" />
        <result column="ins_name" jdbcType="VARCHAR" property="insName" />
        <result column="dept_id" jdbcType="VARCHAR" property="deptId" />
        <result column="create_date" jdbcType="DATE" property="createDate" />
        <result column="insert_date" jdbcType="TIMESTAMP" property="insertDate" />
        <result column="number" jdbcType="INTEGER" property="number" />
        <result column="num_type" jdbcType="INTEGER" property="numType" />
    </resultMap>


    <sql id="Base_Column_List">
    id,app_id,app_name,ins_code,ins_name,dept_id,create_date,insert_date,number,num_type
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.jiuzhekan.statistics.beans.board.TStatisticsPrescriptionType">
        delete from t_statistics_prescription_type where id = #{ id }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_statistics_prescription_type where id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="com.jiuzhekan.statistics.beans.board.TStatisticsPrescriptionType">
        insert into t_statistics_prescription_type (<include refid="Base_Column_List" />) values
        (#{id},#{appId},#{appName},#{insCode},#{insName},#{deptId},#{createDate},#{insertDate},#{number},#{numType})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_statistics_prescription_type (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.id},#{item.appId},#{item.appName},#{item.insCode},#{item.insName},#{item.deptId},#{item.createDate},#{item.insertDate},#{item.number},#{item.numType})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.jiuzhekan.statistics.beans.board.TStatisticsPrescriptionType">
        update t_statistics_prescription_type
        <set>
             <if test="appId != null">
                app_id = #{ appId },
             </if>
             <if test="appName != null">
                app_name = #{ appName },
             </if>
             <if test="insCode != null">
                ins_code = #{ insCode },
             </if>
             <if test="insName != null">
                ins_name = #{ insName },
             </if>
             <if test="deptId != null">
                dept_id = #{ deptId },
             </if>
             <if test="createDate != null">
                create_date = #{ createDate },
             </if>
             <if test="insertDate != null">
                insert_date = #{ insertDate },
             </if>
             <if test="number != null">
                number = #{ number },
             </if>
             <if test="numType != null">
                num_type = #{ numType },
             </if>
        </set>
        where id = #{ id }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_statistics_prescription_type where id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="com.jiuzhekan.statistics.beans.board.TStatisticsPrescriptionType" resultMap="BaseResultMap">
        SELECT id,app_id,app_name,ins_code,ins_name,dept_id,create_date,insert_date,number,num_type
        from t_statistics_prescription_type
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>
        <select id="getOneByObject"  resultMap="BaseResultMap"  parameterType="com.jiuzhekan.statistics.beans.board.TStatisticsPrescriptionType">
            select * from t_statistics_prescription_type
            <where>
                <if test=" appId != null and appId!='' ">
                    and app_id=#{appId}
                </if>
                <if test=" insCode != null and insCode!='' ">
                    and ins_code=#{insCode}
                </if>
                <if test=" numType != null and numType!='' ">
                    and num_type=#{numType}
                </if>
                <if test=" createDate != null">
                    and create_date=#{createDate}
                </if>
            </where>
            limit 1
        </select>
    <select id="getCountByObj" parameterType="com.jiuzhekan.statistics.beans.board.TStatisticsPrescriptionType" resultType="java.lang.Long">
        select  count(*) from t_statistics_prescription_type
        <where>
            <if test=" appId != null and appId!='' ">
                and app_id=#{appId}
            </if>
            <if test=" insCode != null and insCode!='' ">
                and ins_code=#{insCode}
            </if>
            <if test=" numType != null and numType!='' ">
                and num_type=#{numType}
            </if>
            <if test=" createDate != null">
                and create_date=#{createDate}
            </if>
        </where>
    </select>
    <select id="getStaticsResult" parameterType="Map" resultMap="BaseResultMap">
        SELECT c.`id`,b.app_id,b.`INS_CODE` ins_code ,COUNT(b.`PRE_ID`) AS number ,1 as num_type
        FROM `t_prescription` AS b  JOIN  (
            SELECT a2.* FROM (SELECT * FROM `t_order_status` AS aa WHERE aa.`OPERATION_TIME`>'2022-10-18' AND OPERATION_TYPE IN (85,130,135,150)  GROUP BY aa.`PRE_ID`,aa.`OPERATION_TYPE` ORDER BY aa.`OPERATION_TIME` DESC)AS a2 GROUP BY a2.PRE_ID
        )
            AS a ON a.PRE_ID=b.PRE_ID
            LEFT JOIN t_statistics_prescription_type AS c ON c.`app_id`=b.app_id AND c.`ins_code`=b.`INS_CODE` AND c.`create_date`=#{curDate} and c.num_type=1
        WHERE  a.OPERATION_TYPE in (85,130,135) AND
                TIMESTAMPDIFF(DAY,a.`OPERATION_TIME`, DATE_ADD(#{curDate},INTERVAL 1 DAY))=#{diff}
        GROUP BY b.app_id,b.`INS_CODE`
    </select>
    <select id="getStaticsResult2" parameterType="Map" resultMap="BaseResultMap">
        SELECT c.`id`,b.app_id ,b.`INS_CODE` ins_code ,COUNT(b.`PRE_ID`) AS number,2 as num_type
        FROM `t_prescription` AS b  JOIN  (
            select * from t_order_status as aa where aa.`OPERATION_TIME`>#{curDate} AND OPERATION_TYPE = 150 GROUP BY aa.`PRE_ID`
        ) AS a ON a.PRE_ID=b.PRE_ID
        LEFT JOIN t_statistics_prescription_type AS c ON c.`app_id`=b.app_id AND c.`ins_code`=b.`INS_CODE` AND c.`create_date`=#{curDate} and c.num_type=2
        WHERE   a.OPERATION_TYPE in (150) AND
                TIMESTAMPDIFF(DAY,a.`OPERATION_TIME`, DATE_ADD(#{curDate},INTERVAL 1 DAY))=#{diff}
        GROUP BY b.app_id,b.`INS_CODE`
    </select>

    <select id="sumStatistics" resultType="java.lang.Integer">
        select
        SUM(number) as number
        from t_statistics_prescription_type
        <where>
            <if test=" appId != null and appId!='' ">
                and app_id = #{appId}
            </if>
            <if test=" insCode != null and insCode!='' ">
                and ins_code = #{insCode}
            </if>
            <if test=" numType != null">
                and num_type = #{numType}
            </if>
            <if test="queryStartDate != null and queryEndDate!= null">
                and create_date &lt;= #{queryEndDate} and create_date >= #{queryStartDate}
            </if>
        </where>
    </select>

</mapper>