package com.jiuzhekan.cbkj.service.formula.center;

import com.jiuzhekan.cbkj.beans.formula.TFormula;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.DateUtil;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * CenterFormula
 * <p>
 * 杭州聪宝科技有限公司
 *
 * <AUTHOR>
 * @date 2021/8/11
 */
@Data
@NoArgsConstructor
public class CenterFormula {
    /**
     * 配方id
     */
    private String PERSPREID;
    /**
     * 配方名称
     */
    private String PRENAME;
    /**
     * 功效
     */
    private String EFFICACY;
    /**
     * Appid
     */
    private String APPID;
    /**
     * 医疗机构编码
     */
    private String INSID;
    /**
     * 拼音码
     */
    private String PREPY;
    /**
     * 添加时间
     */
    private String INSERTDATE;
    /**
     * 修改时间
     */
    private String UPDATEDATE;
    /**
     * 删除时间
     */
    private String DELDATE;
    /**
     * 删除标志（0未删除 1已删除）
     */
    private String ISDEL;
    /**
     * 对应药品价格id
     */
    private String gaofangid;
    /**
     * 来源（1膏方 2颗粒剂协定方 3饮片协定方）
     */
    private String source;
    /**
     * 销售价
     */
    private String xsj;
    /**
     * 药品剂型（1散装饮片  2散装颗粒  3膏方 4小包装饮片 5小包装颗粒 ）
     */
    private String preMatType;
    /**
     * 药房ID
     */
    private String storeId;
    /**
     * 配方明细
     */
    private List<CenterFormulaItem> preItems;


    public CenterFormula(TFormula formula) {
        this.PERSPREID = formula.getFormulaId();
        this.PRENAME = formula.getPreName();
        this.EFFICACY = formula.getEfficacy();
        this.APPID = formula.getAppId();
        this.INSID = formula.getInsCode();
        this.PREPY = formula.getPrePy();
        this.INSERTDATE = formula.getCreateDate() == null ? null : DateUtil.getDateFormats(DateUtil.YYYY_MM_DD_HH_MM_SS, formula.getCreateDate());
        this.UPDATEDATE = formula.getUpdateDate() == null ? null : DateUtil.getDateFormats(DateUtil.YYYY_MM_DD_HH_MM_SS, formula.getUpdateDate());
        this.DELDATE = formula.getDelDate() == null ? null : DateUtil.getDateFormats(DateUtil.YYYY_MM_DD_HH_MM_SS, formula.getDelDate());
        this.ISDEL = StringUtils.isBlank(formula.getIsDel()) ? "0" : formula.getIsDel();
        this.gaofangid = formula.getStorePriceId();
        this.source = Constant.BASIC_STRING_THREE.equals(formula.getPreMatType()) ? Constant.BASIC_STRING_ONE
                : Constant.BASIC_STRING_TWO.equals(formula.getPreMatType()) || Constant.BASIC_STRING_FIVE.equals(formula.getPreMatType()) ? Constant.BASIC_STRING_TWO
                : Constant.BASIC_STRING_ONE.equals(formula.getPreMatType()) || Constant.BASIC_STRING_FOUR.equals(formula.getPreMatType()) ? Constant.BASIC_STRING_THREE : "";
        this.xsj = formula.getPreSingleMoney() == null ? "0" : formula.getPreSingleMoney().toPlainString();
        this.preMatType = formula.getPreMatType();
        this.storeId = formula.getStoreId();
        if (formula.getItemList() != null) {
            this.preItems = formula.getItemList().stream().map(CenterFormulaItem::new).collect(Collectors.toList());
        }
    }


}
