package com.jiuzhekan.statistics.beans.register;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022$ 05-26$
 **/
@Data
@ApiModel("统计报表")
public class StatisticsChangeResult {

    @ApiModelProperty(value = "体质辨识")
    private int number;
    @ApiModelProperty(value = "协定方")
    private int sumPersonalPrescription;
    @ApiModelProperty(value = "适宜技术")
    private int sumStatisticsPrescription;
    @ApiModelProperty(value = "就诊人次")
    private int registerTimes;
    @ApiModelProperty(value = "电子病历")
    private int electronicRecordNum;
    @ApiModelProperty(value = "中药饮片处方数(内服+外用处方总和)")
    private int sumInnerAndExtPrescription;

    @ApiModelProperty(value = "中药饮片处方数(内服+外用处方总和CDSS)")
    private int sumInnerAndExtPrescriptionCDSS;


    @ApiModelProperty(value = "门诊中药处方数")
    private int sumOutPatientPrescription;

    @ApiModelProperty(value = "住院中药处方数")
    private int sumInPatientPrescription;

    private List<ChangeMap> changeMapList;
}
