package com.jiuzhekan.healthpres.beans;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class TUserQuestionnaireCommonWestDis implements Serializable {

    @ApiModelProperty(value = "常见西医id")
    private String commonWestId;

    @ApiModelProperty(value = "常见西医名")
    private String commonWestName;

    @ApiModelProperty(value = "常见西医代码")
    private String commonWestCode;

    @ApiModelProperty(value = "0正常1删除")
    private Integer status;

    @ApiModelProperty(value = "排序（升序排序")

    private Integer sort;
    @ApiModelProperty(value = "分类:对应questionnaireMainSecondTypeCode字段")
    private String classType;
    @ApiModelProperty(value = "分类名：对应questionnaireMainSecondTypeName字段")
    private String classTypeName;


}
