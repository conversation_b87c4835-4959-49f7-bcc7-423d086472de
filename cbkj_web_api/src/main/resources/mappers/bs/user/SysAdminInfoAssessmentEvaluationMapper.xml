<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.managementTCMSP.mapper.user.SysAdminInfoAssessmentEvaluationMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.managementTCMSP.beans.user.SysAdminInfoAssessmentEvaluation">
        <id column="id" jdbcType="INTEGER"  property="id" />
        <result column="user_name" jdbcType="VARCHAR" property="userName" />
        <result column="user_id" jdbcType="VARCHAR" property="userId" />

        <result column="insert_time" jdbcType="TIMESTAMP" property="insertTime" />
        <result column="assessment_evaluation_class" jdbcType="INTEGER" property="assessmentEvaluationClass" />
        <result column="assessment_evaluation_name" jdbcType="VARCHAR" property="assessmentEvaluationName" />
        <result column="assessment_evaluation_score" jdbcType="DECIMAL" property="assessmentEvaluationScore" />
        <result column="assessment_evaluation_time" jdbcType="TIMESTAMP" property="assessmentEvaluationTime" />
        <result column="assessment_evaluation_user_name" jdbcType="VARCHAR" property="assessmentEvaluationUserName" />
        <result column="assessment_evaluation_user_id" jdbcType="VARCHAR" property="assessmentEvaluationUserId" />
        <result column="assessment_evaluation_comment" jdbcType="VARCHAR" property="assessmentEvaluationComment" />
        <result column="certificate_attachment" jdbcType="VARCHAR" property="certificateAttachment" />
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
        <result column="employee_id" jdbcType="VARCHAR" property="employeeIds" />

    </resultMap>


    <sql id="Base_Column_List">
    id,user_name,user_id,app_id,insert_time,assessment_evaluation_class,assessment_evaluation_name,assessment_evaluation_score,assessment_evaluation_time,
    assessment_evaluation_user_name,assessment_evaluation_user_id,assessment_evaluation_comment,certificate_attachment,create_user_id,create_user_name
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.jiuzhekan.managementTCMSP.beans.user.SysAdminInfoAssessmentEvaluation">
        delete from sys_admin_info_assessment_evaluation where id = #{ id }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from sys_admin_info_assessment_evaluation where id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="com.jiuzhekan.managementTCMSP.beans.user.SysAdminInfoAssessmentEvaluation">
        insert into sys_admin_info_assessment_evaluation (<include refid="Base_Column_List" />) values
        (#{id},#{userName},#{userId},#{insertTime},#{assessmentEvaluationClass},#{assessmentEvaluationName},#{assessmentEvaluationScore},#{assessmentEvaluationTime},#{assessmentEvaluationUserName},#{assessmentEvaluationUserId},#{assessmentEvaluationComment},#{certificateAttachment},#{createUserId},#{createUserName})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into sys_admin_info_assessment_evaluation (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.id},#{item.userName},#{item.userId},#{item.appId},#{item.appName},#{item.insName},#{item.insCode},#{item.deptId},#{item.deptName},#{item.insertTime},#{item.assessmentEvaluationClass},#{item.assessmentEvaluationName},#{item.assessmentEvaluationScore},#{item.assessmentEvaluationTime},#{item.assessmentEvaluationUserName},#{item.assessmentEvaluationUserId},#{item.assessmentEvaluationComment},#{item.certificateAttachment},#{item.createUserId},#{item.createUserName})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.jiuzhekan.managementTCMSP.beans.user.SysAdminInfoAssessmentEvaluation">
        update sys_admin_info_assessment_evaluation
        <set>
             <if test="userName != null">
                user_name = #{ userName },
             </if>
             <if test="userId != null">
                user_id = #{ userId },
             </if>

             <if test="insertTime != null">
                insert_time = #{ insertTime },
             </if>
             <if test="assessmentEvaluationClass != null">
                assessment_evaluation_class = #{ assessmentEvaluationClass },
             </if>
             <if test="assessmentEvaluationName != null">
                assessment_evaluation_name = #{ assessmentEvaluationName },
             </if>
             <if test="assessmentEvaluationScore != null">
                assessment_evaluation_score = #{ assessmentEvaluationScore },
             </if>
             <if test="assessmentEvaluationTime != null">
                assessment_evaluation_time = #{ assessmentEvaluationTime },
             </if>
             <if test="assessmentEvaluationUserName != null">
                assessment_evaluation_user_name = #{ assessmentEvaluationUserName },
             </if>
             <if test="assessmentEvaluationUserId != null">
                assessment_evaluation_user_id = #{ assessmentEvaluationUserId },
             </if>
             <if test="assessmentEvaluationComment != null">
                assessment_evaluation_comment = #{ assessmentEvaluationComment },
             </if>
             <if test="certificateAttachment != null">
                certificate_attachment = #{ certificateAttachment },
             </if>
             <if test="createUserId != null">
                create_user_id = #{ createUserId },
             </if>
             <if test="createUserName != null">
                create_user_name = #{ createUserName },
             </if>
        </set>
        where id = #{ id }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from sys_admin_info_assessment_evaluation where id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="com.jiuzhekan.managementTCMSP.beans.user.SysAdminInfoAssessmentEvaluation" resultMap="BaseResultMap">
        SELECT id,user_name,user_id,insert_time,assessment_evaluation_class,assessment_evaluation_name,assessment_evaluation_score,assessment_evaluation_time,assessment_evaluation_user_name,assessment_evaluation_user_id,assessment_evaluation_comment,certificate_attachment
        from sys_admin_info_assessment_evaluation
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>
    <select id="getAssessmentEvaluationList"
            resultMap="BaseResultMap" parameterType="com.jiuzhekan.managementTCMSP.bean.GetAssessmentEvaluationList">
        select
        a.id,
        a.user_name,
        a.user_id,
        a.app_id,
        a.insert_time,
        a.assessment_evaluation_class,
        a.assessment_evaluation_name,
        a.assessment_evaluation_score,
        a.assessment_evaluation_time,
        a.assessment_evaluation_user_name,
        a.assessment_evaluation_user_id,
        a.assessment_evaluation_comment,
        a.certificate_attachment,
        a.create_user_id,
        a.create_user_name
        from sys_admin_info_assessment_evaluation as a join sys_doctor_multipoint as b on a.user_id = b.user_id
        <where>
            <if test=" userName != null and userName!='' ">
                and (a.user_name like CONCAT('%',trim(#{userName}),'%') or b.doctor_name like
                CONCAT('%',trim(#{userName}),'%'))
            </if>
            <if test=" assessmentEvaluationClass != null and assessmentEvaluationClass!='' ">
                and a.assessment_evaluation_class = #{assessmentEvaluationClass}
            </if>
            <if test=" assessmentEvaluationName != null and assessmentEvaluationName!='' ">
                and a.assessment_evaluation_name like CONCAT('%',trim(#{assessmentEvaluationName}),'%')
            </if>
        </where>

    </select>

</mapper>