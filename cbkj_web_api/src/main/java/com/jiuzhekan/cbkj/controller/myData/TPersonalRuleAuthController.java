package com.jiuzhekan.cbkj.controller.myData;

import com.jiuzhekan.cbkj.beans.myData.TPersonalRuleAuth;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.service.myData.TPersonalRuleAuthService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 协定方权限
 *
 * <AUTHOR>
 * @date 2021/05/20
 */
@Api(value = "协定方权限", tags = "协定方权限")
@Controller
@RequestMapping("tPersonalRuleAuth")
public class TPersonalRuleAuthController {

    @Autowired
    private TPersonalRuleAuthService tPersonalRuleAuthService;

    @RequestMapping(value = "list/preId", method = RequestMethod.GET)
    @ApiOperation(value = "根据处方ID获取权限", response = TPersonalRuleAuth.class)
    @ResponseBody
    @LogAnnotation("根据处方ID获取权限")
    public Object getAuthListByPreId(String persPreId, String ruleId) {
        return tPersonalRuleAuthService.getTreeByPreId(persPreId, ruleId);
    }

    @RequestMapping(value = "save", method = RequestMethod.POST)
    @ApiOperation(value = "根据处方ID获取权限", response = TPersonalRuleAuthReq.class)
    @ResponseBody
    @LogAnnotation("根据处方ID获取权限")
    public Object saveAuth(@RequestBody TPersonalRuleAuthReq req) {
        return tPersonalRuleAuthService.saveAuth(req);
    }
}
