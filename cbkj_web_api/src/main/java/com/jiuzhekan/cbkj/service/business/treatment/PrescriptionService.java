package com.jiuzhekan.cbkj.service.business.treatment;

import com.github.pagehelper.PageHelper;
import com.jiuzhekan.cbkj.beans.business.patients.TPatients;
import com.jiuzhekan.cbkj.beans.business.record.TPrescription;
import com.jiuzhekan.cbkj.beans.business.record.TRecord;
import com.jiuzhekan.cbkj.beans.sysBeans.AdminInfo;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.constant.OrderStatusConstant;
import com.jiuzhekan.cbkj.common.http.InterfaceRestTemplate;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.DateUtil;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.common.utils.pdf.PdfUtil;
import com.jiuzhekan.cbkj.mapper.business.record.*;
import com.jiuzhekan.cbkj.service.business.TSysParamService;
import com.jiuzhekan.cbkj.service.business.patients.TPatientsService;
import com.jiuzhekan.cbkj.service.sysService.AdminService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 处方
 * <p>
 * 杭州聪宝科技有限公司
 *
 * <AUTHOR>
 * @date 2020/10/26
 */
@Service
public class PrescriptionService {

    @Autowired
    private InterfaceRestTemplate interfaceRestTemplate;
    @Autowired
    private TRecordMapper tRecordMapper;
    @Autowired
    private TPrescriptionMapper tPrescriptionMapper;
    @Autowired
    private TPrescriptionItemMapper tPrescriptionItemMapper;
    @Autowired
    private TPrescriptionAcuItemMapper tPrescriptionAcuItemMapper;
    @Autowired
    private TPrescriptionPreparationItemMapper tPrescriptionPreparationItemMapper;
    @Autowired
    private TSysParamService tSysParamService;
    @Autowired
    private PdfUtil pdfUtil;
    @Autowired
    private AdminService adminService;
    @Autowired
    private TPatientsService tPatientsService;

    @Transactional(rollbackFor = Exception.class)
    public ResEntity refundPres(String preId, String recId) {
        if (StringUtils.isBlank(preId)) {
            return ResEntity.entity(false, "参数缺失preId", "");
        }
        if (StringUtils.isBlank(recId)) {
            return ResEntity.entity(false, "参数缺失recId", "");
        }
//        TPrescription pre = tPrescriptionMapper.getObjectById(preId);
        TPrescription tPrescription = new TPrescription();
        tPrescription.setRecId(recId);
        List<TPrescription> pList = tPrescriptionMapper.getPageListByObj(tPrescription);
        List<InterfaceRestTemplate.PreStatus> preStatuList = new ArrayList<>();
        AdminInfo admin = AdminUtils.getCurrentHr();
        String appId = AdminUtils.getCurrentAppId();
        String insCode = AdminUtils.getCurrentInsCode();
        String dateStr = DateUtil.getDateFormats(DateUtil.YYYY_MM_DD_HH_MM_SS, new Date());
        String PRE_INTERFACE = tSysParamService.getSysParam(Constant.PRE_INTERFACE).getParValues();
        for (TPrescription pre : pList) {
            //        收退费状态（0未收费 1已收费 2已退费）
            if (Constant.BASIC_STRING_ONE.equals(pre.getIsPay())) {
                if (
                    //缴费
                        ("50".equals(pre.getRecExtType())) ||
                                //退药
                                ("111".equals(pre.getRecExtType())) ||
                                //药房审核未通过
                                ("62".equals(pre.getRecExtType()))

                ) {
                    if (StringUtils.isNotBlank(PRE_INTERFACE) && PRE_INTERFACE.contains("refund")) {
                        //关闭模拟退费流程
                        InterfaceRestTemplate.PreStatus preStatus = interfaceRestTemplate.new PreStatus();
                        preStatus.setPreNo(pre.getPreNo());
                        preStatus.setStatus(OrderStatusConstant.RETURN.toString());
                        preStatus.setOperationTime(dateStr);
                        preStatus.setOperationName(admin.getNameZh());
                        preStatus.setOperationContent(OrderStatusConstant.RETURN.getName());
                        preStatus.setSource(Constant.BASIC_STRING_ONE);
                        preStatuList.add(preStatus);
                    } else {
                        //演示用。云系统自己修改处方状态。
                        TPrescription prescription = new TPrescription();
                        prescription.setPreId(pre.getPreId());
                        //prescription.setIsDel(Constant.BASIC_STRING_ONE);
                        prescription.setDelUserid(admin.getId());
                        prescription.setDelUsername(admin.getNameZh());
                        prescription.setRecExtType(OrderStatusConstant.RETURN.toString());
                        int i = tPrescriptionMapper.updateByPrimaryKey(prescription);
                        return ResEntity.entity(true, Constant.SUCCESS_DX, i);
                    }
                } else {
                    String msg = "退费失败，编号为" + pre.getPreNo() + "的处方当前状态为：" + OrderStatusConstant.getName(Integer.parseInt(pre.getRecExtType()));
                    return ResEntity.entity(false, msg, "");
                }
            } else {
                String msg = "退费失败，编号为" + pre.getPreNo() + "的处方当前状态为：" + OrderStatusConstant.getName(Integer.parseInt(pre.getRecExtType()));
                return ResEntity.entity(false, msg, "");
            }
        }
        if (!preStatuList.isEmpty()) {
            InterfaceRestTemplate.StatusVO statusVO = interfaceRestTemplate.new StatusVO();
            statusVO.setAppId(appId);
            statusVO.setInsCode(insCode);
            statusVO.setTimestamp(String.valueOf(System.currentTimeMillis()));
            statusVO.setPreStatusList(preStatuList);
            return interfaceRestTemplate.setStatus(statusVO);
        }
        return ResEntity.entity(false, "处方不存在", "");
    }

    public ResEntity repealPres(String preId, String registerId) {
        if (StringUtils.isBlank(preId)) {
            return ResEntity.entity(false, "参数缺失preId", "");
        }
        if (StringUtils.isBlank(registerId)) {
            return ResEntity.entity(false, "参数缺失registerId", "");
        }
        TPrescription tPrescription = new TPrescription();
        tPrescription.setPreId(preId);
        List<TPrescription> pList = tPrescriptionMapper.getPageListByObj(tPrescription);

        List<InterfaceRestTemplate.PreStatus> preStatuList = new ArrayList<>();
        AdminInfo admin = AdminUtils.getCurrentHr();
        String appId = AdminUtils.getCurrentAppId();
        String insCode = AdminUtils.getCurrentInsCode();

        for (TPrescription pre : pList) {

            InterfaceRestTemplate.PreStatus preStatus = interfaceRestTemplate.new PreStatus();
            preStatus.setPreNo(pre.getPreNo());
            preStatus.setStatus(OrderStatusConstant.RETURN.toString());
            preStatus.setOperationTime(DateUtil.getDateFormats(DateUtil.YYYY_MM_DD_HH_MM_SS, new Date()));
            preStatus.setOperationName(admin.getNameZh());
            preStatus.setOperationContent(OrderStatusConstant.RETURN.getName());
            preStatus.setSource(Constant.BASIC_STRING_ONE);

            InterfaceRestTemplate.PreStatus preStatus2 = interfaceRestTemplate.new PreStatus();
            preStatus2.setPreNo(pre.getPreNo());
            preStatus2.setStatus(OrderStatusConstant.DELETE.toString());
            preStatus2.setOperationTime(DateUtil.getDateFormats(DateUtil.YYYY_MM_DD_HH_MM_SS, new Date()));
            preStatus2.setOperationName(admin.getNameZh());
            preStatus2.setOperationContent(OrderStatusConstant.DELETE.getName());
            preStatus2.setSource(Constant.BASIC_STRING_ONE);

            //顺序不能乱
            preStatuList.add(preStatus);
            preStatuList.add(preStatus2);
        }
        if (!preStatuList.isEmpty()) {
            InterfaceRestTemplate.StatusVO statusVO = interfaceRestTemplate.new StatusVO();
            statusVO.setAppId(appId);
            statusVO.setInsCode(insCode);
            statusVO.setTimestamp(String.valueOf(System.currentTimeMillis()));
            statusVO.setPreStatusList(preStatuList);
            return interfaceRestTemplate.setStatus(statusVO);
        }
        return ResEntity.entity(false, "处方不存在", "");
    }
    public ResEntity delPres(String preId) {
        if (StringUtils.isBlank(preId)) {
            return ResEntity.entity(false, "参数缺失", "");
        }

        TPrescription pre = tPrescriptionMapper.getObjectById(preId);
        if (pre == null) {
            return ResEntity.entity(false, "处方不存在", "");
        }

        // 作废校验：支付后isPay置为1不可作废，特例recExtType为110时表示已退药状态，可以作废
//        if (Constant.BASIC_STRING_ONE.equals(pre.getIsPay()) && !"110".equals(pre.getRecExtType())) {
        //作废校验：支付后isPay置为1不可作废，2022-11-23-->特例recExtType为62时表示药房审核未通过状态，可以作废
        if (Constant.BASIC_STRING_ONE.equals(pre.getIsPay()) && !"62".equals(pre.getRecExtType())) {
            String msg = "作废失败，编号为" + pre.getPreNo() + "的处方当前状态为：收费成功";
            return ResEntity.entity(false, msg, "");
        }

        return delPreByPreNo(pre);
    }


    public ResEntity delPreByPreNo(TPrescription pre) {
        return delPreByPreNo(pre, null);
    }

    public ResEntity delPreByPreNo(TPrescription pre, String delUserName) {

        AdminInfo admin = AdminUtils.getCurrentHr();
        String appId = AdminUtils.getCurrentAppId();
        String insCode = AdminUtils.getCurrentInsCode();

        String preInterface = tSysParamService.getSysParam(Constant.PRE_INTERFACE).getParValues();
        if (!StringUtils.isBlank(preInterface) && preInterface.contains("del")) {
            String dateStr = DateUtil.getDateFormats(DateUtil.YYYY_MM_DD_HH_MM_SS, new Date());
            List<InterfaceRestTemplate.PreStatus> preStatusList = new ArrayList<>();

            InterfaceRestTemplate.PreStatus preStatus = interfaceRestTemplate.new PreStatus();
            preStatus.setPreNo(pre.getPreNo());
            preStatus.setStatus(OrderStatusConstant.DELETE.toString());
            preStatus.setOperationTime(dateStr);
            preStatus.setOperationName(admin.getNameZh());
            preStatus.setOperationContent(OrderStatusConstant.DELETE.getName());
            preStatus.setSource(Constant.BASIC_STRING_ONE);
            preStatusList.add(preStatus);

            InterfaceRestTemplate.StatusVO statusVO = interfaceRestTemplate.new StatusVO();
            statusVO.setAppId(appId);
            statusVO.setInsCode(insCode);
            statusVO.setTimestamp(String.valueOf(System.currentTimeMillis()));
            statusVO.setPreStatusList(preStatusList);

            return interfaceRestTemplate.setStatus(statusVO);
        } else {

            TPrescription prescription = new TPrescription();
            prescription.setPreId(pre.getPreId());
            prescription.setIsDel(Constant.BASIC_STRING_ONE);
            prescription.setDelUserid(admin.getId());
            prescription.setDelUsername(StringUtils.isBlank(delUserName) ? admin.getNameZh() : delUserName);
            prescription.setRecExtType(OrderStatusConstant.DELETE.toString());
            int i = tPrescriptionMapper.updateByPrimaryKey(prescription);
            return ResEntity.entity(true, Constant.SUCCESS_DX, i);
        }
    }


    public ResEntity printPres(String recId, String preNo, String preId,String t) {

        TRecord record = tRecordMapper.getObjectById(recId);
        if (null == record) {
            return ResEntity.error("病历不存在");
        }

        String insName = adminService.getInsName(record.getAppId(), record.getInsCode());
        if (!StringUtils.isBlank(insName)) {
            record.setInsName(insName);
        }

        List<TPrescription> preList = new ArrayList<>();

        if (StringUtils.isNotBlank(preId)) {
            for (String pId : preId.split(Constant.ENGLISH_COMMA)) {
                TPrescription pre = tPrescriptionMapper.getObjectByPreId(pId);
                if (pre != null) {
                    preList.add(pre);
                }
            }
            record.setPrescriptionList(preList);
        } else if (StringUtils.isNotBlank(preNo)) {
            for (String pno : preNo.split(Constant.ENGLISH_COMMA)) {
                TPrescription pre = tPrescriptionMapper.getObjectByPreNo(pno);
                if (pre != null) {
                    preList.add(pre);
                }
            }
            record.setPrescriptionList(preList);
        } else {

            TPrescription tPrescription = new TPrescription();
            tPrescription.setRecId(recId);
            preList = tPrescriptionMapper.getListByRecId(tPrescription);
        }

        if (preList.isEmpty()) {
            return ResEntity.entity(false, "没有相应处方！", null);
        }

        for (TPrescription pre : preList) {
            if (Constant.BASIC_STRING_ONE.equals(pre.getPreType()) || Constant.BASIC_STRING_TWO.equals(pre.getPreType())) {
                pre.setItemList(tPrescriptionItemMapper.getListByPreId(pre.getPreId()));
            } else if (Constant.BASIC_STRING_FOUR.equals(pre.getPreType())) {
                pre.setAcuItemList(tPrescriptionAcuItemMapper.getListByPreId(pre.getPreId()));
            } else if (Constant.BASIC_STRING_FIVE.equals(pre.getPreType())) {
                pre.setPreparationItemList(tPrescriptionPreparationItemMapper.getListByPreId(pre.getPreId()));
            }
        }

        record.setPrescriptionList(preList);
        String pdfPath = null;
        String patientId = record.getPatientId();
        TPatients patients = tPatientsService.findById(patientId);
        if (null != patients) {
            StringBuilder stringBuffer = new StringBuilder("");
            String s = StringUtils.isBlank(patients.getPatientCounty()) ? "" : patients.getPatientCounty();
            String s2 = StringUtils.isBlank(patients.getPatientTown()) ? "" : patients.getPatientTown();
            String s3 = StringUtils.isBlank(patients.getPatientVillage()) ? "" : patients.getPatientVillage();
            String s4 = StringUtils.isBlank(patients.getPatientStreet()) ? "" : patients.getPatientStreet();
            String s5 = StringUtils.isBlank(patients.getPatientAddress()) ? "" : patients.getPatientAddress();
            stringBuffer.append(s).append(s2).append(s3).append(s4).append(s5);
            record.setAddress(stringBuffer.toString());
            record.setMobile(StringUtils.isBlank(patients.getPatientMobile()) ? "" : patients.getPatientMobile());
        }
        if (Constant.BASIC_STRING_TWO.equals(t)) {
            pdfPath = pdfUtil.createRecordPdf2("prescriptionPDF", record);
        } else {
            pdfPath = pdfUtil.createRecordPdf("prescriptionPDF", record);

        }

        return ResEntity.entity(true, Constant.SUCCESS_DX, pdfPath);
    }


    public Object getPreList(TPrescription tPrescription, Page page, String visitNo, String registerId) {

        if (StringUtils.isNotBlank(visitNo)) {
            registerId = null;
        }
        PageHelper.startPage(page.getPage(), page.getLimit());
        List<TPrescription> list = tPrescriptionMapper.getPreListByObj(tPrescription, visitNo, registerId);
        return Page.getLayUiTablePageData(list);
    }
}
