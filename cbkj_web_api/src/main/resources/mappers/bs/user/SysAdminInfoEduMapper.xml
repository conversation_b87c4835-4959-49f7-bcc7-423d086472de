<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.managementTCMSP.mapper.user.SysAdminInfoEduMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.managementTCMSP.beans.user.SysAdminInfoEdu">
        <id column="id" jdbcType="INTEGER"  property="id" />
        <result column="user_name" jdbcType="VARCHAR" property="userName" />
        <result column="user_id" jdbcType="VARCHAR" property="userId" />
        <result column="course_name" jdbcType="VARCHAR" property="courseName" />
        <result column="course_type" jdbcType="INTEGER" property="courseType" />
        <result column="credit" jdbcType="DECIMAL" property="credit" />
        <result column="study_status" jdbcType="INTEGER" property="studyStatus" />
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
        <result column="certificate_attachment" jdbcType="VARCHAR" property="certificateAttachment" />
        <result column="app_id" jdbcType="VARCHAR" property="appId" />
        <result column="app_name" jdbcType="VARCHAR" property="appName" />
        <result column="ins_code" jdbcType="VARCHAR" property="insCode" />
        <result column="dept_id" jdbcType="VARCHAR" property="deptId" />
        <result column="employee_id" jdbcType="VARCHAR" property="employeeId" />
        <result column="ins_name" jdbcType="VARCHAR" property="insName" />
        <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
        <result column="insert_time" jdbcType="TIMESTAMP" property="insertTime" />
    </resultMap>


    <sql id="Base_Column_List">
    id,user_name,user_id,course_name,course_type,credit,study_status,start_time,end_time,certificate_attachment,app_id,app_name,ins_code,dept_id,employee_id,ins_name,dept_name,insert_time
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.jiuzhekan.managementTCMSP.beans.user.SysAdminInfoEdu">
        delete from sys_admin_info_edu where id = #{ id }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from sys_admin_info_edu where id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="com.jiuzhekan.managementTCMSP.beans.user.SysAdminInfoEdu">
        insert into sys_admin_info_edu (<include refid="Base_Column_List" />) values
        (#{id},#{userName},#{userId},#{courseName},#{courseType},#{credit},#{studyStatus},#{startTime},#{endTime},#{certificateAttachment},#{appId},#{appName},#{insCode},#{deptId},#{employeeId},#{insName},#{deptName},#{insertTime})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into sys_admin_info_edu (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.id},#{item.userName},#{item.userId},#{item.courseName},#{item.courseType},#{item.credit},#{item.studyStatus},#{item.startTime},#{item.endTime},#{item.certificateAttachment},#{item.appId},#{item.appName},#{item.insCode},#{item.deptId},#{item.employeeId},#{item.insName},#{item.deptName},#{item.insertTime})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.jiuzhekan.managementTCMSP.beans.user.SysAdminInfoEdu">
        update sys_admin_info_edu
        <set>
             <if test="userName != null">
                user_name = #{ userName },
             </if>
             <if test="userId != null">
                user_id = #{ userId },
             </if>
             <if test="courseName != null">
                course_name = #{ courseName },
             </if>
             <if test="courseType != null">
                course_type = #{ courseType },
             </if>
             <if test="credit != null">
                credit = #{ credit },
             </if>
             <if test="studyStatus != null">
                study_status = #{ studyStatus },
             </if>
             <if test="startTime != null">
                start_time = #{ startTime },
             </if>
             <if test="endTime != null">
                end_time = #{ endTime },
             </if>
             <if test="certificateAttachment != null">
                certificate_attachment = #{ certificateAttachment },
             </if>
             <if test="appId != null">
                app_id = #{ appId },
             </if>
             <if test="appName != null">
                app_name = #{ appName },
             </if>
             <if test="insCode != null">
                ins_code = #{ insCode },
             </if>
             <if test="deptId != null">
                dept_id = #{ deptId },
             </if>
             <if test="employeeId != null">
                employee_id = #{ employeeId },
             </if>
             <if test="insName != null">
                ins_name = #{ insName },
             </if>
             <if test="deptName != null">
                dept_name = #{ deptName },
             </if>
             <if test="insertTime != null">
                insert_time = #{ insertTime },
             </if>
        </set>
        where id = #{ id }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from sys_admin_info_edu where id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="com.jiuzhekan.managementTCMSP.beans.user.SysAdminInfoEdu" resultMap="BaseResultMap">
        SELECT id,user_name,user_id,course_name,course_type,credit,study_status,start_time,end_time,certificate_attachment,app_id,app_name,ins_code,dept_id,employee_id,ins_name,dept_name,insert_time
        from sys_admin_info_edu
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>

</mapper>