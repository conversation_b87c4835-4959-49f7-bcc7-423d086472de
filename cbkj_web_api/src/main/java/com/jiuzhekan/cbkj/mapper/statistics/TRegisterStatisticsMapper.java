package com.jiuzhekan.cbkj.mapper.statistics;

import com.jiuzhekan.cbkj.beans.statistics.InstitutionStatsResponse;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 描述
 *
 * <AUTHOR>
 * @Date 2020/2/18
 */
@Component
public interface TRegisterStatisticsMapper {

    /**
     * 按照实际段，统计每个月的就诊人次
     * @param map
     * @return
     */
    List<InstitutionStatsResponse> getRegisterCountByMonth(Map map);
}
