package com.jiuzhekan.cbkj.mapper.business.syndrome;


import com.jiuzhekan.cbkj.beans.business.syndrome.TRecordSyndromeFile;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public interface TRecordSyndromeFileMapper {

    void insertCheckResult(List<TRecordSyndromeFile> tRecordSyndromeFiles);

    Integer deleteCheckResult(List<TRecordSyndromeFile> tRecordSyndromeFiles);

    TRecordSyndromeFile getSyndromeFile(TRecordSyndromeFile tRecordSyndromeFile);

    List<TRecordSyndromeFile> getSyndromeFileBySMFId(@Param("smfId") String smfId);

}
