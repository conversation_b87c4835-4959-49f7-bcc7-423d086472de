package com.jiuzhekan.cbkj.service.business.doctor;


import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.jiuzhekan.cbkj.beans.business.doctor.TDoctorCollect;
import com.jiuzhekan.cbkj.beans.business.doctor.TDoctorNote;
import com.jiuzhekan.cbkj.beans.sysBeans.AdminInfo;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.*;
import com.jiuzhekan.cbkj.mapper.business.doctor.TDoctorCollectMapper;
import com.jiuzhekan.cbkj.mapper.business.doctor.TDoctorNoteMapper;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class TDoctorNoteService {

    @Autowired
    private TDoctorNoteMapper tDoctorNoteMapper;
    @Autowired
    private TDoctorCollectMapper tDoctorCollectMapper;

    /**
     * 加载分页数据
     *
     * @param tDoctorNote
     * @param page
     * @return
     */
    public Object getPageDatas(TDoctorNote tDoctorNote, Page page) {
        PageHelper.startPage(page.getPage(), page.getLimit());
        List<TDoctorNote> list = tDoctorNoteMapper.getPageListByObj(tDoctorNote);
        Object result = Page.getLayUiTablePageData(list);
        return result;
    }

    /**
     * 插入新数据
     *
     * @param tDoctorNote
     * @return
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insert(TDoctorNote tDoctorNote) {
        if (StringUtils.isBlank(tDoctorNote.getConnectId())) {
            return ResEntity.entity(false, "关联id为空!", tDoctorNote);
        }
        if (null == tDoctorNote.getNoteModule()) {
            return ResEntity.entity(false, "模块标志为空!", tDoctorNote);
        }
        if (null == tDoctorNote.getNoteType()) {
            tDoctorNote.setNoteType(1);
        }
        Date createDate = new Date();
        AdminInfo currentHr = AdminUtils.getCurrentHr();
        tDoctorNote.setNoteId(IDUtil.getID());
        tDoctorNote.setCreateUser(currentHr.getId());
        tDoctorNote.setCreateDate(createDate);
        long rows = tDoctorNoteMapper.insert(tDoctorNote);
        if (rows > 0) {
            // 新增笔记就默认收藏
            TDoctorCollect tDoctorCollect = new TDoctorCollect();
            tDoctorCollect.setCollectId(IDUtil.getID());
            tDoctorCollect.setCollectModule(tDoctorNote.getNoteModule());
            tDoctorCollect.setConnectId(tDoctorNote.getConnectId());
            tDoctorCollect.setCreateDate(createDate);
            tDoctorCollect.setCreateUser(currentHr.getId());
            int insert = tDoctorCollectMapper.insert(tDoctorCollect);
            tDoctorNote.setCollectId(tDoctorCollect.getCollectId());
            return ResEntity.entity(true, Constant.SUCCESS_DX, tDoctorNote);
        }
        return new ResEntity(false, "保存失败，数据库异常！！", null);
    }


    /**
     * 修改
     *
     * @param tDoctorNote
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity update(TDoctorNote tDoctorNote) {
        if (StringUtils.isBlank(tDoctorNote.getNoteId())) {
            return ResEntity.entity(false, "笔记id为空!", tDoctorNote);
        }
        long rows = tDoctorNoteMapper.updateByPrimaryKey(tDoctorNote);
        return ResEntity.entity(true, Constant.SUCCESS_DX, tDoctorNote);
    }

    /**
     * 加载某条数据
     *
     * @param noteId
     * @return
     */
    public ResEntity findObj(String noteId) {

        if (StringUtils.isBlank(noteId)) {
            return new ResEntity(false, "参数不能为空哦", null);
        }
        TDoctorNote tDoctorNote = tDoctorNoteMapper.getObjectById(noteId);
        return new ResEntity(true, Constant.SUCCESS_DX, tDoctorNote);
    }

    /**
     * 删除
     *
     * @param ids
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity deleteLis(String ids) {
        if (StringUtils.isBlank(ids)) {
            return new ResEntity(false, "参数错误(缺少参数)！", null);
        }
        long rowsR = tDoctorNoteMapper.deleteBylist(ids.split(","));

        return new ResEntity(true, Constant.SUCCESS_DX, rowsR);
    }


    public Object getNotes(TDoctorNote tDoctorNote, Page page) {
        // 查出所有数据,去除非本人且私有的笔记
        if (StringUtils.isBlank(tDoctorNote.getCreateUser())) {
            tDoctorNote.setCreateUser(AdminUtils.getCurrentHr().getId());
        }

        PageHelper.startPage(page.getPage(), page.getLimit());
        List<TDoctorNote> notes = tDoctorNoteMapper.getAllNotes(tDoctorNote);
        if (CollectionUtils.isNotEmpty(notes)) {
            notes.stream().map(resNote -> {
                // 笔记时间字符串
                resNote.setDateStr(DateUtil.getDateFormats(DateUtil.YYYY_MM_DD, resNote.getCreateDate()));
                // 非本人的匿名共享都要隐藏名字
                if (resNote.getNoteType() == 1) {
                    resNote.setNoteTypeStr("私有");
                } else if (resNote.getNoteType() == 2 && !resNote.getCreateUser().equals(tDoctorNote.getCreateUser())) {
                    resNote.setNoteUsername("***");
                    resNote.setNoteTypeStr("匿名共享");
                } else if (resNote.getNoteType() == 3) {
                    resNote.setNoteTypeStr("");
                }

                resNote.setIsSelf(resNote.getCreateUser().equals(tDoctorNote.getCreateUser()) ? 1 : 0);
                return resNote;
            }).collect(Collectors.toList()).sort(Comparator.comparing(TDoctorNote::getIsSelf).reversed());

        }
        return Page.getLayUiTablePageData(notes);
    }

//    /**
//     * @return : java.lang.Object
//     * @Description : 分页查询笔记,自己的笔记放在最前面，其他人的笔记按时间倒序,一般按照关联id查询;模块字段是
//     * 为了以后可能存在的查询或统计做准备
//     * <AUTHOR> xhq
//     * @updateTime : 2020/6/18 14:43
//     */
//    public Object getNotes(TDoctorNote tDoctorNote, Page page) {
//        // 查出所有数据,去除非本人且私有的笔记
//        if (StringUtils.isBlank(tDoctorNote.getCreateUser())) {
//            tDoctorNote.setCreateUser(AdminUtils.getCurrentHr().getId());
//        }
//        Integer count = 0;
//        Boolean isHasNextPage = false;
//        List<TDoctorNote> notes = tDoctorNoteMapper.getAllNotes(tDoctorNote);
//        if (null != notes && !notes.isEmpty()) {
//            count = notes.size();
//            List<TDoctorNote> selfNotes = new ArrayList<>();
//            // 取出本人 的笔记
//            for (int i = 0; i < notes.size(); i++) {
//                TDoctorNote resNote = notes.get(i);
//                // 笔记时间字符串
//                resNote.setDateStr(DateUtil.getDateFormats(DateUtil.date2, resNote.getCreateDate()));
//                // 非本人的匿名共享都要隐藏名字
//                if (resNote.getNoteType() == 1) {
//                    resNote.setNoteTypeStr("私有");
//                } else if (resNote.getNoteType() == 2 && !resNote.getCreateUser().equals(tDoctorNote.getCreateUser())) {
//                    resNote.setNoteUsername("***");
//                    resNote.setNoteTypeStr("匿名共享");
//                } else if (resNote.getNoteType() == 3) {
//                    resNote.setNoteTypeStr("");
//                }
//                // 取出创建人是登录人的笔记
//                if (resNote.getCreateUser().equals(tDoctorNote.getCreateUser())) {
//                    resNote.setIsSelf(1);
//                    selfNotes.add(resNote);
//                    notes.remove(i);
//                    i--;
//                }
//            }
//            // 将resNote倒序后放入notes集合前方
//            if (!selfNotes.isEmpty()) {
//                notes.addAll(0, selfNotes);
//            }
//
//            Integer pageNum = page.getPage();
//            Integer limit = page.getLimit();
//            Integer fromIdx = (pageNum - 1) * limit;
//            Integer endIdx = fromIdx;
//            if (count > pageNum * limit) {
//                isHasNextPage = true;
//                endIdx = pageNum * limit;
//            } else {
//                endIdx = count;
//            }
//            // 分页截取数据集
//            notes = notes.subList(fromIdx, endIdx);
//        }
//        return Page.getLayuiData(true, Constant.SUCCESS_DX, count, isHasNextPage, notes);
//    }

    /**
     * 根据关联id和登录人查询笔记
     *
     * @param tDoctorNote
     * @return
     */
    public Object getNoteByUser(TDoctorNote tDoctorNote) {
        if (StringUtils.isBlank(tDoctorNote.getConnectId())) {
            return ResEntity.entity(false, "关联id没传", tDoctorNote);
        }
        tDoctorNote.setCreateUser(AdminUtils.getCurrentHr().getId());
        TDoctorNote resNote = tDoctorNoteMapper.getObjectByNote(tDoctorNote);
        return ResEntity.entity(true, Constant.SUCCESS_DX, resNote);
    }
}