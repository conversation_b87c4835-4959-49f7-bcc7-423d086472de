package com.jiuzhekan.cbkj.mapper.business.record;

import com.jiuzhekan.cbkj.beans.business.record.TPrescription;
import com.jiuzhekan.cbkj.beans.business.record.TPrescriptionExamine;
import com.jiuzhekan.cbkj.beans.business.record.VO.TPreRespVO;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 描述
 *
 * <AUTHOR>
 * @Date 2020/2/18
 */
@Component
public interface TPrescriptionExamineMapper extends BaseMapper<TPrescriptionExamine> {

    List<TPrescriptionExamine> getExamineListByPre(TPreRespVO pre);
}
