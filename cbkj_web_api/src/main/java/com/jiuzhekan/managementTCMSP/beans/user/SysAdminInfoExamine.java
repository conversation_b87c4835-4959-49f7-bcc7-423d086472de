package com.jiuzhekan.managementTCMSP.beans.user;//package com.jiuzhekan.cbkj.beans.user;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class SysAdminInfoExamine implements Serializable{

    @ApiModelProperty(value = "人员考核表主键id")
    private Integer id;

    @ApiModelProperty(value = "")
    private String userName;

    @ApiModelProperty(value = "")
    private String userId;

//    @ApiModelProperty(value = "")
//    private String appId;
//
//    @ApiModelProperty(value = "")
//    private String appName;
//
//    @ApiModelProperty(value = "")
//    private String insName;
//
//    @ApiModelProperty(value = "")
//    private String insCode;
//
//    @ApiModelProperty(value = "")
//    private String deptName;
//
//    @ApiModelProperty(value = "")
//    private String deptId;
//
    @ApiModelProperty(value = "工号的集合")
    private String employeeIds;

    @ApiModelProperty(value = "考核等级（1.优秀2.良好3.合格4不合格）")
    private Integer examineClass;

    @ApiModelProperty(value = "考核周期")
    private String examineCycle;

    @ApiModelProperty(value = "考核分数")
    private Double examineScore;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "考核日期")
    private Date examineTime;

    @ApiModelProperty(value = "考核人名")
    private String assessor;

    @ApiModelProperty(value = "考核人id")
    private String assessorId;

    @ApiModelProperty(value = "考核意见")
    private String assessorComment;

    @ApiModelProperty(value = "考核附件")
    private String certificateAttachment;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "数据插入日期")
    private Date insertTime;

    private String createUserId;
    private String createUserName;
}
