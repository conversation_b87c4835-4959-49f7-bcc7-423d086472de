package com.jiuzhekan.cbkj.service.ai;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.jiuzhekan.cbkj.beans.ai.CueWordPres;
import com.jiuzhekan.cbkj.beans.ai.CueWordURLMatRequest;
import com.jiuzhekan.cbkj.beans.ai.CueWordURLRequest;
import com.jiuzhekan.cbkj.beans.ai.SysCueWord;
import com.jiuzhekan.cbkj.beans.business.patients.TPatientAge;
import com.jiuzhekan.cbkj.beans.business.patients.TPatients;
import com.jiuzhekan.cbkj.beans.business.record.*;
import com.jiuzhekan.cbkj.beans.business.record.VO.TBigRecordRespVO;
import com.jiuzhekan.cbkj.beans.business.record.VO.TPreRespVO;
import com.jiuzhekan.cbkj.beans.business.record.VO.TRecordRespVO;
import com.jiuzhekan.cbkj.beans.business.template.TRecordTemplate;
import com.jiuzhekan.cbkj.beans.business.template.TRecordTemplateDetail;
import com.jiuzhekan.cbkj.beans.sysBeans.AdminInfo;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.http.GptAPIRes;
import com.jiuzhekan.cbkj.common.http.SpeechRecordTemplate;
import com.jiuzhekan.cbkj.common.utils.AESPKCS7Util;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.controller.request.MedicalHistorySaveRequest;
import com.jiuzhekan.cbkj.mapper.ai.SysCueWordMapper;
import com.jiuzhekan.cbkj.mapper.business.patients.TPatientsMapper;
import com.jiuzhekan.cbkj.mapper.business.record.TRecordMapper;
import com.jiuzhekan.cbkj.service.business.patients.TPatientsService;
import com.jiuzhekan.cbkj.service.business.record.MedicalRecordService;
import com.jiuzhekan.cbkj.service.business.record.TMedicalRecordSourceService;
import com.jiuzhekan.cbkj.service.business.record.TRecordService;
import com.jiuzhekan.cbkj.service.business.treatment.ElectronicRecordService;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.net.URLEncoder;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/5 11:36
 * @Version 1.0
 */
@Service
@Log4j2
public class SySCueWordService {
    @Value("${urlGpt}")
    private String urlGpt;

    @Value("${urlGptKey}")
    private String urlGptKey;

    @Value("${urlGptAPI}")
    private String urlGptAPI;
    private final SysCueWordMapper sysCueWordMapper;
    private final MedicalRecordService medicalRecordService;

    private final TPatientsMapper tPatientsMapper;
    private final SpeechRecordTemplate speechRecordTemplate;
    private final ElectronicRecordService electronicRecordService;
    private final TRecordService tRecordService;
    private final TRecordMapper tRecordMapper;
    private final TMedicalRecordSourceService tMedicalRecordSourceService;

    public SySCueWordService(SysCueWordMapper sysCueWordMapper, MedicalRecordService medicalRecordService, TPatientsMapper tPatientsMapper, SpeechRecordTemplate speechRecordTemplate, ElectronicRecordService electronicRecordService, TRecordService tRecordService, TRecordMapper tRecordMapper, TMedicalRecordSourceService tMedicalRecordSourceService) {
        this.sysCueWordMapper = sysCueWordMapper;
        this.medicalRecordService = medicalRecordService;
        this.tPatientsMapper = tPatientsMapper;
        this.speechRecordTemplate = speechRecordTemplate;
        this.electronicRecordService = electronicRecordService;
        this.tRecordService = tRecordService;
        this.tRecordMapper = tRecordMapper;
        this.tMedicalRecordSourceService = tMedicalRecordSourceService;
    }

    public GptAPIRes getYiAnList(CueWordURLRequest cueWordURLRequest) {
//        TRegister currentRegister = AdminUtils.getCurrentRegister();
        SysCueWord sysCueWord = new SysCueWord();
//        sysCueWord.setAppId(AdminUtils.getCurrentAppId());
//        sysCueWord.setInsCode(AdminUtils.getCurrentInsCode());
        sysCueWord.setCueWordType("famous_cases");
        SysCueWord sysCueWordOne = sysCueWordMapper.getOne(sysCueWord);
        JSONObject jsonObject = new JSONObject();
        HashMap<String, Object> map = new HashMap<>();
        AdminInfo currentHr = AdminUtils.getCurrentHr();
        map.put("appId", AdminUtils.getCurrentAppId());
        map.put("insCode", AdminUtils.getCurrentInsCode());
        map.put("deptId", AdminUtils.getCurrentDeptId());
        map.put("userName", currentHr.getUsername());
        map.put("origin", "tcm");
        map.put("userId", currentHr.getId());
        String json = JSON.toJSONString(map);
        log.info("======获取GPT请求URL的cbdata:{}", json);
        String s = null;
        try {
            s = AESPKCS7Util.encrypt(json, "99748ee4718e43cd", "base64");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        jsonObject.put("cbdata", s);
        jsonObject.put("knowledge_id", sysCueWordOne.getCueWordAgentId());
//        jsonObject.put("query", getContent(cueWordURLRequest));
        jsonObject.put("query", "疾病："+cueWordURLRequest.getDisName()+", 证型："+cueWordURLRequest.getSymName()+", 治法："+cueWordURLRequest.getTheName());
        return speechRecordTemplate.getGptAPIRes(urlGptAPI, jsonObject);
    }

    private static String replaceSpace(String str) {
        return str.replaceAll("\\s+", "").replaceAll("　", "");
    }

    public String getContent(CueWordURLRequest cueWordURLRequest) {
        SysCueWord sysCueWord = new SysCueWord();
        sysCueWord.setCueWordType("record_structure");
        SysCueWord sysCueWordOne = sysCueWordMapper.getOne(sysCueWord);
        String content = sysCueWordOne.getCueWordText();
        TRegister currentRegister = AdminUtils.getCurrentRegister();
        if (currentRegister != null) {
            TPatients objectById = tPatientsMapper.getObjectById(currentRegister.getPatientId());
            if (null != objectById) {
                String zx = "";
                String xbs = "";
                String jws = "";
                String zysz = "";
                String tgjc = "";
                String fzjc = "";
                String grs = "";
                String zlyj = "";
                String gms = "";

                String patientGender = objectById.getPatientGender();
                String gravidity = currentRegister.getGravidity();
                TPatientAge patientAge = TPatientsService.getAgeByBirth(objectById.getPatientBirthday());
                ResEntity recordDetailListByRegisterId = electronicRecordService.getRecordDetailListByRegisterId(currentRegister.getRegisterId(), cueWordURLRequest.getDisId(), AdminUtils.getCurrentHr());
                boolean status = recordDetailListByRegisterId.getStatus();
                if (status) {
                    TRecordTemplate template = (TRecordTemplate) recordDetailListByRegisterId.getData();
                    List<TRecordTemplateDetail> detailList = template.getDetailList();
                    for (TRecordTemplateDetail detail : detailList) {
                        if ("主诉".equals(replaceSpace(detail.getDetailName()))) {
                            if (StringUtils.isNotBlank(detail.getContent())) {
                                zx = "主诉：" + detail.getContent() + "。";
                            }
                        } else if ("现病史".equals(replaceSpace(detail.getDetailName()))) {
                            if (StringUtils.isNotBlank(detail.getContent())) {
                                xbs = "现病史：" + detail.getContent() + "。";
                            }
                        } else if ("既往史".equals(replaceSpace(detail.getDetailName()))) {
                            if (StringUtils.isNotBlank(detail.getContent())) {
                                jws = "既往史：" + detail.getContent() + "。";
                            }
                        } else if ("中医四诊".equals(replaceSpace(detail.getDetailName()))) {
                            if (StringUtils.isNotBlank(detail.getContent())) {
                                zysz = "中医四诊：" + detail.getContent() + "。";
                            }
                        } else if ("体格检查".equals(replaceSpace(detail.getDetailName()))) {
                            if (StringUtils.isNotBlank(detail.getContent())) {
                                tgjc = "体格检查：" + detail.getContent() + "。";
                            }
                        } else if ("辅助检查".equals(replaceSpace(detail.getDetailName()))) {
                            if (StringUtils.isNotBlank(detail.getContent())) {
                                fzjc = "辅助检查：" + detail.getContent() + "。";
                            }
                        } else if ("个人史".equals(replaceSpace(detail.getDetailName()))) {
                            if (StringUtils.isNotBlank(detail.getContent())) {
                                grs = "个人史：" + detail.getContent() + "。";
                            }
                        } else if ("治疗意见".equals(replaceSpace(detail.getDetailName()))) {
                            if (StringUtils.isNotBlank(detail.getContent())) {
                                zlyj = "治疗意见：" + detail.getContent() + "。";
                            }
                        } else if ("过敏史".equals(replaceSpace(detail.getDetailName()))) {
                            if (StringUtils.isNotBlank(detail.getContent())) {
                                gms = "过敏史：" + detail.getContent() + "。";
                            }
                        }
                    }
                }
                //按格式拼接内容如：内服中药方：冬瓜子:7克广藿香:10克淡竹叶:3克苍术:10克草果:6克菊花:6克青蒿:10克鱼腥草:15克黄连:6克姜厚朴:10克贴数：7,服法：水煎服-饭后服,频次：一日2次。从CueWordURLRequest对象获取
                StringBuilder stringBuilder = new StringBuilder();
                List<CueWordPres> cueWordPresList = cueWordURLRequest.getCueWordPresList();
                for (int i = 0; i < cueWordPresList.size(); i++) {
                    CueWordPres cueWordPres = cueWordPresList.get(i);
                    if ("1".equals(cueWordPres.getPreType())) {
                        //内服中药方
                        stringBuilder.append("内服中药方：");
                        for (CueWordURLMatRequest cueWordURLMatRequest : cueWordPres.getMatList()) {
                            stringBuilder.append(cueWordURLMatRequest.getMatName()).append(":").append(cueWordURLMatRequest.getMatDose()).append(cueWordURLMatRequest.getBzdw()).append(",");
                        }
                        if (StringUtils.isNotBlank(cueWordPres.getPreNum())) {
                            stringBuilder.append("贴数：").append(cueWordPres.getPreNum()).append("，");
                        }
                        if (StringUtils.isNotBlank(cueWordPres.getPreDescription())) {
                            stringBuilder.append("服法：").append(cueWordPres.getPreDescription());
                        }
                        if (StringUtils.isNotBlank(cueWordPres.getPreUsetimeDes())) {
                            stringBuilder.append("-").append(cueWordPres.getPreUsetimeDes());
                        }
                        if (StringUtils.isNotBlank(cueWordPres.getPreFrequency())) {
                            stringBuilder.append(",").append("频次：").append(cueWordPres.getPreFrequency());
                        }
                        if (StringUtils.isNotBlank(cueWordPres.getPreNMlName())) {
                            stringBuilder.append(",").append("每次：").append(cueWordPres.getPreNMlName());
                        }

                    }
                    if ("2".equals(cueWordPres.getPreType())) {
                        //外服中药方
                        stringBuilder.append("外服中药方：");
                        for (CueWordURLMatRequest cueWordURLMatRequest : cueWordPres.getMatList()) {
                            stringBuilder.append(cueWordURLMatRequest.getMatName()).append(":").append(cueWordURLMatRequest.getMatDose()).append(cueWordURLMatRequest.getBzdw()).append(",");
                        }
                        if (StringUtils.isNotBlank(cueWordPres.getPreNum())) {
                            stringBuilder.append("剂数：").append(cueWordPres.getPreNum()).append("，");
                        }
                        if (StringUtils.isNotBlank(cueWordPres.getPreDescription())) {
                            stringBuilder.append("外用方式：").append(cueWordPres.getPreDescription());
                        }
                        if (StringUtils.isNotBlank(cueWordPres.getPreFrequency())) {
                            stringBuilder.append(",").append("频次：").append(cueWordPres.getPreFrequency());
                        }
                        if (StringUtils.isNotBlank(cueWordPres.getPreSmokeInstrument())) {
                            //熏蒸仪器名称
                            stringBuilder.append("熏蒸仪器名称：").append(cueWordPres.getPreSmokeInstrument()).append("。");
                        } else {
                            stringBuilder.append("每袋：").append(cueWordPres.getPreNMlName());
                        }
                    }
                    if ("3".equals(cueWordPres.getPreType())) {
                        stringBuilder.append("中成药：");
                        for (CueWordURLMatRequest cueWordURLMatRequest : cueWordPres.getMatList()) {
                            stringBuilder.append(cueWordURLMatRequest.getMatName()).append(":").append(cueWordURLMatRequest.getMatDose()).append(cueWordURLMatRequest.getBzdw()).append(",");
                        }
                    }
                    if ("4".equals(cueWordPres.getPreType())) {
                        stringBuilder.append("适宜技术方：");
                        for (CueWordURLMatRequest cueWordURLMatRequest : cueWordPres.getMatList()) {
                            stringBuilder.append(cueWordURLMatRequest.getAcuName()).append(":").append(cueWordURLMatRequest.getAcuNum()).append("针").append(",");
                        }
                        stringBuilder.append("天数：").append(cueWordPres.getPreNum()).append(",").append("类型：").append(cueWordPres.getAcuType());
                        if (!StringUtils.isBlank(cueWordPres.getAcuProject())) {
                            stringBuilder.append("针刺种类：").append(cueWordPres.getAcuProject());
                        }
                    }
                    if ("5".equals(cueWordPres.getPreType())) {
                        stringBuilder.append("中药制剂:");
                        for (CueWordURLMatRequest cueWordURLMatRequest : cueWordPres.getMatList()) {
                            stringBuilder.append(cueWordURLMatRequest.getMatName()).append(":").append(cueWordURLMatRequest.getMatDose()).append(cueWordURLMatRequest.getBzdw()).append(",");
                        }
                    }
                }
                content = MessageFormat.format(content,
                        StringUtils.isNotBlank(objectById.getPatientName()) ? "姓名：" + objectById.getPatientName() + "，" : "",
                        StringUtils.isNotBlank(patientGender) ? "性别：" + (("M".equals(patientGender)) ? "男" : "女") + "，" : "",
                        (patientAge.getAge1()) != null ? "年龄：" + patientAge.getAge1() + patientAge.getAgeUnit1() + "，" : "",
                        (("M".equals(patientGender)) ? "" : ("Y".equals(gravidity) ? "妊娠：是" : "妊娠：否")),
                        StringUtils.isNotBlank(cueWordURLRequest.getDisName()) ? "疾病：" + cueWordURLRequest.getDisName() : "",
                        StringUtils.isNotBlank(cueWordURLRequest.getSymName()) ? "，证型：" + cueWordURLRequest.getSymName() : "",
                        StringUtils.isNotBlank(cueWordURLRequest.getTheName()) ? "，治法：" + cueWordURLRequest.getTheName() : ""
                        , zx, xbs, jws, zysz, tgjc, fzjc, grs, zlyj, gms, stringBuilder.toString()
                );

                return content;
            }
        }
        return "";
    }

    public String getCueWordURL(CueWordURLRequest cueWordURLRequest) {
        TRegister currentRegister = AdminUtils.getCurrentRegister();
        SysCueWord sysCueWord = new SysCueWord();
//        sysCueWord.setAppId(AdminUtils.getCurrentAppId());
//        sysCueWord.setInsCode(AdminUtils.getCurrentInsCode());
        sysCueWord.setCueWordType(cueWordURLRequest.getCueWordType());
        if (cueWordURLRequest.getCueWordPresList() != null && !cueWordURLRequest.getCueWordPresList().isEmpty()) {
            CueWordPres cueWordPres = cueWordURLRequest.getCueWordPresList().get(0);
            if (StringUtils.isNotBlank(cueWordPres.getPreOrigin())) {
                if ("diagnostic_base".equals(cueWordURLRequest.getCueWordType())) {
                    sysCueWord.setPreOrigin(null);
                } else {
                    sysCueWord.setPreOrigin(cueWordPres.getPreOrigin());
                }

            } else {
                sysCueWord.setPreOrigin(null);
            }
        } else {
            sysCueWord.setPreOrigin(null);
        }
        SysCueWord sysCueWordOne = sysCueWordMapper.getOne(sysCueWord);
        if (sysCueWordOne == null) {
            log.error("数据库未配置sysCueWord");
            return null;
        }
        String gpBaseTUrl = getGPBaseTUrl();
        if (currentRegister != null) {
            TPatients objectById = tPatientsMapper.getObjectById(currentRegister.getPatientId());
            if (null != objectById) {
                String patientName = objectById.getPatientName();
                if (StringUtils.isNotBlank(patientName)) {
                    gpBaseTUrl += "&username=" + patientName;
                }
                String patientGender = objectById.getPatientGender();
                if (StringUtils.isNotBlank(patientGender)) {
                    gpBaseTUrl += "&gender=" + (("M".equals(patientGender)) ? "男" : "女");
                }
                TPatientAge patientAge = TPatientsService.getAgeByBirth(objectById.getPatientBirthday());
                Short age1 = patientAge.getAge1();
                if (null != age1) {
                    gpBaseTUrl += "&age=" + age1;
                }
            }
        }

        gpBaseTUrl += "&query=" + sysCueWordOne.getCueWordText();
        gpBaseTUrl += "&displayQuery=" + sysCueWordOne.getDisplayText();
        gpBaseTUrl += "&mask=诊疗助手";

        //拼接content
        if ("symptom_analysis".equals(cueWordURLRequest.getCueWordType()) ||
                "record_analysis".equals(cueWordURLRequest.getCueWordType())) {
            String content = getRecordCueWordURL(cueWordURLRequest);
            gpBaseTUrl += "&context=" + content + (StringUtils.isNotBlank(sysCueWordOne.getCueTransText()) ? sysCueWordOne.getCueTransText() : "");
            return gpBaseTUrl;
        } else if ("AI_ELE_RECORD_QUA_CON".equals(cueWordURLRequest.getCueWordType())) {
            //获取content
            MedicalHistorySaveRequest request = cueWordURLRequest.getMedicalHistorySaveRequest();
            TRecord record = new TRecord();
            record.setPatientContent("");
            record.setNowDesc("");
            record.setPastDesc("");
            record.setFamilyDesc("");
            record.setAllergyDesc("");
            record.setTongue("");
            record.setPulse("");
            record.setFourDiagnosis("");
            record.setPhysical("");
            record.setAuxiliaryExam("");
            record.setTreatmentAdvice("");

            record.setVersion(request.getVersion());
            record.setDisId(request.getDisId());
            record.setDisName(request.getDisName());
            record.setWesternDisease(request.getWesternDisease());
            record.setWesternDiseaseId(request.getWesternDiseaseId());
            record.setSymId(request.getSymId());
            record.setSymName(request.getSymName());
            record.setTheNames(request.getTheNames());
            record.setPatientHeight(request.getPatientHeight());
            record.setPatientWeight(request.getPatientWeight());
            record.setTheCode(request.getTheCode());
            tMedicalRecordSourceService.joiningTogetherFourDiagnosis(request, record);
            TPatients objectById = tPatientsMapper.getObjectById(currentRegister.getPatientId());
            Date patientBirthday = objectById.getPatientBirthday();
            TPatientAge patientAge = null;
            if (patientBirthday != null){
                 patientAge = TPatientsService.getAgeByBirth(patientBirthday);
            }

            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append("患者姓名：").append(objectById.getPatientName())
                    .append("性别：").append((("M".equals(objectById.getPatientGender())) ? "男" : "女"))
                    //通过生日获取年龄
                    .append( (patientAge != null) ?"年龄："+patientAge.getAge():"" )
//                    .append("年龄：").append(patientAge.getAge())
                    .append(  (("M".equals(objectById.getPatientGender())) ? "" : ("Y".equals(record.getGravidity()) ? "妊娠：是" : "妊娠：否")) )
                    .append( ( StringUtils.isNotBlank(record.getWesternDisease()))?"西医诊断：" + record.getWesternDisease():""  )
                    .append(  ( StringUtils.isNotBlank(record.getDisName()))?"中医疾病：" + record.getDisName():"")
                    .append(  ( StringUtils.isNotBlank(record.getSymName()))?"证型：" + record.getSymName():"")
                    .append(  ( StringUtils.isNotBlank(record.getTheNames()))?"治法：" + record.getTheNames():"")
                    .append(  ( StringUtils.isNotBlank(record.getPatientContent()))?"主诉：" + record.getPatientContent():"")
                    .append(  ( StringUtils.isNotBlank(record.getNowDesc()))?"现病史：" + record.getNowDesc():"")
                    .append(  ( StringUtils.isNotBlank(record.getPastDesc()))?"既往史：" + record.getPastDesc():"")
                    .append(  ( StringUtils.isNotBlank(record.getFamilyDesc()))?"家族史：" + record.getFamilyDesc():"")
                    .append(  ( StringUtils.isNotBlank(record.getAllergyDesc()))?"过敏史：" + record.getAllergyDesc():"")
                    .append(  ( StringUtils.isNotBlank(record.getPhysical()))?"体格检查：" + record.getPhysical():"")
                    .append(  ( StringUtils.isNotBlank(record.getAuxiliaryExam()))?"辅助检查：" + record.getAuxiliaryExam():"")
                    .append(  ( StringUtils.isNotBlank(record.getTreatmentAdvice()))?"治疗意见：" + record.getTreatmentAdvice():"")
                    .append(  ( StringUtils.isNotBlank(record.getFourDiagnosis()))?"中医四诊：" + record.getFourDiagnosis():"");
            gpBaseTUrl += "&context=" + stringBuilder.toString() + (StringUtils.isNotBlank(sysCueWordOne.getCueTransText()) ? sysCueWordOne.getCueTransText() : "") + "&think=false";
            return gpBaseTUrl;
        } else {
            String content = getContent(cueWordURLRequest);
            gpBaseTUrl += "&context=" + content + (StringUtils.isNotBlank(sysCueWordOne.getCueTransText()) ? sysCueWordOne.getCueTransText() : "");
            return gpBaseTUrl;
        }

    }

    public String getGPBaseTUrl() {
        try {
            HashMap<String, Object> map = new HashMap<>();
            AdminInfo currentHr = AdminUtils.getCurrentHr();
            map.put("appId", AdminUtils.getCurrentAppId());
            map.put("insCode", AdminUtils.getCurrentInsCode());
            map.put("deptId", AdminUtils.getCurrentDeptId());
            map.put("userName", currentHr.getUsername());
            map.put("origin", "tcm");
            map.put("userId", currentHr.getId());
            String json = JSON.toJSONString(map);
            log.info("======获取GPT请求URL的cbdata:{}", json);
            String s = AESPKCS7Util.encrypt(json, urlGptKey, "base64");
            String encode = URLEncoder.encode(s, "UTF-8");
//            String url = "http://healthcare.tcmbrain.com:10114/agent#/partner?cbdata={cbdata}";
            return urlGpt.replace("{cbdata}", encode);
        } catch (Exception e) {
            log.error("getGPTUrl error", e);
        }

        return urlGpt;
    }



    /**
     * 电子病历-ai病历解析
     *
     * @param cueWordURLRequest
     * @return
     */
    public String getRecordCueWordURL(CueWordURLRequest cueWordURLRequest) {
        SysCueWord sysCueWord = new SysCueWord();
        sysCueWord.setCueWordType("record_structure");
        SysCueWord sysCueWordOne = sysCueWordMapper.getOne(sysCueWord);
        String content = sysCueWordOne.getCueWordText();

        if ("record_analysis".equals(cueWordURLRequest.getCueWordType())) {
            TRecord tRecord = new TRecord();
            tRecord.setRecId(cueWordURLRequest.getRecId());
            List<TRecordRespVO> recRespVOS = tRecordMapper.getRecordByRecord(tRecord);
            if (null == recRespVOS || recRespVOS.size() != 1) {
                return null;
            }
            tRecordService.getRecS(recRespVOS, null, null, null, null);
            //处理单个病历

            TRecordRespVO tRecordRespVO = recRespVOS.get(0);

            List<TPreRespVO> preList = tRecordRespVO.getPreList();
            String patientContent = StringUtils.isBlank(tRecordRespVO.getPatientContent()) ? "" : "主诉" + tRecordRespVO.getPatientContent();
            String nowDesc = StringUtils.isBlank(tRecordRespVO.getNowDesc()) ? "" : "现病史" + tRecordRespVO.getNowDesc();
            String pastDesc = StringUtils.isBlank(tRecordRespVO.getPastDesc()) ? "" : "既往史" + tRecordRespVO.getPastDesc();
            String fourDiagnosis = StringUtils.isBlank(tRecordRespVO.getFourDiagnosis()) ? "" : "中医四诊" + tRecordRespVO.getFourDiagnosis();
            String physical = StringUtils.isBlank(tRecordRespVO.getPhysical()) ? "" : "体格检查" + tRecordRespVO.getPhysical();
            String auxiliaryExam = StringUtils.isBlank(tRecordRespVO.getAuxiliaryExam()) ? "" : "辅助检查" + tRecordRespVO.getAuxiliaryExam();
            String treatmentAdvice = StringUtils.isBlank(tRecordRespVO.getTreatmentAdvice()) ? "" : "治疗意见" + tRecordRespVO.getTreatmentAdvice();
            String allergyDesc = StringUtils.isBlank(tRecordRespVO.getAllergyDesc()) ? "" : "过敏史" + tRecordRespVO.getAllergyDesc();
            String personalDesc = StringUtils.isBlank(tRecordRespVO.getPersonalDesc()) ? "" : "个人史" + tRecordRespVO.getPersonalDesc();
//            int flag = 0;
            //判断上述字段如果都为空，那么就不拼接了
//            if (StringUtils.isBlank(patientContent) && StringUtils.isBlank(nowDesc) && StringUtils.isBlank(pastDesc) && StringUtils.isBlank(fourDiagnosis) && StringUtils.isBlank(physical) && StringUtils.isBlank(auxiliaryExam) && StringUtils.isBlank(treatmentAdvice) && StringUtils.isBlank(allergyDesc)) {
//                flag = 1;
//            }
            StringBuilder stringBuilder = getTRecord(preList);
            content = MessageFormat.format(content,
                    StringUtils.isNotBlank(tRecordRespVO.getRecName()) ? "姓名：" + tRecordRespVO.getRecName() + "，" : "",
                    StringUtils.isNotBlank(tRecordRespVO.getRecGender()) ? "性别：" + (("M".equals(tRecordRespVO.getRecGender())) ? "男" : "女") + "，" : "",
                    (tRecordRespVO.getRecAge1()) != null ? "年龄：" + tRecordRespVO.getRecAge1() + tRecordRespVO.getRecAgeunit1() + "，" : "",
                    (("M".equals(tRecordRespVO.getRecGender())) ? "" : ("Y".equals(tRecordRespVO.getGravidity()) ? "妊娠：是" : "妊娠：否")),
                    StringUtils.isNotBlank(tRecordRespVO.getDisName()) ? "疾病：" + tRecordRespVO.getDisName() : "",
                    StringUtils.isNotBlank(tRecordRespVO.getSymName()) ? "，证型：" + tRecordRespVO.getSymName() : "",
                    StringUtils.isNotBlank(tRecordRespVO.getTheNames()) ? "，治法：" + tRecordRespVO.getTheNames() : "",
                    patientContent, nowDesc, pastDesc, fourDiagnosis, physical, auxiliaryExam, treatmentAdvice, allergyDesc,
                    personalDesc,
                    stringBuilder.toString()
            );
            return content;
        } else if ("symptom_analysis".equals(cueWordURLRequest.getCueWordType())) {
//            String insCode = "10002011";
            String insCode = AdminUtils.getCurrentHr().getInsCode();
            String patientId = AdminUtils.getCurrentRegister().getPatientId();
//            String patientId = "31f43ba19e9d4140a50614ab0b42a9a5";
            String muban = "患者信息： {0}{1}{2}。病历及处置办法信息：{3}";
            PageHelper.startPage(1, 10);
            List<TRecordAll> recordAll = tRecordMapper.getRecordByPatIdAndAdm(patientId, null, insCode);
            PageHelper.clearPage();
            if (null != recordAll) {
                StringBuilder stringBuilder = new StringBuilder();
                TPatients objectById = tPatientsMapper.getObjectById(patientId);
                TPatientAge patientAge = TPatientsService.getAgeByBirth(objectById.getPatientBirthday());


                int tempNum = 0;
                for (int i = 0; i < recordAll.size(); i++) {
                    tempNum++;
                    if (tempNum > 10) {
                        break;
                    }
                    TRecordAll tRecordAll = recordAll.get(i);
                    ResEntity recordByToken = medicalRecordService.getRecordByToken(tRecordAll.getRegisterId());
                    if (recordByToken.getStatus()) {
                        List<TRecordVo> record = (List<TRecordVo>) recordByToken.getData();
                        StringBuilder tRecordTRecordVo = getTRecordTRecordVo(record);
                        stringBuilder.append(tRecordTRecordVo);
                    }
                }
                muban = MessageFormat.format(muban,
                        StringUtils.isNotBlank(objectById.getPatientName()) ? "姓名：" + objectById.getPatientName() + "，" : "",
                        StringUtils.isNotBlank(objectById.getPatientGender()) ? "性别：" + (("M".equals(objectById.getPatientGender())) ? "男" : "女") + "，" : "",
                        ((patientAge.getAge1()) != null ? "年龄：" + patientAge.getAge1() + patientAge.getAgeUnit1() + "，" : ""),
//                        (("M".equals(objectById.getPatientGender())) ? "" : ("Y".equals(objectById.getgravidity()) ? "妊娠：是" : "妊娠：否")),
                        stringBuilder.toString()
                );
                //处理大病历
                return muban;
            }
        }
        return null;

    }


    public StringBuilder getTRecordTRecordVo(List<TRecordVo> preList) {
        StringBuilder stringBuilder = new StringBuilder();


        for (int i = 0; i < preList.size(); i++) {
            TRecordVo tPreRespVO = preList.get(i);
            //先取疾病证型治法信息
            String disName = tPreRespVO.getDisName();
            String symName = tPreRespVO.getSymName();
            String theNames = tPreRespVO.getTheNames();


            String dth = (
                    (StringUtils.isNotBlank(disName) ? "疾病：" + disName : "") +
                            (StringUtils.isNotBlank(symName) ? "，证型：" + symName : "") +
                            (StringUtils.isNotBlank(theNames) ? "，治法：" + theNames : "")
            );
            stringBuilder.append(dth);
            String tempMB = "病历信息：{0}{1}{2}{3}{4}{5}{6}{7}{8}";
            stringBuilder.append(MessageFormat.format(tempMB,
                    StringUtils.isNotBlank(tPreRespVO.getPatientContent()) ? "主诉：" + tPreRespVO.getPatientContent() : "",
                    StringUtils.isNotBlank(tPreRespVO.getNowDesc()) ? "现病史：" + tPreRespVO.getNowDesc() : "",
                    StringUtils.isNotBlank(tPreRespVO.getPastDesc()) ? "既往史：" + tPreRespVO.getPastDesc() : "",
                    StringUtils.isNotBlank(tPreRespVO.getFourDiagnosis()) ? "四诊：" + tPreRespVO.getFourDiagnosis() : "",
                    StringUtils.isNotBlank(tPreRespVO.getPhysical()) ? "体格检查：" + tPreRespVO.getPhysical() : "",
                    StringUtils.isNotBlank(tPreRespVO.getAuxiliaryExam()) ? "辅助检查：" + tPreRespVO.getAuxiliaryExam() : "",
                    StringUtils.isNotBlank(tPreRespVO.getTreatmentAdvice()) ? "治疗意见：" + tPreRespVO.getTreatmentAdvice() : "",
                    StringUtils.isNotBlank(tPreRespVO.getAllergyDesc()) ? "过敏史：" + tPreRespVO.getAllergyDesc() : "",
                    StringUtils.isNotBlank(tPreRespVO.getPersonalDesc()) ? "个人史：" + tPreRespVO.getPersonalDesc() : ""
            ));
            List<PrescriptionMin> prescriptionMinList = tPreRespVO.getPrescriptionMinList();
            prescriptionMinList.forEach(a -> {
                if ("内服中药方".equals(a.getPreType())) {
                    //内服中药方
                    stringBuilder.append("内服中药方：");
                    for (PrescriptionItemMin cueWordURLMatRequest : a.getPrescriptionItemMinList()) {
                        stringBuilder.append(cueWordURLMatRequest.getMatName()).append(":").append(cueWordURLMatRequest.getMatDose()).append(cueWordURLMatRequest.getBzdwHis()).append(",");
                    }
                    if (StringUtils.isNotBlank(String.valueOf(a.getPreNum()))) {
                        stringBuilder.append("贴数：").append(a.getPreNum()).append("，");
                    }
                    if (StringUtils.isNotBlank(a.getPreDescription())) {
                        stringBuilder.append("服法：").append(a.getPreDescription());
                    }
                    if (StringUtils.isNotBlank(a.getPreUseTimeDes())) {
                        stringBuilder.append("-").append(a.getPreUseTimeDes());
                    }
                    if (StringUtils.isNotBlank(a.getPreFrequency())) {
                        stringBuilder.append(",").append("频次：").append(a.getPreFrequency());
                    }
                    if (StringUtils.isNotBlank(a.getPreNMlName())) {
                        stringBuilder.append(",").append("每次：").append(a.getPreNMlName());
                    }

                }
                if ("外用中药方".equals(a.getPreType())) {
                    //外服中药方
                    stringBuilder.append("外服中药方：");
                    for (PrescriptionItemMin cueWordURLMatRequest : a.getPrescriptionItemMinList()) {
                        stringBuilder.append(cueWordURLMatRequest.getMatName()).append(":").append(cueWordURLMatRequest.getMatDose()).append(cueWordURLMatRequest.getBzdwHis()).append(",");
                    }
                    if (StringUtils.isNotBlank(String.valueOf(a.getPreNum()))) {
                        stringBuilder.append("剂数：").append(a.getPreNum()).append("，");
                    }
                    if (StringUtils.isNotBlank(a.getPreDescription())) {
                        stringBuilder.append("外用方式：").append(a.getPreDescription());
                    }
                    if (StringUtils.isNotBlank(a.getPreFrequency())) {
                        stringBuilder.append(",").append("频次：").append(a.getPreFrequency());
                    }
                    if (StringUtils.isNotBlank(a.getPreSmokeInstrument())) {
                        //熏蒸仪器名称
                        stringBuilder.append("熏蒸仪器名称：").append(a.getPreSmokeInstrument()).append("。");
                    } else {
                        stringBuilder.append("每袋：").append(a.getPreNMlName());
                    }
                }
                if ("中成药".equals(a.getPreType())) {
                    stringBuilder.append("中成药：");
                    for (PrescriptionItemMin cueWordURLMatRequest : a.getPrescriptionItemMinList()) {
                        stringBuilder.append(cueWordURLMatRequest.getMatName()).append(":").append(cueWordURLMatRequest.getMatDose()).append(cueWordURLMatRequest.getBzdwHis()).append(",");
                    }
                }
                if ("适宜技术方".equals(a.getPreType())) {
                    stringBuilder.append("适宜技术方：");
                    for (TPrescriptionAcuItem cueWordURLMatRequest : a.getPrescriptionAcuItemList()) {
                        stringBuilder.append(cueWordURLMatRequest.getAcuName()).append(":").append(cueWordURLMatRequest.getAcuNum()).append("针").append(",");
                    }
                    stringBuilder.append("天数：").append(a.getPreNum()).append(",").append("类型：").append(a.getAcuType());
                    if (!StringUtils.isBlank(a.getAcuProject())) {
                        stringBuilder.append("针刺种类：").append(a.getAcuProject());
                    }
                }
                if ("中药制剂".equals(a.getPreType())) {
                    stringBuilder.append("中药制剂:");
                    for (TPrescriptionPreparationItem cueWordURLMatRequest : a.getPreparationItemList()) {
                        stringBuilder.append(cueWordURLMatRequest.getYpmcCenter()).append(":").append(cueWordURLMatRequest.getMatDose()).append(cueWordURLMatRequest.getBzdwHis()).append(",");
                    }
                }
            });


        }
        return stringBuilder;
    }

    public StringBuilder getTRecord(List<TPreRespVO> preList) {
        StringBuilder stringBuilder = new StringBuilder();
        for (int i = 0; i < preList.size(); i++) {
            TPreRespVO tPreRespVO = preList.get(i);
            if ("1".equals(tPreRespVO.getPreType())) {
                //内服中药方
                stringBuilder.append("内服中药方：");
                for (TPrescriptionItem cueWordURLMatRequest : tPreRespVO.getItemList()) {
                    stringBuilder.append(cueWordURLMatRequest.getMatName()).append(":").append(cueWordURLMatRequest.getMatDose()).append(cueWordURLMatRequest.getBzdwHis()).append(",");
                }
                if (StringUtils.isNotBlank(String.valueOf(tPreRespVO.getPreNum()))) {
                    stringBuilder.append("贴数：").append(tPreRespVO.getPreNum()).append("，");
                }
                if (StringUtils.isNotBlank(tPreRespVO.getPreDescription())) {
                    stringBuilder.append("服法：").append(tPreRespVO.getPreDescription());
                }
                if (StringUtils.isNotBlank(tPreRespVO.getPreUsetimeDes())) {
                    stringBuilder.append("-").append(tPreRespVO.getPreUsetimeDes());
                }
                if (StringUtils.isNotBlank(tPreRespVO.getPreFrequency())) {
                    stringBuilder.append(",").append("频次：").append(tPreRespVO.getPreFrequency());
                }
                if (StringUtils.isNotBlank(tPreRespVO.getPreNMlName())) {
                    stringBuilder.append(",").append("每次：").append(tPreRespVO.getPreNMlName());
                }

            }
            if ("2".equals(tPreRespVO.getPreType())) {
                //外服中药方
                stringBuilder.append("外服中药方：");
                for (TPrescriptionItem cueWordURLMatRequest : tPreRespVO.getItemList()) {
                    stringBuilder.append(cueWordURLMatRequest.getMatName()).append(":").append(cueWordURLMatRequest.getMatDose()).append(cueWordURLMatRequest.getBzdwHis()).append(",");
                }
                if (StringUtils.isNotBlank(String.valueOf(tPreRespVO.getPreNum()))) {
                    stringBuilder.append("剂数：").append(tPreRespVO.getPreNum()).append("，");
                }
                if (StringUtils.isNotBlank(tPreRespVO.getPreDescription())) {
                    stringBuilder.append("外用方式：").append(tPreRespVO.getPreDescription());
                }
                if (StringUtils.isNotBlank(tPreRespVO.getPreFrequency())) {
                    stringBuilder.append(",").append("频次：").append(tPreRespVO.getPreFrequency());
                }
                if (StringUtils.isNotBlank(tPreRespVO.getPreSmokeInstrument())) {
                    //熏蒸仪器名称
                    stringBuilder.append("熏蒸仪器名称：").append(tPreRespVO.getPreSmokeInstrument()).append("。");
                } else {
                    stringBuilder.append("每袋：").append(tPreRespVO.getPreNMlName());
                }
            }
            if ("3".equals(tPreRespVO.getPreType())) {
                stringBuilder.append("中成药：");
                for (TPrescriptionItem cueWordURLMatRequest : tPreRespVO.getItemList()) {
                    stringBuilder.append(cueWordURLMatRequest.getMatName()).append(":").append(cueWordURLMatRequest.getMatDose()).append(cueWordURLMatRequest.getBzdwHis()).append(",");
                }
            }
            if ("4".equals(tPreRespVO.getPreType())) {
                stringBuilder.append("适宜技术方：");
                for (TPrescriptionAcuItem cueWordURLMatRequest : tPreRespVO.getAcuItemList()) {
                    stringBuilder.append(cueWordURLMatRequest.getAcuName()).append(":").append(cueWordURLMatRequest.getAcuNum()).append("针").append(",");
                }
                stringBuilder.append("天数：").append(tPreRespVO.getPreNum()).append(",").append("类型：").append(tPreRespVO.getAcuType());
                if (!StringUtils.isBlank(tPreRespVO.getAcuProject())) {
                    stringBuilder.append("针刺种类：").append(tPreRespVO.getAcuProject());
                }
            }
            if ("5".equals(tPreRespVO.getPreType())) {
                stringBuilder.append("中药制剂:");
                for (TPrescriptionPreparationItem cueWordURLMatRequest : tPreRespVO.getPreparationItemList()) {
                    stringBuilder.append(cueWordURLMatRequest.getYpmcCenter()).append(":").append(cueWordURLMatRequest.getMatDose()).append(cueWordURLMatRequest.getBzdwHis()).append(",");
                }
            }
        }
        return stringBuilder;
    }



    public static void main(String[] args) {
        String a = "你好{0}，{1}";
        System.out.println(MessageFormat.format(a, "1", "2"));
    }

}
