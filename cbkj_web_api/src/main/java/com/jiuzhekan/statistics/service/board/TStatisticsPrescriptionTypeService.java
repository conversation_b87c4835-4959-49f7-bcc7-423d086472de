package com.jiuzhekan.statistics.service.board;

import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.github.pagehelper.PageHelper;
import com.jiuzhekan.statistics.beans.board.TStatisticsPrescriptionMedication;
import com.jiuzhekan.statistics.beans.board.TStatisticsPrescriptionType;
import com.jiuzhekan.statistics.beans.prescription.StatisticsPrescription;
import com.jiuzhekan.statistics.beans.register.QueryParam;
import com.jiuzhekan.statistics.mapper.board.TStatisticsPrescriptionTypeMapper;
import com.jiuzhekan.statistics.utils.DateUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
public class TStatisticsPrescriptionTypeService {

    @Autowired
    private TStatisticsPrescriptionTypeMapper tStatisticsPrescriptionTypeMapper;

    /**
     * 加载分页数据
     *
     * @param tStatisticsPrescriptionType 统计处方状态
     * @param page                        分页
     * @return Object
     * <AUTHOR>
     * @date 2022-10-09
     */
    public Object getPageDatas(TStatisticsPrescriptionType tStatisticsPrescriptionType, Page page) {

        PageHelper.startPage(page.getPage(), page.getLimit());
        List<TStatisticsPrescriptionType> list = tStatisticsPrescriptionTypeMapper.getPageListByObj(tStatisticsPrescriptionType);
        return Page.getLayUiTablePageData(list);
    }

    /**
     * 加载某条数据
     *
     * @param id 统计处方状态
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-10-09
     */
    public ResEntity findObj(String id) {

        if (StringUtils.isBlank(id)) {
            return ResEntity.entity(false, "参数不能为空哦", null);
        }
        TStatisticsPrescriptionType tStatisticsPrescriptionType = tStatisticsPrescriptionTypeMapper.getObjectById(id);
        return ResEntity.entity(true, Constant.SUCCESS_DX, tStatisticsPrescriptionType);
    }


    /**
     * 插入新数据
     *
     * @param tStatisticsPrescriptionType 统计处方状态
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-10-09
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insert(TStatisticsPrescriptionType tStatisticsPrescriptionType) {

        long rows = tStatisticsPrescriptionTypeMapper.insert(tStatisticsPrescriptionType);

        return ResEntity.success(tStatisticsPrescriptionType);
    }


    /**
     * 修改
     *
     * @param tStatisticsPrescriptionType 统计处方状态
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-10-09
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity update(TStatisticsPrescriptionType tStatisticsPrescriptionType) {

        long rows = tStatisticsPrescriptionTypeMapper.updateByPrimaryKey(tStatisticsPrescriptionType);

        return ResEntity.success(tStatisticsPrescriptionType);
    }

    /**
     * 删除
     *
     * @param ids ids
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-10-09
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity deleteLis(String ids) {
        if (StringUtils.isBlank(ids)) {
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }
        long rowsR = tStatisticsPrescriptionTypeMapper.deleteBylist(ids.split(","));

        return ResEntity.success(rowsR);
    }

    public TStatisticsPrescriptionType insertQuery(TStatisticsPrescriptionType tStatisticsPrescriptionType) {
        if (null == tStatisticsPrescriptionType || null == tStatisticsPrescriptionType.getNumType()) {
            return null;
        }
        long count = tStatisticsPrescriptionTypeMapper.getCountByObj(tStatisticsPrescriptionType);
        if (count > 0) {
            //存在，修改
            return tStatisticsPrescriptionTypeMapper.getOneByObject(tStatisticsPrescriptionType);
        } else {
            //不存在，修改。
            return tStatisticsPrescriptionType.initDate(tStatisticsPrescriptionType);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public ResEntity insertOrUpdate(TStatisticsPrescriptionType tStatisticsPrescriptionType) {
        if (null == tStatisticsPrescriptionType.getId()) {
            int insert = tStatisticsPrescriptionTypeMapper.insert(tStatisticsPrescriptionType);
        } else {
            int i = tStatisticsPrescriptionTypeMapper.updateByPrimaryKey(tStatisticsPrescriptionType);
        }
        return ResEntity.success(tStatisticsPrescriptionType);
    }

    /**
     * 统计煎煮中和已签收处方
     */
    @Transactional(rollbackFor = Exception.class)
    public void settStatisticsPrescriptionType(int diff) {
        String curDate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
        Map<String, Object> param = new HashMap<>(16);
        SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd");
        param.put("curDate",curDate );
        param.put("diff",diff );
        //煎煮中处方:85泡药、130煎药、135包装
        List<TStatisticsPrescriptionType> result = tStatisticsPrescriptionTypeMapper.getStaticsResult(param);
        //已签收处方:150收货
        List<TStatisticsPrescriptionType> result2 = tStatisticsPrescriptionTypeMapper.getStaticsResult2(param);
        List<TStatisticsPrescriptionType> insertRe = new ArrayList<TStatisticsPrescriptionType>();
        List<TStatisticsPrescriptionType> updateRe = new ArrayList<TStatisticsPrescriptionType>();
        result.forEach(tStatisticsPrescriptionType ->
                {
                    if (null != tStatisticsPrescriptionType) {
                        if (null != tStatisticsPrescriptionType.getId()){
                            updateRe.add(tStatisticsPrescriptionType);
                        }else {
                            tStatisticsPrescriptionType.setInsertDate(new Date());
                            try {
                                tStatisticsPrescriptionType.setCreateDate(sdf2.parse(curDate));
                            } catch (ParseException e) {
                                throw new RuntimeException(e);
                            }
                            insertRe.add(tStatisticsPrescriptionType);
                        }
                    }
                }
        );
        result2.forEach(tStatisticsPrescriptionType ->
                {
                    if (null != tStatisticsPrescriptionType) {
                        if (null != tStatisticsPrescriptionType.getId()){
                            updateRe.add(tStatisticsPrescriptionType);
                        }else {
                            tStatisticsPrescriptionType.setInsertDate(new Date());
                            try {
                                tStatisticsPrescriptionType.setCreateDate(sdf2.parse(DateUtils.getWeekEndTim2e(0, true)));
                            } catch (ParseException e) {
                                throw new RuntimeException(e);
                            }
                            insertRe.add(tStatisticsPrescriptionType);
                        }
                    }
                }
        );
        if (insertRe.size()>0){
            tStatisticsPrescriptionTypeMapper.insertList(insertRe);
        }
        for (TStatisticsPrescriptionType tStatisticsPrescriptionType : updateRe) {
            tStatisticsPrescriptionTypeMapper.updateByPrimaryKey(tStatisticsPrescriptionType);
        }
    }

    /**
     * 获取数据
     * @param queryParam
     * @return
     */
    public int getData(QueryParam queryParam){
        Integer i = tStatisticsPrescriptionTypeMapper.sumStatistics(queryParam);
        return null == i ? 0 : i;
    }
}
