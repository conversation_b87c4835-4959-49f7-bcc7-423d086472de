package com.jiuzhekan.cbkj.service.business;

import com.jiuzhekan.cbkj.beans.business.TSysParam;
import com.jiuzhekan.cbkj.beans.business.his.THisRecord;
import com.jiuzhekan.cbkj.beans.business.record.TRegister;
import com.jiuzhekan.cbkj.beans.business.sysCode.TSysCode;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.mapper.business.record.TRegisterMapper;
import com.jiuzhekan.cbkj.service.redis.ClientRedisService;
import com.jiuzhekan.cbkj.service.redis.ParameterRedisService;
import com.jiuzhekan.cbkj.service.statistics.TStatisticsService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class TSysParamService {

    @Autowired
    private ClientRedisService clientRedisService;
    @Autowired
    private ParameterRedisService parameterRedisService;
    @Autowired
    private TStatisticsService tStatisticsService;
    @Autowired
    private TRegisterMapper registerMapper;


    /**
     * 根据当前登录人获取所有参数详情
     *
     * @return com.jiuzhekan.cbkj.beans.sysBeans.ResEntity
     * <AUTHOR>
     * @date 2024/12/26
     */
    public ResEntity getPageDatas(String registerId) {
        //目前仅涉及到参数字典 后续涉及参数过多可以直接调用getAllParams方法
        Map<String, Object> result = parameterRedisService.getAllParams(AdminUtils.getCurrentAppId(), AdminUtils.getCurrentInsCode(), AdminUtils.getCurrentDeptId(), AdminUtils.getCurrentHr().getId());
        HashMap <String, Object> map = new HashMap<>();

        TRegister register = registerMapper.getRegisterByRegId(registerId);
        if(null != register){
            if(Integer.valueOf(Constant.BASIC_STRING_TWO).equals(register.getClinicTypeId())){
                //出院带药是否开启是的话返回1 为空或未配置或未开启均返回0
                String dischargeMedication = StringUtils.isNotEmpty((String) result.get(Constant.DISCHARGE_MEDICATION)) ?( Constant.BASIC_STRING_ONE.equals(result.get(Constant.DISCHARGE_MEDICATION)) ? Constant.BASIC_STRING_ONE : Constant.BASIC_STRING_ZERO):Constant.BASIC_STRING_ZERO;
                map.put(Constant.DISCHARGE_MEDICATION, dischargeMedication);
            }else{
                map.put(Constant.DISCHARGE_MEDICATION, Constant.BASIC_STRING_ZERO);
            }
        }

        return new ResEntity(true, Constant.SUCCESS_DX, map);
    }


    /**
     * 根据当前登录人获取所有参数详情
     *
     * @return com.jiuzhekan.cbkj.beans.sysBeans.ResEntity
     * <AUTHOR>
     * @date 2020/3/2
     */
    public ResEntity getAllParams(String appId, String insCode, String deptId, String userId) {

        //所有参数
        Map<String, Object> result = parameterRedisService.getAllParams(appId, insCode, deptId, userId);

        //医生设置
        result.put("USER_HABITS", clientRedisService.getAdminInfoEx(userId));

        //当前医生今天的月均贴金额
        result.put("MY_MONTHLY_AVG_AMOUNT", tStatisticsService.getDoctorMonthlyAvgAmount());

        //公用代码
        result.put("COMMON_CODE", getAllCodeParams(appId, insCode, deptId));

        return new ResEntity(true, Constant.SUCCESS_DX, result);
    }

    /**
     * 获取公用代码
     *
     * @param appId   appId
     * @param insCode insCode
     * @return java.util.Map<java.lang.String, java.lang.Object>
     * <AUTHOR>
     * @date 2021/7/23
     */
    private Map<String, Object> getAllCodeParams(String appId, String insCode, String deptId) {

        Map<String, Object> result = new HashMap<>();

        Map<String, TSysCode> allCode = parameterRedisService.getCodeMap(appId, insCode, deptId);

        allCode.forEach((dicCode, dicItem) -> {

            if (Constant.CODE_PRE_INTERNAL_DESCRIPTION.equals(dicCode)) {
                result.put("PRESCRIPTION_INTERNAL_USAGE", dicItem);
            } else if (Constant.CODE_PRE_INTERNAL_FREQUENCY.equals(dicCode)) {
                result.put("PRESCRIPTION_INTERNAL_RATE", dicItem);
            } else if (Constant.CODE_PRE_INTERNAL_USETIME.equals(dicCode)) {
                result.put("PRESCRIPTION_INTERNAL_TIME", dicItem);
            } else if (Constant.CODE_PRE_INTERNAL_ML.equals(dicCode)) {
                result.put("PRESCRIPTION_INTERNAL_ML", dicItem);
            } else if (Constant.CODE_PRE_EXTERNAL_ML.equals(dicCode)) {
                result.put("PRESCRIPTION_EXTERNAL_ML", dicItem);
            } else if (Constant.CODE_PRE_EXTERNAL_TYPE.equals(dicCode)) {
                result.put("PRESCRIPTION_EXTERNAL_MODE", dicItem);
            } else if (Constant.CODE_PRE_EXTERNAL_INSTRUMENT.equals(dicCode)) {
                result.put("PRESCRIPTION_EXTERNAL_OBJECT", dicItem);
            } else if (Constant.CODE_PRE_EXTERNAL_FREQUENCY.equals(dicCode)) {
                result.put("PRESCRIPTION_EXTERNAL_RATE", dicItem);
            } else if (Constant.CODE_PRE_ACU_TYPE.equals(dicCode)) {
                result.put("PRESCRIPTION_ACUPOINT_MODE", dicItem);
            } else if (Constant.CODE_PRE_ACU_PROJECT.equals(dicCode)) {
                result.put("PRESCRIPTION_ACUPOINT_OBJECT", dicItem);
            } else if (Constant.CODE_PREPARATION_INTERNAL_PRODUCTION_TYPE.equals(dicCode)) {
                result.put("PRESCRIPTION_INTERNAL_PRODUCTION_TYPE", dicItem);
            } else if (Constant.PRESCRIPTION_TYPES.equals(dicCode)) {
                result.put("PRESCRIPTION_TYPES", dicItem);
            } else {
                result.put(dicCode, dicItem);
            }
        });


        return result;
    }

    /**
     * 根据 code 获取系统参数表
     *
     * @param code
     * @return
     */
    public TSysParam getSysParam(String code) {
        if (StringUtils.isBlank(AdminUtils.getCurrentAppId())){
            return new TSysParam(code);
        }
        return parameterRedisService.getSysParam(AdminUtils.getCurrentAppId(), AdminUtils.getCurrentInsCode(), AdminUtils.getCurrentDeptId(), code);
    }

    public TSysParam getSysParam(String appId, String insCode,String deptId, String code) {

        return parameterRedisService.getSysParam(appId, insCode, deptId, code);
    }


    /**
     * 是否开启医保支付条件限制
     *
     * @return boolean
     * <AUTHOR>
     * @date 2021/9/2
     */
    public boolean insuranceLimitObject() {

        //医保提醒患者范围控制 1 门诊医保、2 门诊自费、3 住院医保、4 住院自费、5 门诊特病
        TSysParam param = getSysParam(Constant.INSURANCE_LIMIT_OBJECT);

        //空 不限制（所有人不提醒）
        if (StringUtils.isBlank(param.getParValues())) {
            return false;
        }
        String[] split = param.getParValues().split(",");
        TRegister register = AdminUtils.getCurrentRegister();
        if (null == register){
            return false;
        }
        //his传来过的信息
        THisRecord hisRecord = register.getHisRecord();
        if (null == hisRecord){
            return false;
        }
        for (String value : split) {
            //1）限制门诊医保
            if (Constant.BASIC_STRING_ONE.equals(value)) {
                if (//是否自费（0否 1是）
                        !Constant.BASIC_STRING_ONE.equals(hisRecord.getIsOwnExp()) &&
                                //门诊类型（1门诊 2住院）
                                register.getClinicTypeId() == 1) {
                    return true;
                }
            }
            //2）限制门诊自费
            else if (Constant.BASIC_STRING_TWO.equals(value)) {
                if (//是否自费（0否 1是）
                        Constant.BASIC_STRING_ONE.equals(hisRecord.getIsOwnExp()) &&
                                //门诊类型（1门诊 2住院）
                                register.getClinicTypeId() == 1) {
                    return true;
                }
            }
            //3）限制住院医保
            else if (Constant.BASIC_STRING_THREE.equals(value)) {
                if (//是否自费（0否 1是）
                        !Constant.BASIC_STRING_ONE.equals(hisRecord.getIsOwnExp()) &&
                                //门诊类型（1门诊 2住院）
                                register.getClinicTypeId() == 2) {
                    return true;
                }
            }
            //4）限制住院自费
            else if (Constant.BASIC_STRING_FOUR.equals(value)) {
                if (//是否自费（0否 1是）
                        Constant.BASIC_STRING_ONE.equals(hisRecord.getIsOwnExp()) &&
                                //门诊类型（1门诊 2住院）
                                register.getClinicTypeId() == 2) {
                    return true;
                }
            }
            //5）限制门诊特病
            else if (Constant.BASIC_STRING_FIVE.equals(value)) {
                //1）限制门特病人
                if (Constant.BASIC_STRING_ONE.equals(hisRecord.getIsSpecialDis()) &&
                        //门诊类型（1门诊 2住院）
                        register.getClinicTypeId() == 1) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 超剂量提醒文字
     *
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021/9/2
     */
    public String ultralimitTips() {

        //超剂量提醒文字设置 (医保限制报销|强制保存|超医保规定用药量)
        TSysParam param = getSysParam(Constant.PRESCRIPTION_SAVE_DOSAGE_ULTRALIMIT_TIPS);
        String[] tips = param.getParValues().split("\\|");
        if (tips.length == 3) {
            return tips[2];
        }
        return "超规定用药量";
    }

}