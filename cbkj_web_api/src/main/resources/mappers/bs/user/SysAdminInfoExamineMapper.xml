<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.managementTCMSP.mapper.user.SysAdminInfoExamineMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.managementTCMSP.beans.user.SysAdminInfoExamine">
        <id column="id" jdbcType="INTEGER"  property="id" />
        <result column="user_name" jdbcType="VARCHAR" property="userName" />
        <result column="user_id" jdbcType="VARCHAR" property="userId" />

        <result column="employee_id" jdbcType="VARCHAR" property="employeeIds" />
        <result column="examine_class" jdbcType="INTEGER" property="examineClass" />
        <result column="examine_cycle" jdbcType="VARCHAR" property="examineCycle" />
        <result column="examine_score" jdbcType="DECIMAL" property="examineScore" />
        <result column="examine_time" jdbcType="TIMESTAMP" property="examineTime" />
        <result column="assessor" jdbcType="VARCHAR" property="assessor" />
        <result column="assessor_id" jdbcType="VARCHAR" property="assessorId" />
        <result column="assessor_comment" jdbcType="VARCHAR" property="assessorComment" />
        <result column="certificate_attachment" jdbcType="VARCHAR" property="certificateAttachment" />
        <result column="insert_time" jdbcType="TIMESTAMP" property="insertTime" />
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    </resultMap>


    <sql id="Base_Column_List">
    id,user_name,user_id,examine_class,examine_cycle,examine_score,examine_time,assessor,assessor_id,assessor_comment,certificate_attachment,insert_time,create_user_id,create_user_name
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.jiuzhekan.managementTCMSP.beans.user.SysAdminInfoExamine">
        delete from sys_admin_info_examine where id = #{ id }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from sys_admin_info_examine where id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="com.jiuzhekan.managementTCMSP.beans.user.SysAdminInfoExamine">
        insert into sys_admin_info_examine (<include refid="Base_Column_List" />) values
        (#{id},#{userName},#{userId},#{examineClass},#{examineCycle},#{examineScore},
         #{examineTime},#{assessor},#{assessorId},#{assessorComment},#{certificateAttachment},#{insertTime},#{createUserId},#{createUserName})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into sys_admin_info_examine (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.id},#{item.userName},#{item.userId},#{item.examineClass},#{item.examineCycle},
             #{item.examineScore},#{item.examineTime},#{item.assessor},#{item.assessorId},#{item.assessorComment},
             #{item.certificateAttachment},#{item.insertTime},#{item.createUserId},#{item.createUserName})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.jiuzhekan.managementTCMSP.beans.user.SysAdminInfoExamine">
        update sys_admin_info_examine
        <set>
             <if test="userName != null">
                user_name = #{ userName },
             </if>
             <if test="userId != null">
                user_id = #{ userId },
             </if>

             <if test="examineClass != null">
                examine_class = #{ examineClass },
             </if>
             <if test="examineCycle != null">
                examine_cycle = #{ examineCycle },
             </if>
             <if test="examineScore != null">
                examine_score = #{ examineScore },
             </if>
             <if test="examineTime != null">
                examine_time = #{ examineTime },
             </if>
             <if test="assessor != null">
                assessor = #{ assessor },
             </if>
             <if test="assessorId != null">
                assessor_id = #{ assessorId },
             </if>
             <if test="assessorComment != null">
                assessor_comment = #{ assessorComment },
             </if>
             <if test="certificateAttachment != null">
                certificate_attachment = #{ certificateAttachment },
             </if>
             <if test="insertTime != null">
                insert_time = #{ insertTime },
             </if>
             <if test="createUserId != null">
                create_user_id = #{ createUserId },
             </if>
              <if test="createUserName != null">
                  create_user_name = #{ createUserName },
              </if>
        </set>
        where id = #{ id }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from sys_admin_info_examine where id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="com.jiuzhekan.managementTCMSP.beans.user.SysAdminInfoExamine" resultMap="BaseResultMap">
        SELECT id,user_name,user_id,examine_class,examine_cycle,examine_score,examine_time,assessor,assessor_id,assessor_comment,certificate_attachment,insert_time
        from sys_admin_info_examine
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>
    <select id="getExamineList" resultMap="BaseResultMap" parameterType="com.jiuzhekan.managementTCMSP.bean.GetExamineList">
        select
        a.id,
        a.user_name,
        a.user_id,
        a.examine_class,
        a.examine_cycle,
        a.examine_score,
        a.examine_time,
        a.assessor,
        a.assessor_id,
        a.assessor_comment,
        a.certificate_attachment,
        a.insert_time,
        group_concat(b.employee_id) as employee_id
        from sys_admin_info_examine as a join sys_doctor_multipoint as b on a.user_id = b.user_id
        <where>
            <if test=" userName != null and userName!='' ">
                and (a.user_name like CONCAT('%',trim(#{userName}),'%') or b.employee_id like
                CONCAT('%',trim(#{userName}),'%'))
            </if>
            <if test=" examineClass != null ">
                and a.examine_class = #{examineClass}
            </if>
        </where>
        group by a.user_id
    </select>

</mapper>