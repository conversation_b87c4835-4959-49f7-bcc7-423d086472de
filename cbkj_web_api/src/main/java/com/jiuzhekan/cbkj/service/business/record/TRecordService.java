package com.jiuzhekan.cbkj.service.business.record;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.util.StringUtil;
import com.jiuzhekan.cbkj.beans.business.TSysParam;
import com.jiuzhekan.cbkj.beans.business.patients.TPatientAge;
import com.jiuzhekan.cbkj.beans.business.record.*;
import com.jiuzhekan.cbkj.beans.business.record.VO.*;
import com.jiuzhekan.cbkj.beans.common.CommonPatientVO;
import com.jiuzhekan.cbkj.beans.http.DicBaseAndStanDis;
import com.jiuzhekan.cbkj.beans.http.DicBaseStandReq;
import com.jiuzhekan.cbkj.beans.sysBeans.AdminInfo;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.beans.validRecordBySDK.EvaluationResponse;
import com.jiuzhekan.cbkj.common.constant.OrderStatusConstant;
import com.jiuzhekan.cbkj.common.http.InterfaceRestTemplate;
import com.jiuzhekan.cbkj.common.utils.*;
import com.jiuzhekan.cbkj.mapper.business.prescription.TPreDecoctionMapper;
import com.jiuzhekan.cbkj.mapper.business.prescription.TPreExpressMapper;
import com.jiuzhekan.cbkj.mapper.business.prescription.TPreProductionMapper;
import com.jiuzhekan.cbkj.mapper.business.record.*;
import com.jiuzhekan.cbkj.mapper.myData.TPersonalPrescriptionMapper;
import com.jiuzhekan.cbkj.service.business.TSysParamService;
import com.jiuzhekan.cbkj.service.business.patients.TPatientsService;
import com.jiuzhekan.cbkj.service.redis.ParameterRedisService;
import com.jiuzhekan.cbkj.service.redis.RedisService;
import com.jiuzhekan.cbkj.service.sysService.AdminService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TRecordService {

    @Autowired
    private TRecordMapper tRecordMapper;
    @Autowired
    private TPrescriptionMapper tPrescriptionMapper;
    @Autowired
    private TPrescriptionItemMapper tPrescriptionItemMapper;
    @Autowired
    private TRecordDetailMapper tRecordDetailMapper;
    @Autowired
    private TPrescriptionAcuItemMapper tPrescriptionAcuItemMapper;
    @Autowired
    private TPrescriptionPreparationItemMapper tPrescriptionPreparationItemMapper;
    @Autowired
    private TPrescriptionExamineMapper tPrescriptionExamineMapper;
    @Autowired
    private TOrderStatusMapper tOrderStatusMapper;
    @Autowired
    private TRegisterMapper tRegisterMapper;
    @Autowired
    private AdminService adminService;
    @Autowired
    private TSysParamService tSysParamService;
    @Autowired
    private TPersonalPrescriptionMapper tPersonalPrescriptionMapper;
    @Autowired
    private TPreDecoctionMapper tPreDecoctionMapper;
    @Autowired
    private TPreProductionMapper tPreProductionMapper;
    @Autowired
    private TPreExpressMapper tPreExpressMapper;
    @Autowired
    private InterfaceRestTemplate interfaceRestTemplate;
    @Autowired
    private RedisService redisService;
    @Autowired
    private TPrescriptionEvaluationMapper tPrescriptionEvaluationMapper;
    @Autowired
    private ParameterRedisService parameterRedisService;
    @Value("${address.cbkj.core.port}cbkj/logistics/query/track?preNo=")
    private String expressUrl;

    /**
     * 加载某条数据
     *
     * @param recId
     * @return
     */
    public ResEntity findObj(String recId) {

        if (StringUtils.isBlank(recId)) {
            return new ResEntity(false, "参数不能为空哦", null);
        }
        TRecord tRecord = tRecordMapper.getObjectById(recId);
        tRecord.setAppName(adminService.getAppName(tRecord.getAppId()));
        tRecord.setInsName(adminService.getInsName(tRecord.getAppId(), tRecord.getInsCode()));
        return new ResEntity(true, Constant.SUCCESS_DX, tRecord);
    }


    /**
     * 我的历史病历模块文档还未更新的.只要查主表和挂号表就行了.是查当前医生关联的病历
     *
     * @param tRecord
     * @param page
     * @return
     */
    public Object getMyPageDatas(TodayPatientReqVO tRecord, Page page) {
        AdminInfo currentHr = AdminUtils.getCurrentHr();
        tRecord.setDocId(currentHr.getId());

        if (StringUtils.isNotBlank(tRecord.getOrderName())) {
            if (Constant.BASIC_STRING_ZERO.equals(tRecord.getOrderName())) {
                CommonPatientVO commonPatientVO = CommonUtil.commonJudge(tRecord.getOrderValue());
                tRecord.setPatientName(commonPatientVO.getName());
                tRecord.setPatientPy(commonPatientVO.getPy());
                tRecord.setPatientWb(commonPatientVO.getWb());
            }
            if (Constant.BASIC_STRING_TWO.equals(tRecord.getOrderName())) {
                tRecord.setPatientMobile(tRecord.getOrderValue());
            }
            if (Constant.BASIC_STRING_THREE.equals(tRecord.getOrderName())) {
                tRecord.setPatientCertificate(tRecord.getOrderValue());
            }
        }


        if (StringUtils.isNotBlank(tRecord.getStartDate())) {

            Date startDate = DateUtil.getDateFormatd(tRecord.getStartDate(), DateUtil.YYYY_MM_DD);

            Calendar cal = Calendar.getInstance();
            cal.add(Calendar.YEAR, -2);
            cal.set(Calendar.HOUR, 0);
            cal.set(Calendar.MINUTE, 0);
            cal.set(Calendar.SECOND, 0);
            cal.set(Calendar.MILLISECOND, 0);
            Date twoYearAgo = cal.getTime();

            assert startDate != null;
            if (startDate.before(twoYearAgo)) {

                cal.setTime(startDate);
                int startYear = cal.get(Calendar.YEAR);

                List<String> tableNames = tRecordMapper.getTableHistoryNames("t_record_", "病历主表");

                List<Integer> historyYears = tableNames.stream()
                        .map(tableName -> tableName.replace("t_record_", ""))
                        .filter(StringUtils::isNumeric)
                        .map(Integer::parseInt)
                        .filter(year -> year >= startYear)
                        .sorted().collect(Collectors.toList());

                if (!historyYears.isEmpty()) {
                    tRecord.setTableHistoryYears(historyYears);
                }
            }
        }


        PageHelper.startPage(page.getPage(), page.getLimit());
        List<TRecordRespVO> list = tRecordMapper.getMyHistoryRecS(tRecord);

        Object result = Page.getLayUiTablePageData(list);
        return result;
    }

    /**
     * @param
     * @return
     * @throws
     * @title 获取今日病人信息
     * @description 获取今日病人信息
     * <AUTHOR> xhq
     * @updateTime 2019/8/2 14:11
     */
    public Object getTodayPageDatas(TodayPatientReqVO patientReqVO, Page page) {
        TRecord tRecord = new TRecord();
        tRecord.setAppId(AdminUtils.getCurrentAppIdIgnoreBasic());
        tRecord.setInsCode(AdminUtils.getCurrentInsCodeIgnoreBasic());
        tRecord.setDocId(AdminUtils.getCurrentHr().getId());
        //判断是否有选择上午活者下午，如果没有选择 判断当前时间戳
        if (StringUtils.isNotBlank(patientReqVO.getOrderName())) {
            // 传入的参数名
            if (Constant.BASIC_STRING_ZERO.equals(patientReqVO.getOrderName())) {
                tRecord.setRecName(patientReqVO.getOrderValue());
            } else if (Constant.BASIC_STRING_ONE.equals(patientReqVO.getOrderName())) {
                tRecord.setMedicalCardNo(patientReqVO.getOrderValue());
            } else if (Constant.BASIC_STRING_TWO.equals(patientReqVO.getOrderName())) {
                tRecord.setPatientMobile(patientReqVO.getOrderValue());
            } else if (Constant.BASIC_STRING_THREE.equals(patientReqVO.getOrderName())) {
                tRecord.setPatientCertificate(patientReqVO.getOrderValue());
            } else if (Constant.BASIC_STRING_FOUR.equals(patientReqVO.getOrderName())) {
                tRecord.setVisitNo(patientReqVO.getOrderValue());
            }
        }
        tRecord.setRecDiagnosisStatus(patientReqVO.getRecDiagnosisStatus());
        if (StringUtil.isNotEmpty(patientReqVO.getTime())) {
            if (Constant.BASIC_STRING_ONE.equals(patientReqVO.getTime())
                    || Constant.BASIC_STRING_TWO.equals(patientReqVO.getTime())) {
                tRecord.setTime(patientReqVO.getTime());
            }
        }

        PageHelper.startPage(page.getPage(), page.getLimit());
        List<TodayPatientRespVO> list = tRecordMapper.getListByTodayRecord(tRecord);

        for (TodayPatientRespVO respVO : list) {
            String timeArange = respVO.getRegisterTimeArange();
            if (Constant.REGISTER_TIME_ARANGE_1.equals(timeArange)) {
                respVO.setRegisterTimeArange(Constant.REGISTER_TIME_ARANGE_AM);
            } else if (Constant.REGISTER_TIME_ARANGE_2.equals(timeArange)) {
                respVO.setRegisterTimeArange(Constant.REGISTER_TIME_ARANGE_PM);
            }

            TPatientAge patientAge = TPatientsService.getAgeByBirth(respVO.getPatientBirthday());
            respVO.setAge(patientAge.getAge());
        }

        return Page.getLayUiTablePageData(list);
    }


    /**
     * 挂号
     *
     * @param patientId   患者id
     * @param patientName 患者姓名
     * @param gravidity   患者是否怀孕（Y为是，N为否）
     * <AUTHOR>
     * @date 2021/3/10
     */
    public Object addRegister(String deptId, String deptName, String patientId, String patientName, String gravidity) {
        if (StringUtils.isBlank(deptId) || StringUtils.isBlank(deptName)) {
            return ResEntity.entity(false, "科室不能为空", null);
        }
        if (StringUtils.isBlank(patientId) || StringUtils.isBlank(patientId)) {
            return ResEntity.entity(false, "患者不能为空", null);
        }

        String registerId = MakeOrderNum.makeOrderNum();
        AdminInfo currentHr = AdminUtils.getCurrentHr();

        TRegister tRegister = new TRegister();
        tRegister.setRegisterId(registerId);
        tRegister.setPatientId(patientId);
        tRegister.setPatientName(patientName);
        tRegister.setGravidity(gravidity);
        tRegister.setDoctorId(currentHr.getId());
        tRegister.setDoctorName(currentHr.getNameZh());
        tRegister.setRegisterTime(new Date());
        tRegister.setClinicTypeMoney(0.0);
        tRegister.setAppId(AdminUtils.getCurrentAppId());
        tRegister.setInsCode(AdminUtils.getCurrentInsCode());
        tRegister.setDeptId(deptId);
        tRegister.setDeptName(deptName);
        /* 今日病人列表除重规则：从患者库添加今日病人时，如果此人当天在 当前机构当前医生下 有未付款的记录（包括：待就诊、已就诊）
        则不能添加。提醒：请不要重复挂号*/
        tRegister.setRegisterDiagnosisStatus(4);
        TRegister existReg = tRegisterMapper.getExistRegByReg(tRegister);
        if (null != existReg) {
            return ResEntity.entity(false, "该病人已经在" + existReg.getDoctorName() + "医生下挂" +
                    "号了,不可重复挂号!", existReg);
        }
        //获取当前时间
        SimpleDateFormat df = new SimpleDateFormat("HH");
        String str = df.format(new Date());
        int a = Integer.parseInt(str);
        if (a >= 0 && a <= 12) {
            tRegister.setRegisterTimeArange(Constant.REGISTER_TIME_ARANGE_1);
        }
        if (a > 12 && a <= 23) {
            tRegister.setRegisterTimeArange(Constant.REGISTER_TIME_ARANGE_2);
        }
        tRegister.setRegisterDiagnosisStatus(2);
        tRegister.setClinicTypeId(1);
        tRegister.setVisitNo(registerId);
        long row = tRegisterMapper.addRegister(tRegister);
        if (row < 1) {
            return ResEntity.entity(false, "添加今日病人失败", row);
        }

        return ResEntity.entity(true, Constant.SUCCESS_DX, row);
    }


    /**
     * @param :
     * @return :
     * @Description : 根据病历id查单病历
     * 1. 查病历主表,挂号表,患者表的关联 2.查病历从表集合,查处方表集合 3.遍历处方表集合查出各自处方明细
     * <AUTHOR> 徐亨期
     * @updateTime : 2019/12/23 21:13
     */
    public Object getSingleRecord(String recId, String preNo) {
        TRecord tRecord = new TRecord();
        tRecord.setRecId(recId);
        List<TRecordRespVO> recRespVOS = tRecordMapper.getRecordByRecord(tRecord);
        TSysParam param = tSysParamService.getSysParam(Constant.USE_PLATFORM_DIS_MAPPING);
        for (TRecordRespVO respVO : recRespVOS) {

            TRecordRespVO hisRecord = tRecordMapper.getHisRecordByRecord(respVO);
            if (Constant.BASIC_STRING_ONE.equals(param.getParValues())){
                //使用综合平台字典疾病映射数据
                DicBaseStandReq dicBaseStandReq = new DicBaseStandReq();
                dicBaseStandReq.setQueryType("1");
                dicBaseStandReq.setDisIdSys(hisRecord.getDisId());
                dicBaseStandReq.setAppId(AdminUtils.getCurrentAppId());
                DicBaseAndStanDis oneDicAndStandInfo = parameterRedisService.getOneDicAndStandInfo(dicBaseStandReq);
                if (oneDicAndStandInfo != null){
                    respVO.setDisCodeHis(oneDicAndStandInfo.getDisCodeHis());
                }
                dicBaseStandReq.setQueryType("2");
                dicBaseStandReq.setAppId(AdminUtils.getCurrentAppId());
                dicBaseStandReq.setSymIdSys(hisRecord.getSymId());
                DicBaseAndStanDis oneDicAndStandInfo2 = parameterRedisService.getOneDicAndStandInfo(dicBaseStandReq);
                if (oneDicAndStandInfo2 != null){
                    respVO.setSymCodeHis(oneDicAndStandInfo2.getSymCodeHis());
                }
            }else {
                respVO.setDisCodeHis(hisRecord.getDisCodeHis());
                respVO.setSymCodeHis(hisRecord.getSymCodeHis());

            }
            respVO.setMedicalTypeName(hisRecord.getMedicalTypeName());

//            for (TPreRespVO tPreRespVO : respVO.getPreList()) {
//                String sdkjson = tPrescriptionEvaluationMapper.getSDKOtherJsonByPreId(tPreRespVO.getPreId());
//                EvaluationResponse evaluationResponse = JSON.parseObject(JSON.toJSONString(sdkjson), EvaluationResponse.class);
//                tPreRespVO.setPreSafetyEvaluationSDK(evaluationResponse);
//            }


        }
        if (recRespVOS.size() != 1) {
            return ResEntity.entity(false, "传入病历id有误", recId);
        }
        getRecS(recRespVOS, preNo, null, null, Constant.BASIC_STRING_ZERO);
        // 处方状态的情况没有查询.
        TRecordRespVO tRecordRespVO = recRespVOS.get(0);
        for (TPreRespVO tPreRespVO : tRecordRespVO.getPreList()) {
                String sdkjson = tPrescriptionEvaluationMapper.getSDKOtherJsonByPreId(tPreRespVO.getPreId());
                EvaluationResponse evaluationResponse = JSON.parseObject(sdkjson, EvaluationResponse.class);
                tPreRespVO.setPreSafetyEvaluationSDK(evaluationResponse);
        }
        return ResEntity.entity(true, Constant.SUCCESS_DX, tRecordRespVO);
    }


    public Object getAllPre(String recId, String preNo) {
        TRecord tRecord = new TRecord();
        tRecord.setRecId(recId);
        List<TRecordRespVO> recRespVOS = tRecordMapper.getRecordByRecord(tRecord);
        if (null == recRespVOS || recRespVOS.size() != 1) {
            return ResEntity.entity(false, "传入病历id有误", recId);
        }
        getRecS(recRespVOS, preNo, null, null, null);

        // 处方状态的情况没有查询.
        return ResEntity.entity(true, Constant.SUCCESS_DX, recRespVOS.get(0));
    }

    public Object getCheckRecord(String recId) {
        TRecord tRecord = new TRecord();
        tRecord.setRecId(recId);
        List<TRecordRespVO> recRespVOS = tRecordMapper.getRecordByRecord(tRecord);
        if (null == recRespVOS || recRespVOS.size() != 1) {
            return ResEntity.entity(false, "传入病历id有误", recId);
        }
        getRecS(recRespVOS, null, Constant.BASIC_STRING_ZERO, null, Constant.BASIC_STRING_ZERO);
        TRecordRespVO tRecordRespVO = recRespVOS.get(0);
        for (TPreRespVO tPreRespVO : tRecordRespVO.getPreList()) {
            String sdkjson = tPrescriptionEvaluationMapper.getSDKOtherJsonByPreId(tPreRespVO.getPreId());
            EvaluationResponse evaluationResponse = JSON.parseObject(sdkjson, EvaluationResponse.class);
            tPreRespVO.setPreSafetyEvaluationSDK(evaluationResponse);

        }
        // 处方状态的情况没有查询.
        return ResEntity.entity(true, Constant.SUCCESS_DX, recRespVOS.get(0));
    }


    /**
     * @param recId :
     * @return : java.lang.Object
     * @Description : 根据病历id找到诊次id,再根据诊次id查询所有病历
     * <AUTHOR> 徐亨期
     * @updateTime : 2019/12/23 14:32
     */
    public Object getBigRecord(String recId) {
        String curAppId = AdminUtils.getCurrentAppId();
        String curInsCode = AdminUtils.getCurrentInsCode();
        if (Constant.BASIC_APP_ID.equals(curAppId)) {
            curAppId = "";
        }
        if (Constant.BASIC_INS_CODE.equals(curInsCode)) {
            curInsCode = "";
        }
        // 根据患者id查出其所有病历
        TRecord tRecord = new TRecord();
        tRecord.setRecId(recId);
        //String patientVisitRange = tSysParamService.getSysParam(Constant.PATIENT_VISIT_RANGE).getParValues();
        //// 患者就诊范围  1.医联体 2.医疗机构
        //if (Constant.BASIC_STRING_ONE.equals(patientVisitRange)) {
        //    tRecord.setAppId(curAppId);
        //} else {
        //    tRecord.setAppId(curAppId);
        //    tRecord.setInsCode(curInsCode);
        //}
        // 其实可以和getRecordByRecord 合并,加个判断参数就好了
        List<TRecordRespVO> bigRecord = tRecordMapper.getBigRecord(tRecord);
        if (null == bigRecord || bigRecord.isEmpty()) {
            return ResEntity.entity(false, "传入病历id有误", recId);
        }
        // 估计查询速度不会快啊
        getRecS(bigRecord, null, null, Constant.BASIC_STRING_ONE, Constant.BASIC_STRING_ZERO);

        TBigRecordRespVO bigRec = new TBigRecordRespVO();
        bigRec.setBigRec(new ArrayList<>());

        for (int i = 0; i < bigRecord.size(); i++) {
            TRecordRespVO respVO = bigRecord.get(i);
            if (respVO.getPreList() != null && respVO.getPreList().size() > 0) {
                respVO.setVisNum(i + 1);
                bigRec.getBigRec().add(respVO);
            }
            String menuByUserId2 = parameterRedisService.getMenuByUserId2(AdminUtils.getCurrentHr().getId());
            if (!StringUtils.isBlank(menuByUserId2)) {
                if ("/manage/case/record".equals(menuByUserId2)) {
                    menuByUserId2 = "/manage/case/record/transfer";
                }
                respVO.setUrl(menuByUserId2 + "/" + recId);
            }
        }

        return ResEntity.entity(true, Constant.SUCCESS_DX, bigRec);
    }


    /**
     * 病历查询处方的公共代码
     *
     * @param recRespVos 病历列表
     * @param preNo      只返回处方编号等于preNo的处方，多个英文逗号拼接，为空时返回所有
     * @param isCheck    审核状态（0未审核 1审核通过 2审核未通过），多个逗号英文拼接，为空时返回所有
     * @param isPay      是否收费（0未收费 1已收费 2已退费），多个逗号英文拼接，为空时返回所有
     * @param isDel      是否删除（0否 1是），为空时返回所有
     */
    public void getRecS(List<TRecordRespVO> recRespVos, String preNo, String isCheck, String isPay, String isDel) {

        for (TRecordRespVO recRespVO : recRespVos) {
            recRespVO.setInsName(adminService.getInsName(recRespVO.getAppId(), recRespVO.getInsCode()));
            getPre(recRespVO, preNo, isCheck, isPay, isDel);
            int detailCount = tRecordDetailMapper.getCountTempDetailByRecId(recRespVO.getRecId());
            if (detailCount > 0) {
                recRespVO.setIsHasRecDetail(Constant.BASIC_STRING_ONE);
            }
        }
    }

    /**
     * 根据病历id查出病历从表及处方信息及明细 等各种信息,未查询订单表.病历主表等信息未查
     *
     * @param recRespVO 病历
     * @param preNo     只返回处方编号等于preNo的处方，多个英文逗号拼接，为空时返回所有
     * @param isCheck   审核状态（0未审核 1审核通过 2审核未通过），多个逗号英文拼接，为空时返回所有
     * @param isPay     是否收费（0未收费 1已收费 2已退费），多个逗号英文拼接，为空时返回所有
     * @param isDel     是否删除（0否 1是），为空时返回所有
     */
    private void getPre(TRecordRespVO recRespVO, String preNo, String isCheck, String isPay, String isDel) {
//        TRecordDetail tRecordDetail = new TRecordDetail();
//        tRecordDetail.setRecId(recRespVO.getRecId());
//        List<TRecordDetail> recDetails = tRecordDetailMapper.getPageListByObj(tRecordDetail);
//        recRespVO.setRecordDetailList(recDetails);
        // 根据病历id查出所有处方
        TPrescription tPrescription = new TPrescription();
        tPrescription.setRecId(recRespVO.getRecId());
        tPrescription.setPreNo(preNo);
        tPrescription.setIsCheck(isCheck);
        tPrescription.setIsPay(isPay);
        tPrescription.setIsDel(isDel);
        List<TPreRespVO> preList = tPrescriptionMapper.getPreByPre(tPrescription);
        // 病历处方集合找到对应的明细
        recRespVO.setIsProduction(Constant.BASIC_STRING_ZERO);
        recRespVO.setDecoctType(Constant.BASIC_STRING_ZERO);
        String LOGISTICS_DISTRIBUTION_QUERY = tSysParamService.getSysParam(Constant.LOGISTICS_DISTRIBUTION_QUERY).getParValues();
        for (TPreRespVO preVO : preList) {

            setItemList(preVO);

            preVO.setDecoction(tPreDecoctionMapper.getObjectById(preVO.getPreId()));
            preVO.setProduction(tPreProductionMapper.getObjectById(preVO.getPreId()));
            preVO.setExpress(tPreExpressMapper.getObjectById(preVO.getPreId()));
            Date preTime = preVO.getPreTime();
            if (null != preTime){
                //获取当前时间减去preTime的毫秒数差值
                long time = System.currentTimeMillis() - preTime.getTime();
                preVO.setPreTimeOverdue(time);
            }
            // 今日病人的订单详情里,收货信息显示在病历上面,那就随便取某个处方的收货信息
            if (StringUtils.isNotBlank(preVO.getDcName()) && StringUtils.isBlank(recRespVO.getDcName())) {
                recRespVO.setDcName(preVO.getDcName());
                String county = StringUtils.isBlank(preVO.getDcCounty()) ? "" : preVO.getDcCounty();
                String town = StringUtils.isBlank(preVO.getDcTown()) ? "" : preVO.getDcTown();
                String village = StringUtils.isBlank(preVO.getDcVillage()) ? "" : preVO.getDcVillage();
                String street = StringUtils.isBlank(preVO.getDcStreet()) ? "" : preVO.getDcStreet();
                String dcAddress = StringUtils.isBlank(preVO.getDcAddress()) ? "" : preVO.getDcAddress();
                recRespVO.setDcAddress(county + town + village + street + dcAddress);
                recRespVO.setDcMobile(preVO.getDcMobile());
            }

            if (Constant.BASIC_STRING_ONE.equals(preVO.getDecoctType())) {
                recRespVO.setDecoctType(Constant.BASIC_STRING_ONE);
            }
            if (Constant.BASIC_STRING_ONE.equals(preVO.getIsProduction())) {
                recRespVO.setIsProduction(Constant.BASIC_STRING_ONE);
            }
            // 根据处方的药房id找到药房名称
            //TDisplay tDisplay = redisService.getDisplayByMatTypeStoreId(recRespVO.getAppId(), recRespVO.getInsCode(),
            //        recRespVO.getDeptId(), preVO.getPreMatType(), preVO.getStoreId());
            //if (tDisplay != null) {
            //    preVO.setStoreName(tDisplay.getStoreName());
            //}

            // 根据处方id查询订单状态
            TOrderStatus tOrderStatus = new TOrderStatus();
            tOrderStatus.setPreId(preVO.getPreId());
            List<TOrderStatus> pageListByObj2 = tOrderStatusMapper.getPageListByObj2(tOrderStatus);
            if (null != pageListByObj2) {

                for (TOrderStatus tOrderStatus1 : pageListByObj2) {
                    if (null != tOrderStatus1.getOperationType()) {
                        String name = OrderStatusConstant.getName(tOrderStatus1.getOperationType());
                        if (!StringUtils.isBlank(name)) {
                            tOrderStatus1.setOperationTypeName(name);
                        }
                    }
                    if (OrderStatusConstant.EXPRESS.getIndex() == tOrderStatus1.getOperationType()) {
                        tOrderStatus1.setRemark(tOrderStatus1.getOperationContent());
                    }
                    //物流页面url设置
                    if (Constant.BASIC_STRING_ONE.equals(LOGISTICS_DISTRIBUTION_QUERY)) {
                        if (tOrderStatus1.getOperationType() == 140) {
                            tOrderStatus1.setExpressageUrl(expressUrl + preVO.getPreNo());
                        }
                    }
                }
            }
            preVO.setOrderList(pageListByObj2);

            if (Constant.BASIC_STRING_FOUR.equals(preVO.getPreOrigin()) || Constant.BASIC_STRING_FIVE.equals(preVO.getPreOrigin())) {
                Integer isModify = tPersonalPrescriptionMapper.personalPrescriptionisModify(preVO.getPreOriginId());
                preVO.setPreOriginUpdate(isModify == null || isModify == 1);
            }
            //审核历史记录
            TPreRespVO tPreRespVO = new TPreRespVO();
            BeanUtils.copyProperties(preVO, tPreRespVO);
            preVO.setExamineList(tPrescriptionExamineMapper.getExamineListByPre(tPreRespVO));

        }
        recRespVO.setPreList(preList);
    }


    /**
     * @param patientId :
     * @return : java.lang.Object
     * @Description : 我的患者，数据从【病历主表】取，按就诊医生ID、中医病名（即疾病ID）、病人信息查询。中医病名可以下拉选择
     * ，参见智能开方界面的中医病名。 点击某一条记录，右侧显示该病人的历史病历(是指已收费的吗? 即病历下的所有处方都收费了.)，
     * 数据取自【病历主表】，按末次就诊时间倒序显示此患者大病历节点，单击查看大病历详情。
     * 通过参数PATIENT_VISIT_RANGE患者就诊范围，控制显示范围，值：1医联体 2医疗机构。
     * <AUTHOR> 徐亨期
     * @updateTime : 2019/12/24 11:47
     */
    public Object getRecSByPatientId(String patientId) {
        TRecord tRecord = new TRecord();
        tRecord.setPatientId(patientId);
        String appId = AdminUtils.getCurrentAppId();
        String insCode = AdminUtils.getCurrentInsCode();
        if (!Constant.BASIC_APP_ID.equals(appId)) {
            tRecord.setAppId(appId);
        }
        if (!Constant.BASIC_INS_CODE.equals(insCode)) {
            tRecord.setInsCode(insCode);
        }

        List<TRecord> list = tRecordMapper.getListByRec(tRecord);
        return ResEntity.entity(true, Constant.SUCCESS_DX, list);
    }

    /**
     * @param todayPatientReqVO :
     * @param page              :
     * @return : java.lang.Object
     * @Description : 智能中医处方-历史病历
     * 历史病历，显示该病人一年内的历史病历，按就诊日期倒序排，可以查看、转方。
     * 点转方，弹出选择框。如果该病例有内服中药方、外用中药方、适宜技术方，点全选，三种类型的处方一起转方。
     * 如果只选了内服中药方，只转内服中药方。默认全选。   暂不考虑病历状态
     * <AUTHOR> 徐亨期
     * @updateTime : 2019/12/30 17:58
     */
    public Object getHistoryRec(TodayPatientReqVO todayPatientReqVO, Page page) {
        String patientVisitRange = tSysParamService.getSysParam(Constant.PATIENT_VISIT_RANGE).getParValues();
        ;
        String currentExtContent = AdminUtils.getCurrentShuruma();
        String curAppId = AdminUtils.getCurrentAppId();
        String curInsCode = AdminUtils.getCurrentInsCode();
        if (Constant.BASIC_APP_ID.equals(curAppId)) {
            curAppId = "";
        }
        if (Constant.BASIC_INS_CODE.equals(curInsCode)) {
            curInsCode = "";
        }
        // 患者就诊范围  1.医联体 2.医疗机构
        if (Constant.BASIC_STRING_ONE.equals(patientVisitRange)) {
            todayPatientReqVO.setAppId(curAppId);
        } else {
            todayPatientReqVO.setAppId(curAppId);
            todayPatientReqVO.setInsCode(curInsCode);
        }
        if (StringUtils.isBlank(todayPatientReqVO.getStartDate())
                && StringUtils.isBlank(todayPatientReqVO.getEndDate())) {
            todayPatientReqVO.setEndDate(DateUtil.getDateFormats(DateUtil.YYYY_MM_DD_HH_MM_SS, new Date()));
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Calendar c = Calendar.getInstance();
            c.setTime(new Date());
            c.add(Calendar.YEAR, -1);
            Date y = c.getTime();
            todayPatientReqVO.setStartDate(format.format(y));
        }
        String orderName = todayPatientReqVO.getOrderName();
        String orderValue = todayPatientReqVO.getOrderValue();
        if (Constant.BASIC_STRING_ZERO.equals(orderName) && StringJudges.isContainChinese(orderValue)) {
            todayPatientReqVO.setPatientName(orderValue);
        } else if (Constant.BASIC_STRING_ZERO.equals(orderName) && !StringJudges.isContainChinese(orderValue) && "1".equals(currentExtContent)) {
            todayPatientReqVO.setPatientPy(orderValue);
        } else if (Constant.BASIC_STRING_ZERO.equals(orderName) && !StringJudges.isContainChinese(orderValue) && "2".equals(currentExtContent)) {
            todayPatientReqVO.setPatientWb(orderValue);
        }
        todayPatientReqVO.setCurrentExtContent(currentExtContent);
        PageHelper.startPage(page.getPage(), page.getLimit());
        List<TRecordRespVO> list = tRecordMapper.getHistoryRec(todayPatientReqVO);
        return Page.getLayUiTablePageData(list);
    }

    /**
     * @param todayPatientReqVO :
     * @param page              :
     * @return : java.lang.Object
     * @Description : 病历管理 查询条件：病历主表中的就诊时间、APPID、医疗机构ID、病历类型、医生UUID。
     * 查询内容取自病历主表
     * <AUTHOR> 徐亨期
     * @updateTime : 2019/12/31 13:20
     */
    public Object recManage(TodayPatientReqVO todayPatientReqVO, Page page) {
        //历史病历显示范围（1医联体 2医疗机构）
        String HISTORY_RECORD = tSysParamService.getSysParam(Constant.HISTORY_RECORD).getParValues();

//        String appId = AdminUtils.getCurrentAppId();
//        String insCode = AdminUtils.getCurrentInsCode();
//
//        if (Constant.BASIC_APP_ID.equals(appId)) {
////            todayPatientReqVO.setAppId("");
//        } else {
//            todayPatientReqVO.setAppId(appId);
//        }
//
//        if (Constant.BASIC_STRING_ONE.equals(HISTORY_RECORD) || Constant.BASIC_INS_CODE.equals(insCode)) {
////            todayPatientReqVO.setInsCode("");
//        } else {
//            todayPatientReqVO.setInsCode(insCode);
//        }


        if (StringUtils.isBlank(todayPatientReqVO.getAppId())) {
            todayPatientReqVO.setAppId(AdminUtils.getCurrentAppId());
        }
        if (StringUtils.isBlank(todayPatientReqVO.getInsCode()) && Constant.BASIC_STRING_TWO.equals(HISTORY_RECORD)) {
            todayPatientReqVO.setInsCode(AdminUtils.getCurrentInsCode());
        }

        if (Constant.BASIC_APP_ID.equals(todayPatientReqVO.getAppId())) {
            todayPatientReqVO.setAppId("");
        }
        if (Constant.BASIC_INS_CODE.equals(todayPatientReqVO.getInsCode())) {
            todayPatientReqVO.setInsCode("");
        }


        if (Constant.BASIC_STRING_ZERO.equals(todayPatientReqVO.getOrderName())) {
            CommonPatientVO commonPatientVO = CommonUtil.commonJudge(todayPatientReqVO.getOrderValue());
            todayPatientReqVO.setPatientName(commonPatientVO.getName());
            todayPatientReqVO.setPatientPy(commonPatientVO.getPy());
            todayPatientReqVO.setPatientWb(commonPatientVO.getWb());
        }
        PageHelper.startPage(page.getPage(), page.getLimit());
        List<TRecordRespVO> list = tRecordMapper.recManage(todayPatientReqVO);
        for (TRecordRespVO respVO : list) {
            respVO.setInsName(adminService.getInsName(respVO.getAppId(), respVO.getInsCode()));
        }
        return Page.getLayUiTablePageData(list);
    }


    /**
     * @param recId
     * @param registerId : 病历id
     * @param preId      : 处方id
     * @return : java.lang.Object
     * @Description : 将【处方表.收退费状态】更新为1已收费,处方状态更新为1已支付，(将处方的收退费状态更新完后,当前病历下的所有处方
     * 都已经支付,或者'已经退费了',就将【挂号表.就诊状态】更新为5已付款),开方系统的状态更新完后还要调用接口将
     * 处方信息传输到老版药房系统用来发药测试
     * <AUTHOR> 徐亨期
     * @updateTime : 2020/1/7 19:36
     */
    @Transactional(rollbackFor = Exception.class)
    public Object updateIsPay(String recId, String registerId, String preId) {
        String PRE_INTERFACE = tSysParamService.getSysParam(Constant.PRE_INTERFACE).getParValues();

        List<InterfaceRestTemplate.PreStatus> preStatuList = new ArrayList<>();
        if (StringUtils.isBlank(recId)) {
            return ResEntity.entity(false, "病历id为空", recId);
        }
        if (StringUtils.isBlank(preId)) {
            return ResEntity.entity(false, "处方id为空", recId);
        }

        TPrescription tPrescription = new TPrescription();
        tPrescription.setRecId(recId);
        List<TPrescription> pList = tPrescriptionMapper.getPageListByObj(tPrescription);


        if (!StringUtils.isBlank(PRE_INTERFACE) && PRE_INTERFACE.contains("pay")) {
            AdminInfo admin = AdminUtils.getCurrentHr();
            String appId = AdminUtils.getCurrentAppId();
            String insCode = AdminUtils.getCurrentInsCode();
            String dateStr = DateUtil.getDateFormats(DateUtil.YYYY_MM_DD_HH_MM_SS, new Date());
            for (TPrescription pre : pList) {
                InterfaceRestTemplate.PreStatus preStatus = interfaceRestTemplate.new PreStatus();
                preStatus.setPreNo(pre.getPreNo());
                preStatus.setStatus(OrderStatusConstant.PRES_PAY.toString());
                preStatus.setOperationTime(dateStr);
                preStatus.setOperationName(admin.getNameZh());
                preStatus.setOperationContent(OrderStatusConstant.PRES_PAY.getName());
                preStatus.setSource(Constant.BASIC_STRING_ONE);
                preStatuList.add(preStatus);
            }
            try {
                // 要调用方法去药房发药
//            ResEntity resEntity = preIntoInterfaceService.sendPreToHis(preList, recId);
                InterfaceRestTemplate.StatusVO statusVO = interfaceRestTemplate.new StatusVO();
                statusVO.setAppId(appId);
                statusVO.setInsCode(insCode);
                statusVO.setTimestamp(String.valueOf(System.currentTimeMillis()));
                statusVO.setPreStatusList(preStatuList);

                ResEntity resEntity = interfaceRestTemplate.setStatus(statusVO);
                if (resEntity.getStatus()) {
                    //TODO 演示环境暂时返回成功 TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    log.info("【HIS系统状态回传接口】 " + resEntity.getMessage());
                    return ResEntity.entity(true, "HIS系统状态回传接口！" + resEntity.getMessage(), null);
                } else {
                    return ResEntity.entity(false, resEntity.getMessage(), null);
                }
            } catch (Exception e) {
                e.printStackTrace();
                log.error("【HIS系统状态回传接口失败】" + e.getMessage());
                return ResEntity.entity(false, e.getMessage(), null);
            }
        } else {
            List<String> preList = Arrays.asList(preId.split(","));
            Map<String, Object> payM = new HashMap<>();
            AdminInfo currentHr = AdminUtils.getCurrentHr();
            payM.put("payUserid", currentHr.getId());
            payM.put("payUsername", currentHr.getNameZh());
            int i = tPrescriptionMapper.updateIsPay(preList, payM);
            if (i != preList.size()) {
                return ResEntity.entity(false, "处方已经支付或者处方id错误", i);
            }
            Boolean status = true; // 已支付
            for (TPrescription prescription : pList) {
                if (!"1".equals(prescription.getIsPay())) {
                    // 存在未支付
                    status = false;
                    break;
                }
            }
            if (status) {
                // 病历下的处方有一个不是已支付状态就,先更新再查询会不会有问题啊?
                TRegister tRegister = new TRegister();
                tRegister.setRegisterDiagnosisStatus(5);
                tRegister.setRegisterId(registerId);
                int j = tRegisterMapper.updateByPrimaryKey(tRegister);
            }
            return ResEntity.entity(true, Constant.SUCCESS_DX, null);
        }

//        try {
//            // 要调用方法去药房发药
//            ResEntity resEntity = preIntoInterfaceService.sendPreToHis(preList, recId);
//            if (resEntity.getStatus()) {
//                //TODO 演示环境暂时返回成功 TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
//                log.error("【发送药房失败】 " + resEntity.getMessage());
//                return ResEntity.entity(true, "支付成功！发送药房失败！" + resEntity.getMessage(), null);
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//            log.error("【发送药房失败】" + e.getMessage());
//        }
        //  return ResEntity.entity(true, Constant.SUCCESS_DX, null);
    }

    /**
     * @param disId : 疾病id
     * @param symId : 证型id
     * @param page
     * @return : java.lang.Object
     * @Description : 根据当前医生id,疾病id,证型id在病历主表查找记录,病历主表要关联挂号表,
     * 挂号表的就诊状态为已收费,已发药 (REGISTER_DIAGNOSIS_STATUS 为5,8  ,已退费为6 不算)
     * 查出来
     * <AUTHOR> xhq
     * @updateTime : 2020/1/16 11:34
     */
    public Object myConsilia(String disId, String symId, Page page) {
        if (StringUtils.isBlank(disId)) {
            return ResEntity.entity(false, "疾病ID不能为空", null);
        }
        TRecord tRecord = new TRecord();
        tRecord.setDisId(disId);
        tRecord.setSymId(symId);
        tRecord.setDocId(AdminUtils.getCurrentHr().getId());
        PageHelper.startPage(page.getPage(), page.getLimit());
        // 查询我的医案
        List<TRecord> recList = tRecordMapper.getMyConsiliaByRec(tRecord);
        return Page.getLayUiTablePageData(recList);
    }


    public ResEntity getPreByPreNo(String preNo) {
        if (StringUtils.isBlank(preNo)) {
            return ResEntity.entity(false, "处方编号不能为空", null);
        }

        TPrescription prescription = tPrescriptionMapper.getObjectByPreNo(preNo);

        if (prescription == null) {
            return ResEntity.entity(false, "处方不存在", null);
        }

        setItemList(prescription);

        return ResEntity.entity(true, Constant.SUCCESS_DX, prescription);
    }


    /**
     * 设置处方明细
     *
     * @param preVO 处方
     */
    private void setItemList(TPreRespVO preVO) {
        if (Constant.BASIC_STRING_ONE.equals(preVO.getPreType()) || Constant.BASIC_STRING_TWO.equals(preVO.getPreType())) {
            List<TPrescriptionItem> itemList = tPrescriptionItemMapper.getListByPreId(preVO.getPreId());
            preVO.setItemList(itemList);
        } else if (Constant.BASIC_STRING_FOUR.equals(preVO.getPreType())) {
            List<TPrescriptionAcuItem> acuItemList = tPrescriptionAcuItemMapper.getListByPreId(preVO.getPreId());
            preVO.setAcuItemList(acuItemList);
        } else if (Constant.BASIC_STRING_FIVE.equals(preVO.getPreType())) {
            List<TPrescriptionPreparationItem> itemList = tPrescriptionPreparationItemMapper.getListByPreId(preVO.getPreId());
            preVO.setPreparationItemList(itemList);
        }

    }

    /**
     * 设置处方明细
     *
     * @param prescription 处方
     */
    private void setItemList(TPrescription prescription) {
        if (Constant.BASIC_STRING_ONE.equals(prescription.getPreType()) || Constant.BASIC_STRING_TWO.equals(prescription.getPreType())) {
            List<TPrescriptionItem> itemList = tPrescriptionItemMapper.getListByPreId(prescription.getPreId());
            prescription.setItemList(itemList);
        } else if (Constant.BASIC_STRING_FOUR.equals(prescription.getPreType())) {
            List<TPrescriptionAcuItem> acuItemList = tPrescriptionAcuItemMapper.getListByPreId(prescription.getPreId());
            prescription.setAcuItemList(acuItemList);
        } else if (Constant.BASIC_STRING_FIVE.equals(prescription.getPreType())) {
            List<TPrescriptionPreparationItem> itemList = tPrescriptionPreparationItemMapper.getListByPreId(prescription.getPreId());
            prescription.setPreparationItemList(itemList);
        }
    }

    /**
     * 设置处方明细
     *
     * @param preVO 处方
     */
    private void setItemList(TPrintPreVO preVO) {
        if (Constant.BASIC_STRING_ONE.equals(preVO.getPreType()) || Constant.BASIC_STRING_TWO.equals(preVO.getPreType())) {
            List<TPrescriptionItem> itemList = tPrescriptionItemMapper.getListByPreId(preVO.getPreId());
            preVO.setItemList(itemList);
        } else if (Constant.BASIC_STRING_FOUR.equals(preVO.getPreType())) {
            List<TPrescriptionAcuItem> acuItemList = tPrescriptionAcuItemMapper.getListByPreId(preVO.getPreId());
            preVO.setAcuItemList(acuItemList);
        } else if (Constant.BASIC_STRING_FIVE.equals(preVO.getPreType())) {
            List<TPrescriptionPreparationItem> itemList = tPrescriptionPreparationItemMapper.getListByPreId(preVO.getPreId());
            preVO.setPreparationItemList(itemList);
        }
    }


}