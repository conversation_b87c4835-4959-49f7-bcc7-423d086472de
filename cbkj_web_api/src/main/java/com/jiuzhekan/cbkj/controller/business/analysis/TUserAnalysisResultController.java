package com.jiuzhekan.cbkj.controller.business.analysis;


import com.jiuzhekan.cbkj.beans.business.analysis.TUserAnalysisResult;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.service.business.analysis.TUserAnalysisResultService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Controller
@RequestMapping("tUserAnalysisResult/")
@Api(value = "体质辨识接口", tags = "体质辨识接口")
public class TUserAnalysisResultController {

    @Autowired
    private TUserAnalysisResultService tUserAnalysisResultService;

    @RequestMapping(value = "getPages", method = RequestMethod.GET)
    @ApiOperation(value = "首页列表-查询体质辨识用户", notes = "首页列表-查询体质辨识用户", response = TUserAnalysisResult.class)
    @ResponseBody
    @LogAnnotation("首页列表-分页查询用户体质辨识结果列表")
    public Object getApps(TUserAnalysisResult tUserAnalysisResult, Page page) {
        return tUserAnalysisResultService.getPageDatas(tUserAnalysisResult, page);
    }

    @RequestMapping(value = "getDetailPages", method = RequestMethod.GET)
    @ApiOperation(value = "查看列表-患者体质辨识结果", notes = "查看列表-患者体质辨识结果", response = TUserAnalysisResult.class)
    @ApiImplicitParam(name = "patientId", value = "患者id", dataType = "String", paramType = "query", required = true)
    @ResponseBody
    @LogAnnotation("查看里的列表-根据患者id分页查询用户体质辨识结果列表")
    public Object getDetailPages(String patientId, Page page) {
        TUserAnalysisResult tUserAnalysisResult = new TUserAnalysisResult();
        tUserAnalysisResult.setPatientId(patientId);
        return tUserAnalysisResultService.getDetailPages(tUserAnalysisResult, page);
    }

    @RequestMapping(value = "manage/list", method = RequestMethod.GET)
    @ApiOperation(value = "体质辨识管理列表", notes = "体质辨识管理列表", response = TUserAnalysisResult.class)
    @ResponseBody
    @LogAnnotation("体质辨识管理列表")
    public Object manageList(TUserAnalysisResult tUserAnalysisResult, Page page) {
        return tUserAnalysisResultService.getDetailPages(tUserAnalysisResult, page);
    }


    @RequestMapping(value = "update/findObj", method = RequestMethod.GET)
    @LogAnnotation("根据辨识id加载用户体质辨识结果详情")
    @ApiOperation(value = "根据辨识id加载详情", notes = "根据辨识id加载详情", response = TUserAnalysisResult.class)
    @ApiImplicitParam(value = "id", name = "id", paramType = "query", dataType = "String", required = true)
    @ResponseBody
    public Object getObj(String id) {
        return tUserAnalysisResultService.detail(id);
    }

    @RequestMapping(value = "insert", method = RequestMethod.POST)
    @LogAnnotation(isWrite = true, value = "修改用户体质辨识结果")
    @ApiOperation(value = "修改用户体质辨识结果", notes = "修改用户体质辨识结果")
    @ResponseBody
    public Object insert(@RequestBody TUserAnalysisResult tUserAnalysisResult) {
        return tUserAnalysisResultService.insert(tUserAnalysisResult);
    }

    @RequestMapping(value = "update", method = RequestMethod.POST)
    @LogAnnotation(isWrite = true, value = "上传得分图片")
    @ApiOperation(value = "上传得分图片", notes = "上传得分图片")
    @ResponseBody
    public Object update(@RequestBody TUserAnalysisResult tUserAnalysisResult) {
        return tUserAnalysisResultService.update(tUserAnalysisResult);
    }

    @RequestMapping(value = "uploadImg", method = RequestMethod.POST)
    @LogAnnotation(isWrite = true, value = "上传得分图片")
    @ApiOperation(value = "上传得分图片", notes = "上传得分图片")
    @ResponseBody
    public Object uploadImg(@RequestBody TUserAnalysisResult tUserAnalysisResult) {
        return tUserAnalysisResultService.uploadImg(tUserAnalysisResult);
    }

    @RequestMapping(value = "deleteLis", method = RequestMethod.GET)
    @LogAnnotation(value = "删除用户体质辨识结果", isWrite = true)
    @ApiOperation(value = "删除用户体质辨识结果", notes = "删除用户体质辨识结果")
    @ApiImplicitParam(value = "辨识id(如果有多个就用英文逗号分隔)", name = "ids", paramType = "query", dataType = "String", required = true)
    @ResponseBody
    public Object deleteLis(String ids) {
        return tUserAnalysisResultService.deleteLis(ids);
    }

    @RequestMapping(value = "print", method = RequestMethod.GET)
    @LogAnnotation(value = "打印用户体质辨识结果")
    @ApiOperation(value = "打印用户体质辨识结果", notes = "打印用户体质辨识结果")
    @ResponseBody
    public Object printResult(String analyId) {
        return tUserAnalysisResultService.printResult(analyId);
    }

    @RequestMapping(value = "download", method = RequestMethod.GET)
    @LogAnnotation(value = "下载用户体质辨识结果")
    @ApiOperation(value = "下载用户体质辨识结果", notes = "下载用户体质辨识结果")
    @ApiImplicitParams({
            @ApiImplicitParam(value = "辨识id", name = "analyId", paramType = "query", dataType = "String", required = true),
            @ApiImplicitParam(value = "文件类型：Word，PDF", name = "fileType", paramType = "query", dataType = "String", required = true)})
    @ResponseBody
    public void downloadResult(HttpServletRequest req, HttpServletResponse resp, String analyId, String fileType) {
        tUserAnalysisResultService.downloadResult(req, resp, analyId, fileType);
    }

}