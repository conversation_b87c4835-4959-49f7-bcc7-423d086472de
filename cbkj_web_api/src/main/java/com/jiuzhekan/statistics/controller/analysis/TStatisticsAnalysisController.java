package com.jiuzhekan.statistics.controller.analysis;

import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.statistics.beans.analysis.TStatisticsAnalysis;
import com.jiuzhekan.statistics.service.analysis.TStatisticsAnalysisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@Api(value = "体质辨识统计表接口", tags = "体质辨识统计表接口")
@RequestMapping("tStatisticsAnalysis")
public class TStatisticsAnalysisController {

    @Autowired
    private TStatisticsAnalysisService tStatisticsAnalysisService;


    @RequestMapping(value = "getPages", method = RequestMethod.GET)
    @ApiOperation(value = "分页查询体质辨识统计表", notes = "分页查询体质辨识统计表", response = TStatisticsAnalysis.class)
    @LogAnnotation("分页查询体质辨识统计表")
    @ResponseBody
    public Object getApps(TStatisticsAnalysis tStatisticsAnalysis, Page page){
        return tStatisticsAnalysisService.getPageDatas(tStatisticsAnalysis,page);
    }

    @RequestMapping(value = "test", method = RequestMethod.GET)
    @ApiOperation(value = "测试体质辨识统计", notes = "测试体质辨识统计", response = TStatisticsAnalysis.class)
    @LogAnnotation("测试体质辨识统计")
    @ResponseBody
    public Object getApps(String curDate,String timeDiff){
         tStatisticsAnalysisService.settStatisticsAnalysis(curDate,timeDiff);
         return  ResEntity.entity(true, Constant.SUCCESS_DX,null);
    }



}