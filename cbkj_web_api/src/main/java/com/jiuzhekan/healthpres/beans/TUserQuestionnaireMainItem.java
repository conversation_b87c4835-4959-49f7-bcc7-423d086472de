package com.jiuzhekan.healthpres.beans;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class TUserQuestionnaireMainItem implements Serializable{

    @ApiModelProperty(value = "")
    private Integer itemId;

    @ApiModelProperty(value = "1.纯文本2.下拉选择")
    private Integer itemType;

    @ApiModelProperty(value = "标题")
    private String itemTitle;

    @ApiModelProperty(value = "内容")
    private String itemContent;

    @ApiModelProperty(value = "下拉默认选择。")
    private String itemSelectDefaultId;

    @ApiModelProperty(value = "")
    private String questionnaireMainId;

    @ApiModelProperty(value = "排序（升序排序）")
    private Integer itemSort;

    @ApiModelProperty(value = "健康处方中是否展示：0前端展示1前端不展示")
    private Integer showType;


}
