package com.jiuzhekan.healthpres.beans;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class TUserQuestionnaireResultItem implements Serializable{

    @ApiModelProperty(value = "")
    private String resultItemId;

    @ApiModelProperty(value = "标题")
    private String resultItemTitle;

    @ApiModelProperty(value = "内容")
    private String resultItemContent;

    @ApiModelProperty(value = "1.纯文本2.下拉选择")
    private Integer resultItemType;

    @ApiModelProperty(value = "如果是下拉则这个字段会保存下拉的id用于回显")
    private String resultItemSelectId;

    @ApiModelProperty(value = "排序（升序排序）")
    private Integer resultItemSort;

    @ApiModelProperty(value = "健康处方中是否展示：0前端展示1前端不展示")
    private Integer showType;

    @ApiModelProperty(value = "")
    private String resultId;


}
