package com.jiuzhekan.appraisal.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/17 16:49
 * @Version 1.0
 */
@ApiModel
@Data
public class PerformanceSubClassDept {
    @ApiModelProperty(value = "科室代码")
    private String jiGouDM;

    @ApiModelProperty(value = "科室名称")
    private String jiGouMC;

    @ApiModelProperty(value = "科室代码")
    private String keShiDM;

    @ApiModelProperty(value = "科室名称")
    private String keShiMC;

    @ApiModelProperty(value = "科室下的所有指标以及对应指标值的列表")
    List<JxkhZibiaoSubClassRes> zibiaoList;
}
