package com.jiuzhekan.statistics.mapper.board;

import com.jiuzhekan.statistics.beans.board.TStatisticsPrescriptionMedication;
import com.jiuzhekan.statistics.beans.register.QueryParam;
import org.springframework.stereotype.Component;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;

@Component
public interface TStatisticsPrescriptionMedicationMapper extends BaseMapper<TStatisticsPrescriptionMedication>{

    public TStatisticsPrescriptionMedication getOneByObject(TStatisticsPrescriptionMedication tStatisticsPrescriptionMedication);

    /**
     * 查询安全用药提醒次数
     * @param queryParam
     * @return
     */
    public Integer sumStatistics(QueryParam queryParam);
}