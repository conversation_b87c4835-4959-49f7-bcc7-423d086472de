package com.jiuzhekan.appraisal.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class JxkhZibiao implements Serializable{

    @ApiModelProperty(value = "指标代码")
    private String zibiaodm;

    @ApiModelProperty(value = "指标名称")
    private String zibiaomc;

    @ApiModelProperty(value = "指标属性")
    private String zibiaosx;

    @ApiModelProperty(value = "计量单位")
    private String jiliangdw;

    @ApiModelProperty(value = "指标定义")
    private String zibiaody;

    @ApiModelProperty(value = "计算方法")
    private String jisuanff;

    @ApiModelProperty(value = "计算公式")
    private String jisuangs;

    @ApiModelProperty(value = "指标说明")
    private String zibiaosm;

    @ApiModelProperty(value = "")
    private Date xiugaisj;

    @ApiModelProperty(value = "0普通科室指标累加，1 科室代码是0的不累加指标取日期最大的那一条",hidden = true)
    private Integer zibiaolb;

    @ApiModelProperty(value = "指标代码：多个用逗号分隔",hidden = true)
    private String[] zibiaodms;

    @ApiModelProperty(value = "指标意义")
    private String significanceIndicators;
    @ApiModelProperty(value = "指标解释")
    private String explanationIndicators;
    @ApiModelProperty(value = "数据来源")
    private String dataSource;
    @ApiModelProperty(value = "指标导向")
    private String indicatorOriented;


}
