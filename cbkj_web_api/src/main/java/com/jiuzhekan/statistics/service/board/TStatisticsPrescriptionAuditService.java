package com.jiuzhekan.statistics.service.board;

import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.github.pagehelper.PageHelper;
import com.jiuzhekan.statistics.beans.board.TStatisticsPrescriptionAudit;
import com.jiuzhekan.statistics.beans.board.TStatisticsPrescriptionType;
import com.jiuzhekan.statistics.beans.register.QueryParam;
import com.jiuzhekan.statistics.mapper.board.TStatisticsPrescriptionAuditMapper;
import com.jiuzhekan.statistics.utils.DateUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
public class TStatisticsPrescriptionAuditService {

    @Autowired
    private TStatisticsPrescriptionAuditMapper tStatisticsPrescriptionAuditMapper;

    /**
     * 加载分页数据
     *
     * @param tStatisticsPrescriptionAudit 中药审方数量
     * @param page 分页
     * @return Object
     * <AUTHOR>
     * @date 2022-10-09
     */
    public Object getPageDatas(TStatisticsPrescriptionAudit tStatisticsPrescriptionAudit, Page page) {

        PageHelper.startPage(page.getPage(), page.getLimit());
        List<TStatisticsPrescriptionAudit> list = tStatisticsPrescriptionAuditMapper.getPageListByObj(tStatisticsPrescriptionAudit);
        return Page.getLayUiTablePageData(list);
    }

    /**
     * 加载某条数据
     *
     * @param id 中药审方数量
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-10-09
     */
    public ResEntity findObj(String id) {

        if (StringUtils.isBlank(id)) {
            return ResEntity.entity(false, "参数不能为空哦", null);
        }
        TStatisticsPrescriptionAudit tStatisticsPrescriptionAudit = tStatisticsPrescriptionAuditMapper.getObjectById(id);
        return ResEntity.entity(true, Constant.SUCCESS_DX, tStatisticsPrescriptionAudit);
    }


    /**
     * 插入新数据
     *
     * @param tStatisticsPrescriptionAudit 中药审方数量
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-10-09
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insert(TStatisticsPrescriptionAudit tStatisticsPrescriptionAudit){

        long rows = tStatisticsPrescriptionAuditMapper.insert(tStatisticsPrescriptionAudit);

        return ResEntity.success(tStatisticsPrescriptionAudit);
    }


    /**
     * 修改
     *
     * @param tStatisticsPrescriptionAudit 中药审方数量
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-10-09
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity update(TStatisticsPrescriptionAudit tStatisticsPrescriptionAudit) {

        long rows = tStatisticsPrescriptionAuditMapper.updateByPrimaryKey(tStatisticsPrescriptionAudit);

        return ResEntity.success(tStatisticsPrescriptionAudit);
    }

    /**
     * 删除
     *
     * @param ids ids
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-10-09
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity deleteLis(String ids){
        if (StringUtils.isBlank(ids)) {
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }
        long rowsR = tStatisticsPrescriptionAuditMapper.deleteBylist(ids.split(","));

        return ResEntity.success(rowsR);
    }

    /**
     * 统计 中药审方数量：系统审方+人工审方之和，统计所有状态处方
     */
    @Transactional(rollbackFor = Exception.class)
    public void settStatisticsPrescriptionAudit(int diff) {
        Map<String, Object> param = new HashMap<>(16);
        String curDate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
        SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd");
        param.put("curDate",curDate );
        param.put("diff",diff );
        List<TStatisticsPrescriptionAudit> staticsResult = tStatisticsPrescriptionAuditMapper.getStaticsResult(param);
        List<TStatisticsPrescriptionAudit> insertRe = new ArrayList<TStatisticsPrescriptionAudit>();
        List<TStatisticsPrescriptionAudit> updateRe = new ArrayList<TStatisticsPrescriptionAudit>();
        staticsResult.forEach(tStatisticsPrescriptionType ->
                {
                    if (null != tStatisticsPrescriptionType) {
                        if (null != tStatisticsPrescriptionType.getId()){
                            updateRe.add(tStatisticsPrescriptionType);
                        }else {
                            tStatisticsPrescriptionType.setInsertDate(new Date());
                            try {
                                tStatisticsPrescriptionType.setCreateDate(sdf2.parse(curDate));
                            } catch (ParseException e) {
                                throw new RuntimeException(e);
                            }
                            insertRe.add(tStatisticsPrescriptionType);
                        }
                    }
                }
        );
        if (insertRe.size()>0){
            tStatisticsPrescriptionAuditMapper.insertList(insertRe);
        }
        for (TStatisticsPrescriptionAudit tStatisticsPrescriptionType : updateRe) {
            tStatisticsPrescriptionAuditMapper.updateByPrimaryKey(tStatisticsPrescriptionType);
        }
    }

    public int getData(QueryParam queryParam){
        Integer i = tStatisticsPrescriptionAuditMapper.sumStatistics(queryParam);
        return null == i ? 0 : i;
    }

}
