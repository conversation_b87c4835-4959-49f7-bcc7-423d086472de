package com.jiuzhekan.statistics.beans.register;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 **/
@Data
public class QueryResult {
    private String appId;
    private String insCode;
    private String userId;
    private String userName;


    private int sumResult;
    private int sumResult2;


}
