package com.jiuzhekan.statistics.beans.board;

import com.jiuzhekan.statistics.utils.DateUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class TStatisticsHealthCare implements Serializable{

    @ApiModelProperty(value = "自增主键")
    private Integer id;

    @ApiModelProperty(value = "")
    private String appId;

    @ApiModelProperty(value = "")
    private String appName;

    @ApiModelProperty(value = "")
    private String insCode;

    @ApiModelProperty(value = "")
    private String insName;

    @ApiModelProperty(value = "")
    private String deptId;



    @ApiModelProperty(value = "统计日期")
    private Date createDate;

    @ApiModelProperty(value = "插入时间")
    private Date insertDate;

    @ApiModelProperty(value = "")
    private String ypdmHis;

    @ApiModelProperty(value = "提醒次数")
    private Integer remindTimes;


    public TStatisticsHealthCare initDate(TStatisticsHealthCare tStatisticsHealthCare) {
        this.appId = tStatisticsHealthCare.getAppId();
        this.insCode = tStatisticsHealthCare.getInsCode();
        this.ypdmHis = tStatisticsHealthCare.getYpdmHis();
        SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd");
        try {
            this.createDate = sdf2.parse(DateUtils.getWeekEndTim2e(0, true));
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        this.insertDate = new Date();
        this.remindTimes = 0;
        return this;
    }
}
