package com.jiuzhekan.cbkj.service.video;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jiuzhekan.cbkj.beans.business.record.TRecord;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.mapper.business.patients.TPatientsMapper;
import com.jiuzhekan.cbkj.mapper.business.record.TRecordMapper;
import com.jiuzhekan.cbkj.service.business.record.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/6/24 11:26
 * @Version 1.0
 */
@Slf4j
@Service
public class VideoDataPush {
    @Value("${jwt.token}")
    private String tokenHeader;

    @Value("${remote.consultation.url:https://************:83/pre_ai/}")
    private String videoUrl;
    @Autowired
    private TMedicalRecordSourceService tMedicalRecordSourceService;
    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private TPatientsMapper tPatientsMapper;
    @Autowired
    private TLookOverService tLookOverService;
    @Autowired
    private TAskService tAskService;
    @Autowired
    private TCutService tCutService;
    @Autowired
    private TSmellService tSmellService;
    @Autowired
    private TRecordMapper tRecordMapper;
    @Async
    public void dataPush(String recId, String registerId) {
        TRecord tRecord = null;
        if (!StringUtils.isBlank(recId)) {
            tRecord = tRecordMapper.getRecInsById(recId);
        } else if (!StringUtils.isBlank(registerId)) {
            tRecord = tRecordMapper.getRecordByRegisterId(registerId);
        }

        if (null == tRecord) {
            return;
        }
        ObjectMapper objectMapper = new ObjectMapper();
        Map map = objectMapper.convertValue(tRecord, Map.class);


        HashMap<String, String> mapValues = tMedicalRecordSourceService.joiningTogetherFourDiagnosisV2(tLookOverService.findByRecId(tRecord.getRecId()),
                tSmellService.findByRecId(tRecord.getRecId()), tAskService.findByRecId(tRecord.getRecId()),
                tCutService.findByRecId(tRecord.getRecId()));
        map.putAll(mapValues);

        //数据推送
        post("rpcVideo/record/data/push", map);


    }

    private ResEntity post(String path, Map params) {

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);

        HttpEntity<Map> request = new HttpEntity<>(params, headers);

        ResEntity result;
        try {
            result = restTemplate.postForEntity(videoUrl + path, request, ResEntity.class).getBody();
            log.info("【远程会诊平台接口】成功 --- URL:{}, request:{}, response:{}", videoUrl + path, request, result);
        } catch (Exception e) {
            log.error("【远程会诊平台接口】异常 --- URL:{}, request:{}, error:{}", videoUrl + path, request, e);
            result = ResEntity.error("【远程会诊平台接口】异常：" + e.getMessage());
        }

        return result;
    }
}
