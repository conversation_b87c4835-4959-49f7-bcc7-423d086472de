package com.jiuzhekan.cbkj.common.scheduler;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.util.StringUtil;
import com.jiuzhekan.cbkj.beans.statistics.TStatisticsPrescriptionExpand;
import com.jiuzhekan.cbkj.beans.sysApp.SysApp;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.http.PlatformRestTemplate;
import com.jiuzhekan.cbkj.common.utils.DateUtil;
import com.jiuzhekan.cbkj.common.utils.RedisLockUtil;
import com.jiuzhekan.cbkj.mapper.statistics.TStatisticsMapper;
import com.jiuzhekan.cbkj.mapper.statistics.TStatisticsPrescriptionExpandMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * @Auther: guowei
 * @Date: 2020/9/1 10:40
 * @Description:
 */
@Slf4j
@Component
@EnableScheduling
@Transactional(rollbackFor = Exception.class)
public class StatisticsScheduler {

    @Autowired
    private TStatisticsMapper tStatisticsMapper;
    @Autowired
    private RedisLockUtil redisLockUtil;
    @Autowired
    private TStatisticsPrescriptionExpandMapper tStatisticsPrescriptionExpandMapper;
    @Autowired
    private PlatformRestTemplate platformRestTemplate;


    /**
     * 统计高发疾病
     *
     * <AUTHOR>
     * @date 2021/2/25
     */
//    @Scheduled(cron = "0 */1 * * * ?") //测试环境下定成1分钟
    @Scheduled(cron = "0 20 0 * * ?")
    public void statisticsTop5DisName() {

        String lockKey = "STATISTIC_TOP5_DISNAME";
        String lockValue = String.valueOf(Thread.currentThread().getId());
        log.info("-------" + lockKey + "------");
//        if (redisLockUtil.lock(lockKey, lockValue, 600)) {
            log.info("-------" + lockKey + "  BEGAN------");

            Map<String, Object> param = new HashMap<>();
            param.put("timeType", "1");
            param.put("timeDiff", 1);
            tStatisticsMapper.statisticsTop5DisName(param);
            param.put("timeType", "2");
            param.put("timeDiff", 15);
            tStatisticsMapper.statisticsTop5DisName(param);
            param.put("timeType", "3");
            param.put("timeDiff", 90);
            tStatisticsMapper.statisticsTop5DisName(param);
            param.put("timeType", "4");
            param.put("timeDiff", 365);
            tStatisticsMapper.statisticsTop5DisName(param);
            param.put("timeType", "5");
            param.put("timeDiff", 180);
            tStatisticsMapper.statisticsTop5DisName(param);

            log.info("-------" + lockKey + "  END------");
//        }
    }

    /**
     * 统计证型分布
     *
     * <AUTHOR>
     * @date 2021/2/25
     */
    //    @Scheduled(cron = "0 */1 * * * ?") //测试环境下定成1分钟
    @Scheduled(cron = "0 25 0 * * ?")
    public void statisticsTop5SymName() {

        String lockKey = "STATISTIC_TOP5_SYMNAME";
        String lockValue = String.valueOf(Thread.currentThread().getId());
        log.info("-------" + lockKey + "------");
//        if (redisLockUtil.lock(lockKey, lockValue, 600)) {
            log.info("-------" + lockKey + "  BEGAN------");

            Map<String, Object> param = new HashMap<>();
            param.put("timeType", "1");
            param.put("timeDiff", 1);
            tStatisticsMapper.statisticsTop5SymName(param);
            param.put("timeType", "2");
            param.put("timeDiff", 15);
            tStatisticsMapper.statisticsTop5SymName(param);
            param.put("timeType", "3");
            param.put("timeDiff", 90);
            tStatisticsMapper.statisticsTop5SymName(param);
            param.put("timeType", "4");
            param.put("timeDiff", 365);
            tStatisticsMapper.statisticsTop5SymName(param);

            log.info("-------" + lockKey + "  END------");
//        }
    }

    /**
     * 统计疾病年龄分布
     *
     * <AUTHOR>
     * @date 2021/2/25
     */
//   @Scheduled(cron = "0 */1 * * * ?") //测试环境下定成1分钟
    @Scheduled(cron = "0 35 0 * * ?")
    public void statisticsTop5DisNameByAge() {

        String lockKey = "STATISTIC_TOP5_DISNAME_AGE";
        String lockValue = String.valueOf(Thread.currentThread().getId());
        log.info("-------" + lockKey + "------");
//        if (redisLockUtil.lock(lockKey, lockValue, 600)) {
            log.info("-------" + lockKey + "  BEGAN------");

            Map<String, Object> param = new HashMap<>();
            param.put("timeType", "1");
            param.put("timeDiff", 1);
            tStatisticsMapper.statisticsTop5DisNameByAge(param);
            param.put("timeType", "2");
            param.put("timeDiff", 15);
            tStatisticsMapper.statisticsTop5DisNameByAge(param);
            param.put("timeType", "3");
            param.put("timeDiff", 90);
            tStatisticsMapper.statisticsTop5DisNameByAge(param);
            param.put("timeType", "4");
            param.put("timeDiff", 365);
            tStatisticsMapper.statisticsTop5DisNameByAge(param);

            log.info("-------" + lockKey + "  END------");
//        }
    }

    /**
     * 统计上升趋势前五疾病
     *
     * <AUTHOR> todo 海盐慢sql
     * @date 2021/2/25
     */
//    @Scheduled(cron = "0 */1 * * * ?")//测试环境下定成1分钟
    @Scheduled(cron = "0 45 0 * * ?")
    public void statisticsTop5DisNameByGoUp() {

        String lockKey = "STATISTIC_TOP5_DISNAME_GOUP";
        String lockValue = String.valueOf(Thread.currentThread().getId());
        log.info("-------" + lockKey + "------");
//        if (redisLockUtil.lock(lockKey, lockValue, 600)) {
            log.info("-------" + lockKey + "  BEGAN------");


            List<Map<String, Object>> rateList = new ArrayList<>();

            //从综合平台获取医联体
            ResEntity resEntity = platformRestTemplate.post("app/list", new HashMap<>());
            if (resEntity.getStatus()) {
                List<SysApp> appList = JSONArray.parseArray(JSONArray.toJSONString(resEntity.getData()), SysApp.class);
                for (SysApp app : appList) {
                    String appId = app.getAppId();
                    rateList.addAll(statisticsTop5DisNameByGoUp(appId, null,null));
                }
            }
            if (rateList.size() > 0) {
                tStatisticsMapper.saveStatisticsTop5DisNameByGoUp(rateList);
            }

            log.info("-------" + lockKey + "  END------");
//        }
    }

    /**
     * 统计上升趋势前五疾病
     *
     * <AUTHOR>
     * @date 2024/11/13
     */
    public void top5DisNameCompensate(String statisticsDate) {

        String lockKey = "STATISTIC_TOP5_DISNAME_GOUP_COMPENSATE";
        String lockValue = String.valueOf(Thread.currentThread().getId());
        log.info("-------" + lockKey + "------");
        if (redisLockUtil.lock(lockKey, lockValue, 600)) {
            log.info("-------" + lockKey + "  BEGAN------");


            List<Map<String, Object>> rateList = new ArrayList<>();

            //从综合平台获取医联体
            ResEntity resEntity = platformRestTemplate.post("app/list", new HashMap<>());
            if (resEntity.getStatus()) {
                List<SysApp> appList = JSONArray.parseArray(JSONArray.toJSONString(resEntity.getData()), SysApp.class);
                for (SysApp app : appList) {
                    String appId = app.getAppId();
                    rateList.addAll(statisticsTop5DisNameByGoUp(appId, null,statisticsDate));
                }
            }
            if (rateList.size() > 0) {
                tStatisticsMapper.saveTop5DisNameCompensate(rateList,statisticsDate);
            }

            log.info("-------" + lockKey + "  END------");
        }
    }

    private List<Map<String, Object>> statisticsTop5DisNameByGoUp(String appId, String insCode,String statisticsDate) {

        Map<String, Object> param = new HashMap<>();
        param.put("create_time", new Date());
        param.put("appId", appId);
        param.put("statisticsDate", statisticsDate);
        //param.put("insCode", insCode);

        param.put("timeSplit", new Integer[]{1, 2, 3, 4, 5, 6, 7});
        param.put("timeDiff", 1);
        param.put("timeType", 1);
        List<Map<String, Object>> goUp1 = statisticsTop5DisNameByGoUp(param);
        List<Map<String, Object>> rateList = new ArrayList<>(goUp1);

        param.put("timeSplit", new Integer[]{1, 2, 3, 4, 5, 6, 7});
        param.put("timeDiff", 2);
        param.put("timeType", 2);
        List<Map<String, Object>> goUp2 = statisticsTop5DisNameByGoUp(param);
        rateList.addAll(goUp2);

        param.put("timeSplit", new Integer[]{1, 2, 3, 4, 5, 6});
        param.put("timeDiff", 15);
        param.put("timeType", 3);
        List<Map<String, Object>> goUp3 = statisticsTop5DisNameByGoUp(param);
        rateList.addAll(goUp3);

        param.put("timeSplit", new Integer[]{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12});
        param.put("timeDiff", 30);
        param.put("timeType", 4);
        List<Map<String, Object>> goUp4 = statisticsTop5DisNameByGoUp(param);
        rateList.addAll(goUp4);


        return rateList;
    }

    private List<Map<String, Object>> statisticsTop5DisNameByGoUp(Map<String, Object> param) {
        List<Map<String, Object>> goUpList;
        String date =(String) param.get("statisticsDate");
        if(StringUtil.isNotEmpty(date)){
            goUpList = tStatisticsMapper.top5DisNameCompensate(param);
        }else {
            goUpList = tStatisticsMapper.statisticsTop5DisNameByGoUp(param);
        }


//        List<Map<String, Object>> rateList = new ArrayList<>();
//        BigDecimal zero = new BigDecimal("0");

//        for (Map<String, Object> map : goUpList) {
//            String disName = map.get("dis_name").toString();
//            String rateName = map.get("rate").toString();
//            if (StringUtils.isNotBlank(disName) && StringUtils.isNotBlank(rateName)) {
//                BigDecimal rate = new BigDecimal(rateName).setScale(0, BigDecimal.ROUND_HALF_UP);
//                if (rate.compareTo(zero) > 0) {
//                    map.put("time_type", 1);
//                    map.put("create_time", param.get("create_time"));
//                    rateList.add(map);
//                }
//            }
//            if (rateList.size() >= 5) {
//                break;
//            }
//        }
        return goUpList;
    }

    /**
     * 统计性别分布
     *
     * <AUTHOR>
     * @date 2021/2/25
     */
//       @Scheduled(cron = "0 */1 * * * ?") //测试环境下定成1分钟
    @Scheduled(cron = "0 50 0 * * ?")
    public void statisticsTop5DisNameByGender() {

        String lockKey = "STATISTIC_TOP5_DISNAME_GENDER";
        String lockValue = String.valueOf(Thread.currentThread().getId());
        log.info("-------" + lockKey + "------");
        if (redisLockUtil.lock(lockKey, lockValue, 600)) {
            log.info("-------" + lockKey + "  BEGAN------");

            Map<String, Object> param = new HashMap<>();
            param.put("timeType", "1");
            param.put("timeDiff", 1);
            tStatisticsMapper.statisticsTop5DisNameByGender(param);
            param.put("timeType", "2");
            param.put("timeDiff", 15);
            tStatisticsMapper.statisticsTop5DisNameByGender(param);
            param.put("timeType", "3");
            param.put("timeDiff", 90);
            tStatisticsMapper.statisticsTop5DisNameByGender(param);
            param.put("timeType", "4");
            param.put("timeDiff", 365);
            tStatisticsMapper.statisticsTop5DisNameByGender(param);

            log.info("-------" + lockKey + "  END------");
        }
    }

    /**
     * 统计节气分布
     *
     * <AUTHOR>
     * @date 2021/2/25
     */
    @Scheduled(cron = "0 50 0 * * ?")
    public void statisticsTop5DisNameByTermName() {

        String lockKey = "STATISTICS_TOP5_DISNAME_TERM";
        String lockValue = String.valueOf(Thread.currentThread().getId());
        log.info("-------" + lockKey + "------");
        if (redisLockUtil.lock(lockKey, lockValue, 600)) {
            log.info("-------" + lockKey + "  BEGAN------");

            String year = DateUtil.getDateFormats(DateUtil.YYYY, new Date());

            switch (DateUtil.getDateFormats(DateUtil.M_D, new Date())) {
                case "1.6":
                    Calendar calendar = Calendar.getInstance();
                    calendar.add(Calendar.YEAR, -1);
                    String beginTime = DateUtil.getDateFormats(DateUtil.YYYY, calendar.getTime()) + "-12-21";
                    statisticsTop5DisNameByTermName("冬至", beginTime, year + "-01-06");
                    break;
                case "1.20":
                    statisticsTop5DisNameByTermName("小寒", year + "-01-06", year + "-01-20");
                    break;
                case "2.4":
                    statisticsTop5DisNameByTermName("大寒", year + "-01-20", year + "-02-04");
                    break;
                case "2.19":
                    statisticsTop5DisNameByTermName("立春", year + "-02-04", year + "-02-19");
                    break;
                case "3.5":
                    statisticsTop5DisNameByTermName("雨水", year + "-02-19", year + "-03-05");
                    break;
                case "3.20":
                    statisticsTop5DisNameByTermName("惊蛰", year + "-03-05", year + "-03-20");
                    break;
                case "4.4":
                    statisticsTop5DisNameByTermName("春分", year + "-03-20", year + "-04-04");
                    break;
                case "4.19":
                    statisticsTop5DisNameByTermName("清明", year + "-04-04", year + "-04-19");
                    break;
                case "5.5":
                    statisticsTop5DisNameByTermName("谷雨", year + "-04-19", year + "-05-05");
                    break;
                case "5.20":
                    statisticsTop5DisNameByTermName("立夏", year + "-05-05", year + "-05-20");
                    break;
                case "6.5":
                    statisticsTop5DisNameByTermName("小满", year + "-05-20", year + "-06-05");
                    break;
                case "6.21":
                    statisticsTop5DisNameByTermName("芒种", year + "-06-05", year + "-06-21");
                    break;
                case "7.6":
                    statisticsTop5DisNameByTermName("夏至", year + "-06-21", year + "-07-06");
                    break;
                case "7.22":
                    statisticsTop5DisNameByTermName("小暑", year + "-07-06", year + "-07-22");
                    break;
                case "8.7":
                    statisticsTop5DisNameByTermName("大暑", year + "-07-22", year + "-08-07");
                    break;
                case "8.22":
                    statisticsTop5DisNameByTermName("立秋", year + "-08-07", year + "-08-22");
                    break;
                case "9.7":
                    statisticsTop5DisNameByTermName("处暑", year + "-08-22", year + "-09-07");
                    break;
                case "9.22":
                    statisticsTop5DisNameByTermName("白露", year + "-09-07", year + "-09-22");
                    break;
                case "10.8":
                    statisticsTop5DisNameByTermName("秋分", year + "-09-22", year + "-10-08");
                    break;
                case "10.23":
                    statisticsTop5DisNameByTermName("寒露", year + "-10-08", year + "-10-23");
                    break;
                case "11.07":
                    statisticsTop5DisNameByTermName("霜降", year + "-10-23", year + "-11-07");
                    break;
                case "11.22":
                    statisticsTop5DisNameByTermName("小雪", year + "-11-07", year + "-11-22");
                    break;
                case "12.7":
                    statisticsTop5DisNameByTermName("小雪", year + "-11-22", year + "-12-07");
                    break;
                case "12.21":
                    statisticsTop5DisNameByTermName("大雪", year + "-12-07", year + "-12-21");
                    break;
                default:
                    break;
            }
            log.info("-------" + lockKey + "  END------");
        }
    }

    private void statisticsTop5DisNameByTermName(String terms, String beginTime, String endTime) {
        Map<String, Object> param = new HashMap<>();
        param.put("terms", terms);
        param.put("beginTime", beginTime);
        param.put("endTime", endTime);
        tStatisticsMapper.statisticsTop5DisNameByTermName(param);
        param.clear();
        param = null;
    }

    /**
     * 统计月均贴金额
     *
     * <AUTHOR>
     * @date 2021/1/6
     */
    @Scheduled(cron = "0 10 0 * * ?")
    public void statisticMonthlyAvgAmount() {

        String lockKey = "STATISTIC_MONTHLY_AVG_AMOUNT";
        String lockValue = String.valueOf(Thread.currentThread().getId());
        log.info("-------" + lockKey + "------");
        if (redisLockUtil.lock(lockKey, lockValue, 600)) {
            log.info("-------" + lockKey + "  BEGAN------");
            tStatisticsMapper.statisticsAvgAmount();
            log.info("-------" + lockKey + "  END------");
        }
    }


    /**
     * 更新医生协定方统计数据
     * zhaolei
     */
//    @Scheduled(cron = "*/15 * * * * ?") //测试环境下定成15s
    @Scheduled(cron = "0 0 1 * * ?")
    public void statisticsPrescriptionExpand() {
        String lockKey = "STATISTIC_PRESCRIPTION_EXPAND";
        String lockValue = String.valueOf(Thread.currentThread().getId());
        log.info("-------" + lockKey + "------");
        if (redisLockUtil.lock(lockKey, lockValue, 600)) {
            log.info("-------" + lockKey + "  BEGAN------");
            List<String> delDoctorList = new ArrayList<String>();
            List<TStatisticsPrescriptionExpand> newPerList = new ArrayList<TStatisticsPrescriptionExpand>();
            List<TStatisticsPrescriptionExpand> doctorList = tStatisticsPrescriptionExpandMapper.getStatisticsPrescriptionDoctorList();
            if (doctorList != null && doctorList.size() > 0) {
                for (TStatisticsPrescriptionExpand s : doctorList) {
                    if (StringUtil.isNotEmpty(s.getDoctorId())) {
                        List<TStatisticsPrescriptionExpand> preList = tStatisticsPrescriptionExpandMapper.getStatisticsPrescriptionListbyDoctorId(s.getDoctorId());
                        if (preList != null && preList.size() > 0) {
                            delDoctorList.add(s.getDoctorId());
                            newPerList.addAll(preList);
                            preList.clear();
                        }
                    }
                }
                doctorList.clear();
                if (delDoctorList != null && delDoctorList.size() > 0) {
                    tStatisticsPrescriptionExpandMapper.delStatisticsPrescriptionByDoctorList(delDoctorList);
                    delDoctorList.clear();
                }
                if (newPerList != null && newPerList.size() > 0) {
                    tStatisticsPrescriptionExpandMapper.insertList(newPerList);
                    newPerList.clear();
                }
            }
            log.info("-------" + lockKey + "  END------");
        }
    }
}
