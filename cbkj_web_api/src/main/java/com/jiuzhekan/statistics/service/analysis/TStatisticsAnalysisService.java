package com.jiuzhekan.statistics.service.analysis;

import com.github.pagehelper.PageHelper;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.statistics.beans.analysis.StatisticsResult;
import com.jiuzhekan.statistics.beans.analysis.TStatisticsAnalysis;
import com.jiuzhekan.statistics.mapper.analysis.TStatisticsAnalysisMapper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.*;

@Service
public class TStatisticsAnalysisService {

    @Autowired
    private TStatisticsAnalysisMapper tStatisticsAnalysisMapper;

    /**
     * 加载分页数据
     *
     * @param tStatisticsAnalysis 体质辨识统计表
     * @param page 分页
     * @return Object
     * <AUTHOR>
     * @date 2022-05-23
     */
    public Object getPageDatas(TStatisticsAnalysis tStatisticsAnalysis, Page page) {

        PageHelper.startPage(page.getPage(), page.getLimit());
        List<TStatisticsAnalysis> list = tStatisticsAnalysisMapper.getPageListByObj(tStatisticsAnalysis);
        return Page.getLayUiTablePageData(list);
    }

    /**
     * 加载某条数据
     *
     * @param id 体质辨识统计表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-05-23
     */
    public ResEntity findObj(String id) {

        if (StringUtils.isBlank(id)) {
            return ResEntity.entity(false, "参数不能为空哦", null);
        }
        TStatisticsAnalysis tStatisticsAnalysis = tStatisticsAnalysisMapper.getObjectById(id);
        return ResEntity.entity(true, Constant.SUCCESS_DX, tStatisticsAnalysis);
    }


    /**
     * 插入新数据
     *
     * @param tStatisticsAnalysis 体质辨识统计表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-05-23
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insert(TStatisticsAnalysis tStatisticsAnalysis){

        long rows = tStatisticsAnalysisMapper.insert(tStatisticsAnalysis);

        return ResEntity.success(tStatisticsAnalysis);
    }


    /**
     * 修改
     *
     * @param tStatisticsAnalysis 体质辨识统计表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-05-23
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity update(TStatisticsAnalysis tStatisticsAnalysis) {

        long rows = tStatisticsAnalysisMapper.updateByPrimaryKey(tStatisticsAnalysis);

        return ResEntity.success(tStatisticsAnalysis);
    }

    /**
     * 删除
     *
     * @param ids ids
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-05-23
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity deleteLis(String ids){
        if (StringUtils.isBlank(ids)) {
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }
        long rowsR = tStatisticsAnalysisMapper.deleteBylist(ids.split(","));

        return ResEntity.success(rowsR);
    }


    /**
     * 统计-体制辨识
     * @param timeDiff
     */
    @Transactional(rollbackFor = Exception.class)
    public void settStatisticsAnalysis(String curDate,String timeDiff){
        Map<String, Object> param = new HashMap<>(16);
        //传1是昨天。2前天。0今天。-1明天
        if (StringUtils.isBlank(timeDiff)){
            timeDiff = "1";
        }
        param.put("timeDiff", timeDiff);
        if (StringUtils.isBlank(curDate)){
            param.put("curDate", new SimpleDateFormat("yyyy-MM-dd").format(new Date()).toString());
        }else {
            param.put("curDate",curDate);
        }
        List<StatisticsResult> insertRe = new ArrayList<StatisticsResult>();
        List<StatisticsResult> updateRe = new ArrayList<StatisticsResult>();
        //统计到所有，包括已经统计的。
        List<StatisticsResult> list = tStatisticsAnalysisMapper.selectStatisticsResult(param);
        for (StatisticsResult statisticsResult : list) {
            if (null == statisticsResult.getId()){
                insertRe.add(statisticsResult);
            }else {
                if (statisticsResult.getNumber() != statisticsResult.getUsageTimes()){
                    updateRe.add(statisticsResult);
                }
            }
        }
        if (insertRe.size()>0){
            tStatisticsAnalysisMapper.insertStatistics(insertRe);
        }
        if (updateRe.size()>0){
            //sql中的参数不能多不能少，否则将新增！
            tStatisticsAnalysisMapper.replaceStatistics(updateRe);
        }
    }

    /**
     * 删除昨天的体制辨识
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteStatisticsAnalysis(){
        List<StatisticsResult> statisticsResults = tStatisticsAnalysisMapper.selectStatisticsResult2();
        statisticsResults.forEach(a->{
            if (null != a.getId()){
                TStatisticsAnalysis objectById = tStatisticsAnalysisMapper.getObjectById(a.getId() + "");
                objectById.setUsageTimes(objectById.getUsageTimes()-a.getNumber());
                tStatisticsAnalysisMapper.updateByPrimaryKey(objectById);
            }
        });

    }

}
