package com.jiuzhekan.cbkj.service.myData;

import com.github.pagehelper.PageHelper;
import com.jiuzhekan.cbkj.beans.myData.TPersonalPrescriptionFmap;
import com.jiuzhekan.cbkj.beans.myData.TPersonalPrescriptionFolder;
import com.jiuzhekan.cbkj.beans.sysBeans.AdminInfo;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.beans.sysExt.SysAdminInfoex;
import com.jiuzhekan.cbkj.common.http.KnowRestTemplate;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.mapper.myData.TPersonalPrescriptionFmapMapper;
import com.jiuzhekan.cbkj.mapper.myData.TPersonalPrescriptionFolderMapper;
import com.jiuzhekan.cbkj.service.business.TSysParamService;
import com.jiuzhekan.cbkj.service.redis.ClientRedisService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
public class TPersonalPrescriptionFolderService {

    @Autowired
    private KnowRestTemplate knowRestTemplate;
    @Autowired
    private TPersonalPrescriptionFolderMapper tPersonalPrescriptionFolderMapper;
    @Autowired
    private TPersonalPrescriptionFmapMapper tPersonalPrescriptionFmapMapper;
    @Autowired
    private ClientRedisService clientRedisService;
    @Autowired
    private TSysParamService tSysParamService;

    public List<SysAdminInfoex> getShareType() {

        List<SysAdminInfoex> list = new ArrayList<>();

        String share = AdminUtils.getCurrentHr().getAdminInfoEx().getPrescriptionShare();

        if (StringUtils.isNotBlank(share)) {
            for (String s : share.split(Constant.ENGLISH_COMMA)) {
                list.add(new SysAdminInfoex(s, Constant.PERSONAL_SHARE_TEXT[Integer.parseInt(s)]));
            }
        }

        if (list.size() == 0) {
            SysAdminInfoex infoex = new SysAdminInfoex();
            infoex.setUserExType(3);
            infoex.setUserExContent(Constant.BASIC_STRING_ZERO);
            infoex.setUserExText(Constant.PERSONAL_SHARE_TEXT[0]);
            list.add(infoex);
        } else {
            list.sort((ex1, ex2) -> ex2.getUserExContent().compareTo(ex1.getUserExContent()));
        }
        return list;
    }

    /**
     * 加载分页数据
     *
     * @param tPersonalPrescriptionFolder tPersonalPrescriptionFolder
     * @param page                        page
     * @return Object
     * <AUTHOR>
     * @date 2021-03-31
     */
    public Object getPageDatas(TPersonalPrescriptionFolder tPersonalPrescriptionFolder, Page page) {

        PageHelper.startPage(page.getPage(), page.getLimit());
        List<TPersonalPrescriptionFolder> list = tPersonalPrescriptionFolderMapper.getPageListByObj(tPersonalPrescriptionFolder);
        return Page.getLayUiTablePageData(list);
    }

    /**
     * 加载某条数据
     *
     * @param folderId folderId
     * @return ResEntity
     * <AUTHOR>
     * @date 2021-03-31
     */
    public ResEntity findObj(String folderId) {

        if (StringUtils.isBlank(folderId)) {
            return new ResEntity(false, "参数不能为空哦", null);
        }
        TPersonalPrescriptionFolder tPersonalPrescriptionFolder = tPersonalPrescriptionFolderMapper.getObjectById(folderId);
        return new ResEntity(true, Constant.SUCCESS_DX, tPersonalPrescriptionFolder);
    }

    /**
     * 包含方剂的文件夹树
     *
     * @param preType 1内服 2外用 3中成药方 4适宜技术方
     * @return java.util.List<com.jiuzhekan.cbkj.beans.myData.TPersonalPrescriptionFolder>
     * <AUTHOR>
     * @date 2021/4/1
     */
    public List<TPersonalPrescriptionFolder> getKnowFolderTree(String preType) {

        List<TPersonalPrescriptionFolder> list = new ArrayList<>();

        TPersonalPrescriptionFolder know = new TPersonalPrescriptionFolder().knowFolder();

        if (Constant.BASIC_STRING_ONE.equals(preType)) {
            ResEntity entity = knowRestTemplate.postKnow("dic/category", new HashMap<>());
            if (entity.getStatus()) {
                Map<String, List<Map<String, String>>> categorys = (Map<String, List<Map<String, String>>>) entity.getData();
                for (Map<String, String> category : categorys.get("categorys")) {
                    TPersonalPrescriptionFolder fo = new TPersonalPrescriptionFolder();
                    fo.setFolderId("know-" + category.get("id"));
                    fo.setFolderName(category.get("name"));
                    know.getChildren().add(fo);
                }
            }
        }
        list.add(know);

        list.addAll(getShowFolderTree());

        String APPOINT_AS_FORMULA = tSysParamService.getSysParam(Constant.APPOINT_AS_FORMULA).getParValues();
        if (Constant.APPOINT_AS_FORMULA.equals(APPOINT_AS_FORMULA)) {

            list.add(new TPersonalPrescriptionFolder().formulaFolder());
        }
        return list;
    }

    /**
     * 获取医生可以查看的文件夹树
     *
     * @return java.util.List<com.jiuzhekan.cbkj.beans.myData.TPersonalPrescriptionFolder>
     * <AUTHOR>
     * @date 2021/4/1
     */
    public List<TPersonalPrescriptionFolder> getShowFolderTree() {

        List<TPersonalPrescriptionFolder> list = new ArrayList<>();

        TPersonalPrescriptionFolder self = new TPersonalPrescriptionFolder().selfFolder();
        self.getChildren().addAll(getSelfFolders());
        list.add(self);

        TPersonalPrescriptionFolder dept = new TPersonalPrescriptionFolder().deptFolder();
        dept.getChildren().addAll(getDeptFolders());
        list.add(dept);

        TPersonalPrescriptionFolder ins = new TPersonalPrescriptionFolder().insFolder();
        ins.getChildren().addAll(getInsFolders());
        list.add(ins);

        TPersonalPrescriptionFolder app = new TPersonalPrescriptionFolder().appFolder();
        app.getChildren().addAll(getAppFolders());
        list.add(app);

        TPersonalPrescriptionFolder area = new TPersonalPrescriptionFolder().areaFolder();
        area.getChildren().addAll(getAreaFolders());
        list.add(area);

        List<SysAdminInfoex> shareType = getShareType();
        for (SysAdminInfoex infoex : shareType) {
            if (Constant.BASIC_STRING_ZERO.equals(infoex.getUserExContent())) {
                self.setCanAddChildren(true);
            } else if (Constant.BASIC_STRING_ONE.equals(infoex.getUserExContent())) {
                dept.setCanAddChildren(true);
            } else if (Constant.BASIC_STRING_TWO.equals(infoex.getUserExContent())) {
                ins.setCanAddChildren(true);
            } else if (Constant.BASIC_STRING_FOUR.equals(infoex.getUserExContent())) {
                ins.setCanAddChildren(true);
            } else if (Constant.BASIC_STRING_FIVE.equals(infoex.getUserExContent())) {
                ins.setCanAddChildren(true);
            }

        }
        return list;
    }

    /**
     * 获取医生可以共享的文件夹树
     *
     * @return java.util.List<com.jiuzhekan.cbkj.beans.myData.TPersonalPrescriptionFolder>
     * <AUTHOR>
     * @date 2021/4/1
     */
    public List<TPersonalPrescriptionFolder> getShareFolderTree() {

        List<TPersonalPrescriptionFolder> list = new ArrayList<>();
        List<SysAdminInfoex> shareType = getShareType();

        for (SysAdminInfoex infoex : shareType) {
            if (Constant.BASIC_STRING_ZERO.equals(infoex.getUserExContent())) {

                TPersonalPrescriptionFolder self = new TPersonalPrescriptionFolder().selfFolder();
                self.getChildren().addAll(getSelfFolders());
                list.add(self);

            } else if (Constant.BASIC_STRING_ONE.equals(infoex.getUserExContent())) {

                TPersonalPrescriptionFolder dept = new TPersonalPrescriptionFolder().deptFolder();
                dept.getChildren().addAll(getDeptFolders());
                list.add(dept);

            } else if (Constant.BASIC_STRING_TWO.equals(infoex.getUserExContent())) {

                TPersonalPrescriptionFolder ins = new TPersonalPrescriptionFolder().insFolder();
                ins.getChildren().addAll(getInsFolders());
                list.add(ins);
            }else if (Constant.BASIC_STRING_FOUR.equals(infoex.getUserExContent())) {

                TPersonalPrescriptionFolder app = new TPersonalPrescriptionFolder().appFolder();
                app.getChildren().addAll(getAppFolders());
                list.add(app);
            }else if (Constant.BASIC_STRING_FIVE.equals(infoex.getUserExContent())) {

                TPersonalPrescriptionFolder area = new TPersonalPrescriptionFolder().areaFolder();
                area.getChildren().addAll(getAreaFolders());
                list.add(area);
            }
        }
        return list;
    }

    /**
     * 获取个人文件夹
     *
     * @return java.util.List<com.jiuzhekan.cbkj.beans.myData.TPersonalPrescriptionFolder>
     * <AUTHOR>
     * @date 2021/4/1
     */
    private List<TPersonalPrescriptionFolder> getSelfFolders() {
        TPersonalPrescriptionFolder param = new TPersonalPrescriptionFolder();
        param.setCreateUser(AdminUtils.getCurrentHr().getId());
        param.setFolderType(Constant.BASIC_STRING_ZERO);
        List<TPersonalPrescriptionFolder> selfList = tPersonalPrescriptionFolderMapper.getPageListByObj(param);
        return makeFolderTree(selfList);
    }

    /**
     * 获取科室文件夹
     *
     * @return java.util.List<com.jiuzhekan.cbkj.beans.myData.TPersonalPrescriptionFolder>
     * <AUTHOR>
     * @date 2021/4/1
     */
    private List<TPersonalPrescriptionFolder> getDeptFolders() {
        String appId = AdminUtils.getCurrentAppId();
        String insCode = AdminUtils.getCurrentInsCode();
        String deptId = AdminUtils.getCurrentDeptId();

        if (StringUtils.isBlank(appId) || Constant.BASIC_APP_ID.equals(appId)
                || StringUtils.isBlank(insCode) || Constant.BASIC_INS_CODE.equals(insCode)
                || StringUtils.isBlank(deptId) || Constant.BASIC_DEPT_ID.equals(deptId)) {
            return new ArrayList<>();
        }

        TPersonalPrescriptionFolder param = new TPersonalPrescriptionFolder();
        param.setAppId(appId);
        param.setInsCode(insCode);
        param.setDeptId(AdminUtils.getCurrentDeptId());
        param.setFolderType(Constant.BASIC_STRING_ONE);
        List<TPersonalPrescriptionFolder> deptList = tPersonalPrescriptionFolderMapper.getPageListByObj(param);
        return makeFolderTree(deptList);
    }

    /**
     * 获取全院文件夹
     *
     * @return java.util.List<com.jiuzhekan.cbkj.beans.myData.TPersonalPrescriptionFolder>
     * <AUTHOR>
     * @date 2021/4/1
     */
    private List<TPersonalPrescriptionFolder> getInsFolders() {

        String appId = AdminUtils.getCurrentAppId();
        String insCode = AdminUtils.getCurrentInsCode();

        if (StringUtils.isBlank(appId) || Constant.BASIC_APP_ID.equals(appId)
                || StringUtils.isBlank(insCode) || Constant.BASIC_INS_CODE.equals(insCode)) {
            return new ArrayList<>();
        }

        TPersonalPrescriptionFolder param = new TPersonalPrescriptionFolder();
        param.setAppId(appId);
        param.setInsCode(insCode);
        param.setFolderType(Constant.BASIC_STRING_TWO);
        List<TPersonalPrescriptionFolder> insList = tPersonalPrescriptionFolderMapper.getPageListByObj(param);
        return makeFolderTree(insList);
    }

    /**
     * 获取医共体文件夹
     *
     * <AUTHOR>
     * @date 2024/7/10
     */
    private List<TPersonalPrescriptionFolder> getAppFolders() {

        String appId = AdminUtils.getCurrentAppId();

        if (StringUtils.isBlank(appId) || Constant.BASIC_APP_ID.equals(appId)) {
            return new ArrayList<>();
        }

        TPersonalPrescriptionFolder param = new TPersonalPrescriptionFolder();
        param.setAppId(appId);
        param.setFolderType(Constant.BASIC_STRING_FOUR);
        List<TPersonalPrescriptionFolder> appList = tPersonalPrescriptionFolderMapper.getPageListByObj(param);
        return makeFolderTree(appList);
    }

    /**
     * 获取区域文件夹
     *
     * <AUTHOR>
     * @date 2024/7/10
     */
    private List<TPersonalPrescriptionFolder> getAreaFolders() {

        TPersonalPrescriptionFolder param = new TPersonalPrescriptionFolder();
        param.setFolderType(Constant.BASIC_STRING_FIVE);
        List<TPersonalPrescriptionFolder> areaList = tPersonalPrescriptionFolderMapper.getPageListByObj(param);
        return makeFolderTree(areaList);
    }

    /**
     * 组装树
     *
     * @param list list
     * @return java.util.List<com.jiuzhekan.cbkj.beans.myData.TPersonalPrescriptionFolder>
     * <AUTHOR>
     * @date 2021/4/1
     */
    private List<TPersonalPrescriptionFolder> makeFolderTree(List<TPersonalPrescriptionFolder> list) {
        List<TPersonalPrescriptionFolder> rootList = new ArrayList<>();
        Map<String, TPersonalPrescriptionFolder> folderMap = new HashMap<>();
        AdminInfo admin = AdminUtils.getCurrentHr();

        for (TPersonalPrescriptionFolder folder : list) {
            folderMap.put(folder.getFolderId(), folder);
        }

        for (TPersonalPrescriptionFolder folder : list) {

            if (admin.getId().equals(folder.getCreateUser())) {
                folder.setCanUpdate(true);
                folder.setCanAddChildren(true);
            }

            if (folder.getFolderPid() != null && folderMap.get(folder.getFolderPid()) != null) {

                TPersonalPrescriptionFolder parent = folderMap.get(folder.getFolderPid());
                parent.setLeaf(false);
                if (parent.getChildren() == null) {
                    parent.setChildren(new ArrayList<>());
                }
                parent.getChildren().add(folder);
            } else {
                rootList.add(folder);
            }
        }
        return rootList;
    }


    /**
     * 插入新数据
     *
     * @param folder tPersonalPrescriptionFolder
     * @return ResEntity
     * <AUTHOR>
     * @date 2021-03-31
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insert(TPersonalPrescriptionFolder folder) {
        if (StringUtils.isBlank(folder.getFolderPid())) {
            return ResEntity.entity(false, "父文件夹ID不能为空", null);
        }
        if (StringUtils.isBlank(folder.getFolderName())) {
            return ResEntity.entity(false, "文件夹名称不能为空", null);
        }
        if (StringUtils.isBlank(folder.getFolderType())) {
            return ResEntity.entity(false, "文件夹类型不能为空（0个人 1科室 2全院）", null);
        }
        if (folder.getFolderNum() == null) {
            return ResEntity.entity(false, "文件夹序号不能为空", null);
        }

        if (folder.getFolderSort() != null && folder.getFolderSort().size() > 0) {
            for (TPersonalPrescriptionFolder folder0 : folder.getFolderSort()) {
                if (StringUtils.isBlank(folder0.getFolderId())) {
                    return ResEntity.entity(false, "排序ID不能为空！", null);
                }
                if (folder0.getFolderNum() == null) {
                    return ResEntity.entity(false, "排序序号不能为空！", null);
                }
            }
        }

        AdminInfo admin = AdminUtils.getCurrentHr();

        folder.setFolderId(IDUtil.getID());
        folder.setAppId(AdminUtils.getCurrentAppId());
        folder.setInsCode(AdminUtils.getCurrentInsCode());
        folder.setDeptId(AdminUtils.getCurrentDeptId());
        folder.setCreateDate(new Date());
        folder.setCreateUser(admin.getId());
        folder.setCreateUsername(admin.getNameZh());
        folder.setCanUpdate(true);
        folder.setCanAddChildren(true);

        long rows = tPersonalPrescriptionFolderMapper.insert(folder);

        if (folder.getFolderSort() != null && folder.getFolderSort().size() > 0) {
            tPersonalPrescriptionFolderMapper.sortFolderNumList(folder.getFolderSort());
        }
        if (rows > 0) {
            return ResEntity.entity(true, Constant.SUCCESS_DX, folder);
        }
        return new ResEntity(false, "保存失败，数据库异常！！", null);
    }


    /**
     * 修改
     *
     * @param folder folder
     * @return ResEntity
     * <AUTHOR>
     * @date 2021-03-31
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity update(TPersonalPrescriptionFolder folder) {

        folder.setUpdateDate(new Date());
        folder.setUpdateUser(AdminUtils.getCurrentHr().getId());
        folder.setUpdateUsername(AdminUtils.getCurrentHr().getNameZh());
        folder.setCanUpdate(true);
        folder.setCanAddChildren(true);
        long rows = tPersonalPrescriptionFolderMapper.updateByPrimaryKey(folder);
        if (rows > 0) {
            return ResEntity.entity(true, Constant.SUCCESS_DX, folder);
        }
        return new ResEntity(false, "修改失败，数据库异常", null);
    }

    /**
     * 删除
     *
     * @param id id
     * @return ResEntity
     * <AUTHOR>
     * @date 2021-03-31
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity delete(String id) {
        if (StringUtils.isBlank(id)) {
            return new ResEntity(false, "ID不能为空！", null);
        }

        List<String> folderIds = getChildrenIdsByPidInMap(id, clientRedisService.getAllFolderMap());

        for (String folderId : folderIds) {

            TPersonalPrescriptionFolder folder = new TPersonalPrescriptionFolder();
            folder.setFolderId(folderId);
            folder.setDelDate(new Date());
            folder.setDelUser(AdminUtils.getCurrentHr().getId());
            folder.setDelUsername(AdminUtils.getCurrentHr().getNameZh());
            tPersonalPrescriptionFolderMapper.deleteByPrimaryKey(folder);
        }

        deleteFmapByFolderIds(folderIds);

        return new ResEntity(true, Constant.SUCCESS_DX, null);
    }


    /**
     * 移动（调整排序）
     *
     * @param list list
     * @return ResEntity
     * <AUTHOR>
     * @date 2021-03-31
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity moveNum(List<TPersonalPrescriptionFolder> list) {

        for (TPersonalPrescriptionFolder folder : list) {
            if (StringUtils.isBlank(folder.getFolderId())) {
                return ResEntity.entity(false, "ID不能为空！", null);
            }
            if (folder.getFolderNum() == null) {
                return ResEntity.entity(false, "序号不能为空！", null);
            }
        }
        tPersonalPrescriptionFolderMapper.sortFolderNumList(list);
        return ResEntity.entity(true, Constant.SUCCESS_DX, null);
    }


    /**
     * 从文件夹树获取某个文件夹及所有父节点ID
     *
     * @param folderId 某个文件夹
     * @param map      文件夹树
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @date 2021/4/2
     */
    public List<String> getParentsIdsByPidInMap(String folderId, Map<String, TPersonalPrescriptionFolder> map) {

        List<String> idList = new ArrayList<>();

        TPersonalPrescriptionFolder folder = map.get(folderId);
        if (folder != null) {

            TPersonalPrescriptionFolder parent = map.get(folder.getFolderPid());

            if (parent != null) {
                idList.addAll(getParentsIdsByPidInMap(parent.getFolderId(), map));
            }

            idList.add(folderId);
        }
        return idList;
    }

    /**
     * 从文件夹树获取某个文件夹及下面所有子节点ID
     *
     * @param folderId 某个文件夹
     * @param map      文件夹树
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @date 2021/4/2
     */
    public List<String> getChildrenIdsByPidInMap(String folderId, Map<String, TPersonalPrescriptionFolder> map) {

        List<String> idList = new ArrayList<>();

        TPersonalPrescriptionFolder folder = map.get(folderId);
        List<TPersonalPrescriptionFolder> list = flatFolderMap(folder);

        for (TPersonalPrescriptionFolder folder1 : list) {
            idList.add(folder1.getFolderId());
        }
        return idList;
    }

    /**
     * 展开文件夹树
     *
     * @param folder 文件夹（包含子节点）
     * @return java.util.List<com.jiuzhekan.cbkj.beans.myData.TPersonalPrescriptionFolder>
     * <AUTHOR>
     * @date 2021/4/2
     */
    private List<TPersonalPrescriptionFolder> flatFolderMap(TPersonalPrescriptionFolder folder) {
        List<TPersonalPrescriptionFolder> flatList = new ArrayList<>();
        if (folder != null) {
            flatList.add(folder);
            if (folder.getChildren() != null) {
                for (TPersonalPrescriptionFolder child : folder.getChildren()) {
                    flatList.addAll(flatFolderMap(child));
                }
            }
        }
        return flatList;
    }


    /**
     * 根据协定方ID获取所在文件夹ID
     *
     * @param perPreId 协定方ID
     * @return java.util.List<com.jiuzhekan.cbkj.beans.myData.TPersonalPrescriptionFolder>
     * <AUTHOR>
     * @date 2021/4/9
     */
    public String getFolderIdByPerPreId(String perPreId) {

        return tPersonalPrescriptionFmapMapper.getFolderIdByPerPreId(perPreId);
    }

    public void deleteFmapByPerPreId(String perPreId) {

        tPersonalPrescriptionFmapMapper.deleteByPersPreId(perPreId);
    }

    public void deleteFmapByFolderId(String folderId) {

        List<String> folderIds = getChildrenIdsByPidInMap(
                folderId, clientRedisService.getAllFolderMap());

        tPersonalPrescriptionFmapMapper.deleteByFolderId(folderIds);
    }

    public void deleteFmapByFolderIds(List<String> folderIds) {
        tPersonalPrescriptionFmapMapper.deleteByFolderId(folderIds);
    }

    public void insertFmap(String folderId, String perPreId) {
        if (StringUtils.isNotBlank(folderId) && StringUtils.isNotBlank(perPreId)
                && !Constant.FOLDER_AREA.equals(folderId) && !Constant.FOLDER_AREA_OTHER.equals(folderId)
                && !Constant.FOLDER_APP.equals(folderId) && !Constant.FOLDER_APP_OTHER.equals(folderId)
                && !Constant.FOLDER_INS.equals(folderId) && !Constant.FOLDER_INS_OTHER.equals(folderId)
                && !Constant.FOLDER_DEPT.equals(folderId) && !Constant.FOLDER_DEPT_OTHER.equals(folderId)
                && !Constant.FOLDER_SELF.equals(folderId) && !Constant.FOLDER_SELF_OTHER.equals(folderId)
                && !Constant.FOLDER_KNOW.equals(folderId) && !folderId.startsWith(Constant.FOLDER_KNOW_)) {
            TPersonalPrescriptionFmap fmap = new TPersonalPrescriptionFmap();
            fmap.setFolderId(folderId);
            fmap.setPersPreId(perPreId);
            tPersonalPrescriptionFmapMapper.insert(fmap);
        }
    }

}
