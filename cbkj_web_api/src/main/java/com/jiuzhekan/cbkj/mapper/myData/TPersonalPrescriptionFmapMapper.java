package com.jiuzhekan.cbkj.mapper.myData;

import com.jiuzhekan.cbkj.beans.myData.TPersonalPrescriptionFmap;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public interface TPersonalPrescriptionFmapMapper extends BaseMapper<TPersonalPrescriptionFmap> {

    /**
     * 根据文件夹删除映射
     *
     * @param folderIds
     * @return long
     * <AUTHOR>
     * @date 2021/4/1
     */
    void deleteByFolderId(List<String> folderIds);

    /**
     * 根据协定方删除映射
     *
     * @param persPreId
     * @return long
     * <AUTHOR>
     * @date 2021/4/1
     */
    void deleteByPersPreId(String persPreId);

    /**
     * 根据协定方ID获取所在文件夹ID
     *
     * @param perPreId 协定方ID
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021/4/9
     */
    String getFolderIdByPerPreId(String perPreId);
}