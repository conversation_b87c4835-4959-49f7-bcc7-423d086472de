package com.jiuzhekan.healthpres.beans.heathPresControllerVo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/5/27 11:31
 * @Version 1.0
 */
@ApiModel
@Data
public class CreateHealthPre
{
    @ApiModelProperty(value = "患者id")
    private String patientId;

    @ApiModelProperty(value = "结果报告名称")
    private String resultReportName;


    @ApiModelProperty(value = "1.儿童保健2.慢病调理")
    private Integer questionnaireMainType;

    @ApiModelProperty(value = "子分类 1-1(6、12月龄儿童)1-2(18、24月龄儿童)1-3(30、36月龄儿童) 等")
    private String questionnaireMainSecondTypeName;

    @ApiModelProperty(value = "子分类 1-1(6、12月龄儿童)1-2(18、24月龄儿童)1-3(30、36月龄儿童) 等")
    private String questionnaireMainSecondTypeCode;
}
