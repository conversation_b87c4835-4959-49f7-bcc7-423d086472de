package com.jiuzhekan.cbkj.service.sysService;

import com.jiuzhekan.cbkj.beans.sysBeans.Logentity;
import com.jiuzhekan.cbkj.beans.sysBeans.RedisMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class Publisher {

    @Autowired
    private  RedisTemplate redisTemplate;

    public void pushMessage(String topic, RedisMessage message) {
        redisTemplate.convertAndSend(topic,message);
    }

    public void pushLogMessage(String topic, Logentity logentity){
        redisTemplate.convertAndSend(topic,logentity);
    }
}