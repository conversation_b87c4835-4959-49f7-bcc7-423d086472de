package com.jiuzhekan.cbkj.mapper.sysMapper;

import com.jiuzhekan.cbkj.beans.sysBeans.Logentity;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public interface LogentityMapper extends BaseMapper<Logentity> {

    /**
     * 返回分页数据 List<Map>
     * @param t
     * @return
     */
    List<Map<String,Object>> getPageDatas(Logentity t);
    long changeStatus(Map<String, Object> params);
}