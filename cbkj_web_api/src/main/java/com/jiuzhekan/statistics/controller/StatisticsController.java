package com.jiuzhekan.statistics.controller;

import com.jiuzhekan.cbkj.beans.statistics.ExcelVO;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.excel.ExportExcel;
import com.jiuzhekan.cbkj.service.business.TSysParamService;
import com.jiuzhekan.statistics.beans.register.ChangeMap;
import com.jiuzhekan.statistics.beans.register.QueryParam;
import com.jiuzhekan.statistics.beans.register.QueryResult;
import com.jiuzhekan.statistics.beans.register.StatisticsChangeResult;
import com.jiuzhekan.statistics.service.statistics.StatisticsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date $ $
 **/
@Api(value = "统计接口-桐庐", tags = "统计接口-桐庐")
@RequestMapping("statistics")
@RestController
public class StatisticsController {
    @Autowired
    private StatisticsService statisticsService;
    @Autowired
    private TSysParamService tSysParamService;

    @RequestMapping(value = "searchAll", method = RequestMethod.GET)
    @ApiOperation(value = "获取统计数据（桐庐）", notes = "获取统计数据（桐庐）", response = StatisticsChangeResult.class)
    //@LogAnnotation("获取统计数据（桐庐）")
    @ResponseBody
    public Object getData(QueryParam queryParam) throws ParseException {

        return ResEntity.entity(true, Constant.SUCCESS_DX, statisticsService.getData(queryParam));
    }


    @RequestMapping(value = "export", method = RequestMethod.GET)
    @ApiOperation(value = "导出统计数据（桐庐）", notes = "导出统计数据（桐庐）")
    @LogAnnotation("导出统计数据（桐庐）")
    @ResponseBody
    public void exportData(QueryParam queryParam, HttpServletResponse response) throws ParseException {
        String title = "系统使用统计";
        ExportExcel exportExcel = new ExportExcel();
        StatisticsChangeResult statisticsChangeResult = statisticsService.getData(queryParam);
        List<ChangeMap> data = statisticsChangeResult.getChangeMapList();
        ArrayList<ExcelVO> voArrayList = new ArrayList<>();
        String parValues = tSysParamService.getSysParam(Constant.OPEN_CDSS_MODE).getParValues();
        data.forEach(
                a -> {
                    ExcelVO excelVOP = new ExcelVO();
                    excelVOP.setRow1(a.getAppName());
                    excelVOP.setRow2(a.getRegisterTimes() + "");
                    excelVOP.setRow3(a.getSumInnerAndExtPrescription() + "");
                    excelVOP.setRow4(a.getSumStatisticsPrescription() + "");
                    excelVOP.setRow5(a.getElectronicRecordNum() + "");
                    excelVOP.setRow6(a.getNumber() + "");
                    excelVOP.setRow7(a.getSumPersonalPrescription() + "");
                    if (Constant.BASIC_STRING_ONE.equals(parValues)) {
                        excelVOP.setRow8(a.getSumInnerAndExtPrescriptionCDSS() + "");
                    }
                    voArrayList.add(excelVOP);
                    a.getInsList().forEach(
                            b -> {
                                ExcelVO excelVOC = new ExcelVO();
                                excelVOC.setRow1("  " + b.getInsName());
                                excelVOC.setRow2(b.getRegisterTimes() + "");
                                excelVOC.setRow3(b.getSumInnerAndExtPrescription() + "");
                                excelVOC.setRow4(b.getSumStatisticsPrescription() + "");
                                excelVOC.setRow5(b.getElectronicRecordNum() + "");
                                excelVOC.setRow6(b.getNumber() + "");
                                excelVOC.setRow7(b.getSumPersonalPrescription() + "");
                                if (Constant.BASIC_STRING_ONE.equals(parValues)) {
                                    excelVOC.setRow8(b.getSumInnerAndExtPrescriptionCDSS() + "");
                                }
                                voArrayList.add(excelVOC);
                                b.getInsList().forEach(
                                        changeMap -> {
                                            ExcelVO excelVOC2 = new ExcelVO();
                                            excelVOC2.setRow1("    " + changeMap.getInsName());
                                            excelVOC2.setRow2(changeMap.getRegisterTimes() + "");
                                            excelVOC2.setRow3(changeMap.getSumInnerAndExtPrescription() + "");
                                            excelVOC2.setRow4(changeMap.getSumStatisticsPrescription() + "");
                                            excelVOC2.setRow5(changeMap.getElectronicRecordNum() + "");
                                            excelVOC2.setRow6(changeMap.getNumber() + "");
                                            excelVOC2.setRow7(changeMap.getSumPersonalPrescription() + "");
                                            if (Constant.BASIC_STRING_ONE.equals(parValues)) {
                                                excelVOC2.setRow8(changeMap.getSumInnerAndExtPrescriptionCDSS() + "");
                                            }
                                            voArrayList.add(excelVOC2);
                                            changeMap.getInsList().forEach(
                                                    c -> {
                                                        ExcelVO excelVOC3 = new ExcelVO();
                                                        excelVOC3.setRow1("      " + c.getInsName());
                                                        excelVOC3.setRow2(c.getRegisterTimes() + "");
                                                        excelVOC3.setRow3(c.getSumInnerAndExtPrescription() + "");
                                                        excelVOC3.setRow4(c.getSumStatisticsPrescription() + "");
                                                        excelVOC3.setRow5(c.getElectronicRecordNum() + "");
                                                        excelVOC3.setRow6(c.getNumber() + "");
                                                        excelVOC3.setRow7(c.getSumPersonalPrescription() + "");
                                                        if (Constant.BASIC_STRING_ONE.equals(parValues)) {
                                                            excelVOC3.setRow8(c.getSumInnerAndExtPrescriptionCDSS() + "");
                                                        }
                                                        voArrayList.add(excelVOC3);
                                                    });
                                        }
                                );
                            }
                    );
                }
        );
        /**
         * 补上合计
         */
        ExcelVO excelToTal = new ExcelVO();
        excelToTal.setRow1("合计");
        excelToTal.setRow2(statisticsChangeResult.getRegisterTimes() + "");
        excelToTal.setRow3(statisticsChangeResult.getSumInnerAndExtPrescription() + "");
        excelToTal.setRow4(statisticsChangeResult.getSumStatisticsPrescription() + "");
        excelToTal.setRow5(statisticsChangeResult.getElectronicRecordNum() + "");
        excelToTal.setRow6(statisticsChangeResult.getNumber() + "");
        excelToTal.setRow7(statisticsChangeResult.getSumPersonalPrescription() + "");
        excelToTal.setRow8(statisticsChangeResult.getSumInnerAndExtPrescriptionCDSS() + "");
        voArrayList.add(excelToTal);
        exportExcel.getStatistics(voArrayList, title, response, parValues);
    }
}
