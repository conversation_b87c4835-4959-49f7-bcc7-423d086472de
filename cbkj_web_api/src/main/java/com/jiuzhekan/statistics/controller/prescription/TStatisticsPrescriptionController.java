package com.jiuzhekan.statistics.controller.prescription;

import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.statistics.beans.prescription.TStatisticsPrescription;
import com.jiuzhekan.statistics.service.prescription.TStatisticsPrescriptionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@Api(value = "处方统计表接口", tags = "处方统计表接口")
@RequestMapping("tStatisticsPrescription")
public class TStatisticsPrescriptionController {

    @Autowired
    private TStatisticsPrescriptionService tStatisticsPrescriptionService;


    @RequestMapping(value = "getPages", method = RequestMethod.GET)
    @ApiOperation(value = "分页查询处方统计表", notes = "分页查询处方统计表", response = TStatisticsPrescription.class)
    @LogAnnotation("分页查询处方统计表")
    @ResponseBody
    public Object getApps(TStatisticsPrescription tStatisticsPrescription, Page page){
        return tStatisticsPrescriptionService.getPageDatas(tStatisticsPrescription,page);
    }

}