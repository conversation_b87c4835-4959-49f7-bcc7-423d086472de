package com.jiuzhekan.cbkj.mapper.statistics;

import com.jiuzhekan.cbkj.controller.statistics.vo.PrescriptionStatisticsVO;

import java.util.List;

/**
 * PrescriptionStatisticsMapper
 * <p>
 * 杭州聪宝科技有限公司
 *
 * <AUTHOR>
 * @date 2022/1/11
 */
public interface PrescriptionStatisticsMapper {

    /**
     * 组合条件查询处方条数
     *
     * @param prescriptionStatisticsVO prescriptionStatisticsVO
     * @return java.util.List<com.jiuzhekan.cbkj.controller.statistics.vo.PrescriptionStatisticsVO>
     * <AUTHOR>
     * @date 2022/1/11
     */
    Integer detailedCount(PrescriptionStatisticsVO prescriptionStatisticsVO);
    Integer detailedScienceCount(PrescriptionStatisticsVO prescriptionStatisticsVO);

    /**
     * 组合条件查询处方明细
     *
     * @param prescriptionStatisticsVO prescriptionStatisticsVO
     * @return java.util.List<com.jiuzhekan.cbkj.controller.statistics.vo.PrescriptionStatisticsVO>
     * <AUTHOR>
     * @date 2022/1/11
     */
    List<PrescriptionStatisticsVO> detailedList(PrescriptionStatisticsVO prescriptionStatisticsVO);
    List<PrescriptionStatisticsVO> detailedListScience(PrescriptionStatisticsVO prescriptionStatisticsVO);
}
