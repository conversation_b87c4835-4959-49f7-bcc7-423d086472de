package com.jiuzhekan.statistics.service.board;

import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.github.pagehelper.PageHelper;
import com.jiuzhekan.statistics.beans.board.TStatisticsHealthCare;
import com.jiuzhekan.statistics.beans.board.TStatisticsPrescriptionMedication;
import com.jiuzhekan.statistics.beans.register.QueryParam;
import com.jiuzhekan.statistics.mapper.board.TStatisticsHealthCareMapper;
import com.jiuzhekan.statistics.utils.DateUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Map;

@Service
public class TStatisticsHealthCareService {

    @Autowired
    private TStatisticsHealthCareMapper tStatisticsHealthCareMapper;

    /**
     * 加载分页数据
     *
     * @param tStatisticsHealthCare 医保控费提醒：日最大提醒+医保外药品提醒+基金提醒，按药品记次
     * @param page 分页
     * @return Object
     * <AUTHOR>
     * @date 2022-10-09
     */
    public Object getPageDatas(TStatisticsHealthCare tStatisticsHealthCare, Page page) {

        PageHelper.startPage(page.getPage(), page.getLimit());
        List<TStatisticsHealthCare> list = tStatisticsHealthCareMapper.getPageListByObj(tStatisticsHealthCare);
        return Page.getLayUiTablePageData(list);
    }

    public TStatisticsHealthCare insertQuery(TStatisticsHealthCare tStatisticsHealthCare) {
        long count = tStatisticsHealthCareMapper.getCountByObj(tStatisticsHealthCare);
        if (count > 0) {
            //存在，修改
            TStatisticsHealthCare medication = tStatisticsHealthCareMapper.getOneByObject(tStatisticsHealthCare);
            return medication;
        } else {
            //不存在，插入。
            return tStatisticsHealthCare.initDate(tStatisticsHealthCare);
        }
    }
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insertOrUpdate(List<String> aloneMatYpdmHis) {
        for (String aloneMatYpdmHi : aloneMatYpdmHis) {
            insertOrUpdate(aloneMatYpdmHi);
        }
        return ResEntity.success(null);
    }
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insertOrUpdate(String ypdmHis) {
        if (StringUtils.isBlank(ypdmHis)){
            return null;
        }
        TStatisticsHealthCare tStatisticsHealthCare = new TStatisticsHealthCare();
        tStatisticsHealthCare.setAppId(AdminUtils.getCurrentAppId());
        tStatisticsHealthCare.setInsCode(AdminUtils.getCurrentInsCode());
        tStatisticsHealthCare.setYpdmHis(ypdmHis);
        SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd");
        try {
            tStatisticsHealthCare.setCreateDate(sdf2.parse(DateUtils.getWeekEndTim2e(0, true)));
        } catch (ParseException e) {
            //throw new RuntimeException(e);
        }
        TStatisticsHealthCare tStatisticsHealthCare1 = insertQuery(tStatisticsHealthCare);
        tStatisticsHealthCare1.setRemindTimes(tStatisticsHealthCare1.getRemindTimes()+1);
        if (null == tStatisticsHealthCare1.getId()){
            int insert = tStatisticsHealthCareMapper.insert(tStatisticsHealthCare1);
        }else {
            int i = tStatisticsHealthCareMapper.updateByPrimaryKey(tStatisticsHealthCare1);
        }
        return ResEntity.success(tStatisticsHealthCare1);
    }


    /**
     * 插入新数据
     *
     * @param tStatisticsHealthCare 医保控费提醒：日最大提醒+医保外药品提醒+基金提醒，按药品记次
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-10-09
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insert(TStatisticsHealthCare tStatisticsHealthCare){

        long rows = tStatisticsHealthCareMapper.insert(tStatisticsHealthCare);

        return ResEntity.success(tStatisticsHealthCare);
    }


    /**
     * 修改
     *
     * @param tStatisticsHealthCare 医保控费提醒：日最大提醒+医保外药品提醒+基金提醒，按药品记次
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-10-09
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity update(TStatisticsHealthCare tStatisticsHealthCare) {

        long rows = tStatisticsHealthCareMapper.updateByPrimaryKey(tStatisticsHealthCare);

        return ResEntity.success(tStatisticsHealthCare);
    }

    /**
     * 删除
     *
     * @param ids ids
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-10-09
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity deleteLis(String ids){
        if (StringUtils.isBlank(ids)) {
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }
        long rowsR = tStatisticsHealthCareMapper.deleteBylist(ids.split(","));

        return ResEntity.success(rowsR);
    }

    public int getData(QueryParam queryParam){
        Integer i = tStatisticsHealthCareMapper.sumStatistics(queryParam);
        return null == i ? 0 : i;
    }

}
