package com.jiuzhekan.knowledgeshare.controller;

import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.knowledgeshare.beans.ChineseMedicineListReq;
import com.jiuzhekan.knowledgeshare.beans.ShareListReq;
import com.jiuzhekan.knowledgeshare.beans.TKnowledgeShare;
import com.jiuzhekan.knowledgeshare.service.TKnowledgeShareService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/7/1 10:06
 * @Version 1.0
 */
@Api(value = "知识共享管理", tags = "知识共享管理")
@RestController
@RequestMapping("/tKnowledgeShare")
public class TKnowledgeShareController {
    private final TKnowledgeShareService tKnowledgeShareService;

    public TKnowledgeShareController(TKnowledgeShareService tKnowledgeShareService) {
        this.tKnowledgeShareService = tKnowledgeShareService;
    }

    /**
     * 获取知识共享 页面列表
     */
    @GetMapping("/getShareList")
    @ApiOperation(value = "获取知识共享 列表", response = TKnowledgeShare.class)
    public Object getShareList(ShareListReq shareListReq) {
        return tKnowledgeShareService.getShareList(shareListReq);
    }
    /**
     * 新增知识共享
     */
    @PostMapping("/createShare")
    @ApiOperation(value = "新增知识共享", response = TKnowledgeShare.class)
    public Object createShare(@Valid @RequestBody TKnowledgeShare tKnowledgeShare) {
        return (tKnowledgeShareService.createShare(tKnowledgeShare));
    }

    /**
     * 获取知识共享详情
     */
    @GetMapping("/getShareDetail")
    @ApiOperation(value = "获取知识共享详情", response = TKnowledgeShare.class)
    public Object getShareDetail(String shareId) {
        return ResEntity.success(tKnowledgeShareService.getObjectById(shareId));
    }

    /**
     * 编辑知识共 只能管理员和自己能编辑
     * * */
    @PostMapping("/updateShare")
    @ApiOperation(value = "编辑知识共享", response = ResEntity.class)
    public Object updateShare(@Valid @RequestBody TKnowledgeShare tKnowledgeShare) {
        return (tKnowledgeShareService.updateShare(tKnowledgeShare));
    }

    /**
     * 删除知识共享 只能管理员和自己能删除
     */
    @GetMapping("/deleteShare")
    @ApiOperation(value = "删除知识共享", response = ResEntity.class)
    public Object deleteShare(String shareId) {
        return (tKnowledgeShareService.deleteShare(shareId));
    }

    /* 以下接口是中医古籍和共享文献接口  */

    /**
     * 获取中医古籍列表接口
     */
    @GetMapping("/getChineseMedicineList")
    @ApiOperation(value = "获取中医古籍列表接口", response = ResEntity.class)
    public Object getChineseMedicineList(ChineseMedicineListReq bookName, Page page) {
        return (tKnowledgeShareService.getChineseMedicineList(bookName,page));
    }

    /**
     * 获取共享文献列表接口
     */
    @GetMapping("/getSharedLiteratureList")
    @ApiOperation(value = "获取共享文献列表接口", response = ResEntity.class)
    public Object getSharedLiteratureList(ChineseMedicineListReq bookName, Page page) {
        return (tKnowledgeShareService.getSharedLiteratureList(bookName,page));
    }



}
