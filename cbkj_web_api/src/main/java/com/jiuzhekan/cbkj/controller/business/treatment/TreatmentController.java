package com.jiuzhekan.cbkj.controller.business.treatment;

import com.jiuzhekan.cbkj.beans.business.record.TPrescription;
import com.jiuzhekan.cbkj.beans.business.record.TPrescriptionPreparationItem;
import com.jiuzhekan.cbkj.beans.business.record.TRecord;
import com.jiuzhekan.cbkj.beans.business.record.TRegister;
import com.jiuzhekan.cbkj.beans.business.store.TDisplay;
import com.jiuzhekan.cbkj.beans.business.sysCode.THisCodeItem;
import com.jiuzhekan.cbkj.beans.drug.CenterHisMappingVO;
import com.jiuzhekan.cbkj.beans.http.DosageFormDicListReq;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.beans.validRecordBySDK.ValidRecordSend;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.common.http.InterfaceRestTemplate;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.controller.business.treatment.vo.CenterHisMappingVORequest;
import com.jiuzhekan.cbkj.service.business.TSysParamService;
import com.jiuzhekan.cbkj.service.business.sysCode.TSysCodeService;
import com.jiuzhekan.cbkj.service.business.treatment.*;
import com.jiuzhekan.cbkj.service.redis.ClientRedisService;
import com.jiuzhekan.cbkj.service.redis.ParameterRedisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.net.UnknownHostException;
import java.util.List;
import java.util.Map;

/**
 * 智能开方
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2019年08月21日 15:51:00
 */
@Controller
@Api(value = "智能开方", tags = "智能开方")
public class TreatmentController {

    @Autowired
    private TreatmentService treatmentService;
    @Autowired
    private SearchMatService searchMatService;
    @Autowired
    private ValidRecordService validRecordService;
    @Autowired
    private ValidRecordBySDKService validRecordBySDKService;
    @Autowired
    private PrescriptionService prescriptionService;
    @Autowired
    private TSysCodeService tSysCodeService;
    @Autowired
    private InterfaceRestTemplate interfaceRestTemplate;
    @Autowired
    private TSysParamService tSysParamService;

    private final ParameterRedisService parameterRedisService;
//    private final RecordService recordService;
    public TreatmentController(ParameterRedisService parameterRedisService) {

//        this.recordService = recordService;
        this.parameterRedisService = parameterRedisService;
    }

    @RequestMapping(value = "treatment/params/display", method = RequestMethod.GET)
    @ApiOperation(value = "中药方开方时选择药房或者中药类型", notes = "参数挂号ID", response = TDisplay.class)
    @LogAnnotation("中药方开方时选择药房或者中药类型")
    @ResponseBody
    public ResEntity getDisplayParams(String registerId) {
        return searchMatService.getDisplayParams(registerId);
    }

    @RequestMapping(value = "treatment/hisCode", method = RequestMethod.GET)
    @ApiOperation(value = "获取his公用代码", notes = "获取his公用代码", response = THisCodeItem.class)
    @ApiImplicitParam(name = "code", value = "1中药用法 4中药单位 " +
            "5内服处方服法 6内服处方频次 7内服处方服药时间 8内服处方浓煎" +
            "9外用处方浓煎 11外用方式 12熏蒸仪选择 13外用处方频次 " +
            "21适宜技术方类型 22针刺项目 " +
            "29体质辨识结果项 30制剂药品用法 31制剂药品频次 33无糖" +
            "34证件类型" +
            "record_type 病历分类" +
            "CVX-WZ-SDM 望诊-舌" +
            "CVX-WZ-TDM 望诊-苔" +
            "CVX-WZ-YSYKWDM 问诊-饮食与口味" +
            "CVX-WZ-SMDM 问诊-睡眠" +
            "CVX-WZ-DBDM 问诊-大便" +
            "CVX-QZ-MZDM 切诊-脉诊",
            paramType = "query", required = true)
    @ResponseBody
    @LogAnnotation("获取his公用代码")
    public ResEntity hisCode(String code) {
        if ("1".equals(code)) {
            code = Constant.CODE_MAT_USEAGE;
        } else if ("30".equals(code)) {
            code = Constant.CODE_PREPARATION_MAT_USEAGE;
        } else if ("31".equals(code)) {
            code = Constant.CODE_PREPARATION_MAT_FREQUENCY;
        }

        return ResEntity.success(tSysCodeService.getCodeByDicCode(code));
    }


    @RequestMapping(value = "treatment/searchMat", method = RequestMethod.GET)
    @ApiOperation(value = "搜索药品", response = CenterHisMappingVO.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "val", value = "搜索药品关键字", paramType = "query", required = true),
            @ApiImplicitParam(name = "centerYplx", value = "药品类型ID", paramType = "query"),
            @ApiImplicitParam(name = "storeId", value = "药房ID", paramType = "query"),
            @ApiImplicitParam(name = "chanDi", value = "产地ID", paramType = "query"),
            @ApiImplicitParam(name = "manySpeSwitch", value = "药品开方多规格下拉开关 1是 0否", paramType = "query")
    }
    )
    @LogAnnotation("搜索药品")
    @ResponseBody
    public Object searchMat(String val, String centerYplx, String storeId, String drugId, String chanDi, Page page, Integer manySpeSwitch) throws UnknownHostException {
        return searchMatService.searchMat(val, centerYplx, storeId, drugId, chanDi, page, manySpeSwitch);
    }

    @RequestMapping(value = "treatment/searchPreparationMat", method = RequestMethod.GET)
    @ApiOperation(value = "搜索中药制剂", response = CenterHisMappingVO.class)
    @ApiImplicitParams({@ApiImplicitParam(name = "val", value = "搜索中药制剂关键字", paramType = "query", required = true)})
    @LogAnnotation("搜索中药制剂")
    @ResponseBody
    public Object searchPreparationMat(String val, Page page) {
        return searchMatService.searchPreparationMat(val, page);
    }

    @RequestMapping(value = "treatment/moreMat", method = RequestMethod.GET)
    @ApiOperation(value = "显示更多药品", response = CenterHisMappingVO.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "matId", value = "知识库药品ID", paramType = "query", required = true),
            @ApiImplicitParam(name = "hisYpdm", value = "HIS药品代码", paramType = "query", required = true),
            @ApiImplicitParam(name = "centerYplx", value = "药品类型ID", paramType = "query"),
            @ApiImplicitParam(name = "storeId", value = "药房ID", paramType = "query"),
            @ApiImplicitParam(name = "chanDi", value = "产地ID", paramType = "query"),
            @ApiImplicitParam(name = "manySpeSwitch", value = "药品开方多规格下拉开关 1是 0否", paramType = "query")
    })
    @LogAnnotation("显示更多药品")
    @ResponseBody
    public Object moreMat(String matId, String hisYpdm, String centerYplx, String storeId, String chanDi, Page page, Integer manySpeSwitch) {
        return searchMatService.getMoreMat(matId, hisYpdm, centerYplx, storeId, chanDi, page, manySpeSwitch);
    }

    @RequestMapping(value = "treatment/transMat", method = RequestMethod.POST)
    @ApiOperation(value = "转换处方药品",
            notes = "matId 知识库药品ID  必填\n hisYpdm HIS药品ID  必填\n centerYplx 中药类型\n centerStoreId 药房ID\n",
            response = CenterHisMappingVO.class)
    @LogAnnotation("转换处方药品")
    @ResponseBody
    public ResEntity transMat(@RequestBody CenterHisMappingVORequest list) {
        return searchMatService.transMat(list);
    }

    @RequestMapping(value = "treatment/transPreparationMat", method = RequestMethod.POST)
    @ApiOperation(value = "转换知识库制剂", notes = "matId 知识库制剂ID  必填", response = CenterHisMappingVO.class)
    @LogAnnotation("转换知识库制剂")
    @ResponseBody
    public ResEntity transMatKnow(@RequestBody CenterHisMappingVORequest list) {
        return searchMatService.transPreparationMat(list);
    }


    @RequestMapping(value = "treatment/check", method = RequestMethod.POST)
    @ApiOperation(value = "校验处方")
    @LogAnnotation(value = "校验处方", isWrite = true)
    @ResponseBody
    public ResEntity checkPres(@RequestBody TRecord record) {
        //增加是否调用合理安全用药SDK
        if (Constant.BASIC_STRING_ONE.equals(tSysParamService.getSysParam(Constant.CHECK_RATIONAL_USE_BY_SDK).getParValues())) {
            return validRecordBySDKService.validRecord(record);
        } else {
            //走原来逻辑
            return validRecordService.validRecord(record);
        }

    }

    @RequestMapping(value = "treatment/validDailyMaxNum", method = RequestMethod.POST)
    @ApiOperation(value = "校验中药制剂日最大开药量")
    @LogAnnotation(value = "校验中药制剂日最大开药量")
    @ResponseBody
    public Object validPreparationItemDailyMaxNum(@RequestBody List<TPrescriptionPreparationItem> preparationItemList) {

        return validRecordService.validPreparationItemDailyMaxNum(preparationItemList);
    }

    @RequestMapping(value = "treatment/save", method = RequestMethod.POST)
    @ApiOperation(value = "保存处方")
    @LogAnnotation(value = "保存处方", isWrite = true)
    @ResponseBody
    public ResEntity savePres(@RequestBody TRecord record) {
        return treatmentService.savePre(record);

    }

    @RequestMapping(value = "treatment/saveAndPush", method = RequestMethod.POST)
    @ApiOperation(value = "保存并推送")
    @LogAnnotation(value = "保存并推送", isWrite = true)
    @ResponseBody
    public ResEntity saveAndPush(@RequestBody TRecord record) {
        ResEntity resEntity = treatmentService.saveAndPush(record);
        if (resEntity.getStatus()) {
            Map<String, Object> result = (Map<String, Object>) resEntity.getData();
            String registerId = (String) result.get("registerId");

            List<String> checkedPreNos = (List<String>) result.get("checkedPreNos");
            for (String preNo : checkedPreNos) {

                //审核通过推送HIS
                ResEntity res = interfaceRestTemplate.sendPreItem(registerId, preNo);
                if (!res.getStatus()) {
                    return ResEntity.entity(false, "保存成功，推送失败\r\n"
                            + res.getMessage(), resEntity.getData());
                }
            }
        }
        return resEntity;
    }


    @RequestMapping(value = "treatment/pre/delete", method = RequestMethod.GET)
    @ApiOperation(value = "删除处方", notes = "只能删除一个")
    @LogAnnotation(value = "删除处方", isWrite = true)
    @ResponseBody
    public ResEntity delPres(String preId) {
        return prescriptionService.delPres(preId);
    }

    @RequestMapping(value = "treatment/pre/refund", method = RequestMethod.GET)
    @ApiOperation(value = "处方退费", notes = "只能一个处方退费")
    @LogAnnotation(value = "处方退费", isWrite = true)
    @ResponseBody
    public ResEntity refundPres(String preId, String recId) {
        return prescriptionService.refundPres(preId, recId);
    }

    @RequestMapping(value = "treatment/pre/repeal", method = RequestMethod.GET)
    @ApiOperation(value = "撤销医嘱", notes = "撤销医嘱")
    @LogAnnotation(value = "撤销医嘱", isWrite = true)
    @ResponseBody
    public ResEntity repealPres(String preId, String registerId) {
        return prescriptionService.repealPres(preId, registerId);
    }

    @RequestMapping(value = "treatment/pre/print", method = RequestMethod.GET)
    @ApiOperation(value = "打印处方", notes = "多个处方ID英文逗号拼接")
    @LogAnnotation(value = "打印处方", isWrite = true)
    @ResponseBody
    public ResEntity printPres(String recId, String preNo, String preId) {
        return prescriptionService.printPres(recId, preNo, preId, "1");
    }

    @RequestMapping(value = "treatment/pre/printDZBL", method = RequestMethod.GET)
    @ApiOperation(value = "中医电子病历打印")
    @LogAnnotation(value = "中医电子病历打印", isWrite = true)
    @ResponseBody
    public ResEntity printPres2(String recId, String preNo, String preId) {
        return prescriptionService.printPres(recId, preNo, preId, "2");
    }

    @RequestMapping(value = "treatment/pre/getPages", method = RequestMethod.GET)
    @ApiOperation(value = "展示处方", notes = "通过preNo或registerId或visitNo查询")
    @LogAnnotation(value = "展示处方", isWrite = true)
    @ResponseBody
    public Object getPreList(TPrescription tPrescription, Page page, String visitNo, String registerId) {
        return prescriptionService.getPreList(tPrescription, page, visitNo, registerId);
    }

    @RequestMapping(value = "/drug/tCenterHisYpmlmx/update/findObj", method = RequestMethod.GET)
    @LogAnnotation("加载药品明细详情")
    @ResponseBody
    public Object getObj(String ypmlId, String yaopindm) {
        return validRecordService.findObj(ypmlId, yaopindm);
    }

    @Autowired
    private WithholdStockService withholdStockService;


    @RequestMapping(value = "treatment/cancelSave", method = RequestMethod.POST)
    @ApiOperation(value = "取消保存处方")
    @LogAnnotation(value = "取消保存处方", isWrite = true)
    @ResponseBody
    public ResEntity cancelSave(@RequestBody TRecord record) {
        return withholdStockService.cancelWithholdStock(record);

    }

    @RequestMapping(value = "treatment/sdkPreSave", method = RequestMethod.POST)
    @ApiOperation(value = "安全合理用药SDK调用")
    @LogAnnotation(value = "安全合理用药SDK调用", isWrite = true)
    @ResponseBody
    public ResEntity safeSDKPreSave(@RequestBody ValidRecordSend validRecordSend) {
        return validRecordBySDKService.safeSDKPreSave(validRecordSend);
    }

    @RequestMapping(value = "treatment/dosageFormDetailsList", method = RequestMethod.POST)
    @ApiOperation(value = "搜索制剂费用明细列表")
    @LogAnnotation(value = "搜索制剂费用明细列表", isWrite = true)
    @ResponseBody
    public Object dosageFormDetailsList(@RequestBody DosageFormDicListReq dosageFormDicListReq) {
        if (StringUtils.isBlank(dosageFormDicListReq.getItemId())){
            return ResEntity.error("缺少参数itemId");
        }
        if (StringUtils.isBlank(dosageFormDicListReq.getRegisterId())){
            return ResEntity.error("缺少挂号id");
        }
        if (dosageFormDicListReq.getDicType() == null){
            return ResEntity.error("缺少类型");
        }
        return parameterRedisService.getDosageFormDicList(dosageFormDicListReq);
    }

}