package com.jiuzhekan.cbkj.controller.business.setting;

import com.jiuzhekan.cbkj.beans.business.record.TBusinessQuestion;
import com.jiuzhekan.cbkj.beans.business.setting.TBusinessQuestionEx;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.common.annotaionUtil.StatisticsFunction;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.service.business.setting.TBusinessQuestionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping(value = "tBusinessQuestion/")
@Api(value = "常见问题接口", tags = "常见问题接口")
public class TBusinessQuestionController {

    @Autowired
    private TBusinessQuestionService tBusinessQuestionService;

    @RequestMapping(value = "getPages",method = RequestMethod.GET)
    @ApiOperation(value = "分页查询常见问题",notes = "分页查询常见问题",response = TBusinessQuestion.class)
    @ResponseBody
    @LogAnnotation("分页查询常见问题")
    public Object getApps(TBusinessQuestion tBusinessQuestion, Page page){
        Object obj = tBusinessQuestionService.getPageDatas(tBusinessQuestion,page);
        return obj;
    }


    @RequestMapping(value = "update/findObj",method = RequestMethod.GET)
    @LogAnnotation( value = "查看常见问题详情")
    @StatisticsFunction(source="设置", value = "常见问题查阅次数")
    @ApiOperation(value = "查看常见问题详情",notes = "查看常见问题详情",response = TBusinessQuestion.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "问题id", dataType = "String", paramType = "query", required = true),
            @ApiImplicitParam(name = "operateType", value = "操作类型(查看答案时获取详情传 operateType 为1 ,查看人次加1 ; 修改时获取详情可不传)"
                    , dataType = "String", paramType = "query"),
    })
    @ResponseBody
    public Object getObj(String id, String operateType){
        return tBusinessQuestionService.findObj(id, operateType);
    }

    @RequestMapping(value = "insert",method = RequestMethod.POST)
    @LogAnnotation(isWrite = true,value = "新增常见问题")
    @ApiOperation(value = "新增常见问题",notes = "新增常见问题")
    @ResponseBody
    public Object insert(@RequestBody TBusinessQuestion tBusinessQuestion) {
        ResEntity result = tBusinessQuestionService.insert(tBusinessQuestion);
        return result;
    }

    @RequestMapping(value ="update",method = RequestMethod.POST)
    @LogAnnotation(isWrite = true,value = "修改常见问题")
    @ApiOperation(value = "修改常见问题",notes = "修改常见问题")
    @ResponseBody
    public Object update(@RequestBody TBusinessQuestion tBusinessQuestion) {
        ResEntity result = tBusinessQuestionService.update(tBusinessQuestion);
        return result;
    }

    @RequestMapping(value="deleteLis",method = RequestMethod.GET)
    @LogAnnotation(value = "删除常见问题",isWrite = true)
    @ApiOperation(value = "删除常见问题",notes = "删除常见问题")
    @ResponseBody
    public Object deleteLis(String ids) throws Exception {
        ResEntity result   = tBusinessQuestionService.deleteLis(ids);
        return result;
    }

    @RequestMapping(value = "insertUpdateEx",method = RequestMethod.POST)
    @LogAnnotation(isWrite = true,value = "常见问题扩展表数据插入修改")
    @ApiOperation(value = "插入/修改常见问题扩展表",notes = "插入/修改常见问题扩展表")
    @ResponseBody
    public Object insertUpdateEx(@RequestBody TBusinessQuestionEx tBusinessQuestionEx) {
        ResEntity result = tBusinessQuestionService.insertUpdateEx(tBusinessQuestionEx);
        return result;
    }
}