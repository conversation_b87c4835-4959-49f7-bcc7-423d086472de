package com.jiuzhekan.cbkj.mapper.business.patients;

import com.jiuzhekan.cbkj.beans.business.patients.TPatients;
import com.jiuzhekan.cbkj.beans.business.patients.VO.TPatientsReqVO;
import com.jiuzhekan.cbkj.beans.business.record.model.SickPersonModel;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;

import java.util.List;

@Component
public interface TPatientsMapper extends BaseMapper<TPatients> {
    /**
     * 获取患者信息数量
     *
     * @param tPatients
     * @return
     */
    int getPatientsCountByPatients(TPatients tPatients);

    TPatients checkPatientsByPatients(TPatients tPatients);

    TPatients getPatientsByPatients(TPatients tPatients);

    List<TPatients> getTodayPatients(TPatientsReqVO tPatients);

    List<TPatients> getMyPatients(TPatientsReqVO tPatients);

    TPatients findByInsCodeAndPatientId(@Param("patientId")String patientId, @Param("insCode") String insCode);

    void insertPatients(TPatients tPatients);

    SickPersonModel findModelByRegisterId(@Param("registerId") String registerId);

    TPatients getOneByMobileAndName(TPatients tPatients);
}