package com.jiuzhekan.statistics.controller.borad;

import com.jiuzhekan.cbkj.beans.statistics.TStatistics;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.DateUtil;
import com.jiuzhekan.cbkj.mapper.statistics.TStatisticsMapper;
import com.jiuzhekan.cbkj.service.statistics.TStatisticsService;
import com.jiuzhekan.statistics.beans.register.QueryParam;
import com.jiuzhekan.statistics.beans.register.StatisticsChangeResult;
import com.jiuzhekan.statistics.service.board.BoardService;
import com.jiuzhekan.statistics.service.statistics.StatisticsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.jiuzhekan.statistics.utils.DateUtils.getMonth;

/**
 * <AUTHOR>
 **/
@Controller
@Api(value = "监管看板接口", tags = "监管看板接口")
@RequestMapping("board")
public class BoardController {
    private final BoardService boardService;

    public BoardController(BoardService boardService) {
        this.boardService = boardService;
    }

    @RequestMapping(value = "board/one", method = RequestMethod.GET)
    @ApiOperation(value = "获取监管平台看板-1", notes = "获取监管平台看板-1")
    @ApiImplicitParams({
            @ApiImplicitParam(value="时间类型(1.昨天 6.本周 7.本月 8.本年 0全部)",name="timeType",dataType="Integer",paramType = "query",required = true),
            @ApiImplicitParam(value="未选择不要传",name="appId",dataType="String",paramType = "query"),
            @ApiImplicitParam(value="未选择不要传",name="insCode",dataType="String",paramType = "query")
    })
    @ResponseBody
    public ResEntity getIndexPlotDataOne(Integer timeType, String appId, String insCode) throws ParseException {
        List<Map<String, Object>> indexPlotDataOne = boardService.getIndexPlotDataOne(timeType, appId, insCode);
        return ResEntity.entity(true, Constant.SUCCESS_DX, indexPlotDataOne);
    }

    /**
     * @param timeType 1昨天,2近半月,3近一个季度,4近一年 5 近半年
     * @param appId
     * @param insCode
     */
    @RequestMapping(value = "board/two", method = RequestMethod.GET)
    @ApiOperation(value = "获取监管平台看板-2", notes = "获取监管平台看板-2")
    @ApiImplicitParams({
            @ApiImplicitParam(value="时间类型(4近一年 5 近半年)",name="timeType",dataType="Integer",paramType = "query",required = true),
            @ApiImplicitParam(value="未选择不要传",name="appId",dataType="String",paramType = "query"),
            @ApiImplicitParam(value="未选择不要传",name="insCode",dataType="String",paramType = "query")
    })
    @LogAnnotation("获取监管平台看板-2")
    @ResponseBody
    public ResEntity getIndexPlotData(Integer timeType, String appId, String insCode) throws ParseException {
        HashMap<Object, Object> indexPlotData = boardService.getIndexPlotData(timeType, appId, insCode);
        return ResEntity.entity(true, Constant.SUCCESS_DX, indexPlotData);
    }
}
