package com.jiuzhekan.cbkj.service.business.sysCode;

import com.jiuzhekan.cbkj.beans.business.sysCode.TSysCode;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.service.redis.ParameterRedisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class TSysCodeService {

    @Autowired
    private ParameterRedisService parameterRedisService;

    public TSysCode getCodeByDicCode(String dicCode) {
        return parameterRedisService.getDicCode(AdminUtils.getCurrentAppId(), AdminUtils.getCurrentInsCode(), AdminUtils.getCurrentDeptId(), dicCode);
    }

    public TSysCode getCodeByDicCode(String dicCode, String displayId) {
        return parameterRedisService.getDicCode(AdminUtils.getCurrentAppId(), AdminUtils.getCurrentInsCode(), AdminUtils.getCurrentDeptId(), displayId, dicCode);
    }

}