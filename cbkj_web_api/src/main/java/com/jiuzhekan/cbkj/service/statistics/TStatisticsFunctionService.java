package com.jiuzhekan.cbkj.service.statistics;

import com.jiuzhekan.cbkj.beans.statistics.FunctionEnum;
import com.jiuzhekan.cbkj.beans.statistics.TStatisticsFunction;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.DateUtil;
import com.jiuzhekan.cbkj.mapper.statistics.TStatisticsFunctionMapper;
import com.jiuzhekan.cbkj.service.redis.RedisService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Service
public class TStatisticsFunctionService {

    @Autowired
    private TStatisticsFunctionMapper tStatisticsFunctionMapper;
    @Autowired
    private RedisService redisService;

    /**
     * 查询功能使用量总和
     *
     * @param beginDate beginDate
     * @param endDate   endDate
     * @return com.jiuzhekan.cbkj.beans.sysBeans.ResEntity
     * <AUTHOR>
     * @date 2021/4/15
     */
    public ResEntity statisticsSum(String beginDate, String endDate) {

        if (StringUtils.isBlank(beginDate)) {
            Calendar cal = Calendar.getInstance();
            cal.add(Calendar.YEAR, -1);
            beginDate = DateUtil.getDateFormats(DateUtil.YYYY_MM, cal.getTime());
        }
        if (StringUtils.isBlank(endDate)) {
            endDate = DateUtil.getDateFormats(DateUtil.YYYY_MM, new Date());
        }

        beginDate += "-01 00:00:00";
        endDate += "-31 23:59:59";

        List<TStatisticsFunction> list = new ArrayList<>();

        FunctionEnum[] funs = FunctionEnum.values();

        for (FunctionEnum fun : funs) {
            list.add(newStatisticFunction(fun.getName(), fun.getSource(), beginDate, endDate));
        }

        return ResEntity.success(list);
    }

    /**
     * 查询功能每月使用量
     *
     * @param beginDate beginDate
     * @param endDate   endDate
     * @return com.jiuzhekan.cbkj.beans.sysBeans.ResEntity
     * <AUTHOR>
     * @date 2021/4/15
     */
    public ResEntity statisticsMonthly(String functionName, String functionSource, String beginDate, String endDate) {

        List<TStatisticsFunction> list = new ArrayList<>();

        FunctionEnum fun = FunctionEnum.find(functionSource, functionName);
        if (fun == null) {
            return ResEntity.success(list);
        }

        Calendar cal = Calendar.getInstance();

        Date begin = null;
        Date end = null;

        if (StringUtils.isNotBlank(beginDate)) {
            begin = DateUtil.getDateFormatd(beginDate, DateUtil.YYYY_MM);
        }
        if (StringUtils.isNotBlank(endDate)) {
            end = DateUtil.getDateFormatd(endDate, DateUtil.YYYY_MM);
        }

        if (end == null) {
            end = cal.getTime();
        }
        if (begin == null) {
            cal.add(Calendar.YEAR, -1);
            begin = cal.getTime();
        }

        while (begin.compareTo(end) <= 0) {

            String beginStr = DateUtil.getDateFormats(DateUtil.YYYY_MM, begin) + "-01 00：00：00";
            String endStr = DateUtil.getDateFormats(DateUtil.YYYY_MM, begin) + "-31 23:59:59";

            TStatisticsFunction tsf = newStatisticFunction(functionName, functionSource, beginStr, endStr);
            tsf.setCreateDate(DateUtil.getDateFormats(DateUtil.YYYY_MM, begin));
            list.add(tsf);

            cal.setTime(begin);
            cal.add(Calendar.MONTH, 1);
            begin = cal.getTime();
        }
        return ResEntity.success(list);
    }

    private TStatisticsFunction newStatisticFunction(String name, String source, String beginDate, String endDate) {
        Date begin = DateUtil.getDateFormatd(beginDate, DateUtil.YYYY_MM_DD_HH_MM_SS);
        Date end = DateUtil.getDateFormatd(endDate, DateUtil.YYYY_MM_DD_HH_MM_SS);

        TStatisticsFunction tsf = new TStatisticsFunction(name, source, beginDate, endDate);

        tsf.setStartTime(begin);
        tsf.setEndTime(end);

        FunctionEnum fun = FunctionEnum.find(source, name);

        if (fun != null) {
            tsf.setShowChart(fun.isShowChart());

            if (fun.getType() == 2) {

                TStatisticsFunction p1 = new TStatisticsFunction(name, source);
                Integer usageTimes = tStatisticsFunctionMapper.lastUsageTimes(p1);
                tsf.setUsageTimes(new BigDecimal(usageTimes == null ? 0 : usageTimes));

            } else if (fun.getType() == 3) {

                TStatisticsFunction p1 = new TStatisticsFunction(fun.getNumerator(), beginDate, endDate);
                Integer usageTimes1 = tStatisticsFunctionMapper.sumUsageTimes(p1);
                usageTimes1 = usageTimes1 == null ? 0 : usageTimes1;

                TStatisticsFunction p2 = new TStatisticsFunction(fun.getDenominator(), beginDate, endDate);
                Integer usageTimes2 = tStatisticsFunctionMapper.sumUsageTimes(p2);
                usageTimes2 = usageTimes2 == null || usageTimes2 == 0 ? 1 : usageTimes2;

                tsf.setUsageTimes(new BigDecimal(usageTimes1 * 100).divide(new BigDecimal(usageTimes2), 2, BigDecimal.ROUND_HALF_UP));
                tsf.setSuffix("%");

            } else if (fun.getType() == 4) {

                TStatisticsFunction p1 = new TStatisticsFunction(fun.getNumerator(), beginDate, endDate);
                Integer usageTimes1 = tStatisticsFunctionMapper.sumUsageTimes(p1);
                usageTimes1 = usageTimes1 == null ? 0 : usageTimes1;

                TStatisticsFunction p2 = new TStatisticsFunction(fun.getDenominator(), null);
                Integer usageTimes2 = tStatisticsFunctionMapper.lastUsageTimes(p2);
                usageTimes2 = usageTimes2 == null || usageTimes2 == 0 ? 1 : usageTimes2;

                tsf.setUsageTimes(new BigDecimal(usageTimes1).divide(new BigDecimal(usageTimes2), 2, BigDecimal.ROUND_HALF_UP));

            } else {


                Integer usageTimes = tStatisticsFunctionMapper.sumUsageTimes(tsf);
                tsf.setUsageTimes(new BigDecimal(usageTimes == null ? 0 : usageTimes));
            }
        }
        return tsf;
    }


    /**
     * 累计每天功能项使用次数
     *
     * @param functionName   functionName
     * @param functionSource functionSource
     * <AUTHOR>
     * @date 2021/4/13
     */
    @Transactional(rollbackFor = Exception.class)
    public void addUpFunctionUsageTimes(String functionName, String functionSource) {

        String key = "client::function::usage-times:" + functionSource + "::" + functionName;
        Object o = redisService.get(key);
        TStatisticsFunction oldFun = null == o ? null : (TStatisticsFunction) o;

        TStatisticsFunction newFun = addUpFunctionUsageTimes(functionName, functionSource, oldFun);

        redisService.set(key, newFun);
    }

    /**
     * 累计每天功能项使用次数，并保存不是当天的统计
     *
     * @param functionName   functionName
     * @param functionSource functionSource
     * @param old            old
     * <AUTHOR>
     * @date 2021/4/13
     */
    @Transactional(rollbackFor = Exception.class)
    public TStatisticsFunction addUpFunctionUsageTimes(String functionName, String functionSource, TStatisticsFunction old) {

        String nowDate = DateUtil.getDateFormats(DateUtil.YYYY_MM_DD, new Date());
        TStatisticsFunction tsf;

        if (old == null) {
            tsf = new TStatisticsFunction();
            tsf.setUsageTimes(new BigDecimal(1));
            tsf.setCreateDate(nowDate);
            tsf.setFunctionName(functionName);
            tsf.setFunctionSource(functionSource);
        } else {

            if (!nowDate.equals(old.getCreateDate())) {

                tStatisticsFunctionMapper.insert(old);

                tsf = new TStatisticsFunction();
                tsf.setUsageTimes(new BigDecimal(1));
                tsf.setCreateDate(nowDate);
                tsf.setFunctionName(functionName);
                tsf.setFunctionSource(functionSource);
            } else {

                tsf = old;
                tsf.setUsageTimes(tsf.getUsageTimes().add(new BigDecimal(1)));
            }
        }
        return tsf;
    }


}
