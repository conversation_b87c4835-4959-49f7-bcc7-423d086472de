package com.jiuzhekan.healthpres.beans.heathPresControllerVo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/6/3 16:26
 * @Version 1.0
 */
@Data
public class LookHealthPreMainItemResponse {
    @ApiModelProperty(value = "保存报告结果明细的id")
    private String resultItemId;

    @ApiModelProperty(value = "标题")
    private String itemTitle;

    @ApiModelProperty(value = "内容")
    private String itemContent;

    @ApiModelProperty(value = "1.纯文本2.下拉选择")
    private Integer itemType;

    @ApiModelProperty(value = "如果是下拉则这个字段会保存下拉的id用于回显")
    private String itemSelectId;

    @ApiModelProperty(value = "排序（升序排序）")
    private Integer itemSort;

    @ApiModelProperty(value = "健康处方中是否展示：0前端展示1前端不展示")
    private Integer showType;

    @ApiModelProperty(value = "")
    private String resultId;

    @ApiModelProperty(value = "穴位acuList")
    private List<LookHealthPreMainItemAcuResponse> lookHealthPreMainItemAcuResponseList;
}
