package com.jiuzhekan.cbkj.service.formula.center;

import com.jiuzhekan.cbkj.common.utils.Constant;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Map;

/**
 * CenterFormulaStock
 * <p>
 * 杭州聪宝科技有限公司
 *
 * <AUTHOR>
 * @date 2021/8/11
 */
@Data
@NoArgsConstructor
public class CenterFormulaStock {
    /**
     * 膏方ID
     */
    public String gaofangid;
    /**
     * 库存数量
     */
    public String KUCUNSL;
    /**
     * 应用ID，即药房ID
     */
    public String YINGYONGID;
    /**
     * APP_ID
     */
    public String APPID;
    /**
     * 机构ID
     */
    public String INSID;

    public CenterFormulaStock(Map<String, Object> map) {
        this.gaofangid = map.getOrDefault("gaofangid", Constant.BASIC_STRING_SPACE).toString();
        this.KUCUNSL = map.getOrDefault("KUCUNSL", Constant.BASIC_STRING_SPACE).toString();
        this.YINGYONGID = map.getOrDefault("YINGYONGID", Constant.BASIC_STRING_SPACE).toString();
        this.APPID = map.getOrDefault("APPID", Constant.BASIC_STRING_SPACE).toString();
        this.INSID = map.getOrDefault("INSID", Constant.BASIC_STRING_SPACE).toString();
    }

    public BigDecimal getKucunslToNumber() {
        try {
            return new BigDecimal(KUCUNSL);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new BigDecimal("0");
    }
}
