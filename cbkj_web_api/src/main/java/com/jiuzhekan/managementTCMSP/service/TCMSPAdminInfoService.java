package com.jiuzhekan.managementTCMSP.service;

import com.github.pagehelper.PageHelper;

import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.managementTCMSP.bean.AppsInfo;
import com.jiuzhekan.managementTCMSP.bean.TCMSPAdminInfoReq;
import com.jiuzhekan.managementTCMSP.mapper.AdminInfoMapper;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/8/13 14:04
 * @Version 1.0
 */
@Service
public class TCMSPAdminInfoService {

    private final AdminInfoMapper adminInfoMapper;

    public TCMSPAdminInfoService(AdminInfoMapper adminInfoMapper) {
        this.adminInfoMapper = adminInfoMapper;
    }

    public Object getPageDatas(TCMSPAdminInfoReq admin, Page page) {

        PageHelper.startPage(page.getPage(), page.getLimit());
        List<AppsInfo> lis = adminInfoMapper.getPageDatas(admin);
        for (int i = 0; i < lis.size(); i++) {
//            if (StringUtils.isNotBlank(lis.get(i).getRnamess()) ){
//                AtomicReference<String> temp = new AtomicReference<>("");
//                List<String> rnamess = Arrays.stream(lis.get(i).getRnamess().split(",")).distinct().collect(Collectors.toList());
//                rnamess.forEach(r -> temp.set(temp + r + ","));
//                lis.get(i).setRnamess(temp.toString());
//            }
            if (StringUtils.isNotBlank(lis.get(i).getInsNames())) {
                AtomicReference<String> temp = new AtomicReference<>("");
                List<String> rnamess = Arrays.stream(lis.get(i).getInsNames().split(",")).distinct().collect(Collectors.toList());
                rnamess.forEach(r -> temp.set(temp + r + ","));
                lis.get(i).setInsNames(temp.toString());
            }
            if (StringUtils.isNotBlank(lis.get(i).getDoctorMultipoint())) {
                AtomicReference<String> temp = new AtomicReference<>("");
                List<String> rnamess = Arrays.stream(lis.get(i).getDoctorMultipoint().split(",")).distinct().collect(Collectors.toList());
                rnamess.forEach(r -> temp.set(temp + r + ","));
                lis.get(i).setDoctorMultipoint(temp.toString());
            }
        }
        Object result = Page.getLayUiTablePageData(lis);
        return result;
    }

    /**
     * 保存或者编辑更新用户信息
     *
     * @param admin
     * @return
     */
    public ResEntity saveOrEdit(AppsInfo admin) {
        if (StringUtils.isBlank(admin.getUserId())) {

        } else {

        }
        return ResEntity.success();
    }

    public Object getAppList() {
        List<HashMap<String,String>> list = adminInfoMapper.getAppList();
        return ResEntity.success(list);
    }

    public Object getInsList(String appId) {
        List<HashMap<String,String>> list = adminInfoMapper.getInsList(appId);
        //返回是所有的机构，但是需要重构层级，有上下级，需要用到树形结构，使用递归

        return ResEntity.success(list);
    }
}
