package com.jiuzhekan.recordquality.mapper;

import com.jiuzhekan.recordquality.beans.RecordQualityStaticsListReq;
import com.jiuzhekan.recordquality.beans.TRecordQualityStatistics;
import org.springframework.stereotype.Component;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;

import java.util.List;

@Component
public interface TRecordQualityStatisticsMapper extends BaseMapper<TRecordQualityStatistics>{


    List<TRecordQualityStatistics> getPageListByMyObj(RecordQualityStaticsListReq recordQualityStaticsListReq);
}