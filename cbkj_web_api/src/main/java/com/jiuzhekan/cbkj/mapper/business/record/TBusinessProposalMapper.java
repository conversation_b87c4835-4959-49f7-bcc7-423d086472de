package com.jiuzhekan.cbkj.mapper.business.record;

import com.jiuzhekan.cbkj.beans.business.record.TBusinessProposal;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public interface TBusinessProposalMapper extends BaseMapper<TBusinessProposal> {

    long insertProposal(TBusinessProposal tBusinessProposal);

    long deleteByProArr(@Param("list") List<String> split, @Param("pro") TBusinessProposal pro);
}