package com.jiuzhekan.cbkj.controller.business;

import com.jiuzhekan.cbkj.beans.business.ProjectBinding;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.service.redis.ParameterRedisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * Created by zbh on 2025/2/24 14:40
 *
 * @description：适宜技术收费项目绑定相关接口
 */
@Api(tags = "收费项目绑定相关接口")
@RequestMapping("/pro/binding")
@Controller
public class ProjectBindingController {

    private final ParameterRedisService service;

    public ProjectBindingController(ParameterRedisService parameterRedisService) {
        this.service = parameterRedisService;
    }


    @ApiOperation(value = "获取收费项目绑定列表", response = ProjectBinding.class)
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    @ResponseBody
    public ResEntity getList(String dicId,String keyWord) {
        //预留后期搜索功能
        String appId = AdminUtils.getCurrentAppId();
        String insCode = AdminUtils.getCurrentInsCode();
        String deptId = AdminUtils.getCurrentDeptId();
        return service.getRelevanceItemList(appId,insCode ,deptId
                , dicId,keyWord);
    }
}
