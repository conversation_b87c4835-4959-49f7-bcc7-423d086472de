package com.jiuzhekan.healthpres.beans.healthPrescribingControllerVo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/5/28 14:12
 * @Version 1.0
 */
@Data
@ApiModel
public class GetTypesHealthPrescribingContent {

    @ApiModelProperty(value = "内容名称")
    private String OptionTypeName;

    @ApiModelProperty(value = "内容id")
    private String OptionTypeId;






    @ApiModelProperty(value = "处方内容明细")
    private GetTypesHealthPrescribingItem item;

}
