package com.jiuzhekan.cbkj.service.statistics;

import com.jiuzhekan.cbkj.mapper.statistics.TRecordStatisticsMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 描述  临床业务监管模块
 *
 * <AUTHOR>
 * @Date 2020/2/18
 */
@Service
public class TRecordStatisticsService {

    @Autowired
    TRecordStatisticsMapper trecordStatisticsMapper;

    /**
     * 患者分析，患者性别比
     *  appId 医联体id
     *  insCode 医疗机构id
     *  recTreTimeBegin 开始时间
     *  recTreTimeEnd 结束时间
     *  recGender 性别  F:男，M:女
     *  recAgeBegin 年龄开始时间
     *  recAgeEnd   年龄结束时间
     * @return  数量
     * <AUTHOR>
     */
    public Integer getCountByGenderAndAge(String appId,
                                    String insCode,
                                    String recTreTimeBegin,
                                    String recTreTimeEnd,
                                    String recGender,
                                    Integer recAgeBegin,
                                    Integer recAgeEnd){
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("appId",appId);
        paramMap.put("insCode",insCode);
        paramMap.put("recTreTimeBegin",recTreTimeBegin);
        paramMap.put("recTreTimeEnd",recTreTimeEnd);
        paramMap.put("recGender",recGender);
        paramMap.put("recAgeBegin",recAgeBegin);
        paramMap.put("recAgeEnd",recAgeEnd);
        return trecordStatisticsMapper.getCountByGenderAndAge(paramMap);
    }

    /**
     * 根据时间段获取前几位疾病病名
     *  appId 医联体id
     *  insCode 医疗机构id
     *  recTreTimeBegin 开始时间
     *  recTreTimeEnd 结束时间
     *  recAgeBegin 年龄开始时间
     *  recAgeEnd   年龄结束时间
     *  num 前几位
     * @return
     * <AUTHOR>
     */
    public List<Map> getDisNameGroupDisNameAge (String appId,
                                                        String insCode,
                                                        String recTreTimeBegin,
                                                        String recTreTimeEnd,
                                                        Integer recAgeBegin,
                                                        Integer recAgeEnd,
                                                        Integer num){
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("appId",appId);
        paramMap.put("insCode",insCode);
        paramMap.put("recTreTimeBegin",recTreTimeBegin);
        paramMap.put("recTreTimeEnd",recTreTimeEnd);
        paramMap.put("recAgeBegin",recAgeBegin);
        paramMap.put("recAgeEnd",recAgeEnd);
        paramMap.put("num",num);
        return trecordStatisticsMapper.getDisNameGroupDisNameAge(paramMap);
    }
}
