package com.jiuzhekan.cbkj.mapper.business.syndrome;

import com.jiuzhekan.cbkj.beans.business.syndrome.TRecordMaster;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * 保存国医大师的辩证结果表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-25
 */
@Repository
public interface TRecordMasterMapper {
    int insertMasterResult (TRecordMaster tRecordMaster);
    TRecordMaster getMasterRecord(String registerId);
    void deleteByRegisterId(String registerId );

}
