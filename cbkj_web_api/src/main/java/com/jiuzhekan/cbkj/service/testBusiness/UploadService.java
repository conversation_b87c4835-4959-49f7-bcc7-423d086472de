package com.jiuzhekan.cbkj.service.testBusiness;

import com.jcraft.jsch.*;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.sftp.FtpUtil;
import com.jiuzhekan.cbkj.common.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.util.Base64;
import java.util.HashMap;
import java.util.Properties;
import java.util.UUID;

/**
 * UploadService
 * <p>
 * 杭州聪宝科技有限公司
 * <p>
 *
 * <AUTHOR>
 * @date 2021/4/23 11:10
 */
@Service
@Slf4j
public class UploadService {


    @Value("${file.address}")
    private String location;

    @Value("${root.preview}")
    private String preview;

    @Value("${root.upload.relative}")
    private String relative;

    @Value("${ftp.file.url}")
    private String ftpFilePath;

    private static final String BASE64_IMAGES_PNG = "data:image/png;base64,";

    private static final String PDF_FOLDER = "zxkjnPDF/";
    private static final String IMG_FOLDER = "zxkjnIMG/";
//    private static final String ZKXC_PATH = "preview/zkxc/zxkjnPDF";
@Value("${ftp.zkxc.path}")
private String zkxcfthpath;
    private final FtpUtil ftpUtil;

    public UploadService(FtpUtil ftpUtil) {
        this.ftpUtil = ftpUtil;
    }
//    @Async
    public  ResEntity uploadPdf2(MultipartFile pdfFile,String fileName) throws IOException {
        boolean pdfOrZipByContent = FileValidator.isPdfOrZipByContent(pdfFile);
        if (!pdfOrZipByContent){
            return ResEntity.error("文件类型不符合");
        }
        String dateFolder = DateUtil.getDateFormats(DateUtil.YYYYMMDD, null);
        if (pdfFile != null && !pdfFile.isEmpty()) {

            ResEntity resEntity = ftpUtil.upload(ftpFilePath+dateFolder, fileName, pdfFile);
            HashMap<String, String> map = new HashMap<>();
            map.put("path", zkxcfthpath+"/"+dateFolder+"/"+fileName);
            resEntity.setData(map);
            return resEntity;
        }
        return ResEntity.error("失败");
    }


    public String uploadPdfTest(MultipartFile pdfFile, Integer type) {
        JSch jsch = new JSch();
        ChannelSftp sftp = null;
        try {
            String dateFolder = DateUtil.getDateFormats(DateUtil.YYYYMMDD, null);
            String remoteFolder = "/" + dateFolder;

            if (pdfFile != null) {
                String fileName = IDUtil.getID() + (1 == type ? ".pdf" : ".zip");

                Session session = jsch.getSession("root", "************", 22);
                session.setPassword("cbkj123!@#");
                Properties config = new Properties();
                config.put("StrictHostKeyChecking", "no");
                session.setConfig(config);
                session.connect();

                sftp = (ChannelSftp) session.openChannel("sftp");
                sftp.connect();

                // 切换到根目录
                sftp.cd("/");

                // 创建远程目录
                sftp.mkdir(remoteFolder);

                // 上传文件
                File tempFile = File.createTempFile("upload", null);
                pdfFile.transferTo(tempFile);

                sftp.put(tempFile.getAbsolutePath(), remoteFolder + "/" + fileName);

                // 返回文件的远程访问路径
                return "http://************/" + PDF_FOLDER + dateFolder + "/" + fileName;
            }
        } catch (IOException | SftpException e) {
            e.printStackTrace();
            return null;
        } catch (JSchException e) {
            throw new RuntimeException(e);
        } finally {
            if (sftp != null) {
                sftp.disconnect();
            }
        }
        return null;
    }


    /**
     * 上传文件到当前服务器
     *
     * @param file file
     * @return com.jiuzhekan.cbkj.beans.sysBeans.ResEntity
     * <AUTHOR>
     * @date 2021/4/23
     */
    public ResEntity uploadMultipartFile(MultipartFile file) {

        if (file == null || file.isEmpty()) {
            return new ResEntity(false, "未选择文件", null);
        }

        if (!FileTypeUtil.isImage(file) && !FileTypeUtil.isDoc(file) && !FileTypeUtil.isPdf(file)) {
            return new ResEntity(false, "仅支持图片、word、PDF", null);
        }

        String fileName = file.getOriginalFilename();
        String suffix = fileName.substring(fileName.lastIndexOf("."));
        StringBuilder filePath = new StringBuilder(relative)
                .append(DateUtil.getDateFormats(DateUtil.YYYYMMDD, null))
                .append("/")
                .append(UUID.randomUUID().toString())
                .append(suffix);
        File dest = new File(String.format("%s%s", location, filePath.toString()));
        //判断文件父目录是否存在
        if (!dest.getParentFile().exists()) {
            dest.getParentFile().mkdirs();
        }
        try {
            //保存文件
            file.transferTo(dest);
            return new ResEntity(true, Constant.SUCCESS_DX, preview + filePath.toString());
        } catch (IllegalStateException | IOException e) {
            e.printStackTrace();
            return new ResEntity(false, "服务异常", null);
        }
    }


    /**
     * 上传base64文件到当前服务器
     *
     * @param baseStr baseStr
     * @return com.jiuzhekan.cbkj.beans.sysBeans.ResEntity
     * <AUTHOR>
     * @date 2021/4/23
     */
    public ResEntity uploadBase64PngImage(String baseStr) {

        if (StringUtils.isBlank(baseStr)) {
            return ResEntity.error("base64不能为空");
        }

        if (!baseStr.startsWith(BASE64_IMAGES_PNG)) {
            return ResEntity.error("base64内容不是png图片");
        }

        baseStr = baseStr.replace(BASE64_IMAGES_PNG, "");
        byte[] bytes;
        try {

            bytes = Base64.getDecoder().decode(baseStr);
        } catch (Exception e) {
            e.printStackTrace();
            return ResEntity.error("base64无法转文件");
        }

        StringBuilder sb = new StringBuilder(relative)
                .append(DateUtil.getDateFormats(DateUtil.YYYYMMDD, null))
                .append("/")
                .append(UUID.randomUUID().toString())
                .append(".png");
        File file = new File(String.format("%s%s", location, sb.toString()));
        //判断文件父目录是否存在
        if (!file.getParentFile().exists()) {
            file.getParentFile().mkdirs();
        }

        BufferedOutputStream bos = null;
        FileOutputStream fos = null;
        try {
            fos = new java.io.FileOutputStream(file);
            bos = new BufferedOutputStream(fos);
            bos.write(bytes);
            return ResEntity.success(preview + sb.toString());
        } catch (IllegalStateException | IOException e) {
            e.printStackTrace();
            return ResEntity.error("服务异常");
        } finally {
            if (bos != null) {
                try {
                    bos.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }


}
