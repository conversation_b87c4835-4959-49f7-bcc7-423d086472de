//package com.jiuzhekan.cbkj.common.scheduler;
//
//import com.jiuzhekan.cbkj.beans.business.TSysParam;
//import com.jiuzhekan.cbkj.common.utils.Constant;
//import com.jiuzhekan.cbkj.common.utils.RedisLockUtil;
//import com.jiuzhekan.cbkj.mapper.business.record.TPrescriptionMapper;
//import com.jiuzhekan.cbkj.service.formula.center.TFormulaInterface;
//import com.jiuzhekan.cbkj.service.redis.RedisService;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang.StringUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.scheduling.annotation.EnableScheduling;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Component;
//import org.springframework.transaction.annotation.Transactional;
//
///**
// * DelPreScheduler
// * <p>
// * 杭州聪宝科技有限公司
// * <p>
// *
// * <AUTHOR>
// * @date 2020/12/16 20:53
// */
//@Slf4j
//@Component
//@EnableScheduling
//public class FormulaScheduler {
//    @Autowired
//    private RedisLockUtil redisLockUtil;
//    @Autowired
//    private TFormulaInterface tFormulaInterface;
//
//    /**
//     * 同步配方库存
//     *
//     * <AUTHOR>
//     * @date 2020/12/16
//     */
//    @Scheduled(cron = "0 * * * * ?")
//    public void syncFormulaStock() {
//
//        String lockKey = "FORMULA_STOCK";
//        String lockValue = String.valueOf(Thread.currentThread().getId());
//        log.info("-------" + lockKey + "------");
//        if (redisLockUtil.lock(lockKey, lockValue, 10)) {
//            log.info("-------" + lockKey + "  BEGAN------");
//
//            try {
//                tFormulaInterface.syncFormulaStock();
//            } catch (Exception e) {
//                log.error("【配方库存同步失败】" + e.getMessage());
//            }
//
//            log.info("-------" + lockKey + "  END------");
//        }
//    }
//}
