package com.jiuzhekan.knowledgeshare.mapper;

import com.jiuzhekan.knowledgeshare.beans.ChineseMedicineListReq;
import com.jiuzhekan.knowledgeshare.beans.ShareListReq;
import com.jiuzhekan.knowledgeshare.beans.TKnowledgeShare;
import org.springframework.stereotype.Component;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;

import java.util.List;

@Component
public interface TKnowledgeShareMapper extends BaseMapper<TKnowledgeShare>{


    List<TKnowledgeShare> getShareList(ShareListReq shareListReq);

    List<TKnowledgeShare> getChineseMedicineList(ChineseMedicineListReq bookName);

    List<TKnowledgeShare> getSharedLiteratureList(ChineseMedicineListReq bookName);
}