//package com.jiuzhekan.cbkj.service.interaction;
//
//import com.alibaba.dubbo.config.annotation.Reference;
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//import com.cbkj.service.PushPharmacyService;
//import com.cbkj.service.SynchronousMedicineService;
//import com.cbkj.service.SynchronousStockService;
//import com.jiuzhekan.cbkj.beans.drug.TCenterHisYpml;
//import com.jiuzhekan.cbkj.beans.drug.TCenterYpkc;
//import com.jiuzhekan.cbkj.beans.interaction.*;
//import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
//import com.jiuzhekan.cbkj.common.utils.Constant;
//import com.jiuzhekan.cbkj.common.utils.IDUtil;
//import com.jiuzhekan.cbkj.common.utils.RedisLockUtil;
//import com.jiuzhekan.cbkj.mapper.business.record.TPrescriptionMapper;
//import com.jiuzhekan.cbkj.mapper.business.record.TRecordMapper;
////import com.jiuzhekan.cbkj.mapper.drug.TCenterHisYpmlMapper;
//import com.jiuzhekan.cbkj.mapper.drug.TCenterHisYpmlmxMapper;
//import com.jiuzhekan.cbkj.mapper.drug.TCenterYpkcMapper;
//import com.jiuzhekan.cbkj.service.drug.TCenterHisMappingService;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang.StringUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
///**
// * @program: pre_api
// * @description: 接口信息交互接口业务层
// * @author: wangtao
// * @create: 2021-07-16 10:49
// **/
//@Service
//@Slf4j
//public class PreIntoInterfaceService {
//
//    @Autowired
//    private TRecordMapper tRecordMapper;
//    @Autowired
//    private TPrescriptionMapper tPrescriptionMapper;
////    @Autowired
////    private TCenterHisYpmlMapper tCenterHisYpmlMapper;
//    @Autowired
//    private TCenterHisMappingService tCenterHisMappingService;
////    @Autowired
////    private TCenterHisYpmlmxMapper tCenterHisYpmlmxMapper;
//    @Autowired
//    private TCenterYpkcMapper tCenterYpkcMapper;
//    @Autowired
//    private RedisLockUtil redisLockUtil;
//    private static final String KEY = "DRUG_SYNCHRONIZATION";
//    @Reference(version = "1.0.0", group = "anji")
//    private PushPharmacyService pushPharmacyService;
//    @Reference(version = "1.0.0", group = "anji")
//    private SynchronousMedicineService synchronousMedicineService;
//    @Reference(version = "1.0.0", group = "anji")
//    private SynchronousStockService synchronousStockService;
//
//    /**
//     * @description: 药品同步定时任务
//     * @author: wangtao
//     * @create: 2021-07-16 10:49
//     **/
//
////    @Scheduled(cron = "0 0 1 * * ?")
////    @Transactional(rollbackFor = Exception.class)
////    public void drugDirectorySynchronized() {
////        log.info("-------" + KEY + "------");
////        String lockValue = String.valueOf(Thread.currentThread().getId());
////        int lockTime = 30 * 60;
////        List<TCenterHisYpml> allDrugYpml = tCenterHisYpmlMapper.getAllDrugYpml();
////        Map<String, YpmlInfo> ypmlMap = new HashMap<>();
////        if (allDrugYpml==null){ return; }
////        allDrugYpml.forEach(
////                ypml ->{
////                    YpmlInfo ypmlInfo;
////                    if (ypmlMap.get(ypml.getAppId())==null){
////                        ypmlInfo   = new YpmlInfo();
////                    }else {
////                        ypmlInfo = ypmlMap.get(ypml.getAppId());
////                    }
////                    ypmlInfo.setAppId(ypml.getAppId());
////                    if (Constant.BASIC_STRING_ZERO.equals(ypml.getYpmlType())){
////                        ypmlInfo.setCenterYpmlId(ypml.getYpmlId());
////                    }else {
////                        ypmlInfo.setHisYpmlId(ypml.getYpmlId());
////                    }
////                    ypmlMap.put(ypml.getAppId(),ypmlInfo);
////                }
////        );
////        if (redisLockUtil.lock(KEY, lockValue, lockTime)) {
////            log.info("-------" + KEY + " BEGAN------");
////            ypmlMap.keySet().forEach(key ->getDrugDirectory(ypmlMap.get(key) ));
////            log.info("------- " + KEY + " END ------");
////        }
////    }
//
//
//    /**
//     * @description: 药品库存同步定时任务
//     * @author: wangtao
//     * @create: 2021-07-16 10:49
//     **/
////    @Scheduled(cron = "0 0 * * * ?")
////    @Transactional(rollbackFor = Exception.class)
////    public void drugInventorySynchronized() {
////        log.info("-------" + KEY + "------");
////        String lockValue = String.valueOf(Thread.currentThread().getId());
////        int lockTime = 30 * 60;
////        List<TCenterHisYpml> allDrugYpml = tCenterHisYpmlMapper.getAllDrugYpml();
////        if (allDrugYpml==null){ return; }
////        List<YpmlInfo> ypmlList = new ArrayList<>();
////        allDrugYpml.forEach(
////                ypml ->{
////                    YpmlInfo ypmlInfo = new YpmlInfo();
////                    ypmlInfo.setAppId(ypml.getAppId());
////                    if (Constant.BASIC_STRING_ZERO.equals(ypml.getYpmlType())){
////                        ypmlInfo.setCenterYpmlId(ypml.getYpmlId());
////                        ypmlList.add(ypmlInfo);
////                    }
////                }
////        );
////        if (redisLockUtil.lock(KEY, lockValue, lockTime)) {
////            log.info("-------" + KEY + " BEGAN------");
////            ypmlList.forEach(ypml->getDrugInventory(ypml));
////            log.info("------- " + KEY + " END ------");
////        }
////    }
//
//    /**
//     * @description: 发送处方到接口项目
//     * @author: wangtao
//     * @create: 2021-07-16 10:49
//     **/
////    @Transactional(rollbackFor = Exception.class)
////    public ResEntity sendPreToHis(List<String> preList, String recId) {
////        ResEntity resEntity = getPrescriptionInfo(preList, recId);
////        HashMap<String, Object> request = new HashMap<>();
////        Map map;
////        if (resEntity.getStatus()) {
////            //发送数据
////            request.put("status", resEntity.getStatus());
////            request.put("message", resEntity.getMessage());
////            request.put("data", resEntity.getData());
////            try {
////                map = pushPharmacyService.pushPharmacy(request);
////            } catch (Exception e) {
////                return new ResEntity(false, "接口项目异常:" + e.getMessage(), null);
////            }
////
////        } else {
////            return new ResEntity(false, resEntity.getMessage(), null);
////        }
////        return new ResEntity((boolean) map.get("status"), (String) map.get("message"), map.get("data"));
////    }
//
//
//    /**
//     * 从接口项目获取药品
//     *
//     * <AUTHOR>
//     * @date 2021/7/20
//     */
//    @Transactional(rollbackFor = Exception.class)
//    public ResEntity getDrugDirectory(YpmlInfo ypmlInfo) {
//        if (ypmlInfo.getAppId() == null || ypmlInfo.getCenterYpmlId() == null || ypmlInfo.getHisYpmlId() == null) {
//            return new ResEntity(false, "APP_ID 和 药品目录ID不能为空", null);
//        }
//        List<DrugDirectory> centerInventories = new ArrayList<>();
//        HashMap<String, Object> request = new HashMap<>();
//        request.put("timestamp", ypmlInfo.getTimestamp());
//        request.put("appId", ypmlInfo.getAppId());
//        request.put("centerYpmlId", ypmlInfo.getCenterYpmlId());
//        request.put("hisYpmlId", ypmlInfo.getHisYpmlId());
//        Map map;
//        try {
//            map = synchronousMedicineService.synchronousMedicine(request);
//        } catch (Exception e) {
//            return new ResEntity(false, "接口项目异常:" + e.getMessage(), null);
//        }
//        //返回结果插入数据库
//        if (!(boolean) map.get("status")) {
//            return new ResEntity(false, (String) map.get("message"), null);
//        }
//        JSONArray jsonArray = (JSONArray) map.get("data");
//        centerInventories = JSONObject.parseArray(jsonArray.toJSONString(), DrugDirectory.class);
//        if (!centerInventories.isEmpty()) {
//            try {
//                insertDrugDirectory(ypmlInfo, centerInventories);
//            } catch (Exception e) {
//                return new ResEntity(false, e.toString(), null);
//            }
//        } else {
//            return new ResEntity(false, (String) map.get("message"), null);
//        }
//        return new ResEntity(true, Constant.SUCCESS_DX, null);
//    }
//
//    /**
//     * 从接口项目获取库存
//     *
//     * <AUTHOR>
//     * @date 2021/7/20
//     */
//    @Transactional(rollbackFor = Exception.class)
//    public ResEntity getDrugInventory(YpmlInfo ypmlInfo) {
//        if (ypmlInfo.getAppId() == null || ypmlInfo.getCenterYpmlId() == null) {
//            return new ResEntity(false, "APP_ID 和 药品目录ID不能为空", null);
//        }
//        List<DrugInventory> centerInventories = new ArrayList<>();
//        HashMap<String, Object> request = new HashMap<>();
//        request.put("centerYpmlId", ypmlInfo.getCenterYpmlId());
//        request.put("timestamp", ypmlInfo.getTimestamp());
//        request.put("appId", ypmlInfo.getAppId());
//        Map map;
//        try {
//            map = synchronousStockService.synchronousStock(request);
//        } catch (Exception e) {
//            return new ResEntity(false, "接口项目异常:" + e.getMessage(), null);
//        }
//        //返回结果插入数据库
//        if (!(boolean) map.get("status")) {
//            return new ResEntity(false, (String) map.get("message"), null);
//        }
//        JSONArray jsonArray = (JSONArray) map.get("data");
//        centerInventories = JSONObject.parseArray(jsonArray.toJSONString(), DrugInventory.class);
//        if (!centerInventories.isEmpty()) {
//            try {
//                insertDrugInventory(centerInventories);
//            } catch (Exception e) {
//                return new ResEntity(false, e.toString(), null);
//            }
//        } else {
//            return new ResEntity(false, (String) map.get("message"), null);
//        }
//        return new ResEntity(true, Constant.SUCCESS_DX, null);
//
//    }
//
//    /**
//     * 药品插入数据库
//     *
//     * <AUTHOR>
//     * @date 2021/7/20
//     */
//    @Transactional(rollbackFor = Exception.class)
//    public void insertDrugDirectory(YpmlInfo ypmlInfo, List<DrugDirectory> centerInventories) {
//        for (DrugDirectory drug : centerInventories) {
//            //特殊字段处理
//            if (StringUtils.isBlank(drug.getJiLiang())) {
//                drug.setJiLiang("");
//            }
//            if (StringUtils.isBlank(drug.getJiLiangDw())) {
//                drug.setJiLiangDw("");
//            }
//            if (drug.getYpmlId().equals(ypmlInfo.getCenterYpmlId())) {
//                //三方映射的唯一标识
//                drug.setYaoPindmTy(IDUtil.getID());
//            }
//        }
//        if (centerInventories.size() >= Constant.ListSize) {
//            //插入/修改药品目录明细（药房）
//            tCenterHisYpmlmxMapper.insertOrUpdateHisYpmlmx(centerInventories);
//            centerInventories.clear();
//        }
//
//        if (centerInventories.size() > 0) {
//            tCenterHisYpmlmxMapper.insertOrUpdateHisYpmlmx(centerInventories);
//            centerInventories.clear();
//        }
//        tCenterHisMappingService.centerAutoMapping(ypmlInfo.getCenterYpmlId(), ypmlInfo.getHisYpmlId());
//    }
//
//    /**
//     * 库存插入数据库
//     *
//     * <AUTHOR>
//     * @date 2021/7/20
//     */
//    @Transactional(rollbackFor = Exception.class)
//    public void insertDrugInventory(List<DrugInventory> drugInventoryList) {
//        List<TCenterYpkc> ypkcList = new ArrayList<>();
//        for (DrugInventory drugInventory : drugInventoryList
//        ) {
//            String ypdmTy = tCenterYpkcMapper.getYpdmTy(drugInventory.getYpmlId(), drugInventory.getYaoPinDm());
//            if (ypdmTy!=null) {
//                TCenterYpkc tCenterYpkc = new TCenterYpkc(drugInventory, ypdmTy);
//                ypkcList.add(tCenterYpkc);
//            }else { return; }
//        }
//        tCenterYpkcMapper.insertOrUpdateList(ypkcList);
//    }
//
//    /**
//     * 获取处方信息
//     *
//     * <AUTHOR>
//     * @date 2021/7/20
//     */
//    @Transactional(rollbackFor = Exception.class)
//    public ResEntity getPrescriptionInfo(List<String> preList, String recId) {
//        OralPrescription oralPre;
//        ExternalPrescription externalPre;
//        List<preItem> preItem = new ArrayList<>();
//        RecordInfo baseInfo = tRecordMapper.getBaseInfo(recId);
//        if (baseInfo == null) {
//            return new ResEntity(false, "没有就诊记录", null);
//        }
//        baseInfo.setAge(baseInfo.getRecAge() + baseInfo.getRecAgeUnit());
//
//        for (String preId : preList
//        ) {
//            String preType = tPrescriptionMapper.getPreType(preId);
//            if (preType == null) {
//                return new ResEntity(false, "没有处方信息", null);
//            }
//            if (Constant.BASIC_STRING_ONE.equals(preType)) {
//                oralPre = tPrescriptionMapper.getOralPre(preId);
//                preItem = tPrescriptionMapper.getPreItem(oralPre.getPreId());
//                oralPre.setOralPreItemList(preItem);
//                baseInfo.setOralPre(oralPre);
//            } else if (Constant.BASIC_STRING_TWO.equals(preType)) {
//                externalPre = tPrescriptionMapper.getExternalPre(preId);
//                preItem = tPrescriptionMapper.getPreItem(externalPre.getPreId());
//                externalPre.setOralPreItemList(preItem);
//                baseInfo.setExternalPre(externalPre);
//            } else {
//                return new ResEntity(false, "暂时只支持内服和外用", null);
//            }
//        }
//        return new ResEntity(true, Constant.SUCCESS_DX, baseInfo);
//    }
//}
