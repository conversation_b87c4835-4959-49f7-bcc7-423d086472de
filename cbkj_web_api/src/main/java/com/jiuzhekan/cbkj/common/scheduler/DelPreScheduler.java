package com.jiuzhekan.cbkj.common.scheduler;

import com.jiuzhekan.cbkj.beans.business.TSysParam;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.RedisLockUtil;
import com.jiuzhekan.cbkj.mapper.business.record.TPrescriptionMapper;
import com.jiuzhekan.cbkj.service.redis.ParameterRedisService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

/**
 * DelPreScheduler
 * <p>
 * 杭州聪宝科技有限公司
 * <p>
 *
 * <AUTHOR>
 * @date 2020/12/16 20:53
 */
@Slf4j
@Component
@EnableScheduling
@Transactional(rollbackFor = Exception.class)
public class DelPreScheduler {
    @Autowired
    private RedisLockUtil redisLockUtil;
    @Autowired
    private TPrescriptionMapper tPrescriptionMapper;
    @Autowired
    private ParameterRedisService parameterRedisService;

    /**
     * 定时作废未缴费处方
     *
     * <AUTHOR>
     * @date 2020/12/16
     */
//    @Scheduled(cron = "0 * * * * ?")
    @Scheduled(cron = "10 0 0 * * ?")
    public void delPre() {

        String lockKey = "DEL_PRE";
        String lockValue = String.valueOf(Thread.currentThread().getId());
        int lockTime = 100;
        log.info("-------DEL_PRE------");
        if (redisLockUtil.lock(lockKey, lockValue, lockTime)) {
            log.info("-------DEL_PRE  BEGAN------");

            long outpatientService = 72;//门诊
            long beHospitalized = 72;//住院

            TSysParam param = parameterRedisService.getSysParam(Constant.BASIC_APP_ID, Constant.BASIC_INS_CODE, Constant.BASIC_DEPT_ID, Constant.PRESCRIPTION_DEL_TIME);
//            TSysParam param = redisService.getSysParam("", "", "", Constant.PRESCRIPTION_DEL_TIME);
            if (param != null && StringUtils.isNotBlank(param.getParValues()) && StringUtils.isNumeric(param.getParValues())) {
                try {
                    String value = param.getParValues();

                    if(value.contains(",")){
                        String[] split = value.split(",");
                        List<String> list = Arrays.asList(split);
                        outpatientService =  Long.parseLong(list.get(Constant.BASIC_INTEGER_ZERO));
                        beHospitalized =  Long.parseLong(list.get(Constant.BASIC_INTEGER_ONE));
                    }else{
                        long l = Long.parseLong(value);
                        outpatientService = l;
                        beHospitalized = l;
                    }

                } catch (NumberFormatException e) {
                    log.info("-------DEL_PRE  处方自动作废的时间转换异常------");
                }
            }
            if(outpatientService !=0){
                tPrescriptionMapper.delPreIfNoPayForMz(outpatientService);
            }
            if(beHospitalized !=0){
                tPrescriptionMapper.delPreIfNoPayForZy(beHospitalized);
            }

            tPrescriptionMapper.delPreIfIsDraft();

            log.info("-------DEL_PRE  END------");
        }
    }


}
