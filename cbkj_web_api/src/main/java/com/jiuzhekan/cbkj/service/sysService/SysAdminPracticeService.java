package com.jiuzhekan.cbkj.service.sysService;

import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.beans.sysBeans.SysAdminPractice;
import com.jiuzhekan.cbkj.common.http.PlatformRestTemplate;
import com.jiuzhekan.cbkj.common.utils.Constant;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class SysAdminPracticeService {

    private final PlatformRestTemplate platformRestTemplate;

    public SysAdminPracticeService(PlatformRestTemplate platformRestTemplate) {
        this.platformRestTemplate = platformRestTemplate;
    }

    /**
     * 查询用户执业机构
     *
     * @param username 用户名称
     * @return Object
     * <AUTHOR>
     * @date 2021-07-08
     */
    public Object validPractices(String username) {

        List<SysAdminPractice> list = new ArrayList<>();

        Map<String, Object> params = new HashMap<>(1);
        params.put("userName", username);
        ResEntity resEntity = platformRestTemplate.post("user/doctor/multipoint", params);
        if (resEntity.getStatus() && resEntity.getData() instanceof List) {
            List<Map<String, Object>> dataList = (List<Map<String, Object>>) resEntity.getData();

            for (int i = 0; i < dataList.size(); i++) {
                Map<String, Object> data = dataList.get(i);
                SysAdminPractice practice = new SysAdminPractice();
                practice.setPracticeId(i);
                practice.setAppId(String.valueOf(data.get("appId")));
                practice.setInsCode(String.valueOf(data.get("insCode")));
                practice.setInsName(String.valueOf(data.get("insName")));
                practice.setDepId(String.valueOf(data.get("deptId")));
                practice.setDepName(String.valueOf(data.get("deptName")));
                practice.setEmployeeId(String.valueOf(data.get("employeeId")));
                list.add(practice);
            }
        }

        if (list.isEmpty()) {
            SysAdminPractice practice = new SysAdminPractice();
            practice.setPracticeId(0);
            practice.setAppId(Constant.BASIC_APP_ID);
            practice.setInsCode(Constant.BASIC_INS_CODE);
            practice.setInsName("");
            practice.setDepId(Constant.BASIC_DEPT_ID);
            practice.setDepName("");
            practice.setEmployeeId("");
            list.add(practice);
        }

        return ResEntity.success(list);
    }

    public Object validPractices2(String username) {

        List<SysAdminPractice> list = new ArrayList<>();

        Map<String, Object> params = new HashMap<>(1);
        params.put("userId", username);
        ResEntity resEntity = platformRestTemplate.post("user/doctor/multipoint", params);
        if (resEntity.getStatus() && resEntity.getData() instanceof List) {
            List<Map<String, Object>> dataList = (List<Map<String, Object>>) resEntity.getData();

            for (int i = 0; i < dataList.size(); i++) {
                Map<String, Object> data = dataList.get(i);
                SysAdminPractice practice = new SysAdminPractice();
                practice.setPracticeId(i);
                practice.setAppId(String.valueOf(data.get("appId")));
                practice.setInsCode(String.valueOf(data.get("insCode")));
                practice.setInsName(String.valueOf(data.get("insName")));
                practice.setDepId(String.valueOf(data.get("deptId")));
                practice.setDepName(String.valueOf(data.get("deptName")));
                practice.setEmployeeId(String.valueOf(data.get("employeeId")));
                list.add(practice);
            }
        }

        if (list.isEmpty()) {
            SysAdminPractice practice = new SysAdminPractice();
            practice.setPracticeId(0);
            practice.setAppId(Constant.BASIC_APP_ID);
            practice.setInsCode(Constant.BASIC_INS_CODE);
            practice.setInsName("");
            practice.setDepId(Constant.BASIC_DEPT_ID);
            practice.setDepName("");
            practice.setEmployeeId("");
            list.add(practice);
        }

        return ResEntity.success(list);
    }

}
