package com.jiuzhekan.healthpres.beans.heathPresControllerVo;

import com.jiuzhekan.healthpres.beans.TUserQuestionnaireResultAcu;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/5/27 13:53
 * @Version 1.0
 */
@ApiModel
@Data
public class HealthPreModelMainItemResponse {

    @ApiModelProperty(value = "模板明细id")
    private Integer itemId;

    @ApiModelProperty(value = "1.纯文本2.下拉选择")
    private Integer itemType;

    @ApiModelProperty(value = "标题")
    private String itemTitle;

    @ApiModelProperty(value = "内容")
    private String itemContent;

    @ApiModelProperty(value = "下拉选择。")
    private String itemSelectDefaultId;

    @ApiModelProperty(value = "健康处方中是否展示：0前端展示1前端不展示")
    private Integer showType;

    @ApiModelProperty(value = "穴位定位")
    private List<TUserQuestionnaireResultAcu> lookHealthPreMainItemAcuResponseList;
}
