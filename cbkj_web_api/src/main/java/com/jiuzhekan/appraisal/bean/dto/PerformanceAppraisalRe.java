package com.jiuzhekan.appraisal.bean.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/27 16:29
 * @Version 1.0
 */
@Data
@ApiModel
public class PerformanceAppraisalRe {
//    private String appId;

    @ApiModelProperty(value = "机构代码：多个用逗号分隔")
    private String insCode;

    @ApiModelProperty(value = "子指标代码：多个用逗号分隔")
    private String zibiaodm;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date yeWuRQStart;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date yeWuRQEnd;

//    private Integer page;
//    private Integer limit;
}
