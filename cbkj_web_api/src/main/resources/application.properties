#???????????docker????88????????????????????
server.port=88

#?????
#spring.datasource.primary.username = DgUfz0nYLCR7UJ66sgG1ag==
#spring.datasource.primary.password = OPXCNabmBpi/FKayjb2kubBySH5LiQln
#spring.datasource.primary.url=****************************************************************************************************************************************************
#spring.datasource.primary.url=****************************************************************************************************************************************************
#
spring.datasource.primary.username = DgUfz0nYLCR7UJ66sgG1ag==
spring.datasource.primary.password = OPXCNabmBpi/FKayjb2kubBySH5LiQln
#spring.datasource.primary.password = Bn0iFiUGIoSrkoVamBYETA==
spring.datasource.primary.url=****************************************************************************************************************************************************


#spring.datasource.primary.url=****************************************************************************************************************************************************

#spring.datasource.primary.username = Ssc3i19nT3hnQnhBHMPoXQ==
#spring.datasource.primary.password = E7li8tEgZt/VPuOKPqwclNdZ6sYrqk22
#spring.datasource.primary.url=*****************************************************************************************************************************************************

## redis??
spring.redis.host=***********
spring.redis.port=6379
spring.redis.password=cbkj123!@#
#??????
#spring.redis.database=2


#spring.redis.host = ************
#spring.redis.port = 6379
#spring.redis.password = cbkj123!@#

#HIS??????
#address.pre.interface = http://*************:94/pre_interface/V5.0/
address.pre.interface = http://***********:94/pre_interface/V5.0/

#??
address.cbkj.core.port = http://***********:86/

#????
address.cbkj.zb.port = http://***********:90/
address.cbkj.zb.token = f2ad828416c6413ea0dc7dcb85a27f01

#????
#address.cbkj.chuancheng.url = https://ai.tcmbrain.com:85
address.cbkj.chuancheng.url =
address.cbkj.chuancheng.list = /api/v1/open/zhuanbing/
address.cbkj.chuancheng.page = /case/collection
address.cbkj.chuancheng.medcase = /api/v1/open/medcase/
address.cbkj.chuancheng.appId = 5cd8536527152966-cloudtcm
address.cbkj.chuancheng.appSecret = 56b49d37cf06411ea9853652f05a469d5e8e574e51cf447681183527031044e2


#?????
#address.know.interface.url=https://kb.tcmbrain.com/zyznyxt_basic/interface/
address.know.interface.url=http://***********:7082/zyznyxt_basic/interface/

#?????
smf.file.address=http://*************:9999

#?????????????5.12.3?5.15.1?????????5.12.4~5.12.6?????
address.mobile.decoct = http://**************:91/pre/common/toInfo?preNo=

#????????ID
providerId=336699

#????????

address.platform.api.url=http://***********:65/platform/api/
#address.platform.api.url=http://************:65/platform/api/

#??????????
address.drugs.api.url=http://***********:8010/

#dubbo ??
#???????????
#spring.dubbo.application.name=consumer
#zookeeper??????
#spring.dubbo.registry.address=zookeeper://*************:2181
##redis?????
##dubbo.registry.address=redis://*************:6379
##dubbo.registry.username=1
##dubbo.registry.password=cbkj123!@#
#dubbo??
#spring.dubbo.protocol.name=dubbo
#duboo???
#spring.dubbo.protocol.port=20880
#??? ?? ??? @Reference?? ?????
#spring.dubbo.scan=com.jiuzhekan.cbkj.service.interaction

rest.template.config.keepalive = 20
# ?????
ftp.host=************
# ???
ftp.port=22
# ???
ftp.userName=root
# ??
ftp.password=cbkj123!@#
# ????
ftp.workingDirectory=/
# ??
ftp.encoding=utf-8
#????
ftp.passiveMode=true
#??????
ftp.clientTimeout=30000
# ???
ftp.threaNum=1
# 0=ASCII_FILE_TYPE(ASCII??)?1=EBCDIC_FILE_TYPE?2=LOCAL_FILE_TYPE(?????)
ftp.transferFileType=2
# ?????
ftp.renameUploaded=true
# ??????
ftp.retryTimes=1200
# ????
ftp.bufferSize=8192

# ???
ftp.maxTotal=50
# ????
ftp.minldle=10
# ????
ftp.maxldle=50
# ??????
ftp.maxWait=30000
# ????????????maxWait < 0 ?????
ftp.blockWhenExhausted=true
# ??????
ftp.testOnBorrow=true
# ????
ftp.testOnReturn=true
# ?????
ftp.testOnCreate=true
# ????
ftp.testWhileldle=false
# ????
ftp.lifo=false
# ????ftp?????????????????nginx????????
ftp.isFtp=false
#???????????????
ftp.file.url=/home/<USER>/ftp/
ftp.zkxc.path=home/zkxcFile/ftp/
#???
# ??nginx???
# 1.??????????
# location  /zkxc/pdf/ {
#        alias /home/<USER>/ftp/; # ???????
#        autoindex on; # ?????????????????????
#    }
# 2.??????nginx?????????ftp???:
##?????????????????????????????ftp?????????????????????????????????????????
# location ^~ /pre_ai/preview/zkxc/zxkjnPDF/ {
#
#        proxy_pass http://************/zkxc/pdf/;#ip???????????ip
#
#       }


#




######??????????????
spring.datasource.primary.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.primary.driver-class-name=com.mysql.jdbc.Driver
#spring.datasource.primary.driver-class-name=dm.jdbc.driver.DmDriver
spring.datasource.common=yes
spring.datasource.common.initialSize=0
spring.datasource.common.minIdle=0
spring.datasource.common.maxActive=10
spring.datasource.common.maxWait=60000
spring.datasource.common.timeBetweenEvictionRunsMillis=60000
spring.datasource.common.minEvictableIdleTimeMillis=300000
spring.datasource.common.validationQuery=SELECT 1 FROM DUAL
spring.datasource.common.testWhileIdle=true
spring.datasource.common.testOnBorrow=false
spring.datasource.common.testOnReturn=false
spring.datasource.common.poolPreparedStatements=true
spring.datasource.common.filters=stat, wall
spring.datasource.common.maxPoolPreparedStatementPerConnectionSize=20
spring.datasource.common.connectionProperties=druid.stat.mergeSql=true;druid.stat.slowSqlMillis=500
spring.datasource.common.useGlobalDataSourceStat=true
spring.datasource.common.removeAbandoned=true
spring.datasource.common.removeAbandonedTimeout=300
spring.datasource.common.logAbandoned=false


server.tomcat.uri-encoding = UTF-8
spring.http.encoding.charset = UTF-8
spring.http.encoding.enabled = true
spring.http.encoding.force = true
spring.messages.encoding = UTF-8

management.endpoints.web.exposure.include=*

server.tomcat.max-http-post-size=-1
spring.servlet.multipart.max-file-size=-1
spring.servlet.multipart.max-request-size=-1
server.servlet.context-path =/pre_ai
spring.mvc.favicon.enabled = false

logging.config=classpath:zlogback.xml
system.name=?????

mybatis.mapper-locations = classpath:mappers/**/*.xml
mybatis.type-aliases-package = com.jiuzhekan.cbkj.beans
mybatis.config-location = classpath:/mybatis-config.xml
pagehelper.helperDialect=mysql

spring.resources.chain.cache = true
spring.resources.chain.compressed = true
spring.resources.chain.strategy.fixed.enabled = true
spring.resources.chain.strategy.fixed.paths=/sys/**
spring.resources.chain.strategy.fixed.version=2.0.0

spring.jmx.enabled=false

## redis
spring.redis.timeout = 5000ms
spring.redis.jedis.pool.max-active = 100
spring.redis.jedis.pool.max-idle = 20
spring.redis.jedis.pool.min-idle = 1
spring.redis.jedis.pool.max-wait = 100ms

## http ?????
http-pool.max-total = 200
http-pool.default-max-per-Route = 100
http-pool.connect-timeout = 3000
http-pool.connection-request-timeout = 1000
http-pool.socket-timeout = 60000
http-pool.validate-after-inactivity = 2000
## ???
root.encryptor.password = ::!qb#9467@
## ????
root.upload.relative = cbkjFile/
## ????
root.preview = preview/
##
jwt.secret = secret
jwt.expiration = 10800000
jwt.expirationExt = 3600000
jwt.token = Authorization

#??????
file.address=/app/uploadFile/
#????+NFS???????
#file.address=/share/uploadFile/

#?????????????????????????
template.address=/app/templates/

prescription.origin.master=????

login.one=false

rsa.publicKey=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDaKmCXLWqBIVOVSa/dTY08bgKhWcvi3j6vPksjrV7X3oFZ6dbF3XhaTXuV65x9qqmlQqlNepkqv03miY72QkjWMgbaEt0v5MFnQ+B2AWPIGunvSbaDIBwer/6V9f+ra7imLdgt9jwlkjDYvRjvw7mDHFkXkMvKWl1FFihV0EN8JwIDAQAB
rsa.privateKey=MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBANoqYJctaoEhU5VJr91NjTxuAqFZy+LePq8+SyOtXtfegVnp1sXdeFpNe5XrnH2qqaVCqU16mSq/TeaJjvZCSNYyBtoS3S/kwWdD4HYBY8ga6e9JtoMgHB6v/pX1/6truKYt2C32PCWSMNi9GO/DuYMcWReQy8paXUUWKFXQQ3wnAgMBAAECgYBnRa4pgg8rh0oYniQIv0A/Pdgy5t3zy76il/tbrSL7TtGubVoEmfzxykHZSwnuFs5tc2vPSFye9qX6nl01R1VQZ7JM2FVpldRw0Rrqv3paQjyHdKOcIYBJG2mxI7fpg/du8kwlkNCHRl39aJHQXHbY+lLEwE4HBJ7niMvdGoIpGQJBAPb+47h3++8vFFx99GlsrEVS5NAYNr8VyuGRYfxJHD/qNFcOZmosi8NnziFFnyQ/0C0rou5EDJu+jlBJdl1KNHsCQQDiHm2J9zSLDAr70ZyQ4l+vaPbw+26CFByBG4dsh7xeNa95mSbj72JuCZCyuQZaacThf3iWQuzy2N9TUFib+9VFAkEAlamnFJzndGwDm3PayJLH5A2xhgJWEf1DfODaDcPDMVtZsbKRDh7F5Xad6X1FS/K60tQRGuzy8uBJXY7WAPs4xwJAZ1zNadHM/PrGUpJg5YH1h3ON3l6xB1k2JnZ1E1GA8/fKfOVbd7pH3lEVCf22P8I1s3bXoqh5NBGbFLSXrMYTmQJBAJBxs9VEz2+qFfJmWSYiJBugoFD/cBWfL1VHwNeuZtJxFXXpkB3G1CLaII1SaGOWXHFMOycbemfTQzxSEiRFjOE=

rsa.publicKey2=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAjzm8Gij4bzGp5oVP/6IjUwKGtuAt/MX2di7/e09teyL0JD3E2TFY8hfJE+RN4tp8SwZi+44mKwZUNhjhtDayEBx+LnKnhtkxtruGbyteHe0GUczDy2FFh4eT6cN/KXR/Bcc19TSfleZMRq56l1atKicPgXfysEzWq+O8UgRiZ/UyY/pf8UNec3PePtlj47hzs+7Bh+q1tUYOHqrogeSD92xvG6xushjQcuoTcdDJCVCLVxXVmff/YBbpZAwCQKB28W0gH4B7n9IpaONL6/jghbNUahKO6NL6iofxE6TOgc2VfzR5wlqaqD3wi0NRxx+S9JiSA+k3O5uqFk7V7URiwQIDAQAB
rsa.privateKey2=MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCPObwaKPhvManmhU//oiNTAoa24C38xfZ2Lv97T217IvQkPcTZMVjyF8kT5E3i2nxLBmL7jiYrBlQ2GOG0NrIQHH4ucqeG2TG2u4ZvK14d7QZRzMPLYUWHh5Ppw38pdH8FxzX1NJ+V5kxGrnqXVq0qJw+Bd/KwTNar47xSBGJn9TJj+l/xQ15zc94+2WPjuHOz7sGH6rW1Rg4equiB5IP3bG8brG6yGNBy6hNx0MkJUItXFdWZ9/9gFulkDAJAoHbxbSAfgHuf0ilo40vr+OCFs1RqEo7o0vqKh/ETpM6BzZV/NHnCWpqoPfCLQ1HHH5L0mJID6Tc7m6oWTtXtRGLBAgMBAAECggEBAI6+pveXn8sKv19tvIZ17SjdWrBHC+5iyrmNK7mVNvNDqfm4ykkBQrm99PzrTKjswnXvvpUiXm4BF8r2z3RSEvO1whi+0rK2IzMRAPIpUdsXkXXTU8yOfMKP4F5aJpGFdtembOOe9/JpHdpQKQROMbXSSkTp7CnD2yxjhBxmHEGog8PbpHTElP0SsYPPf3PkfkoEiwkTPS18j8sK5kEdqgZ3/y5GUKOb1iBsoGxM1TGDFD+iux3Jc1WbuYeRKdsQjvT2KMV5V+9+OFj4yqd70/v2lq2RW80B9H15dKWvPYHfEA69KV6nJneD6gVJ2G+/RLr/pfqHXZt1hsMgXexY/1UCgYEAycUxbtdi5Tjg1xV6gfhjNwoqkGdC0V7EMrJqv+OwuQLBciMWYo1AskBO9EwbUHwes2fTFDTAj//sSY/mLlp9k9UpofqisUCw3u2Vz4rRTThRDlNNMkZ4B3JtSEshzGoT/6TIYG7SZwfY99hjabbVHHf8NNxguIcVEhzQaE988lcCgYEAtbhea6lQJAACPrdkXViHtKfnxIId/h3VotA3/FRMpxTYfj/okNJsIhWrnWjLmv5QV0N40OP0Sc/5NmuuxJ/XVnVrpcMgD6eQGW8m88oHz6k6RTngW1vl63spdFF9TQS1v6Reo7QYScSrn5p0JifQ4gBRqESdByJpdtV9f8WYlKcCgYEApNgVjfAsnQe+MhsbLqpnLKWCpt2manXZJ2465rE4Rb8pmn1uIXAz5i3CE7dGLZhqYLq3ae+7TyqQunz1WvOVWz0xOWQSnWwz1L4Ywiow27ziBCksjaGjGLp07DZt05rq4daX2qlP/tsbeUnx1oGIVSz2AZ3262t5XIXsAvzGIRsCgYAdkxX626FpWn8DCgHRl1jus+zqdGrBMsXUK6MQ8/9NqfzZ/ziwaS6kFXbKtYV9gio9KYP+KztUz/41Ny1IXuNC9PVCExGiupifKxHh112SFG6hWZwEAl7XBkJm5eRIpp2VxQCKEANr7hAod5CzrQM35OllQ1VzFcyoRHKaWuRMPQKBgHaMzmTgk+32ds7VVpAdds+xaOByPF6ZFaq47x0vDXUjPgQOse07h5mA32CA0nbQUbI2yVzYAa+Gdrlnn0U8thnwn0lSp7dzh/c/UWEkHDaqp9YyaVBUw3G7K0dpb5UdfAhD1vhqecAmERirJLTxXqW8UuoiuaqqqAXDR+nRXLXg


#?????????
interceptor.tianan=false
interceptor.secret.type=AESSecret
#interceptor.secret.type=tianan
#interceptor.secret.type=defaultSecret
tianan.encrypt.url = https://***********:8341

statistics.function2=1800000

directory.json.filePath=/app/config/recordTemplate.json
#??????????
china.digital.medicine.url = http://*************:91/tzbs/
#??????api-key
void.api.key=e58b80ac4c2e424bbed3b65300ab4320

#??????????????????????  ??????5?31???01:30:00??
statistics.history.cron=0 48 14 31 5 *
#??????  ???????01:30:00??
statistics.cron=0 30 01 * * *

#???????????
statistics.mat.startDate=2022-01-11 01:30:40
statistics.work.startDate= 2022-01-11

#????-ip
#zkxc.info.his.url = http://ip:port
#????-????????
#zkxc.info.his.url.getHisPatients = /his/patients
#??????????zip????????,??????????
zkxc.info.zip.url = http://************:78/pre_ai

yuzhen.system.url = http://************:8071/diagnosis_web_api

default.patient.source = 0
swagger.basic.enable=true
swagger.basic.username=jack
swagger.basic.password=Admin2021@999



#history.replacement.task.enable=true
#remote.consultation.url=https://************:83/pre_ai/
#waiting.room.url=http://************/pdfu/waiting-room/headless/report/7330485c98fe43998bb7bbf211c2ff04


hlyy.url = http://************:95
#hlyy.url =
hlyy.url.check = /api/treatment/insuranceCheck
hlyy.url.result = /api/common/passTprescription
hlyy.url.token = /api/common/responseToken
#hlyy.shenhe.url = http://*************:95/api/common/passTprescription


history.replacement.task.enable=true

remote.consultation.url=https://************:83/pre_ai/
waiting.room.url=http://************/pdfu/waiting-room/headless/report/7330485c98fe43998bb7bbf211c2ff04

rsa.whitelist.privateKey=MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBAKvh/hNt0LVcvB2mwaMjKfbQLMjkxYBrlLox6zxMrMxq4xetGiJqnFXaZhfQD5NC5DhREwKd0DcjOSbiRpmVUydPqgGS03ICsKTnyxAPqoIGgog9s7gm5+a9ccdrfJdACSKquPd3gtk0Oh43QR9RORD/+vdkLKCv5bIqdSroQsj7AgMBAAECgYEAp5Q2u4qdqbU4U1B6CiwGhfSBNXyWcpZw8vgoeSnob4Tm0/aZPGTk929vilf4V4MoLyAcgLiMgLV+y0JhwuurnVZjBvhVMUUpulr2119QzOgL8wsy103Fr7qjp0K70x66UfYLJsidBz84mx77HLFfUxBqynbTfhofIwaDB5k77cECQQDfORfVwMtRV441npW9i7+LYj+AfdpQBpRG8jkqHz1xZUlF9cDtK6EVVKSZZilqnHva4gMg/+uWVZ7INi1JPH83AkEAxR8H0m9W36a4ghc//k2ijb60iJ8VYhgsWgXACeb1gYhxIHBnNmGKYaEhcWf5mdSwIkDhJyMi2xKB0AhA2XD+XQJBAL2EQok3MydvV4yMhIM117zmABX+Q9B3lYIaepAZj9frb2dXO6HgzdVHVKvsQbODAeCWzKNCxGiRbsp6vL4V/8cCQQDArcA7ldFY8+JOZuOKc+B8pPhawwVnyTExHYXFKBi58OCDUNvScC2PmKzT6Uo1b4xrLvfmBZZAhSqWajrZUHWRAkATrgMDBL/CKCaslMjsrVyud+3Pgna/w5se++PRQItag+BVmpF+CUBPrNFEIG3mJoYAEiwmvsnMJIZndoPobl4L
#whitelist.machineCode=drnrWCa47TLA/q6wcVEh+F/jpNoQnxUAiiZgUxImVNDL86Qx6MTJDl9gG87CQH7LxEGEvkwiM9RBLT1Ep34MeFPbYp8MZDamhIqjEu2jI/qWKZnNAilIWoTJQyx7N2VRgZkoY83j88tQ+LqJ9sqi+bxAxrWVoMHXO9LNV95p2ZE=
whitelist.machineCode=
urlGpt =
urlGptKey =
urlGptAPI =
idutils.Snowflake=1