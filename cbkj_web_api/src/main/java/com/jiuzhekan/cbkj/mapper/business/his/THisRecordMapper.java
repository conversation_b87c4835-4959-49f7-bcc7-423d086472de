package com.jiuzhekan.cbkj.mapper.business.his;

import com.jiuzhekan.cbkj.beans.business.his.BMappingDisease;
import com.jiuzhekan.cbkj.beans.business.his.BMappingSymptom;
import com.jiuzhekan.cbkj.beans.business.his.THisRecord;
import com.jiuzhekan.cbkj.beans.business.his.XDFQuery;
import com.jiuzhekan.cbkj.beans.business.prescription.THisXdfResult2;
import com.jiuzhekan.cbkj.controller.business.treatment.vo.SDKQuery;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public interface THisRecordMapper {

    THisRecord getHisRecordByToken(String token);
    List<THisXdfResult2> getHisXHByToken(XDFQuery xdfQuery);

    THisRecord getHisRecordByVisitNo(String appId, String insCode, String visitNo);

    /**
     * 根据HIS疾病编码获取知识库疾病
     *
     * @param record
     * @return java.util.List<com.jiuzhekan.cbkj.beans.business.his.BMappingDisease>
     * <AUTHOR>
     * @date 2020/11/21
     */
    List<BMappingDisease> getSysDisease(THisRecord record);

    /**
     * 根据HIS证型编码获取知识库证型
     *
     * @param record
     * @return java.util.List<com.jiuzhekan.cbkj.beans.business.his.BMappingSymptom>
     * <AUTHOR>
     * @date 2020/11/21
     */
    List<BMappingSymptom> getSysSymptom(THisRecord record);

    BMappingDisease getHisDisease(SDKQuery record);
    BMappingSymptom getHisSymptom(SDKQuery record);
}