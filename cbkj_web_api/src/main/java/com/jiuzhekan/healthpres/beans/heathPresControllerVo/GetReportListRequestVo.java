package com.jiuzhekan.healthpres.beans.heathPresControllerVo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/6/3 10:59
 * @Version 1.0
 */
@Data
public class GetReportListRequestVo {

    private Integer page;
    private Integer limit;

    @ApiModelProperty(value = "appid ID")
    private String appId;

    @ApiModelProperty(value = "机构编码")
    private String insCode;

    @ApiModelProperty(value = "科室ID")
    private String deptId;

    @ApiModelProperty(value = "开始时间")
    private String beginTime;

    @ApiModelProperty(value = "结束时间")
    private String endTime;

    @ApiModelProperty(value = "医生姓名")
    private String doctorName;


    @ApiModelProperty(value = "患者姓名")
    private String patientName;

    @ApiModelProperty(value = "类型:下拉条件值")
    private String type;


}
