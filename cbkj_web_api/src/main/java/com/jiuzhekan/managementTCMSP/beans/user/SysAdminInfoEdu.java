package com.jiuzhekan.managementTCMSP.beans.user;//package com.jiuzhekan.cbkj.beans.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class SysAdminInfoEdu implements Serializable {

    @ApiModelProperty(value = "继续教育主键id")
    private Integer id;

    @ApiModelProperty(value = "人员名称")
    private String userName;

    @ApiModelProperty(value = "人员id")
    private String userId;

    @ApiModelProperty(value = "课程名称")
    private String courseName;

    @ApiModelProperty(value = "课程类型（1.学历教育类2.非学历教育类）")
    private Integer courseType;

    @ApiModelProperty(value = "学分")
    private Double credit;

    @ApiModelProperty(value = "学习状态1进行中2完成")
    private Integer studyStatus;

    @ApiModelProperty(value = "开始日期")
    private Date startTime;

    @ApiModelProperty(value = "结束日期")
    private Date endTime;

    @ApiModelProperty(value = "证书附件")
    private String certificateAttachment;

//    @ApiModelProperty(value = "appId")
//    private String appId;
//
//    @ApiModelProperty(value = "")
//    private String appName;
//
//    @ApiModelProperty(value = "机构代码")
//    private String insCode;
//
//    @ApiModelProperty(value = "科室id")
//    private String deptId;
//
//    @ApiModelProperty(value = "工号")
//    private String employeeId;

//    @ApiModelProperty(value = "")
//    private String insName;
//
//    @ApiModelProperty(value = "")
//    private String deptName;

    @ApiModelProperty(value = "工号集合")
    private String employeeIds;
    private String createUserId;
    private String createUserName;

    @ApiModelProperty(value = "数据插入日期")
    private Date insertTime;


}
