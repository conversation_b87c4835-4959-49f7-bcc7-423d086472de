package com.jiuzhekan.statistics.aop;

import com.jiuzhekan.statistics.aop.annotation.StatisticsFunction2;
import com.jiuzhekan.statistics.service.function.TStatisticsFunction2Service;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.Arrays;

/**
 * 功能使用统计埋点
 *
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Component
public class StatisticsFunctionAop {


    private final TStatisticsFunction2Service tStatisticsFunction2Service;

    public StatisticsFunctionAop(TStatisticsFunction2Service tStatisticsFunction2Service) {
        this.tStatisticsFunction2Service = tStatisticsFunction2Service;
    }

    @Pointcut("execution(public * com.jiuzhekan.cbkj.controller..*.*(..))" +
            "||execution(public * com.jiuzhekan.statistics.controller..*.*(..))")
    public void web() {
    }

    @Before("web()")
    public void before(JoinPoint joinPoint) {
        try {

            RequestAttributes ra = RequestContextHolder.getRequestAttributes();

            if (ra == null) {
                return;
            }

            HttpServletRequest request = ((ServletRequestAttributes) ra).getRequest();

            Method method = getMethod(joinPoint);

            if (null == method) {
                return;
            }

            StatisticsFunction2 statisticsFunction2 = method.getAnnotation(StatisticsFunction2.class);

            if (null != statisticsFunction2 && statisticsFunction2.isStatistic()) {

                String function = request.getParameter("function");
                String classify = request.getParameter("classify");

                if (StringUtils.isBlank(function)) {
                    function = statisticsFunction2.function();
                }
                if (StringUtils.isBlank(classify)) {
                    classify = statisticsFunction2.classify();
                }

                log.info("-----功能使用统计埋点：function=#{},classify=#{}", function, classify);

                tStatisticsFunction2Service.addUpFunctionUsageTimes(function, classify);
            }

        } catch (Exception e) {
            log.error(e.toString());
        }
    }

    /**
     * 获取方法
     *
     * @param joinPoint joinPoint
     * @return Method
     */
    private Method getMethod(JoinPoint joinPoint) {

        Method[] methods = joinPoint.getTarget().getClass().getMethods();
        String methodName = joinPoint.getSignature().getName();
        Object[] objs = joinPoint.getArgs();

        return Arrays.stream(methods).filter(method ->
                        (method.getName().equals(methodName) && method.getParameterTypes().length == objs.length))
                .findAny()
                .orElse(null);

    }
}