package com.jiuzhekan.cbkj.service.http;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jiuzhekan.cbkj.beans.http.ZbReqVO;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.service.business.TSysParamService;
import com.jiuzhekan.cbkj.service.redis.KnowRedisService;
import com.jiuzhekan.cbkj.service.redis.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 *
 */
@Slf4j
@Service
public class CbkjZbService {

    @Autowired
    private TSysParamService tSysParamService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private KnowRedisService knowRedisService;

    @Value("${address.cbkj.zb.port}cbkj/public/api/")
    private String cbkjZbUrl;
    @Value("${address.cbkj.zb.port}")
    private String cbkjZbPort;
    @Value("${address.cbkj.zb.token}")
    private String cbkjZbToken;

    /**
     * 获取经方辩证URL
     *
     * @return
     */
    public ResEntity getDialectical() {
        String userId = StringUtils.isBlank(AdminUtils.getCurrentPractice().getEmployeeId()) ? AdminUtils.getCurrentHr().getEmployeeId() : AdminUtils.getCurrentPractice().getEmployeeId();
        if (StringUtils.isBlank(userId)) {
            return ResEntity.error("当前执业机构用户工号为空，无法使用经方辩证！");
        }
        return knowRedisService.getDialectical(userId, AdminUtils.getCurrentInsCode());
    }

    /**
     * 获取专病列表
     *
     * @param apiToken
     * @return
     */
    public ResEntity getListByApiToken(String apiToken, String type) {

        if ("gydsbz".equals(type)) {
            return getDiseaseFromMaster(apiToken);
        } else if ("zbbz".equals(type)) {
            return getDiseaseFromSrip();
        } else {
            return getListByApiToken();
        }
    }

    /**
     * 获取两种专病列表
     *
     * @return ResEntity
     */
    public ResEntity getListByApiToken() {

        //专病参数为空时，不需要调用接口
        String masterDisease = tSysParamService.getSysParam(Constant.MASTER_DISEASE).getParValues();
        if (StringUtils.isBlank(masterDisease)) {
            return ResEntity.success(new JSONArray());
        }

        JSONArray arr = new JSONArray();
        ResEntity res1 = getDiseaseFromMaster(cbkjZbToken);
        if (res1.getStatus()) {
            JSONArray arr1 = (JSONArray) res1.getData();
            arr.addAll(arr1);
        }
        ResEntity res2 = getDiseaseFromSrip();
        if (res2.getStatus()) {
            JSONArray arr2 = (JSONArray) res2.getData();
            arr.addAll(arr2);
        }
        return ResEntity.success(arr);

    }


    /**
     * 获取国医大师专病列表
     *
     * @param apiToken apiToken
     * @return ResEntity
     */
    private ResEntity getDiseaseFromMaster(String apiToken) {
        if (StringUtils.isBlank(apiToken)) {
            apiToken = cbkjZbToken;
        }


        String masterDisease = tSysParamService.getSysParam(Constant.MASTER_DISEASE).getParValues();
        if (StringUtils.isBlank(masterDisease)) {
            return ResEntity.success(new JSONArray());
        }

        JSONArray dataArr = new JSONArray();
        List<String> masterDiseases = Arrays.asList(masterDisease.split(","));

        String url = cbkjZbUrl + "getListByApiToken?format=json&apiToken=" + apiToken;
        JSONObject jsonObject = knowRedisService.callZbInterface(url, "get", null);
        if (jsonObject.getBoolean("status")) {
            JSONArray data = jsonObject.getJSONArray("data");
            for (Object object : data) {
                JSONObject obj = (JSONObject) object;
                String diseaseCode = obj.getString("diseaseCode");
                if (masterDiseases.contains(diseaseCode)) {
                    obj.put("type", "master");
                    obj.put("url", cbkjZbUrl + "toDiseasePage?apiToken=" + cbkjZbToken + "&diseaseCode=" + diseaseCode);
                    dataArr.add(obj);
                }
            }
        }

        return ResEntity.success(dataArr);
    }

    /**
     * 获取传承系统专病列表
     *
     * @return ResEntity
     */
    private ResEntity getDiseaseFromSrip() {

        String employeeId = StringUtils.isBlank(AdminUtils.getCurrentPractice().getEmployeeId()) ? AdminUtils.getCurrentHr().getEmployeeId() : AdminUtils.getCurrentPractice().getEmployeeId();
        if (StringUtils.isBlank(employeeId)) {
            return ResEntity.error("访问传承系统需要设置用户工号");
        }

        ResEntity res = knowRedisService.callZhuanbingInterface(employeeId, AdminUtils.getCurrentInsCode());
        if (!res.getStatus()) {
            return res;
        }

        JSONArray dataArr = new JSONArray();
        List<Map<String, Object>> data = (List<Map<String, Object>>) res.getData();
        for (Map<String, Object> ccm : data) {
            String diseaseId = (String) ccm.get("disease_id");
            String name = (String) ccm.get("name");
            String url2 = (String) ccm.get("url");
            JSONObject obj = new JSONObject();
            obj.put("diseaseId", diseaseId);
            obj.put("diseaseName", name);
            obj.put("url", url2);
            obj.put("type", "chuancheng");
            dataArr.add(obj);
        }

        return ResEntity.success(dataArr);
    }

    /**
     * 2.2获取专病模板
     *
     * @param apiToken
     * @param diseaseCode
     * @return
     */
    public Object getDiseaseTemplate(String apiToken, String diseaseCode) {
        if (StringUtils.isBlank(apiToken)) {
            apiToken = cbkjZbToken;
        }
        String url = cbkjZbUrl + "getDiseaseTemplate?format=json&apiToken=" + apiToken + "&diseaseCode=" + diseaseCode;
        JSONObject jsonObject = knowRedisService.callZbInterface(url, "get", null);
        if (jsonObject.getBoolean("status")) {
            JSONObject data = jsonObject.getJSONObject("data");
            if (null != data.get("headUrl") && StringUtils.isNotBlank(data.get("headUrl").toString())) {
                data.put("headUrl", cbkjZbPort + data.get("headUrl"));
                jsonObject.put("data", data);
            }
        }
        return jsonObject;
    }

    /**
     * 2.3提交专病数据计算并返回处方信息
     *
     * @param zbReqVO
     * @return
     */
    public Object getDiseaseResult(ZbReqVO zbReqVO) {
        if (StringUtils.isBlank(zbReqVO.getApiToken())) {
            zbReqVO.setApiToken(cbkjZbToken);
        }
        if (StringUtils.isBlank(zbReqVO.getDoctorName())) {
            zbReqVO.setDoctorName(AdminUtils.getCurrentHr().getNameZh());
        }
        String url = cbkjZbUrl + "getDiseaseResult?format=json";
        JSONObject jsonObject = knowRedisService.callZbInterface(url, "post", zbReqVO);
        return jsonObject;
    }


}