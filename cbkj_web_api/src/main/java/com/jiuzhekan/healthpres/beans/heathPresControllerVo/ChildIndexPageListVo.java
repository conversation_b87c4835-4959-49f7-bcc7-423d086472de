package com.jiuzhekan.healthpres.beans.heathPresControllerVo;

import com.jiuzhekan.cbkj.common.utils.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/5/26 17:28
 * @Version 1.0
 */
@ApiModel
@Data
public class ChildIndexPageListVo {

    Integer page = 0;
    Integer limit =10;
    @ApiModelProperty(value = "患者姓名")
    String patientName;
    @ApiModelProperty(value = "1.儿童保健2.慢病调理")
    private Integer questionnaireMainType;
}
