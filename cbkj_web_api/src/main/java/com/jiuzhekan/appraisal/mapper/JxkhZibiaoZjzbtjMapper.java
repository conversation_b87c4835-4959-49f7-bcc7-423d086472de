package com.jiuzhekan.appraisal.mapper;

import com.jiuzhekan.appraisal.bean.JxkhZibiaoZjzbtj;
import com.jiuzhekan.appraisal.bean.dao.PerformanceAppraisalDao;
import com.jiuzhekan.appraisal.bean.dto.PerformanceAppraisalDeptRe;
import com.jiuzhekan.appraisal.bean.dto.PerformanceAppraisalRe;
import org.springframework.stereotype.Component;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;

import java.util.List;

@Component
public interface JxkhZibiaoZjzbtjMapper extends BaseMapper<JxkhZibiaoZjzbtj>{


    List<JxkhZibiaoZjzbtj> getPageListByPerformanceAppraisalRe(PerformanceAppraisalDao dao);
    List<JxkhZibiaoZjzbtj> getMZBPageDatas(PerformanceAppraisalDao dao);
    List<JxkhZibiaoZjzbtj> getMZBPageDatasOnlyINS(PerformanceAppraisalDao dao);

    List<JxkhZibiaoZjzbtj> getZZBDeptPageDatas(PerformanceAppraisalDeptRe performanceAppraisalRe);
}