package com.jiuzhekan.cbkj.common.scheduler;

import com.jiuzhekan.cbkj.beans.business.doctor.TDoctorDisease;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import com.jiuzhekan.cbkj.common.utils.RedisLockUtil;
import com.jiuzhekan.cbkj.mapper.business.doctor.TDoctorDiseaseMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2020/9/1 10:40
 */
@Slf4j
@Component
@EnableScheduling
@Transactional(rollbackFor = Exception.class)
public class DoctorDiseaseScheduler {

    @Autowired
    private TDoctorDiseaseMapper tDoctorDiseaseMapper;
    @Autowired
    private RedisLockUtil redisLockUtil;


    /**
     * 统计医生常用疾病
     * <p>
     * 0 1/5 * * * ?  每天五分钟执行执行,测试用
     * 0 0 2 * * ?    每天两点执行,对应statisticDay = 1
     * 0 0 2 1 * ?    每月一号执行,对应statisticDay = 30
     *
     * <AUTHOR>
     * @date 2020/8/24
     */
    @Scheduled(cron = "0 20 0 1 * ?")
    public void statisticDoctorDisease() {

        String lockKey = "STATISTIC_DOCTOR_DISEASE";
        String lockValue = String.valueOf(Thread.currentThread().getId());
        int lockTime = 100;
        log.info("-------STATISTIC_DOCTOR_DISEASE------");
        if (redisLockUtil.lock(lockKey, lockValue, lockTime)) {
            log.info("-------STATISTIC_DOCTOR_DISEASE  BEGAN------");

            int statisticDay = 30;

            List<TDoctorDisease> allList = tDoctorDiseaseMapper.statisticDoctorDiseaseFromGroup(statisticDay);
            LinkedHashMap<String, List<TDoctorDisease>> map = new LinkedHashMap<>();

            allList.forEach(dis -> {
                List<TDoctorDisease> disList = map.computeIfAbsent(dis.getDocId(), k -> new ArrayList<>());

                if (disList.size() < 2 || (disList.size() < 5 && dis.getDisNum() > 100)) {
                    disList.add(dis);
                }
            });

            Date date = new Date();
            List<TDoctorDisease> insertList = new ArrayList<>();

            map.forEach((key, disList) -> {
                disList.forEach(dis -> {
                    dis.setId(IDUtil.getID());
                    dis.setCreateTime(date);
                    insertList.add(dis);
                });
            });

            if (insertList.size() > 0) {
                tDoctorDiseaseMapper.insertList(insertList);
            }

            allList.clear();
            map.clear();
            insertList.clear();

//            redisLockUtil.unLock(lockKey, lockValue);
            log.info("-------STATISTIC_DOCTOR_DISEASE  END------");
        }

    }
}