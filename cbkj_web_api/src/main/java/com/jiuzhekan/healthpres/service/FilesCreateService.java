package com.jiuzhekan.healthpres.service;

import com.jiuzhekan.cbkj.beans.business.patients.TPatientAge;
import com.jiuzhekan.cbkj.beans.business.patients.TPatients;
import com.jiuzhekan.cbkj.common.utils.DateUtil;
import com.jiuzhekan.cbkj.common.utils.word.WordGenerator;
import com.jiuzhekan.cbkj.common.utils.word.WordToPdfByAspose;
import com.jiuzhekan.cbkj.service.business.patients.TPatientsService;
import com.jiuzhekan.healthpres.beans.TUserQuestionnaireResult;
import com.jiuzhekan.healthpres.beans.heathPresControllerVo.LookHealthPreMainResponse;
import com.jiuzhekan.healthpres.mapper.TUserQuestionnaireResultMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/6/5 13:37
 * @Version 1.0
 */
@Slf4j
@Service
public class FilesCreateService {
    @Value("${file.address}")
    private String location;
    @Value("${root.preview}")
    private String preview;
    @Value("${root.upload.relative}")
    private String relative;

    private static final String ANALYSIS_PDF_FOLDER = "analysis2/";


    private final TUserQuestionnaireResultMapper tUserQuestionnaireResultMapper;
    private final TPatientsService patientsService;

    public FilesCreateService(TUserQuestionnaireResultMapper tUserQuestionnaireResultMapper, TPatientsService patientsService) {
        this.tUserQuestionnaireResultMapper = tUserQuestionnaireResultMapper;
        this.patientsService = patientsService;
    }

//    @Async
    public void createWordPdf(String resultId) {

        String dateFolder = DateUtil.getDateFormats(DateUtil.YYYYMMDD, null);
        String realFolder = location + relative + ANALYSIS_PDF_FOLDER + dateFolder + "/";
        String fileFolder = preview + relative + ANALYSIS_PDF_FOLDER + dateFolder + "/";


        LookHealthPreMainResponse lookHealthPreMainResponse = tUserQuestionnaireResultMapper.lookHealthPre(resultId);

        TPatients tPatients = patientsService.findById(lookHealthPreMainResponse.getPatientId());
        Date resultReportTime = lookHealthPreMainResponse.getResultReportTime();

        TPatientAge ageByBirth = TPatientsService.getAgeByBirth(tPatients.getPatientBirthday());
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("patientName", lookHealthPreMainResponse.getPatientName());
        dataMap.put("patientAge", ageByBirth.getAge());
        dataMap.put("analyCode", lookHealthPreMainResponse.getResultCode());
        dataMap.put("reportTitle", lookHealthPreMainResponse.getResultReportName());
        dataMap.put("reportTime", DateUtil.getDateFormats(DateUtil.YYYY__MM__DD_, resultReportTime));
        dataMap.put("insName", lookHealthPreMainResponse.getInsName());
        dataMap.put("doctorName", lookHealthPreMainResponse.getCreateDoctorName());
        dataMap.put("patientSex", (StringUtils.isNotBlank(tPatients.getPatientGender()) ? ("M".equals(tPatients.getPatientGender()) ? "男" : "女") : ""));

        //构建报告内容：showType 是 0 的
        ArrayList<Object> list = new ArrayList<>();
        //构建报告内容：showType 是 1 的
        ArrayList<Object> list2 = new ArrayList<>();

        lookHealthPreMainResponse.getQuestionnaireMainItemList().forEach(item -> {
            if (item.getShowType() == 0) {
                HashMap<Object, Object> map = new HashMap<>();
                map.put("itemTitle",  item.getItemTitle());
                map.put("itemContent", item.getItemContent());
                list.add(map);
            } else {
                HashMap<Object, Object> map = new HashMap<>();
                map.put("itemTitle",  item.getItemTitle());
                map.put("itemContent", item.getItemContent());
                list2.add(map);
            }
        });
        dataMap.put("dataListOne", list);
        dataMap.put("dataListTwo", list2);


        String analyName = "analysisReport" + DateUtil.getDateFormats(DateUtil.YYYYMMDDHHMMSS, new Date());
        String wordName = analyName + ".doc";
        String pdfName = analyName + ".pdf";

        //Word访问路径
        String wordFilePath = fileFolder + wordName;
        //Word存储路径
        String wordRealPath = realFolder + wordName;
        //PDF访问路径
        String pdfFilePath = fileFolder + pdfName;
        //PDF存储路径
        String pdfRealPath = realFolder + pdfName;

        File folder = new File(realFolder);
        if (!folder.exists()) {
            folder.mkdirs();
        }

        try {
            //生成Word文档
            File word = WordGenerator.createDoc2(wordRealPath, dataMap);
            //Word转PDF
            WordToPdfByAspose.wordToPdf(pdfRealPath, Files.newInputStream(word.toPath()));

        } catch (IOException e) {
            log.error("生成Word文档失败:{}", e.getMessage());
        }
        TUserQuestionnaireResult result = new TUserQuestionnaireResult();
        result.setResultId(resultId);
        result.setWordPath(wordFilePath);
        result.setPdfPath(pdfFilePath);
        tUserQuestionnaireResultMapper.updateWordPdf(result);


    }
}
