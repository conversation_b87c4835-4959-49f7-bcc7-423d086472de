package com.jiuzhekan.cbkj.service.myData;

import com.github.pagehelper.PageHelper;
import com.jiuzhekan.cbkj.beans.business.TSysParam;
import com.jiuzhekan.cbkj.beans.business.prescription.THisXdf;
import com.jiuzhekan.cbkj.beans.business.prescription.THisXdfMapping;
import com.jiuzhekan.cbkj.beans.business.prescription.THisXdfResult;
import com.jiuzhekan.cbkj.beans.business.prescription.TPersonalPrescriptionDisMapping;
import com.jiuzhekan.cbkj.beans.business.record.TRegister;
import com.jiuzhekan.cbkj.beans.business.store.TDisplay;
import com.jiuzhekan.cbkj.beans.formula.TFormula;
import com.jiuzhekan.cbkj.beans.myData.FolderIdD;
import com.jiuzhekan.cbkj.beans.myData.TPersonalPrescription;
import com.jiuzhekan.cbkj.beans.myData.TPersonalPrescriptionItem;
import com.jiuzhekan.cbkj.beans.myData.TPersonalRuleAuth;
import com.jiuzhekan.cbkj.beans.sysBeans.AdminInfo;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.http.KnowRestTemplate;
import com.jiuzhekan.cbkj.common.http.PlatformRestTemplate;
import com.jiuzhekan.cbkj.common.utils.*;
import com.jiuzhekan.cbkj.mapper.business.prescription.THisXdfMapper;
import com.jiuzhekan.cbkj.mapper.business.prescription.THisXdfMappingMapper;
import com.jiuzhekan.cbkj.mapper.business.prescription.TPersonalPrescriptionDisMappingMapper;
import com.jiuzhekan.cbkj.mapper.myData.TPersonalPrescriptionItemMapper;
import com.jiuzhekan.cbkj.mapper.myData.TPersonalPrescriptionMapper;
import com.jiuzhekan.cbkj.mapper.myData.TPersonalRuleAuthMapper;
import com.jiuzhekan.cbkj.service.business.TSysParamService;
import com.jiuzhekan.cbkj.service.convertor.TPersonalRuleAuthConvertor;
import com.jiuzhekan.cbkj.service.formula.TFormulaService;
import com.jiuzhekan.cbkj.service.redis.ClientRedisService;
import com.jiuzhekan.cbkj.service.redis.ParameterDisplayRedisService;
import com.jiuzhekan.cbkj.service.redis.RedisService;
import com.jiuzhekan.cbkj.service.sysService.AdminService;
import org.apache.commons.lang.StringUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

@Service
public class TPersonalPrescriptionService {

    private final TPersonalPrescriptionMapper tPersonalPrescriptionMapper;
    private final TPersonalPrescriptionItemMapper tPersonalPrescriptionItemMapper;
    private final TPersonalPrescriptionFolderService tPersonalPrescriptionFolderService;
    private final TPersonalRuleAuthMapper tPersonalRuleAuthMapper;
    private final RedisService redisService;
    private final ClientRedisService clientRedisService;
    private final ParameterDisplayRedisService parameterDisplayRedisService;
    private final AdminService adminService;
    private final TSysParamService tSysParamService;
    private final KnowRestTemplate knowRestTemplate;
    private final TFormulaService tFormulaService;
    private final PlatformRestTemplate platformRestTemplate;
    private final THisXdfMapper tHisXdfMapper;
    private final THisXdfMappingMapper tHisXdfMappingMapper;

    private final TPersonalPrescriptionDisMappingMapper tPersonalPrescriptionDisMappingMapper;

    public TPersonalPrescriptionService(TPersonalPrescriptionMapper tPersonalPrescriptionMapper,
                                        TPersonalPrescriptionItemMapper tPersonalPrescriptionItemMapper,
                                        TPersonalPrescriptionFolderService tPersonalPrescriptionFolderService,
                                        TPersonalRuleAuthMapper tPersonalRuleAuthMapper,
                                        RedisService redisService,
                                        AdminService adminService, TSysParamService tSysParamService,
                                        KnowRestTemplate knowRestTemplate,
                                        TFormulaService tFormulaService,
                                        THisXdfMapper tHisXdfMapper,
                                        THisXdfMappingMapper tHisXdfMappingMapper,
                                        PlatformRestTemplate platformRestTemplate, ClientRedisService clientRedisService, ParameterDisplayRedisService parameterDisplayRedisService, TPersonalPrescriptionDisMappingMapper tPersonalPrescriptionDisMappingMapper) {
        this.tPersonalPrescriptionMapper = tPersonalPrescriptionMapper;
        this.tPersonalPrescriptionItemMapper = tPersonalPrescriptionItemMapper;
        this.tPersonalPrescriptionFolderService = tPersonalPrescriptionFolderService;
        this.tPersonalRuleAuthMapper = tPersonalRuleAuthMapper;
        this.redisService = redisService;
        this.adminService = adminService;
        this.tSysParamService = tSysParamService;
        this.knowRestTemplate = knowRestTemplate;
        this.tFormulaService = tFormulaService;
        this.platformRestTemplate = platformRestTemplate;
        this.clientRedisService = clientRedisService;
        this.parameterDisplayRedisService = parameterDisplayRedisService;
        this.tHisXdfMapper = tHisXdfMapper;
        this.tHisXdfMappingMapper = tHisXdfMappingMapper;
        this.tPersonalPrescriptionDisMappingMapper = tPersonalPrescriptionDisMappingMapper;
    }


    public Object getPersonalPresByFolder(TPersonalPrescription perPre, Page page) {
//        AdminInfo currentHr = AdminUtils.getCurrentHr();



        if (perPre.getFolderId() == null) {
            perPre.setFolderId("");
        }

        //知识库
        if (perPre.getFolderId().startsWith(Constant.FOLDER_KNOW)) {
            return getKnowPresByFolder(perPre, page);
        }

        if (perPre.getFolderId().equals(Constant.FOLDER_FORMULA_INSURANCE)) {
            TFormula formula = new TFormula(perPre.getPreName(), perPre.getPreType(), perPre.getStoreId(),perPre.getFolderId());
            return tFormulaService.search(formula, page, Constant.BASIC_STRING_ONE,"1");
        }
        if (perPre.getFolderId().equals(Constant.FOLDER_FORMULA_SELFPAYING)) {
            TFormula formula = new TFormula(perPre.getPreName(), perPre.getPreType(), perPre.getStoreId(),perPre.getFolderId());
            return tFormulaService.search(formula, page, Constant.BASIC_STRING_ONE,"2");
        }

        //配方
        if (perPre.getFolderId().equals(Constant.FOLDER_FORMULA)) {
            TFormula formula = new TFormula(perPre.getPreName(), perPre.getPreType(), perPre.getStoreId(),perPre.getFolderId());
            return tFormulaService.search(formula, page, Constant.BASIC_STRING_ONE,"0");
        }

        //膏方
        if (perPre.getFolderId().startsWith(Constant.FOLDER_PASTE)) {
            TFormula formula = new TFormula(perPre.getPreName(), perPre.getPreType(), perPre.getStoreId(),perPre.getFolderId());
            return tFormulaService.search(formula, page, Constant.BASIC_STRING_TWO,"0");
        }

        //协定方
        initSearchParams(perPre);

        perPre.setPage( ( page.getPage() - 1)* page.getLimit());
        perPre.setLimit(page.getLimit());
        List<TPersonalPrescription> list;
        int temp = 0;
        if (Constant.FOLDER_SELF.equals(perPre.getFolderId())) {
            temp = 1;
            perPre.setPreOwner(AdminUtils.getCurrentHr().getId() );
            perPre.setShareType(Constant.BASIC_STRING_ZERO);

        } else if (Constant.FOLDER_SELF_OTHER.equals(perPre.getFolderId())) {
            temp = 1;
            perPre.setShareType(Constant.BASIC_STRING_ZERO);
            perPre.setOther(true);

        } else if (Constant.FOLDER_DEPT.equals(perPre.getFolderId())) {
            temp = 1;
            perPre.setAppId(AdminUtils.getCurrentAppId());
            perPre.setInsCode(AdminUtils.getCurrentInsCode());
            perPre.setDeptId(AdminUtils.getCurrentDeptId());
            perPre.setShareType(Constant.BASIC_STRING_ONE);

        } else if (Constant.FOLDER_DEPT_OTHER.equals(perPre.getFolderId())) {
            temp = 1;
            perPre.setAppId(AdminUtils.getCurrentAppId());
            perPre.setInsCode(AdminUtils.getCurrentInsCode());
            perPre.setDeptId(AdminUtils.getCurrentDeptId());
            perPre.setShareType(Constant.BASIC_STRING_ONE);
            perPre.setOther(true);

        } else if (Constant.FOLDER_INS.equals(perPre.getFolderId())) {
            temp = 1;
            perPre.setAppId(AdminUtils.getCurrentAppId());
            perPre.setInsCode(AdminUtils.getCurrentInsCode());
            perPre.setShareType(Constant.BASIC_STRING_TWO);

        } else if (Constant.FOLDER_INS_OTHER.equals(perPre.getFolderId())) {
            temp = 1;
            perPre.setAppId(AdminUtils.getCurrentAppId());
            perPre.setInsCode(AdminUtils.getCurrentInsCode());
            perPre.setShareType(Constant.BASIC_STRING_TWO);
            perPre.setOther(true);
        }else if (Constant.FOLDER_APP.equals(perPre.getFolderId())) {
            temp = 1;
            perPre.setAppId(AdminUtils.getCurrentAppId());
            perPre.setShareType(Constant.BASIC_STRING_FOUR);

        } else if (Constant.FOLDER_APP_OTHER.equals(perPre.getFolderId())) {
            temp = 1;
            perPre.setAppId(AdminUtils.getCurrentAppId());
            perPre.setShareType(Constant.BASIC_STRING_FOUR);
            perPre.setOther(true);
        }else if (Constant.FOLDER_AREA.equals(perPre.getFolderId())) {
            temp = 1;
            perPre.setShareType(Constant.BASIC_STRING_FIVE);

        } else if (Constant.FOLDER_AREA_OTHER.equals(perPre.getFolderId())) {
            temp = 1;
            perPre.setShareType(Constant.BASIC_STRING_FIVE);
            perPre.setOther(true);
        }

        if (temp == 1) {
            //PageHelper.startPage(page.getPage(), page.getLimit());
            //if (StringUtils.isNotBlank(perPre.getDisId()) || StringUtils.isNotBlank(perPre.getSymId()) || StringUtils.isNotBlank(perPre.getTheCode())){

            list = tPersonalPrescriptionMapper.getPersonalPresByTypeDisMapping(perPre);
            Long listCount = tPersonalPrescriptionMapper.countGetPersonalPresByTypeDisMapping(perPre);
            canUpdate(list);
            //总页数
            long taotalPage = listCount / page.getLimit();
            long a = listCount % page.getLimit();
            if (a > 0) {
                taotalPage = taotalPage + 1;
            }
            return Page.getLayuiData(true, "success", listCount, taotalPage > page.getPage(), list);

        } else if (StringUtils.isNotBlank(perPre.getFolderId())) {
            perPre.setAppId(AdminUtils.getCurrentAppId());
            perPre.setInsCode(AdminUtils.getCurrentInsCode());
            
            List<String> folderIds = tPersonalPrescriptionFolderService.getChildrenIdsByPidInMap(
                    perPre.getFolderId(), clientRedisService.getAllFolderMap());
            //perPre.setFolderIdList(folderIds);

            ArrayList<FolderIdD> folderIdDS = new ArrayList<>();
            folderIds.forEach(a -> folderIdDS.add(new FolderIdD(a)));
            perPre.setFolders(folderIdDS);
            //PageHelper.startPage(page.getPage(), page.getLimit());
            list = tPersonalPrescriptionMapper.getPersonalPresByFolder(perPre);
            Long listCount = tPersonalPrescriptionMapper.countGetPersonalPresByFolder(perPre);
            canUpdate(list);
            //总页数
            long taotalPage = listCount / page.getLimit();
            long a = listCount % page.getLimit();
            if (a > 0) {
                taotalPage = taotalPage + 1;
            }
            return Page.getLayuiData(true, "success", listCount, taotalPage > page.getPage(), list);
        } else {
            PageHelper.startPage(page.getPage(), page.getLimit());
            //文件夹ID为空时，查询全部协定方
            perPre.setShareType("0,1,2,4,5");
            list = tPersonalPrescriptionMapper.getSearchList(perPre);
            PageHelper.clearPage();
        }
        //PageHelper.startPage(page.getPage(), page.getLimit());
        for (TPersonalPrescription pre : list) {
            pre.setFolderId(perPre.getFolderId());
            pre.setInsName(adminService.getInsName(pre.getAppId(), pre.getInsCode()));
            pre.setDeptName(adminService.getDeptName(pre.getAppId(), pre.getInsCode(), pre.getDeptId()));
        }

        canUpdate(list);
        //PageHelper.clearPage();
        return Page.getLayUiTablePageData(list);
    }

    /**
     * 根据方名,功效与适应症,协定方类型,共享级别,preOwner 拥有者 加载分页数据
     *
     * @param tPersonalPrescription
     * @param page
     * @return
     */
    public Object getPageDatas(TPersonalPrescription tPersonalPrescription, Page page) {

//        initSearchParams(tPersonalPrescription);
        tPersonalPrescription.setAppId(AdminUtils.getCurrentAppId());
        tPersonalPrescription.setInsCode(AdminUtils.getCurrentInsCode());
        tPersonalPrescription.setPreOwner(Constant.ADMIN.equals(AdminUtils.getCurrentHr().getName()) ? null : AdminUtils.getCurrentHr().getId());
        PageHelper.startPage(page.getPage(), page.getLimit());

        List<TPersonalPrescription> list = tPersonalPrescriptionMapper.getPageListByPer(tPersonalPrescription);

        for (TPersonalPrescription pre : list) {
            pre.setInsName(adminService.getInsName(pre.getAppId(), pre.getInsCode()));
            pre.setDeptName(adminService.getDeptName(pre.getAppId(), pre.getInsCode(), pre.getDeptId()));
        }

        return Page.getLayUiTablePageData(list);
    }


    public Object searchList(TPersonalPrescription tPersonalPrescription, Page page) {

        initSearchParams(tPersonalPrescription);

        PageHelper.startPage(page.getPage(), page.getLimit());

        List<TPersonalPrescription> list = tPersonalPrescriptionMapper.getSearchList(tPersonalPrescription);
        for (TPersonalPrescription pre : list) {
            pre.setInsName(adminService.getInsName(pre.getAppId(), pre.getInsCode()));
            pre.setDeptName(adminService.getDeptName(pre.getAppId(), pre.getInsCode(), pre.getDeptId()));
        }
        canUpdate(list);

        return Page.getLayUiTablePageData(list);
    }

    /**
     * 初始化参数
     *
     * @param tPersonalPrescription tPersonalPrescription
     * @return void
     * <AUTHOR>
     * @date 2021/4/9
     */
    private void initSearchParams(TPersonalPrescription tPersonalPrescription) {
        String preName = tPersonalPrescription.getPreName();
        if (StringUtils.isNotBlank(preName)) {
            String curExtContent = AdminUtils.getCurrentShuruma();
            if (!StringJudges.isContainChinese(preName)) {
                tPersonalPrescription.setPreName("");
                if ("2".equals(curExtContent)) {
                    tPersonalPrescription.setPreWb(preName);
                } else {
                    tPersonalPrescription.setPrePy(preName);
                }
            }
        }

        String appId = AdminUtils.getCurrentAppId();
        String insCode = AdminUtils.getCurrentInsCode();
        String deptId = AdminUtils.getCurrentDeptId();

        tPersonalPrescription.setPreOwner(AdminUtils.getCurrentHr().getId());

        if (StringUtils.isNotBlank(appId) && !appId.equals(Constant.BASIC_APP_ID)) {
            tPersonalPrescription.setAppId(appId);
        }
        if (StringUtils.isNotBlank(insCode) && !insCode.equals(Constant.BASIC_INS_CODE)) {
            tPersonalPrescription.setInsCode(insCode);
        }


        if (StringUtils.isNotBlank(deptId) && !deptId.equals(Constant.BASIC_DEPT_ID)) {
            tPersonalPrescription.setDeptId(deptId);
        }

        TRegister register = AdminUtils.getCurrentRegister();

        if (register == null && StringUtils.isNotBlank(tPersonalPrescription.getRegisterId())) {
            register = clientRedisService.getRegisterById(tPersonalPrescription.getRegisterId());
        }

        if (register != null) {

            if (StringUtils.isNotBlank(register.getDeptId()) && !register.getDeptId().equals(Constant.BASIC_DEPT_ID)) {
                tPersonalPrescription.setDeptId(register.getDeptId());
            }

            if (register.getClinicTypeId() != null) {

                TSysParam param = tSysParamService.getSysParam(Constant.PERSONAL_PRESCRIPTION_MZ_ZY);
                if (Constant.BASIC_STRING_ONE.equals(param.getParValues())) {

                    tPersonalPrescription.setPreMzZy(String.valueOf(register.getClinicTypeId()));
                }
            }
        }

        String filterAppCode = tSysParamService.getSysParam(Constant.PERSONAL_PRESCRIPTION_FILTER_APP).getParValues();
        if (Constant.BASIC_STRING_ONE.equals(filterAppCode)) {
            tPersonalPrescription.setFilterApp(true);
        }
        String filterInsCode = tSysParamService.getSysParam(Constant.PERSONAL_PRESCRIPTION_FILTER_INSCODE).getParValues();
        if (Constant.BASIC_STRING_ONE.equals(filterInsCode)) {
            tPersonalPrescription.setFilterInsCode(true);
        }
        String filterDept = tSysParamService.getSysParam(Constant.PERSONAL_PRESCRIPTION_FILTER_DEPT).getParValues();
        if (Constant.BASIC_STRING_ONE.equals(filterDept)) {
            tPersonalPrescription.setFilterDept(true);
        }
        String filterSelfByIns = tSysParamService.getSysParam(Constant.PERSONAL_PRESCRIPTION_FILTER_SELF_BY_INS).getParValues();
        if (Constant.BASIC_STRING_ONE.equals(filterSelfByIns)) {
            tPersonalPrescription.setFilterSelfByIns(true);
        }
    }

    /**
     * 能否修改协定方
     *
     * @param list list
     * <AUTHOR>
     * @date 2021/4/9
     */
    private void canUpdate(List<TPersonalPrescription> list) {
        if (list != null && list.size() > 0) {
            AdminInfo admin = AdminUtils.getCurrentHr();

//            Set<String> displaySet = new HashSet<>();
//            List<TDisplay> displayList = parameterDisplayRedisService.showDisplayList2(
//                    AdminUtils.getCurrentAppId(), AdminUtils.getCurrentInsCode(), AdminUtils.getCurrentDeptId(), null);
//            displayList.forEach(display -> displaySet.add(display.getPreMatType() + display.getStoreId()));
            String param = tSysParamService.getSysParam("ONLY_CREATE_USER_EDIT_PERSON_PRE").getParValues();
            for (TPersonalPrescription prescription : list) {
                if (Constant.BASIC_STRING_ZERO.equals(param)){
                    prescription.setCanUpdate(Constant.BASIC_STRING_ONE);
                }else {
                    if (admin.getId().equals(prescription.getPreOwner())
//                        && displaySet.contains(dKey)
                    ) {
                        prescription.setCanUpdate(Constant.BASIC_STRING_ONE);
                    } else {
                        prescription.setCanUpdate(Constant.BASIC_STRING_ZERO);
                    }
                }
//                String dKey = prescription.getPreMatType() + prescription.getStoreId();
//                if (admin.getId().equals(prescription.getPreOwner())
////                        && displaySet.contains(dKey)
//                ) {
//                    prescription.setCanUpdate(Constant.BASIC_STRING_ONE);
//                } else {
//                    prescription.setCanUpdate(Constant.BASIC_STRING_ZERO);
//                }

                if (admin.getId().equals(prescription.getPreOwner())) {
                    prescription.setCanDelete(Constant.BASIC_STRING_ONE);
                } else {
                    prescription.setCanDelete(Constant.BASIC_STRING_ZERO);
                }

            }
        }
    }

    /**
     * 查询方剂，返回协定方格式
     *
     * @param tPersonalPrescription tPersonalPrescription
     * @param page                  page
     * @return java.lang.Object
     * <AUTHOR>
     * @date 2021/4/9
     */
    private Object getKnowPresByFolder(TPersonalPrescription tPersonalPrescription, Page page) {
        Map<String, Object> map = new HashMap<>();
        map.put("keyword", tPersonalPrescription.getPreName());
        map.put("searchtype", StringJudges.isContainChinese(tPersonalPrescription.getPreName()) ? "" :
                AdminUtils.getCurrentShuruma());
//        map.put("disname", tPersonalPrescription.getDisName());
        map.put("pageid", page.getPage().toString());
        map.put("pagesize", page.getLimit().toString());
        if (tPersonalPrescription.getFolderId().startsWith(Constant.FOLDER_KNOW_)) {
            map.put("categoryid", tPersonalPrescription.getFolderId().substring(5));
        }


        String url = Constant.BASIC_STRING_TWO.equals(tPersonalPrescription.getPreType()) ? "smoked/list" : "pres/list";
        ResEntity preEntity = knowRestTemplate.postKnow(url, map);
        if (preEntity.getStatus()) {
            Map<String, Object> data = (Map<String, Object>) preEntity.getData();
            List<Map<String, String>> preList = (List<Map<String, String>>) data.get("pres");
            int prenum = (int) data.get("presnum");

            List<TPersonalPrescription> perPreList = new ArrayList<>();
            TPersonalPrescription perPre;
            for (Map<String, String> preMap : preList) {
                perPre = new TPersonalPrescription();
                perPre.setPersPreId(preMap.get("preid"));
                perPre.setPreName(preMap.get("prename"));
                perPre.setEfficacy(preMap.get("efficacy"));
                perPre.setPreItem(preMap.get("matNames"));
                perPre.setPreOwnerName(preMap.get("book"));
                perPre.setFolderId(tPersonalPrescription.getFolderId());
                perPreList.add(perPre);
            }

            Map<String, Object> result = new HashMap<>();
            result.put("status", true);
            result.put("message", Constant.SUCCESS_DX);
            result.put("data", perPreList);
            result.put("count", prenum);
            result.put("isHasNextPage", page.getPage() * page.getLimit() < prenum);
            return result;
        }
        return Page.getLayUiTablePageData(new ArrayList<>());
    }

    /**
     * 插入新数据
     *
     * @param tPersonalPrescription
     * @return
     * @throws Exception
     */
    @CacheEvict(value = "know::scheme", allEntries = true, condition = "\"3\".equals(#tPersonalPrescription.isShare)")
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insert(TPersonalPrescription tPersonalPrescription) {
        String persPreId = IDUtil.getID();
        tPersonalPrescription.setPersPreId(persPreId);
        initPersVal(tPersonalPrescription);


        List<TPersonalPrescriptionItem> items = tPersonalPrescription.getPerItems();
        if (null != items && !items.isEmpty()) {
            initPreItemS(persPreId, items);
        } else {
            return ResEntity.entity(false, "协定方明细不能为空", tPersonalPrescription);
        }

        int existNum = tPersonalPrescriptionMapper.getNumByPreNameOnOwner(tPersonalPrescription);
        if (existNum > 0) {
            return new ResEntity(false, "您已添加过" + tPersonalPrescription.getPreName(), null);
        }

        tPersonalPrescription.setPreMzZy(Constant.BASIC_STRING_ONE);
        if (StringUtils.isNotBlank(tPersonalPrescription.getRegisterId())) {
            TRegister register = clientRedisService.getRegisterById(tPersonalPrescription.getRegisterId());
            if (register != null && register.getClinicTypeId() != null) {
                tPersonalPrescription.setPreMzZy(String.valueOf(register.getClinicTypeId()));
            }
        }

        long rows = tPersonalPrescriptionMapper.insert(tPersonalPrescription);
        long rowItem = tPersonalPrescriptionItemMapper.insertList(items);
        tPersonalPrescriptionFolderService.insertFmap(tPersonalPrescription.getFolderId(), tPersonalPrescription.getPersPreId());

        //维护协定方权限
//        if (Constant.BASIC_STRING_ZERO.equals(tPersonalPrescription.getIsShare())
//                || Constant.BASIC_STRING_ONE.equals(tPersonalPrescription.getIsShare())
//                || Constant.BASIC_STRING_TWO.equals(tPersonalPrescription.getIsShare())
//                || Constant.BASIC_STRING_FOUR.equals(tPersonalPrescription.getIsShare())
//                || Constant.BASIC_STRING_FIVE.equals(tPersonalPrescription.getIsShare())) {
//
//            savePersPreAuth(tPersonalPrescription);
//        }

        //插入协定方病症法信息
        List<TPersonalPrescriptionDisMapping> tPersonalPrescriptionDisMappingsList = tPersonalPrescription.getTPersonalPrescriptionDisMappingsList();
        if (!CollectionUtils.isEmpty(tPersonalPrescriptionDisMappingsList)) {
            for (int i = 0; i < tPersonalPrescriptionDisMappingsList.size(); i++) {
                tPersonalPrescriptionDisMappingsList.get(i).setPersPreId(persPreId);
            }
            tPersonalPrescriptionDisMappingMapper.insertList(tPersonalPrescriptionDisMappingsList);
        }

        if (rows > 0 && rowItem > 0) {
            if (StringUtils.isNotBlank(tPersonalPrescription.getXh())) {
                //插入
                THisXdfMapping tHisXdfMappingNew = new THisXdfMapping();
                tHisXdfMappingNew.setXh(tPersonalPrescription.getXh());
                tHisXdfMappingNew.setPersPreId(persPreId);
                tHisXdfMappingMapper.insert(tHisXdfMappingNew);
            }
            return ResEntity.entity(true, Constant.SUCCESS_DX, tPersonalPrescription);
        } else {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return new ResEntity(false, "保存失败，数据库异常！！", null);
        }

    }


    /**
     * 修改
     * 选择先删除详情，再新增的方式修改。
     *
     * @param tPersonalPrescription
     * @return
     */
    @CacheEvict(value = "know::scheme", allEntries = true, condition = "\"3\".equals(#tPersonalPrescription.isShare)")
    @Transactional(rollbackFor = Exception.class)
    public ResEntity update(TPersonalPrescription tPersonalPrescription) {
        // 删除详情
        String persPreId = tPersonalPrescription.getPersPreId();
        initPersVal(tPersonalPrescription);
        TPersonalPrescription objectById = tPersonalPrescriptionMapper.getObjectById(persPreId);
        if (objectById != null)
        {
            //修改不要修改原始创建人
            tPersonalPrescription.setPreOwner(objectById.getPreOwner());
            tPersonalPrescription.setPreOwnerName(objectById.getPreOwnerName());
        }
        List<TPersonalPrescriptionItem> items = tPersonalPrescription.getPerItems();
        if (null != items && !items.isEmpty()) {
            initPreItemS(persPreId, items);
        } else {
            return ResEntity.entity(false, "协定方明细不能为空", tPersonalPrescription);
        }


        int existNum = tPersonalPrescriptionMapper.getNumByPreNameOnOwner(tPersonalPrescription);
        if (existNum > 0) {
            return new ResEntity(false, "您已添加过" + tPersonalPrescription.getPreName(), null);
        }

        long rows = tPersonalPrescriptionMapper.updateByPrimaryKey(tPersonalPrescription);
        // 先删除 原先协定方绑定的协定方详情
        long delRows = tPersonalPrescriptionItemMapper.deleteByPersPreId(persPreId);
        if (delRows <= 0) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return new ResEntity(false, "修改失败，数据库异常", null);
        }
        long updateItemRows = tPersonalPrescriptionItemMapper.insertList(items);

        tPersonalPrescriptionFolderService.deleteFmapByPerPreId(tPersonalPrescription.getPersPreId());
        tPersonalPrescriptionFolderService.insertFmap(tPersonalPrescription.getFolderId(), tPersonalPrescription.getPersPreId());

        //维护协定方权限
//        if (Constant.BASIC_STRING_ZERO.equals(tPersonalPrescription.getIsShare())
//                || Constant.BASIC_STRING_ONE.equals(tPersonalPrescription.getIsShare())
//                || Constant.BASIC_STRING_TWO.equals(tPersonalPrescription.getIsShare())
//                || Constant.BASIC_STRING_FOUR.equals(tPersonalPrescription.getIsShare())
//                || Constant.BASIC_STRING_FIVE.equals(tPersonalPrescription.getIsShare())) {
//
//            tPersonalRuleAuthMapper.deleteByPreId(tPersonalPrescription.getPersPreId());
//            savePersPreAuth(tPersonalPrescription);
//        }

        if (updateItemRows <= 0) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return new ResEntity(false, "修改失败，数据库异常", null);
        }


        //插入协定方病症法信息
        List<TPersonalPrescriptionDisMapping> tPersonalPrescriptionDisMappingsList = tPersonalPrescription.getTPersonalPrescriptionDisMappingsList();
        //先删除协定方病症法维护信息
        tPersonalPrescriptionDisMappingMapper.deleteBypresId(persPreId);
        if (!CollectionUtils.isEmpty(tPersonalPrescriptionDisMappingsList)) {
            for (int i = 0; i < tPersonalPrescriptionDisMappingsList.size(); i++) {
                TPersonalPrescriptionDisMapping tPersonalPrescriptionDisMapping = tPersonalPrescriptionDisMappingsList.get(i);
                tPersonalPrescriptionDisMapping.setPersPreId(persPreId);
            }
            tPersonalPrescriptionDisMappingMapper.insertList(tPersonalPrescriptionDisMappingsList);
        }


        //修改协定方序号绑定
        //先删除，在插入。
        THisXdfMapping tHisXdfMapping = new THisXdfMapping();
        tHisXdfMapping.setPersPreId(persPreId);
        tHisXdfMapper.deleteMapping(tHisXdfMapping);
        if (StringUtils.isNotBlank(tPersonalPrescription.getXh())) {
            //插入
            THisXdfMapping tHisXdfMappingNew = new THisXdfMapping();
            tHisXdfMappingNew.setXh(tPersonalPrescription.getXh());
            tHisXdfMappingNew.setPersPreId(persPreId);
            tHisXdfMappingMapper.insert(tHisXdfMappingNew);
        }
        return new ResEntity(true, Constant.SUCCESS_DX, tPersonalPrescription);
    }

    private void savePersPreAuth(TPersonalPrescription pers) {

        String deptId = StringUtils.isBlank(pers.getDeptId()) ? Constant.BASIC_DEPT_ID : pers.getDeptId();

        List<TPersonalRuleAuth> authList = new ArrayList<>();

        boolean containPreOwner =  false;
        AtomicBoolean isContainPreOwner = new AtomicBoolean(false);

        if (Constant.BASIC_STRING_ZERO.equals(pers.getIsShare())) {

            TPersonalRuleAuth auth = new TPersonalRuleAuth();
            auth.setRuleId(Constant.BASIC_STRING_ZERO);
            auth.setDeptId(deptId);
            auth.setDeptCheckAll(false);
            auth.setUserId(pers.getPreOwner());
            auth.setPersPreId(pers.getPersPreId());
            auth.setCreateDate(new Date());
            auth.setCreateUser(AdminUtils.getCurrentHr().getNameZh());
            authList.add(auth);

            containPreOwner=true;

        } else if (Constant.BASIC_STRING_ONE.equals(pers.getIsShare())) {

            Map<String, Object> param = new HashMap<>();
            param.put("insCode", pers.getInsCode());
            param.put("deptId", deptId);
            ResEntity resEntity = platformRestTemplate.post("user/list", param);
            if (resEntity.getStatus()) {
                List<Object> list = (List<Object>) resEntity.getData();
                for (Object o : list) {
                    AdminInfo admin = new AdminInfo(o);
                    TPersonalRuleAuth auth = new TPersonalRuleAuth();
                    auth.setRuleId(Constant.BASIC_STRING_ONE);
                    auth.setDeptId(deptId);
                    auth.setDeptCheckAll(true);
                    auth.setUserId(admin.getId());
                    auth.setPersPreId(pers.getPersPreId());
                    auth.setCreateDate(new Date());
                    auth.setCreateUser(AdminUtils.getCurrentHr().getNameZh());
                    authList.add(auth);

                    if (pers.getPreOwner().equals(admin.getId())) {
                        containPreOwner=true;
                    }
                }
            }
        } else if (Constant.BASIC_STRING_TWO.equals(pers.getIsShare())) {
            Map<String, Object> param = new HashMap<>();
            param.put("insCode", pers.getInsCode());
            ResEntity resEntity = platformRestTemplate.post("user/list", param);
            if (resEntity.getStatus()) {
                List<Object> list = (List<Object>) resEntity.getData();
                for (Object o : list) {
                    AdminInfo admin = new AdminInfo(o);
                    TPersonalRuleAuth auth = new TPersonalRuleAuth();
                    auth.setRuleId(Constant.BASIC_STRING_TWO);
                    auth.setDeptId(admin.getDeptId());
                    auth.setDeptCheckAll(true);
                    auth.setUserId(admin.getId());
                    auth.setPersPreId(pers.getPersPreId());
                    auth.setCreateDate(new Date());
                    auth.setCreateUser(AdminUtils.getCurrentHr().getNameZh());
                    authList.add(auth);

                    if (pers.getPreOwner().equals(admin.getId())) {
                        containPreOwner=true;
                    }
                }
            }
        }else if (Constant.BASIC_STRING_FOUR.equals(pers.getIsShare())) {
            Map<String, Object> param = new HashMap<>();
            param.put("appId", pers.getAppId());
            ResEntity resEntity = platformRestTemplate.post("user/list", param);
            if (resEntity.getStatus()) {
                List<Object> list = (List<Object>) resEntity.getData();
                List<TPersonalRuleAuth> ruleAuthList = list.stream().map(o -> {
                    AdminInfo admin = new AdminInfo(o);
                    if (pers.getPreOwner().equals(admin.getId())) {
                        isContainPreOwner.set(true);
                    }
                    return admin;
                }).map(i -> TPersonalRuleAuthConvertor.convertorToInsert(Constant.BASIC_STRING_FOUR,pers.getPersPreId(), i)).collect(Collectors.toList());
               authList.addAll(ruleAuthList);
            }
        }else if (Constant.BASIC_STRING_FIVE.equals(pers.getIsShare())) {
            ResEntity resEntity = platformRestTemplate.post("user/list", new HashMap<>());
            if (resEntity.getStatus()) {
                List<Object> list = (List<Object>) resEntity.getData();
                List<TPersonalRuleAuth> ruleAuthList = list.stream().map(o -> {
                    AdminInfo admin = new AdminInfo(o);
                    if (pers.getPreOwner().equals(admin.getId())) {
                        isContainPreOwner.set(true);
                    }
                    return admin;
                }).map(i -> TPersonalRuleAuthConvertor.convertorToInsert(Constant.BASIC_STRING_FIVE,pers.getPersPreId(), i)).collect(Collectors.toList());
                authList.addAll(ruleAuthList);
            }
        }

        if (!(containPreOwner||isContainPreOwner.get())) {

            TPersonalRuleAuth auth = new TPersonalRuleAuth();
            auth.setRuleId(pers.getIsShare());
            auth.setDeptId(deptId);
            auth.setDeptCheckAll(true);
            auth.setUserId(pers.getPreOwner());
            auth.setPersPreId(pers.getPersPreId());
            auth.setCreateDate(new Date());
            auth.setCreateUser(AdminUtils.getCurrentHr().getNameZh());
            authList.add(auth);
        }

        tPersonalRuleAuthMapper.insertList(authList);
    }

    @Transactional(rollbackFor = Exception.class)
    public ResEntity updateOrder(TPersonalPrescription tPersonalPrescription) {
        if (StringUtils.isBlank(tPersonalPrescription.getPersPreId())) {
            return new ResEntity(false, "协定方ID不能为空", null);
        }
        long rows = tPersonalPrescriptionMapper.updateOrder(tPersonalPrescription);

        return new ResEntity(true, Constant.SUCCESS_DX, tPersonalPrescription);
    }

    /**
     * @param persPreId :
     * @param items     :
     * @Description :  协定方明细赋值
     * <AUTHOR> 徐亨期
     * @updateTime : 2019/12/25 11:13
     */
    private void initPreItemS(String persPreId, List<TPersonalPrescriptionItem> items) {
        Integer seqn = 1;
        for (TPersonalPrescriptionItem item : items) {
            item.setPersPreId(persPreId);
            item.setPersPreItemId(IDUtil.getID());
            item.setMatSeqn(seqn);
            seqn++;
        }
    }

    /**
     * 新增和修改操作有一些赋值是一样的。
     *
     * @param tPersonalPrescription
     * @return
     */
    private void initPersVal(TPersonalPrescription tPersonalPrescription) {
        AdminInfo currentHr = AdminUtils.getCurrentHr();
        if (!Constant.BASIC_APP_ID.equals(AdminUtils.getCurrentAppId())) {
            tPersonalPrescription.setAppId(AdminUtils.getCurrentAppId());
        }
        if (!Constant.BASIC_INS_CODE.equals(AdminUtils.getCurrentInsCode())) {
            tPersonalPrescription.setInsCode(AdminUtils.getCurrentInsCode());
        }

        //String deptId = AdminUtils.getCurrentDeptId();
        //if (StringUtils.isNotBlank(deptId) && !Constant.BASIC_DEPT_ID.equals(deptId)) {
        //    tPersonalPrescription.setDeptId(deptId);
        //} else if (StringUtils.isNotBlank(AdminUtils.getCurrentDeptId()) && !Constant.BASIC_DEPT_ID.equals(AdminUtils.getCurrentDeptId())) {
        //    tPersonalPrescription.setDeptId(AdminUtils.getCurrentDeptId());
        //}

        tPersonalPrescription.setDeptId(AdminUtils.getCurrentDeptIdIgnoreBasic());

        tPersonalPrescription.setPreOwner(currentHr.getId());
        tPersonalPrescription.setPreOwnerName(currentHr.getNameZh());
        if (StringUtils.isBlank(tPersonalPrescription.getPrePy())) {
            tPersonalPrescription.setPrePy(Dist.getFirstPinYins(tPersonalPrescription.getPreName()));
        }
        if (StringUtils.isBlank(tPersonalPrescription.getPreWb())) {
            tPersonalPrescription.setPreWb(Dist.getFives(tPersonalPrescription.getPreName()));
        }
        tPersonalPrescription.setIsDel(Constant.BASIC_DEL_NO);
    }


    /**
     * 加载某条数据
     *
     * @param persPreId
     * @return
     */
    public ResEntity findObj(String persPreId) {

        if (StringUtils.isBlank(persPreId)) {
            return new ResEntity(false, "参数不能为空哦", null);
        }
        TPersonalPrescription pre = tPersonalPrescriptionMapper.getObjectById(persPreId);

        if (null == pre || Constant.BASIC_STRING_ONE.equals(pre.getIsDel()) && Constant.BASIC_STRING_ZERO.equals(pre.getIsModify())) {
            return ResEntity.error("来源处方不存在，不可转方！");
        }

        pre.setPerItems(tPersonalPrescriptionItemMapper.getPersItems(persPreId));

        String folderId = tPersonalPrescriptionFolderService.getFolderIdByPerPreId(pre.getPersPreId());
        pre.setFolderId(folderId);

        List<String> folderIds = tPersonalPrescriptionFolderService.getParentsIdsByPidInMap(folderId,
                clientRedisService.getAllFolderMap());
        pre.setFolderIdList(folderIds);
        THisXdfMapping tHisXdfMapping = new THisXdfMapping();
        tHisXdfMapping.setPersPreId(persPreId);
        THisXdfResult byxhAndPresId = tHisXdfMappingMapper.getByxhAndPresId(tHisXdfMapping);
        if (byxhAndPresId != null) {
            pre.setXh(byxhAndPresId.getXh());
            pre.setXhName(byxhAndPresId.getXhName());
        }
        List<TPersonalPrescriptionDisMapping> tPersonalPrescriptionDisMappingList = tPersonalPrescriptionDisMappingMapper.getListByPersPreId(persPreId);
        pre.setTPersonalPrescriptionDisMappingsList(tPersonalPrescriptionDisMappingList);
        return new ResEntity(true, Constant.SUCCESS_DX, pre);
    }

    /**
     * 删除
     *
     * @param ids
     * @return
     */
    @CacheEvict(value = "know::scheme", allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public ResEntity deleteLis(String ids) {
        if (StringUtils.isBlank(ids)) {
            return new ResEntity(false, "参数错误(缺少参数)！", null);
        }
        long rowsR = tPersonalPrescriptionMapper.deleteBylist(ids.split(","));
        tPersonalPrescriptionFolderService.deleteFmapByPerPreId(ids);
//        tPersonalRuleAuthMapper.deleteByPreId(ids);
        //删除协定方和his处方号的绑定关系
        tHisXdfMappingMapper.deleteBylist(ids.split(","));
        return new ResEntity(true, Constant.SUCCESS_DX, rowsR);
    }

    /**
     * 根据疾病证型推导院内方
     *
     * @param disId        disId
     * @param symId        symId
     * @param businessType businessType
     * @return com.jiuzhekan.cbkj.beans.sysBeans.ResEntity
     * <AUTHOR>
     * @date 2020/7/2
     */
    public List<Map> getAppPersonalPres(String disId, String symId, String businessType) {
        List<Map> pres = new ArrayList<>();
        if (StringUtils.isBlank(disId) || StringUtils.isBlank(symId) || StringUtils.isBlank(businessType)) {
            return pres;
        }

        TPersonalPrescription tPersonalPrescription = new TPersonalPrescription();
        //ShareType=3 院内方
        tPersonalPrescription.setShareType(Constant.BASIC_STRING_THREE);
        tPersonalPrescription.setDisId(disId);
        tPersonalPrescription.setSymId(symId);
        tPersonalPrescription.setPreType(businessType);

        String appId = AdminUtils.getCurrentAppId();
        if (StringUtils.isNotBlank(appId) && !appId.equals(Constant.BASIC_DEPT_ID)) {
            tPersonalPrescription.setAppId(appId);
        }
        pres = tPersonalPrescriptionMapper.getAppPres(tPersonalPrescription);
        for (Map pre : pres) {
            List<Map> items = tPersonalPrescriptionItemMapper.getPerItems(pre.get("preid").toString());
            pre.put("items", items);
        }
        return pres;
    }

    public Object getHISList(String presPreId, String searchStr, Page page) {
        PageHelper.startPage(page.getPage(), page.getLimit());
        THisXdfMapping tHisXdfMapping = new THisXdfMapping();
        tHisXdfMapping.setPersPreId(presPreId);
        tHisXdfMapping.setSearchStr(searchStr);
        List<THisXdf> list = tHisXdfMapper.getList(tHisXdfMapping);
        return Page.getLayUiTablePageData(list);

    }
}