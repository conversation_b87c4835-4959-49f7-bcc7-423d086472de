package com.jiuzhekan.recordquality.beans;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class TRecordQualityRuleItemsMapping implements Serializable {

    @ApiModelProperty(value = "映射自增主键id")
    private Integer id;

    @ApiModelProperty(value = "第一条件的id（下拉id）")
    private String id1;

    @ApiModelProperty(value = "第一条件的名称（下拉名称）")
    private String name1;

    @ApiModelProperty(value = "第二条件id（互斥下拉的id）")
    private String id2;

    @ApiModelProperty(value = "第二条件名称（互斥下拉的名称）")
    private String name2;

    @ApiModelProperty(value = "上级id")
    private String qualityRuleItemsId;

    @ApiModelProperty(value = "(判断互斥这个字段用到)互斥关系 1疾病2西医3症状4关键词，疾病质控有：1-3 1-4 2-3 2-4 ，症状质控有：3-3")
    private String qualityRuleItemsClass;

    @ApiModelProperty(value = "后端查询使用字段(判断互斥这个字段用到)")
    private String recordQualityRuleName;

    @ApiModelProperty(value = "(判断互斥这个字段用到) 如果 id2 是 关键字类型，那么这个对应是 nowDesc（现病史） pastDesc（既往史）physical（体格检查），或者是代码是.99的末尾的自定义症状")
    private String huChiTwoId;


}
