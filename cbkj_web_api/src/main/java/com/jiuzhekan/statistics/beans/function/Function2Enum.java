package com.jiuzhekan.statistics.beans.function;

import com.jiuzhekan.cbkj.beans.statistics.FunctionEnum;

/**
 * <AUTHOR>
 */

public enum Function2Enum {
    /**
     * 智能辨证:【中医诊疗-智能辨证】“推导”的次数
     * 智能推导:【中医诊疗-智能开方】“推导”的次数
     * 名家验案:【中医知识库-名家验案/我的收藏】、【中医诊疗-智能开方-参考医案】阅读医案详情的次数
     * 中药查询:【中医知识库-中药查询/我的收藏】、【中医诊疗-智能开方-药品说明书】阅读中药详情的次数
     * 方剂查询:【中医知识库-方剂查询/我的收藏】阅读方剂详情的次数+【中医诊疗-智能开方-方剂】 换方、合方的次数
     * 经络穴位查询:  中医知识库-经络穴位查询/我的收藏】阅读详情的次数
     * 经方查询：【中医知识库-经方查询/我的收藏】阅读经方详情的次数
     * 疾病查询：【中医知识库-疾病查询/我的收藏】阅读疾病详情的次数
     * 中成药：【中医知识库-中成药/我的收藏】阅读中成药详情的次数
     * 临床诊疗指南：【中医知识库-临床诊疗指南/我的收藏】阅读临床诊疗指南详情的次数
     * 舌诊：【中医知识库-舌诊/我的收藏】阅读舌诊详情的次数
     * 脉诊： 【中医知识库-脉诊/我的收藏】阅读脉诊详情的次数
     * （预留）古书籍：【中医知识库-古书籍/我的收藏】阅读古书籍详情的次数
     */
    ONE("智能辩证",1),
    TWO("智能推导",2),
    THREE("参考医案",3),
    FOUR("中药查询",4),
    FIVE("方剂查询",5),
    SIX("经络穴位查询",6),
    SEVEN("经方查询",7),
    EIGHT("疾病查询",8),
    NIGHT("中成药",9),
    TEN("临床诊疗指南",10),
    ELEVEN("舌诊",11),
    TOWELETTE("脉诊",12),
    THREATEN("古书籍",13)

    ;
    /**
     * 分类
     */
//    private String classify;

    private String function;
    /**
     * 分类对应参数编号
     */
    private int number;

    Function2Enum(String function, int number) {
        this.function = function;
        this.number = number;
    }



    public int getNumber() {
        return number;
    }

    public String getFunction() {
        return function;
    }

    public static Function2Enum find(int number) {
        for (Function2Enum fun : Function2Enum.values()) {
            if (fun.getNumber() == number) {
                return fun;
            }
        }
        return null;
    }
}
