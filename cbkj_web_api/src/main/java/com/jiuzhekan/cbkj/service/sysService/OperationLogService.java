package com.jiuzhekan.cbkj.service.sysService;

import com.github.pagehelper.PageHelper;
import com.jiuzhekan.cbkj.beans.sysBeans.OperationLog;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.mapper.sysMapper.OperationLogMapper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Transactional(rollbackFor = Exception.class)
public class OperationLogService {

    @Autowired
    private OperationLogMapper operationLogMapper;

    /**
     * 加载分页数据
     *
     * @param operationLog
     * @param page
     * @return
     */
    public Object getPageDatas(OperationLog operationLog, Page page) {

        PageHelper.startPage(page.getPage(), page.getLimit());
        if (StringUtils.isNotBlank(operationLog.getBeginTime())) {
            operationLog.setBeginTime(operationLog.getBeginTime() + " 00:00:00");
        }
        if (StringUtils.isNotBlank(operationLog.getEndTime())) {
            operationLog.setEndTime(operationLog.getEndTime() + " 23:59:59");
        }

        operationLog.setTableSuffix("_" + new SimpleDateFormat("yyyyMM").format(new Date()));

        List<Map<String, Object>> lis = operationLogMapper.getPageDatas(operationLog);
        Object result = Page.getLayUiTablePageData(lis);
        return result;
    }

    /**
     * 插入新数据
     *
     * @param operationLog
     * @return
     * @throws Exception
     */
    public ResEntity insert(OperationLog operationLog) {

        operationLog.setCreateDate(new Date());
        operationLog.setTableSuffix("_" + new SimpleDateFormat("yyyyMM").format(operationLog.getCreateDate()));

        operationLogMapper.createTimeTable(operationLog);
        long rows = operationLogMapper.insert(operationLog);
        if (rows > 0) {
            return new ResEntity(true, Constant.SUCCESS_DX, operationLog);
        }
        return new ResEntity(false, "保存失败，数据库异常！！", null);
    }


    /**
     * 修改
     *
     * @param operationLog
     * @return
     */
    public ResEntity update(OperationLog operationLog) {

        long rows = operationLogMapper.updateByPrimaryKey(operationLog);

        if (rows > 0) {
            return new ResEntity(true, Constant.SUCCESS_DX, null);
        }

        return new ResEntity(false, "修改失败，数据库异常", null);
    }

    /**
     * 加载某条数据
     *
     * @param id
     * @return
     */
    public ResEntity findObj(String id) {

        if (StringUtils.isBlank(id)) {
            return new ResEntity(false, "参数不能为空哦", null);
        }



        HashMap<String, String> map = new HashMap<>();
        map.put("id", id);
        map.put("tableSuffix", "_" + new SimpleDateFormat("yyyyMM").format(new Date()));
        OperationLog objM = operationLogMapper.getObjectByMap(map);
        return new ResEntity(true, Constant.SUCCESS_DX, objM);
    }

    /**
     * 删除
     *
     * @param ids
     * @return
     */
    public ResEntity deleteLis(String ids) {
        if (StringUtils.isBlank(ids)) {
            return new ResEntity(false, "参数错误(缺少参数)！", null);
        }
        long rowsR = operationLogMapper.deleteBylist(ids.split(Constant.ENGLISH_COMMA));

        return new ResEntity(true, Constant.SUCCESS_DX, rowsR);
    }
}
