package com.jiuzhekan.healthpres.beans;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class TUserQuestionnaireResult implements Serializable {

    @ApiModelProperty(value = "报告id")
    private String resultId;

    @ApiModelProperty(value = "患者id")
    private String patientId;

    @ApiModelProperty(value = "患者姓名（其他数据关联患者表查询）")
    private String patientName;

    @ApiModelProperty(value = "报告名称")
    private String resultReportName;

    @ApiModelProperty(value = "报告时间")
    private Date resultReportTime;

    @ApiModelProperty(value = "插入时间")
    private Date insertTime;

    @ApiModelProperty(value = "报告类型信息")
    private String resultReportTypeInfo;

    @ApiModelProperty(value = "创建医生id")
    private String createDoctorId;

    @ApiModelProperty(value = "创建医生名称")
    private String createDoctorName;

    @ApiModelProperty(value = "最后修改医生id")
    private String lastUpdateDoctorId;

    @ApiModelProperty(value = "最后修改医生名字")
    private String lastUpdateDoctorName;

    @ApiModelProperty(value = "最后修改时间")
    private Date lastUpdateTime;

    @ApiModelProperty(value = "删除医生id")
    private String delDoctorId;

    @ApiModelProperty(value = "删除医生名称")
    private String delDoctorName;

    @ApiModelProperty(value = "删除时间")
    private Date delTime;

    @ApiModelProperty(value = "标题")
    private String questionnaireMainTitle;

    @ApiModelProperty(value = "内容首行文本")
    private String questionnaireMainFirstLine;

    @ApiModelProperty(value = "内容第二行提示。")
    private String questionnaireMainTips;

    @ApiModelProperty(value = "1.儿童保健2.慢病调理3.健康处方（开方界面没有默认的慢病和儿童保健）")
    private Integer questionnaireMainType;

    @ApiModelProperty(value = "子分类：1-1(6、12月龄儿童)1-2(18、24月龄儿童)1-3(30、36月龄儿童) ")
    private String questionnaireMainSecondTypeCode;

    @ApiModelProperty(value = "子类型名称")
    private String questionnaireMainSecondTypeName;

    @ApiModelProperty(value = "0.正常1.删除")
    private Integer status;

    @ApiModelProperty(value = "word地址")
    private String wordPath;

    @ApiModelProperty(value = "pdf地址")
    private String pdfPath;
    private String appId;
    private String insCode;
    private String deptId;
    private String appName;
    private String insName;
    private String deptName;

    private String resultCode;


}
