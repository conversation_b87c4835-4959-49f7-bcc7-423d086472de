package com.jiuzhekan.statistics.mapper.board;

import com.jiuzhekan.statistics.beans.board.TStatisticsPrescriptionType;
import com.jiuzhekan.statistics.beans.register.QueryParam;
import org.springframework.stereotype.Component;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;

import java.util.List;
import java.util.Map;

@Component
public interface TStatisticsPrescriptionTypeMapper extends BaseMapper<TStatisticsPrescriptionType>{


    TStatisticsPrescriptionType getOneByObject(TStatisticsPrescriptionType tStatisticsPrescriptionType);

    /**
     * 从处方状态表 t_order_status 获取今天的煎煮中处方 实时数据
     * @param param
     * @return
     */
    List<TStatisticsPrescriptionType> getStaticsResult(Map<String, Object> param);
    /**
     * 从处方状态表 t_order_status 获取今天的已签收处方 实时数据
     * @param param
     * @return
     */
    List<TStatisticsPrescriptionType> getStaticsResult2(Map<String, Object> param);

    /**
     * 获取统计数据
     * @param queryParam
     * @return
     */
    Integer sumStatistics(QueryParam queryParam);
}