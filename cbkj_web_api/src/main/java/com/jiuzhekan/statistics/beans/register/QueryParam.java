package com.jiuzhekan.statistics.beans.register;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 **/
@Data
@ApiModel("统计报表查询数据")
public class QueryParam {
    private String appId;
    private String insCode;
    private String doctorId;
    /**
     * 0 搜索 1不搜索
     */
    private String searchDoctor ;
    private String doctorName;
    /**
     * 1.煎煮处方（85 130 135）
     * 2.已签收书房（150）
     */
    private Integer numType;
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date queryStartDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date queryEndDate;

    List<QueryParam> queryParamList;
    @ApiModelProperty(hidden = true)
    private String beginDate;
    @ApiModelProperty(hidden = true)
    private String endDate;
}
