package com.jiuzhekan.cbkj.mapper.statistics;

import com.jiuzhekan.cbkj.beans.statistics.TStatisticsFunction;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component
public interface TStatisticsFunctionMapper extends BaseMapper<TStatisticsFunction> {

    /**
     * 获取某时间段某功能的使用量总和
     *
     * @param tStatisticsFunction functionName
     *                            functionSource
     *                            beginDate
     *                            endDate
     * @return com.jiuzhekan.cbkj.beans.statistics.TStatisticsFunction
     * <AUTHOR> cong bao
     * @date 2021/4/15
     */
    Integer sumUsageTimes(TStatisticsFunction tStatisticsFunction);

    /**
     * 获取某功能最后的使用量
     *
     * @param tStatisticsFunction functionName
     *                            functionSource
     * @return com.jiuzhekan.cbkj.beans.statistics.TStatisticsFunction
     * <AUTHOR> cong bao
     * @date 2021/4/15
     */
    Integer lastUsageTimes(TStatisticsFunction tStatisticsFunction);

//    /**
//     * 五笔使用人数
//     *
//     * <AUTHOR>
//     * @date 2021/4/16
//     */
//    void statisticWb();

//    /**
//     * 拼音使用人数
//     *
//     * <AUTHOR>
//     * @date 2021/4/16
//     */
//    void statisticPy();

//    /**
//     * 科室数
//     *
//     * <AUTHOR>
//     * @date 2021/4/16
//     */
//    void statisticDept();

    /**
     * 中医体质辨识-体质辨识数量
     *
     * <AUTHOR>
     * @date 2021/4/16
     */
    void statisticAnalysis();

    /**
     * 总处方数
     *
     * <AUTHOR>
     * @date 2021/4/16
     */
    void statisticPreNum();

    /**
     * 处方审核-人工审核的处方数
     *
     * <AUTHOR>
     * @date 2021/4/16
     */
    void statisticCheckPreNum();

    /**
     * 智能开方-内服处方数量
     *
     * <AUTHOR>
     * @date 2021/4/16
     */
    void statisticInPreNum();

    /**
     * 智能开方-内服膏方数量
     *
     * <AUTHOR>
     * @date 2021/4/16
     */
    void statisticInPreNumProduction();

    /**
     * 智能开方-内服代煎数量
     *
     * <AUTHOR>
     * @date 2021/4/16
     */
    void statisticInPreNumDecoction();

    /**
     * 智能开方-内服配送数量
     *
     * <AUTHOR>
     * @date 2021/4/16
     */
    void statisticInPreNumExpress();

    /**
     * 协定方-协定方数量
     *
     * <AUTHOR>
     * @date 2021/4/16
     */
    void statisticPersonalNum();

    /**
     * 协定方-个人协定方数量
     *
     * <AUTHOR>
     * @date 2021/4/16
     */
    void statisticPersonalSelfNum();

    /**
     * 协定方-科室协定方数量
     *
     * <AUTHOR>
     * @date 2021/4/16
     */
    void statisticPersonalDeptNum();

    /**
     * 协定方-全院协定方数量
     *
     * <AUTHOR>
     * @date 2021/4/16
     */
    void statisticPersonalInsNum();

    /**
     * 电子病历模板配置-电子病历模板数
     *
     * <AUTHOR>
     * @date 2021/4/16
     */
    void statisticRecordTemplate();

    /**
     * 专家经验共享-专家经验共享条数
     *
     * <AUTHOR>
     * @date 2021/4/16
     */
    void statisticAppExpertPreNum();

    /**
     * 专家经验共享-专家经验共享内服处方数
     *
     * <AUTHOR>
     * @date 2021/4/16
     */
    void statisticAppExpertInPreNum();

    /**
     * 专家经验共享-专家经验共享外用处方数
     *
     * <AUTHOR>
     * @date 2021/4/16
     */
    void statisticAppExpertExtPreNum();

    /**
     * 专家经验共享-专家经验共享专家数
     *
     * <AUTHOR>
     * @date 2021/4/16
     */
    void statisticAppExpertNum();

    /**
     * 名医验案-收藏次数
     *
     * <AUTHOR>
     * @date 2021/4/16
     */
    void statisticVerifyCollection();

    /**
     * 中药查询-收藏次数
     *
     * <AUTHOR>
     * @date 2021/4/16
     */
    void statisticMatCollection();

    /**
     * 方剂查询-收藏次数
     *
     * <AUTHOR>
     * @date 2021/4/16
     */
    void statisticPreCollection();

    /**
     * 经络穴位查询-收藏次数
     *
     * <AUTHOR>
     * @date 2021/4/16
     */
    void statisticAcuCollection();

    /**
     * 疾病查询-收藏次数
     *
     * <AUTHOR>
     * @date 2021/4/16
     */
    void statisticDisCollection();
}