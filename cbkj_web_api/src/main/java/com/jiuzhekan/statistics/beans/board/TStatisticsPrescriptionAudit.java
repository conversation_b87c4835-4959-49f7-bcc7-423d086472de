package com.jiuzhekan.statistics.beans.board;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class TStatisticsPrescriptionAudit implements Serializable{

    @ApiModelProperty(value = "")
    private Integer id;

    @ApiModelProperty(value = "")
    private String appId;

    @ApiModelProperty(value = "")
    private String appName;

    @ApiModelProperty(value = "")
    private String insCode;

    @ApiModelProperty(value = "")
    private String insName;

    @ApiModelProperty(value = "")
    private String deptId;

    @ApiModelProperty(value = "统计日期")
    private Date createDate;

    @ApiModelProperty(value = "插入时间")
    private Date insertDate;

    @ApiModelProperty(value = "中药审方数量：系统审方+人工审方之和，统计所有状态处方；")
    private Integer tcmAllNum;


}
