package com.jiuzhekan.cbkj.service.business.patients;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.util.StringUtil;
import com.jiuzhekan.cbkj.beans.business.TSysParam;
import com.jiuzhekan.cbkj.beans.business.patients.TDcAddress;
import com.jiuzhekan.cbkj.beans.business.patients.TPatientAge;
import com.jiuzhekan.cbkj.beans.business.patients.TPatients;
import com.jiuzhekan.cbkj.beans.business.patients.VO.TPatientsReqVO;
import com.jiuzhekan.cbkj.beans.business.record.TRecord;
import com.jiuzhekan.cbkj.beans.business.record.TRegister;
import com.jiuzhekan.cbkj.beans.sysApp.SysInstitution;
import com.jiuzhekan.cbkj.beans.sysBeans.AdminInfo;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.http.PlatformRestTemplate;
import com.jiuzhekan.cbkj.common.utils.*;
import com.jiuzhekan.cbkj.mapper.business.patients.TDcAddressMapper;
import com.jiuzhekan.cbkj.mapper.business.patients.TPatientsMapper;
import com.jiuzhekan.cbkj.mapper.business.record.TRegisterMapper;
import com.jiuzhekan.cbkj.service.business.TSysParamService;
import com.jiuzhekan.cbkj.service.convertor.SickPersonInfoConvertor;
import com.jiuzhekan.cbkj.service.redis.ClientRedisService;
import com.jiuzhekan.cbkj.service.redis.OrgRedisService;
import com.jiuzhekan.cbkj.service.redis.RedisService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.time.LocalDate;
import java.time.Period;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.ConcurrentMap;

@Service
public class TPatientsService {
    private static final String PATIENT_CERTIFICATE = "patientCertificate";
    private static final String PATIENT_NAME = "patientName";
    private static final String PATIENT_MOBILE = "patientMobile";


    @Autowired
    private TPatientsMapper tPatientsMapper;
    @Autowired
    private TDcAddressMapper tDcAddressMapper;
    @Autowired
    private OrgRedisService orgRedisService;
    @Autowired
    private ClientRedisService clientRedisService;
    @Autowired
    private TSysParamService tSysParamService;
    @Autowired
    private TRegisterMapper tRegisterMapper;
    @Autowired
    private PlatformRestTemplate platformRestTemplate;

    /**
     * 加载分页数据
     *
     * @param tPatients
     * @param page
     * @return
     */
    public Object getPageDatas(TPatients tPatients, Page page) {
        PageHelper.startPage(page.getPage(), page.getLimit());
        List<TPatients> list = tPatientsMapper.getPageListByObj(tPatients);
        Object result = Page.getLayUiTablePageData(list);
        return result;
    }


    /**
     * @return : com.jiuzhekan.cbkj.beans.sysBeans.ResEntity
     * @Description : 患者就诊范围,1 医联体内不需要重复建档, 2 医疗机构内不需要重复建档
     * 范围是2 时: 病人第一次在A医疗机构看病时首次建档，第二次在B医疗机构看病时，不需要重新建档，如果重新建档需提示该病人信息已存在。
     * 首次建档的时候将地址插一份到收货地址里去,默认地址id就取这一份的,要改收货地址就开方的时候去维护了.
     * 同时创建数据重复的话?
     * <AUTHOR> 徐亨期
     * @updateTime : 20   19/12/20 13:08
     */
    public ResEntity patientsInsert(TPatients tPatients) {

        String patientVisitRange = tSysParamService.getSysParam(Constant.PATIENT_VISIT_RANGE).getParValues();
      /*  if(StringUtils.isBlank(patientVisitRange) || (!"1".equals(patientVisitRange)&&!"2".equals(patientVisitRange))){
            return ResEntity.entity(false,"患者就诊范围为空,请重新传参",tPatients);
        }*/
        if (StringUtils.isBlank(tPatients.getPatientName())) {
            return ResEntity.entity(false, "患者姓名为空,请重新传参", tPatients);
        }
        if (StringUtils.isBlank(tPatients.getPatientGender())) {
            return ResEntity.entity(false, "患者性别为空,请重新传参", tPatients);
        }
        if (StringUtil.isEmpty(tPatients.getPatientBirthdayStr())) {
            return ResEntity.entity(false, "患者出生日期为空,请重新传参", tPatients);
        } else {
            try {
                tPatients.setPatientBirthday(DateUtil.getDateFormatd(tPatients.getPatientBirthdayStr(), DateUtil.YYYY_MM_DD));
            } catch (Exception e) {
                return ResEntity.entity(false, "患者出生日期格式不正确,请重新传参", tPatients);
            }
        }
        String patientCertificate = tPatients.getPatientCertificate();
        if (StringUtils.isBlank(patientCertificate)) {
            return ResEntity.entity(false, "患者证件号码为空,请重新传参", tPatients);
        }
        AdminInfo currentHr = AdminUtils.getCurrentHr();
        String appid = AdminUtils.getCurrentAppId();
        String insCode = AdminUtils.getCurrentInsCode();
        // 查询参数,判断是否存在
        TPatients param = new TPatients();
        param.setAppId(tPatients.getAppId());
        param.setPatientCertificate(patientCertificate);
        String message = "当前医联体中的该患者证件号码已经建档过了,不可重复建档";
        if (Constant.BASIC_STRING_TWO.equals(patientVisitRange)) {
            //医疗机构内不需要重复建档
            param.setInsCode(tPatients.getInsCode());
            message = "当前医疗机构中的该患者证件号码已经建档过了,不可重复建档";
        }
        TPatients patientsByPatients = tPatientsMapper.checkPatientsByPatients(param);
        if (null != patientsByPatients) {
            return ResEntity.entity(false, message, tPatients);
        }
        String patientId = IDUtil.getID();
        String addressId = IDUtil.getID();
        Date createDate = new Date();
        String createUser = currentHr.getId();
        String createUsername = currentHr.getNameZh();

        // 插入患者信息表的数据
        tPatients.setPatientId(patientId);
        tPatients.setCreateDate(createDate);
        tPatients.setCreateUser(createUser);
        tPatients.setCreateUsername(createUsername);
        tPatients.setDcAddressId(addressId);
        tPatients.setIsDel(Constant.BASIC_DEL_NO);
        tPatients.setAppId(appid);
        tPatients.setInsCode(insCode);
        // 要在收货地址表里默认插入一条数据
        TDcAddress tDcAddress = new TDcAddress(addressId, tPatients, currentHr);
        tPatients.setPatientPy(Dist.getFirstPinYins(tPatients.getPatientName()));
        tPatients.setPatientWb(Dist.getFives(tPatients.getPatientName()));
        long row = tPatientsMapper.insert(tPatients);
        if (row != 1) {
            return ResEntity.entity(false, "插入数据库失败,建档失败!", tPatients);
        }
        long row2 = tDcAddressMapper.insert(tDcAddress);
        if (row2 != 1) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return ResEntity.entity(false, "插入数据库失败,建档失败!", tPatients);
        }
        return ResEntity.entity(true, "建档成功!", tPatients);
    }

    /**
     * @return : com.jiuzhekan.cbkj.beans.sysBeans.ResEntity
     * @Description : 哪吒 - 体质辨识 - 新建患者
     * <AUTHOR> czh
     */
    @Transactional
    public ResEntity patientsCreate(TPatients tPatients) {
        // 插入患者信息表的数据
        tPatients.setPatientId(IDUtil.getID());
        tPatients.setCreateDate(new Date());
        tPatients.setCreateUser("admin");
        tPatients.setCreateUsername("哪吒");
        tPatients.setDcAddressId(IDUtil.getID());
        tPatients.setIsDel(Constant.BASIC_DEL_NO);
        tPatients.setAppId("");
        tPatients.setInsCode("");

        long row = tPatientsMapper.insert(tPatients);
        if (row != 1) {
            return ResEntity.entity(false, "插入数据库失败,建档失败!", tPatients);
        }
        return ResEntity.entity(true, "建档成功!", tPatients);
    }

    /**
     * 修改
     *
     * @param pat
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity update(TPatients pat) {
        if (StringUtils.isBlank(pat.getPatientId())) {
            return new ResEntity(false, "患者id不能为空", pat);
        }
        if (StringUtils.isBlank(pat.getPatientName())) {
            return ResEntity.entity(false, "患者姓名为空,请重新传参", pat);
        }
        if (StringUtils.isBlank(pat.getPatientGender())) {
            return ResEntity.entity(false, "患者性别为空,请重新传参", pat);
        }
        if (StringUtil.isEmpty(pat.getPatientBirthdayStr())) {
            return ResEntity.entity(false, "患者出生日期为空,请重新传参", pat);
        } else {
            try {
                pat.setPatientBirthday(DateUtil.getDateFormatd(pat.getPatientBirthdayStr(), DateUtil.YYYY_MM_DD));
            } catch (Exception e) {
                return ResEntity.entity(false, "患者出生日期格式不正确,请重新传参", pat);
            }
        }
        String patientCertificate = pat.getPatientCertificate();
        if (StringUtils.isBlank(patientCertificate)) {
            return ResEntity.entity(false, "患者证件号码为空,请重新传参", pat);
        }
        //获取到当前患者信息
        TPatients p = tPatientsMapper.getObjectById(pat.getPatientId());
        if (p == null) {
            return new ResEntity(false, "当前患者不存在", pat);
        }
        String patientVisitRange = tSysParamService.getSysParam(Constant.PATIENT_VISIT_RANGE).getParValues();
        // 查询参数,判断是否存在
        TPatients param = new TPatients();
        param.setPatientId(pat.getPatientId());
        param.setAppId(pat.getAppId());
        param.setPatientCertificate(patientCertificate);
        String message = "当前医联体中的该患者证件号码已经建档过了,不可重复建档";
        if (Constant.BASIC_STRING_TWO.equals(patientVisitRange)) {
            //医疗机构内不需要重复建档
            param.setInsCode(pat.getInsCode());
            message = "当前医疗机构中的该患者证件号码已经建档过了,不可重复建档";
        }
        TPatients patientsByPatients = tPatientsMapper.checkPatientsByPatients(param);
        if (null != patientsByPatients) {
            return ResEntity.entity(false, message, pat);
        }
        AdminInfo currentHr = AdminUtils.getCurrentHr();
        pat.setUpdateDate(new Date());
        pat.setUpdateUser(currentHr.getId());
        pat.setUpdateUsername(currentHr.getNameZh());

        //判断地址有没有修改,如果有修改，则增加一条新的收获地址
        if ((StringUtil.isNotEmpty(pat.getPatientAddress()) && !pat.getPatientAddress().equals(p.getPatientAddress())) || (StringUtil.isNotEmpty(pat.getPatientStreet()) && !pat.getPatientStreet().equals(p.getPatientStreet()))) {
            String addressId = IDUtil.getID();
            TDcAddress tDcAddress = new TDcAddress(addressId, pat, currentHr);
            long row = tDcAddressMapper.insert(tDcAddress);
            if (row != 1) {
                return ResEntity.entity(false, "插入数据库失败,建档失败!", pat);
            }
            pat.setDcAddressId(addressId);
        }
        long rows = tPatientsMapper.updateByPrimaryKey(pat);
        if (rows > 0) {
            return ResEntity.entity(true, Constant.SUCCESS_DX, null);
        }
        return new ResEntity(false, "修改失败，数据库异常", null);
    }

    /**
     * 加载某条数据
     *
     * @param patientId
     * @return
     */
    public TPatients findById(String patientId) {
        return tPatientsMapper.getObjectById(patientId);
    }

    public ResEntity findObj(String patientId) {

        if (StringUtils.isBlank(patientId)) {
            return new ResEntity(false, "参数不能为空哦", null);
        }
        TPatients tPatients = tPatientsMapper.getObjectById(patientId);
        TPatientAge patientAge = TPatientsService.getAgeByBirth(tPatients.getPatientBirthday());
        tPatients.setAge(patientAge.getAge());
        return new ResEntity(true, Constant.SUCCESS_DX, tPatients);
    }

    /**
     * 删除
     *
     * @param ids
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity deleteLis(String ids) {
        if (StringUtils.isBlank(ids)) {
            return new ResEntity(false, "参数错误(缺少参数)！", null);
        }
        long rowsR = tPatientsMapper.deleteBylist(ids.split(Constant.ENGLISH_COMMA));

        return new ResEntity(true, Constant.SUCCESS_DX, rowsR);
    }

    /**
     * @param
     * @return
     * @throws
     * @title 根据出生日期计算年龄
     * @description 按患者表中的出生日期 计算 病历主表中的年龄，用实岁的算法，当前日期-出生
     * 日期，不足一天的显示几小时，不足一月的显示几天，不足一周岁的显示几月几天，不
     * 足7周岁的显 示几岁几月，其他显示几岁。例1：3月7天，病历表中的年龄1存3，年龄
     * 单位1存月，年龄2存7，年龄单位2存天。例2：3岁，病历表中的年龄1存3，年龄单位1存岁。
     * <AUTHOR>
     * @updateTime 2019/8/6 10:54
     */
    public static TPatientAge getAgeByBirth(Date birthDay) {

        if (birthDay == null) {
            return new TPatientAge();
        }

        TPatientAge patientAge = new TPatientAge();
        Calendar cal = Calendar.getInstance();
//        if (cal.before(birthDay)) { //出生日期晚于当前时间，无法计算
//            throw new IllegalArgumentException(
//                    "The birthDay is before Now.It's unbelievable!");
//        }
        //当前年份
        int yearNow = cal.get(Calendar.YEAR);
        //当前月份
        int monthNow = cal.get(Calendar.MONTH);
        //当前日期
        int dayOfMonthNow = cal.get(Calendar.DAY_OF_MONTH);
        cal.setTime(birthDay);
        //传入年份
        int yearBirth = cal.get(Calendar.YEAR);
        //传入月份
        int monthBirth = cal.get(Calendar.MONTH);
        int dayOfMonthBirth = cal.get(Calendar.DAY_OF_MONTH);
        //整岁数
        int age1 = yearNow - yearBirth;
        //整月数,现在减去生日月份
        int mon1 = monthNow - monthBirth;
        //天数
        int day1 = dayOfMonthNow - dayOfMonthBirth;
        if (age1 >= 3) {
            if (mon1 > 0) {
                //过生日了
                patientAge.setAge1(Short.parseShort(age1 + ""));  //岁数
            } else if (mon1 == 0) {
                // 在生日这个月，还要判断是否过了这一天
                if (day1 <= 0) {
                    //没过生日
                    patientAge.setAge1(Short.parseShort((age1 - 1) + ""));  //岁数
                } else {
                    //过了
                    patientAge.setAge1(Short.parseShort(age1 + ""));  //岁数
                }
            } else {
                //没过生日
                patientAge.setAge1(Short.parseShort((age1 - 1) + ""));  //岁数
            }
            patientAge.setAgeUnit1("岁");
            // 0-3岁
        } else if (age1 > 0) {
            //如果月数小于0
            if (mon1 < 0) {
                if (age1 - 1 == 0) {
                    patientAge.setAge2(Short.parseShort((mon1 + 12) + ""));//月数在原来基础上+12
                    patientAge.setAgeUnit2("月");
                } else {
                    patientAge.setAge1(Short.parseShort((age1 - 1) + ""));  //岁数在原来基础上-1
                    patientAge.setAgeUnit1("岁");
                    patientAge.setAge2(Short.parseShort((mon1 + 12) + ""));//月数在原来基础上+12
                    patientAge.setAgeUnit2("月");
                }
            } else {
                patientAge.setAge1(Short.parseShort(age1 + ""));
                patientAge.setAgeUnit1("岁");
                patientAge.setAge2(Short.parseShort(mon1 + ""));
                patientAge.setAgeUnit2("月");
            }
            //不足一岁的
        } else {
            //判断月份是否大于0
            if (mon1 > 0) {
                //不满一个月的
                if (day1 < 0) {
                    if (mon1 - 1 == 0) {
                        patientAge.setAge2(Short.parseShort((day1 + 30) + ""));
                        patientAge.setAgeUnit2("天");
                    } else {
                        patientAge.setAge1(Short.parseShort((mon1 - 1) + ""));
                        patientAge.setAgeUnit1("月");
                    }
                } else {
                    patientAge.setAge1(Short.parseShort(mon1 + ""));
                    patientAge.setAgeUnit1("月");
                }
            } else {
                if (day1 > 0) {
                    patientAge.setAge1(Short.parseShort(day1 + ""));
                    patientAge.setAgeUnit1("天");
                }
            }
        }
        return patientAge;
    }

    /**
     *
     * @param birthDay
     * @return 返回出生到现在月数，不到生日不算这个月。
     */
    public static TPatientAge getAgeMonthByBirth(Date birthDay) {
        if (birthDay == null) {
            return new TPatientAge();
        }
//        // 指定东八区时区
//        ZoneId zone = ZoneId.of("Asia/Shanghai");
//        // 设置时区为东八区（Asia/Shanghai）
//        TimeZone timeZone = TimeZone.getTimeZone("Asia/Shanghai");
//        Calendar cal = Calendar.getInstance(timeZone);
//        //当前年份
//        int yearNow = cal.get(Calendar.YEAR);
//        //当前月份
//        int monthNow = cal.get(Calendar.MONTH);
//        //当前日期
//        int dayOfMonthNow = cal.get(Calendar.DAY_OF_MONTH);
//        // 使用东八区的当前时间
//        ZonedDateTime birthDate = ZonedDateTime.of(yearNow, monthNow, dayOfMonthNow, 0, 0, 0, 0, zone);
//        ZonedDateTime currentDate = ZonedDateTime.now(zone);
//        // 计算月数差
//        long totalMonths = ChronoUnit.MONTHS.between(birthDate, currentDate);


        LocalDate birth = birthDay.toInstant()
                .atZone(ZoneId.of("Asia/Shanghai"))
                .toLocalDate();
        LocalDate today = LocalDate.now(ZoneId.of("Asia/Shanghai"));  // 当前东八区日期
        Period period = Period.between(birth, today);
        int months = period.getYears() * 12 + period.getMonths();

        if (today.getDayOfMonth() < birth.getDayOfMonth()) {
            months--;
        }
        TPatientAge patientAge = new TPatientAge();
        patientAge.setAge2(Short.parseShort(months + ""));
        patientAge.setAgeUnit2("月");
        return patientAge;
    }

    /**
     * 获取全部地址
     *
     * @param patients
     * @param record
     * <AUTHOR>
     * @date 2020/3/10
     */
    public static void getAddressByPatients(TPatients patients, TRecord record) {
        StringBuilder address = new StringBuilder();
        if (StringUtils.isNotBlank(patients.getPatientCounty())) {
            address.append(patients.getPatientCounty());
        }
        if (StringUtils.isNotBlank(patients.getPatientTown())) {
            address.append(patients.getPatientTown());
        }
        if (StringUtils.isNotBlank(patients.getPatientVillage())) {
            address.append(patients.getPatientVillage());
        }
        if (StringUtils.isNotBlank(patients.getPatientStreet())) {
            address.append(patients.getPatientStreet());
        }
        if (StringUtils.isNotBlank(patients.getPatientAddress())) {
            address.append(patients.getPatientAddress());
        }
        record.setRecAddress(address.toString());
    }

    /**
     * @param tPatients :
     * @param page      :
     * @return : java.lang.Object
     * @Description :
     * <AUTHOR> 徐亨期
     * @updateTime : 2019/12/23 13:34
     */
    public Object getTodayPatients(TPatientsReqVO tPatients, Page page) {
        String curAppId = AdminUtils.getCurrentAppId();
        String curInsCode = AdminUtils.getCurrentInsCode();
        if (Constant.BASIC_APP_ID.equals(curAppId)) {
            curAppId = "";
        }
        if (Constant.BASIC_INS_CODE.equals(curInsCode)) {
            curInsCode = "";
        }


        // 患者就诊范围  1.医联体 2.医疗机构
        TSysParam param = tSysParamService.getSysParam(Constant.PATIENT_VISIT_RANGE);

        if (param != null && "1".equals(param.getParValues())) {
            if (StringUtils.isBlank(tPatients.getAppId())) {
                tPatients.setAppId(curAppId);
            }
        } else {
            if (StringUtils.isBlank(tPatients.getAppId())) {
                tPatients.setAppId(curAppId);
            }
            if (StringUtils.isBlank(tPatients.getInsCode())) {
                tPatients.setInsCode(curInsCode);
            }
        }

        tPatients.initOrderParams();

        PageHelper.startPage(page.getPage(), page.getLimit());
        List<TPatients> list = tPatientsMapper.getTodayPatients(tPatients);

        for (TPatients patients : list) {
            TPatientAge patientAge = getAgeByBirth(patients.getPatientBirthday());
            patients.setAge(patientAge.getAge());
        }
        Object result = Page.getLayUiTablePageData(list);
        return result;
    }

    /**
     * @param tPatients :
     * @param page      :
     * @return : java.lang.Object
     * @Description : 分页查询当前医生的患者信息,关联了病历表.
     * <AUTHOR> 徐亨期
     * @updateTime : 2019/12/24 20:14
     */
    public Object getMyPatients(TPatientsReqVO tPatients, Page page) {
        // 根据医生id查出患者信息
        AdminInfo currentHr = AdminUtils.getCurrentHr();
        if (!Constant.BASIC_APP_ID.equals(AdminUtils.getCurrentAppId())) {
            tPatients.setAppId(AdminUtils.getCurrentAppId());
        }

        tPatients.setDocId(currentHr.getId());

        tPatients.initOrderParams();

        PageHelper.startPage(page.getPage(), page.getLimit());
        List<TPatients> list = tPatientsMapper.getMyPatients(tPatients);

        for (TPatients patients : list) {
            TPatientAge patientAge = TPatientsService.getAgeByBirth(patients.getPatientBirthday());
            patients.setAge(patientAge.getAge());
        }

        Object result = Page.getLayUiTablePageData(list);
        return result;
    }

    /**
     * 患者默认收货地址
     *
     * @param patientId
     * @return
     */
    public ResEntity getPatientAddress(String patientId, String registerId) {

        if (StringUtils.isBlank(patientId)) {
            return new ResEntity(false, "参数不能为空哦", null);
        }
        TRegister register = clientRedisService.getRegisterById(registerId);
        TDcAddress address = tDcAddressMapper.getLastAddressByPatientId(patientId);
        if (address == null || StringUtils.isBlank(address.getDcCounty()) || StringUtils.isBlank(address.getDcTown()) || StringUtils.isBlank(address.getDcVillage())) {
            // 未找到收货地址
            TDcAddress addressPre = tDcAddressMapper.getLastAddressByPatientPre(patientId);
            if (addressPre == null && address != null) {
                //有可能没开方过，用接口插入的地址

            } else {
                address = addressPre;
            }
        }
        SysInstitution ins = orgRedisService.getSysInstitution(AdminUtils.getCurrentAppId(), AdminUtils.getCurrentInsCode());

        TDcAddress insAddr = new TDcAddress(ins);

        if (address == null) {
            address = new TDcAddress();
            TPatients patients = tPatientsMapper.getObjectById(patientId);

            if (StringUtils.isNotBlank(patients.getPatientCounty())
                    && StringUtils.isNotBlank(patients.getPatientTown())
                    && StringUtils.isNotBlank(patients.getPatientVillage())
                    && StringUtils.isNotBlank(patients.getPatientStreet())
                    && StringUtils.isNotBlank(patients.getPatientAddress())
                    && StringUtils.isNotBlank(patients.getPatientStreetCode())) {

                address.setDcCounty(patients.getPatientCounty());
                address.setDcCountyCode(patients.getPatientStreetCode().substring(0, 2) + "0000");
                address.setDcTown(patients.getPatientTown());
                address.setDcTownCode(patients.getPatientStreetCode().substring(0, 4) + "00");
                address.setDcVillage(patients.getPatientVillage());
                address.setDcVillageCode(patients.getPatientStreetCode().substring(0, 6));
                address.setDcStreet(patients.getPatientStreet());
                address.setDcStreetCode(patients.getPatientStreetCode());
                address.setDcAddress(patients.getPatientAddress());

            } else {
                address.setDcCounty(ins.getProvinceName());
                address.setDcCountyCode(ins.getProvinceCode());
                address.setDcTown(ins.getCityName());
                address.setDcTownCode(ins.getCityCode());
                address.setDcVillage(ins.getAreaName());
                address.setDcVillageCode(ins.getAreaCode());
                address.setDcStreet(ins.getStreetName());
                address.setDcStreetCode(ins.getStreetCode());
//            address.setDcAddress(ins.getDcAddress());
            }
        }

        Map<String, Object> result = new HashMap<>();

        //是否使用隔离点的地址信息
        TSysParam sysParam = tSysParamService.getSysParam(Constant.ISOLATED_POINTS_ADDRESS);
        if (Constant.BASIC_STRING_ONE.equals(sysParam.getParValues())) {

            Map<String, TDcAddress> isolatedAddress = getIsolatedAddress();
            if (isolatedAddress.get(register.getDeptId()) != null) {
                insAddr.setDcAddress(isolatedAddress.get(register.getDeptId()).getDcAddress());
                insAddr.setDcStreet(isolatedAddress.get(register.getDeptId()).getDcStreet());
                insAddr.setDcStreetCode(isolatedAddress.get(register.getDeptId()).getDcStreetCode());
            }
        }

        //患者最新地址
        result.put("patient", address);
        //当前医疗机构地址
        result.put("ins", insAddr);
        return new ResEntity(true, Constant.SUCCESS_DX, result);
    }


    public SysInstitution getSysInstitution() {
        Map<String, Object> params = new HashMap<>();
        params.put("appId", AdminUtils.getCurrentAppId());
        params.put("insCode", AdminUtils.getCurrentInsCode());
        //从综合平台获取医疗机构
        ResEntity resEntity = platformRestTemplate.post("ins/detail", params);
        if (resEntity.getStatus() && resEntity.getData() != null) {
            return JSONObject.parseObject(JSONObject.toJSONString(resEntity.getData()), SysInstitution.class);
        }
        return new SysInstitution();
    }


    /**
     * 获取挂号记录
     *
     * @param registerId registerId
     * @return ResEntity
     */
    public ResEntity getRegisterById(String registerId) {
        if (StringUtils.isBlank(registerId)) {
            return new ResEntity(false, "参数不能为空哦", null);
        }
        TRegister tRegister = clientRedisService.getRegisterById(registerId);
        return new ResEntity(true, Constant.SUCCESS_DX, tRegister);
    }

    /**
     * 隔离点的地址信息
     */
    public Map<String, TDcAddress> getIsolatedAddress() {
        Map<String, TDcAddress> address = new HashMap<>();
        address.put("gld001", new TDcAddress("常熟市沙家浜芦苇荡路3号", "沙家浜镇", "320581105"));
        address.put("gld002", new TDcAddress("常熟市环湖北路60号", "尚湖镇", "320581111"));
        address.put("gld003", new TDcAddress("东张育才路20号", "碧溪街道", "320581405"));
        address.put("gld004", new TDcAddress("江苏省常熟市莫城大道645号", "虞山镇", "320581100"));
        address.put("gld005", new TDcAddress("江苏省常熟市黄山路188号", "虞山镇", "320581100"));
        address.put("gld006", new TDcAddress("江苏省常熟市白茆芙蓉路22号", "古里镇", "320581104"));
        address.put("gld007", new TDcAddress("常熟市尚湖镇冶塘人民南路1号", "尚湖镇", "320581111"));
        address.put("gld008", new TDcAddress("常熟市美乐迪音乐酒店", "辛庄镇", "320581110"));

        address.put("gld009", new TDcAddress("长江路606号", "虞山镇", "320581100"));
        address.put("gld010", new TDcAddress("常熟市梅李镇恒丰街7一1号", "梅李镇", "320581101"));
        address.put("gld011", new TDcAddress("常熟市支塘镇谢圩新村三区35一5", "支塘镇", "320581106"));
        address.put("gld012", new TDcAddress("海虞镇学前路28号", "海虞镇", "320581102"));
        address.put("gld013", new TDcAddress("碧溪东路22号", "海虞镇", "320581102"));
        address.put("gld014", new TDcAddress("辛庄镇光华环路48号", "辛庄镇", "320581110"));
        return address;
    }

    /**
     * 云系统唯一患者识别逻辑
     *
     * @param patients
     */
    public String getPatientId(TPatients patients) {
        TPatients tp = tPatientsMapper.checkPatientsByPatients(patients);
        if (tp != null) {
            return tp.getPatientId();
        }
        return null;
    }

    /**
     * 提取患者信息
     *
     * @param map
     */
    public TPatients toTPatients(Map<String, Object> map) {
        String patientCertificate = map.getOrDefault(PATIENT_CERTIFICATE, "").toString();
        TPatients patients = new TPatients();
        //0 防止创建错误，预设数据
        Date now = new Date();
        patients.setPatientBirthday(now);
        patients.setPatientBirthdayStr(getBirthday("yyyy-MM-dd", now));
        patients.setPatientGender("F");

        //1 证件号码存在
        if (StringUtils.isNotBlank(patientCertificate)) {
            patients.setPatientCertificate(patientCertificate);
            Date birthday = getBirthday(patientCertificate, "yyyyMMdd");
            patients.setPatientBirthday(birthday);
            patients.setPatientBirthdayStr(getBirthday("yyyy-MM-dd", birthday));
            patients.setPatientGender(getGender(patientCertificate));
        }
        //2 姓名存在
        String patientName = map.getOrDefault(PATIENT_NAME, "").toString();
        String patientMobile = map.getOrDefault(PATIENT_MOBILE, "").toString();
        if (StringUtils.isNotBlank(patientName) || StringUtils.isNotBlank(patientMobile)) {
            patients.setPatientName(patientName);
            patients.setPatientMobile(patientMobile);
        }
        return patients;
    }


    public String getGender(String idCard) {
        //第17位数字是奇数性别为男，否则为女
        return idCard.charAt(16) % 2 == 0 ? "F" : "M";

    }

    public Date getBirthday(String idCard, String format) {
        return DateUtil.getDateFormatd(idCard.substring(6, 14), format);
    }

    public String getBirthday(String format, Date date) {
        return DateUtil.getDateFormats(format, date);
    }

    /**
     * 保存患者信息
     *
     * @param info
     * @return
     */
    public String saveSickPersonInfo(TPatients info) {
        if (info == null || (StringUtils.isEmpty(info.getPatientId()) && StringUtils.isEmpty(info.getInsCode()))) {
            return null;
        }
        //根据 患者id 和 第三方Id 来判断唯一性
        TPatients sickPerson = tPatientsMapper.findByInsCodeAndPatientId(info.getPatientId(), info.getInsCode());
        if (sickPerson != null) {
            //更新患者信息
            TPatients sickPersonInfo = SickPersonInfoConvertor.convertorToInsert(info, sickPerson);
            tPatientsMapper.insertPatients(sickPersonInfo);
            return sickPersonInfo.getPatientId();
        } else {
            tPatientsMapper.insertPatients(info);
            return info.getPatientId();
        }
    }

    /**
     * 获取 患者信息
     *
     * @param patientId
     * @param insCode
     * @return
     */
    public TPatients findByInsCodeAndPatientId(String patientId, String insCode) {
        TPatients sickPerson = tPatientsMapper.findByInsCodeAndPatientId(patientId, insCode);
        return sickPerson;
    }
}