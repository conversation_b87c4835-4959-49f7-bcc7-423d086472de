package com.jiuzhekan.appraisal.utils;

import java.util.LinkedHashSet;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/18 14:19
 * @Version 1.0
 */
public class FormulaVariableExtractor {

    public static Set<String> extractVariables(String formula) {
        // 假设变量名是以字母开头，后接字母或数字，比如 T1003
        Pattern pattern = Pattern.compile("\\b[A-Za-z][A-Za-z0-9]*\\b");
        Matcher matcher = pattern.matcher(formula);

        // 使用 LinkedHashSet 保持顺序 & 去重
        Set<String> variables = new LinkedHashSet<>();

        while (matcher.find()) {
            String token = matcher.group();
            // 可以根据需要过滤掉数字等关键词（例如100%中的100）
            if (!isNumeric(token)) {
                variables.add(token);
            }
        }

        return variables;
    }

    private static boolean isNumeric(String str) {
        return str.matches("\\d+");
    }

    public static void main(String[] args) {
        String formula = "T1003/(T1002-T1004)*100% + A1 - B2";
        Set<String> vars = extractVariables(formula);
        System.out.println("变量名: " + vars);
    }
}
