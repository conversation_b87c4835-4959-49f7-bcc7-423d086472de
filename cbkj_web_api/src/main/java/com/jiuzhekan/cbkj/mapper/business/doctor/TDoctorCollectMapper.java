package com.jiuzhekan.cbkj.mapper.business.doctor;

import com.jiuzhekan.cbkj.beans.business.doctor.TDoctorCollect;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Component
public interface TDoctorCollectMapper extends BaseMapper<TDoctorCollect> {


    TDoctorCollect insertTDoctor(TDoctorCollect tDoctorCollect);

    long deleteByObj(TDoctorCollect tDoctorCollect);

    TDoctorCollect getCollectByObj(TDoctorCollect tDoctorCollect);

    /**
     * 筛选某用户收藏的关联ID
     * @param tDoctorCollect  createUser connectId 必填
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @date 2020/7/1
     */
    List<String> getExistCollectByConnectIds(TDoctorCollect tDoctorCollect);

    /**
     * 筛选某用户收藏的临床诊疗关联ID
     * @param tDoctorCollect
     * @return
     */
    List<String> getGuideExistCollectByConnectIds(TDoctorCollect tDoctorCollect);


    List<Map<String, String>> getListJoinTKnowledgeShare(HashMap<String ,String> tDoctorCollect);
}