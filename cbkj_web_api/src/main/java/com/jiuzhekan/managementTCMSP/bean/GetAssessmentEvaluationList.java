package com.jiuzhekan.managementTCMSP.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/8/19 09:41
 * @Version 1.0
 */
@Data
@ApiModel
public class GetAssessmentEvaluationList {
    @ApiModelProperty(value = "工号或者姓名")
    private String userName;
    @ApiModelProperty(value = "1职场/人才测评2.教育测评3.心理测评4.个人成长测评")
    private Integer assessmentEvaluationClass;

    @ApiModelProperty(value = "测评名称")
    private String assessmentEvaluationName;
}
