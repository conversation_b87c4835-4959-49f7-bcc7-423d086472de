package com.jiuzhekan.cbkj.mapper.business.doctor;

import com.jiuzhekan.cbkj.beans.business.doctor.TDoctorBook;
import org.springframework.stereotype.Component;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;

import java.util.List;

@Component
public interface TDoctorBookMapper extends BaseMapper<TDoctorBook> {

    /**
     * 首页最近阅读
     *
     * @param book book
     * @return java.util.List<com.jiuzhekan.cbkj.beans.business.doctor.TDoctorBook>
     * <AUTHOR>
     * @date 2021/11/1
     */
    TDoctorBook recentReading(TDoctorBook book);

    /**
     * 阅读记录
     *
     * @param book book
     * @return java.util.List<com.jiuzhekan.cbkj.beans.business.doctor.TDoctorBook>
     * <AUTHOR>
     * @date 2021/11/1
     */
    List<TDoctorBook> readRecord(TDoctorBook book);

    /**
     * 阅读记录
     *
     * @param book book
     * @return java.util.List<com.jiuzhekan.cbkj.beans.business.doctor.TDoctorBook>
     * <AUTHOR>
     * @date 2021/11/1
     */
    List<TDoctorBook> shelf(TDoctorBook book);

    /**
     * 医生阅读某书籍的记录
     *
     * @param tDoctorBook doctorId + bookId
     * @return com.jiuzhekan.cbkj.beans.business.doctor.TDoctorBook
     * <AUTHOR>
     * @date 2021/10/28
     */
    TDoctorBook readDetail(TDoctorBook tDoctorBook);

    /**
     * 书籍的阅读人数
     *
     * @param bookId bookId
     * @return java.lang.Integer
     * <AUTHOR>
     * @date 2021/10/28
     */
    Integer readBookNum(String bookId);
}