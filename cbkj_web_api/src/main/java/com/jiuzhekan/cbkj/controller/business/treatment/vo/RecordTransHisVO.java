package com.jiuzhekan.cbkj.controller.business.treatment.vo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2020年01月07日 10:22:00
 */

@Data
public class RecordTransHisVO {
    private String app_id;//由智能开方系统分配给HIS系统接入的唯一标识，固定传“100003”
    private String ins_code;//医疗机构代码，HIS系统各医疗机构的唯一识别码
    private String timestamp;//时间戳，格式为yyyy-MM-dd HH:mm:ss如：2016-09-26 10:30:25
    private String doctor_id;//当前登录医生ID，医生在HIS唯一识别码
    private String doctor_name;//医生姓名，值经过Base64加密的数据，以避免中文乱码
    private String doctor_gender;//医生性别（M为男，F为女）
    private String employee_id;//医生工号
    private String dept;//所属科室/病区名称，值经过Base64加密的数据，以避免中文乱码
    private String ch;//床位号
    private String brlb;//病人类别，自费、医保等费用类别，值经过Base64加密的数据，以避免中文乱码
    private String zyzz;//医生资质（1为中医资质，0为其他资质）
    private String user_id;//当前就诊的用户ID，患者在HIS唯一识别码
    private String user_name;//患者姓名，值经过Base64加密的数据，以避免中文乱码
    private String user_gender;//患者性别（M为男，F为女）
    private String user_age;//患者年龄
    private String user_address;//患者地址（注意避免“#”出现），值经过Base64加密的数据，以避免中文乱码
    private String user_mobile;//患者手机号码
    private String medical_card_no;//患者就诊卡号/医保卡号
    private String certificate;//患者身份证号
    private String gravidity;//患者是否怀孕（Y为是，N为否）

    private String hospitalNo; //住院号
    private String visitNo;// HIS诊序号

    private String diseaseId; //疾病id
    private String symptomId;// 证型id

    private String diseaseName;//疾病名称，值经过Base64加密的数据，以避免中文乱码
    private String symptomName;//证型名称，值经过Base64加密的数据，以避免中文乱码
    private String therapyName;//治法，值经过Base64加密的数据，以避免中文乱码
    private String chineseDiagnosis;//中医诊断结果，值经过Base64加密的数据，以避免中文乱码
    private String westDiagnosis;//西医诊断，值经过Base64加密的数据，以避免中文乱码
    private String skinHair;//皮肤毛发，值经过Base64加密的数据，以避免中文乱码
    private String soundSmell;//生息气味，值经过Base64加密的数据，以避免中文乱码
    private String lookmorphology;//神色形态，值经过Base64加密的数据，以避免中文乱码
    private String tongue;//舌像，值经过Base64加密的数据，以避免中文乱码
    private String pulse;//脉象，值经过Base64加密的数据，以避免中文乱码
    private String chiefComplaint;//主诉，值经过Base64加密的数据，以避免中文乱码
    private String nowDesc;//现病史，值经过Base64加密的数据，以避免中文乱码
    private String auxiliaryExam;//辅助检查，值经过Base64加密的数据，以避免中文乱码
    private String physical;//体格检查，值经过Base64加密的数据，以避免中文乱码

    private String insTreId;//处方ID，获取唯一处方
    private String preNumber;//草药处方贴数
    private String preUsage;//草药处方服法
    private String preType;//剂型，1饮片、2颗粒、3膏方、5配方
    private String packType;//包装类型，分两类：大包装、小包装，对应值分别为B、S
    private String recTreType;//病例类型，分两类：门诊、住院，对应值分别为1、2
    private String sfdj;//是否代煎（1代煎，0自煎）
    private String sfgf;//是否膏方（1是，0否）
    private String dcType;//1配送，0自提
    private String province;//配送时必填   省
    private String city;//配送时必填    市
    private String area;//配送时必填   区
    private String userAddress;//配送时比填   详细地址
    private String userName;//配送时必填    收货人
    private String userMobile;//配送时必填    联系方式
    private String djf;//代煎费，默认0
    private String zgf;//制膏费，默认0
    private String singlePreMoney;//单剂金额
    private String yaofang;//药房ID，草药必须同一药房
    private List<RecordTransHisItemVO> preItemList;//草药处方明细列表，数组格式
}