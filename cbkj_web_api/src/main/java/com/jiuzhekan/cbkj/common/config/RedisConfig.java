
package com.jiuzhekan.cbkj.common.config;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.*;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;


/**
 * <AUTHOR>
 * @date 2019/2/1
 * Redis 配置类
 */
@EnableCaching
@Configuration
@AllArgsConstructor
public class RedisConfig {

    private final RedisConnectionFactory factory;


    @Bean
    public RedisTemplate<String, Object> redisTemplate() {
        Jackson2JsonRedisSerializer jackson2JsonRedisSerializer = new Jackson2JsonRedisSerializer(Object.class);

        //解决查询缓存转换异常的问题
        ObjectMapper om = new ObjectMapper();
        om.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        om.enableDefaultTyping(ObjectMapper.DefaultTyping.NON_FINAL);
        //解决反序列化时有返回值的方法无对应字段 UserDetails
        om.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        jackson2JsonRedisSerializer.setObjectMapper(om);

        RedisTemplate<String, Object> redisTemplate = new RedisTemplate<>();
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setHashKeySerializer(new StringRedisSerializer());
        redisTemplate.setValueSerializer(jackson2JsonRedisSerializer);
        redisTemplate.setHashValueSerializer(jackson2JsonRedisSerializer);
        redisTemplate.setConnectionFactory(factory);
        return redisTemplate;
    }


    @Bean
    public RedisCacheManager cacheManager(RedisConnectionFactory factory) {
        //设置默认缓存100秒
        RedisCacheConfiguration config = RedisCacheConfiguration.defaultCacheConfig().entryTtl(Duration.ofMinutes(10L)).disableCachingNullValues();

        Set<String> cacheNames = new HashSet<>(30);
        cacheNames.add("org::app");
        cacheNames.add("org::ins");
        cacheNames.add("org::dept");
        cacheNames.add("org::pha");
        cacheNames.add("parameter::address");
        cacheNames.add("parameter::menu");
        cacheNames.add("parameter::params");
        cacheNames.add("parameter::dic");
        cacheNames.add("parameter::display");
        cacheNames.add("client::user");
        cacheNames.add("client::token");
        cacheNames.add("client::register");
        cacheNames.add("client::personalPre");
        cacheNames.add("know::common");
        cacheNames.add("know::token");
        cacheNames.add("know::check");
        cacheNames.add("know::disease");
        cacheNames.add("know::scheme");
        cacheNames.add("know::master");
        cacheNames.add("know::inherit");
        cacheNames.add("know::askinfo");
        // 对每个缓存空间应用不同的配置
        Map<String, RedisCacheConfiguration> configMap = new HashMap<>(30);
        configMap.put("org::app", config.entryTtl(Duration.ofDays(100L)));
        configMap.put("org::ins", config.entryTtl(Duration.ofDays(100L)));
        configMap.put("org::dept", config.entryTtl(Duration.ofDays(100L)));
        configMap.put("org::pha", config.entryTtl(Duration.ofDays(100L)));
        configMap.put("parameter::address", config.entryTtl(Duration.ofDays(100L)));
        configMap.put("parameter::menu", config.entryTtl(Duration.ofDays(100L)));
        configMap.put("parameter::params", config.entryTtl(Duration.ofDays(100L)));
        configMap.put("parameter::dic", config.entryTtl(Duration.ofDays(100L)));
        configMap.put("parameter::display", config.entryTtl(Duration.ofDays(100L)));
        configMap.put("client::user", config.entryTtl(Duration.ofHours(24L)));
        configMap.put("client::token", config.entryTtl(Duration.ofMillis(14400000L)));
        configMap.put("client::register", config.entryTtl(Duration.ofHours(24L)));
        configMap.put("client::personalPre", config.entryTtl(Duration.ofDays(7L)));
        configMap.put("know::common", config.entryTtl(Duration.ofDays(7L)));
        configMap.put("know::token", config.entryTtl(Duration.ofDays(7L)));
        configMap.put("know::check", config.entryTtl(Duration.ofDays(100L)));
        configMap.put("know::disease", config.entryTtl(Duration.ofDays(100L)));
        configMap.put("know::scheme", config.entryTtl(Duration.ofDays(100L)));
        configMap.put("know::master", config.entryTtl(Duration.ofDays(100L)));
        configMap.put("know::inherit", config.entryTtl(Duration.ofDays(100L)));
        configMap.put("know::askinfo", config.entryTtl(Duration.ofDays(100L)));

        return RedisCacheManager.builder(factory)
                .cacheDefaults(config)
                // 注意这两句的调用顺序，一定要先调用该方法设置初始化的缓存名，再初始化相关的配置
                .initialCacheNames(cacheNames)
                .withInitialCacheConfigurations(configMap)
                .build();
    }


    @Bean
    public KeyGenerator cacheKeyGenerator() {
        return (target, method, params) -> {
            StringBuilder sb = new StringBuilder();
            sb.append(target.getClass().getName());
            sb.append(method.getName());
            for (Object obj : params) {
                // 由于参数可能不同, hashCode肯定不一样, 缓存的key也需要不一样
                sb.append(JSON.toJSONString(obj).hashCode());
            }
            return sb.toString();
        };
    }


    @Bean
    public HashOperations<String, String, Object> hashOperations(RedisTemplate<String, Object> redisTemplate) {
        return redisTemplate.opsForHash();
    }

    @Bean
    public ValueOperations<String, String> valueOperations(RedisTemplate<String, String> redisTemplate) {
        return redisTemplate.opsForValue();
    }

    @Bean
    public ListOperations<String, Object> listOperations(RedisTemplate<String, Object> redisTemplate) {
        return redisTemplate.opsForList();
    }

    @Bean
    public SetOperations<String, Object> setOperations(RedisTemplate<String, Object> redisTemplate) {
        return redisTemplate.opsForSet();
    }

    @Bean
    public ZSetOperations<String, Object> zSetOperations(RedisTemplate<String, Object> redisTemplate) {
        return redisTemplate.opsForZSet();
    }
}