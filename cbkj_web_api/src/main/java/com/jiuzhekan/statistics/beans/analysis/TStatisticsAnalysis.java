package com.jiuzhekan.statistics.beans.analysis;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class TStatisticsAnalysis implements Serializable{

    @ApiModelProperty(value = "自增主键")
    private Integer id;

    @ApiModelProperty(value = "医联体ID")
    private String appId;

    @ApiModelProperty(value = "医联体")
    private String appName;

    @ApiModelProperty(value = "医疗机构代码")
    private String insCode;

    @ApiModelProperty(value = "医疗机构")
    private String insName;

    @ApiModelProperty(value = "科室ID")
    private String deptId;

    @ApiModelProperty(value = "科室")
    private String deptName;

    @ApiModelProperty(value = "用户ID")
    private String userId;

    @ApiModelProperty(value = "用户")
    private String userName;

    @ApiModelProperty(value = "体质辨识报告数量")
    private Integer usageTimes;

    @ApiModelProperty(value = "统计日期")
    private Date createDate;

    @ApiModelProperty(value = "插入时间")
    private Date insertDate;


}
