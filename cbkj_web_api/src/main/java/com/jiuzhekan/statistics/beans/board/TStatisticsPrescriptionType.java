package com.jiuzhekan.statistics.beans.board;

import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.statistics.utils.DateUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class TStatisticsPrescriptionType implements Serializable{

    @ApiModelProperty(value = "")
    private Integer id;

    @ApiModelProperty(value = "")
    private String appId;

    @ApiModelProperty(value = "")
    private String appName;

    @ApiModelProperty(value = "")
    private String insCode;

    @ApiModelProperty(value = "")
    private String insName;

    @ApiModelProperty(value = "")
    private String deptId;

    @ApiModelProperty(value = "统计日期")
    private Date createDate;

    @ApiModelProperty(value = "插入时间")
    private Date insertDate;

    @ApiModelProperty(value = "")
    private Integer number;

    @ApiModelProperty(value = "1.煎煮中处方:【实时】处方状态为“85泡药、130煎药、135包装”的处方数量之和;" +
            "2.已签收处方:已签收处方【实时】处方状态为“150收货”的处方数量之和")
    private Integer numType;

    public TStatisticsPrescriptionType  initDate(TStatisticsPrescriptionType tStatisticsPrescriptionType){
        this.appId = AdminUtils.getCurrentAppId();
        this.insCode = AdminUtils.getCurrentInsCode();
        SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd");
        try {
            this.createDate = sdf2.parse(DateUtils.getWeekEndTim2e(0, true));
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        this.insertDate = new Date();
        this.number = 0;
        return this;
    }

}
