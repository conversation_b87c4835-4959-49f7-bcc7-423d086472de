package com.jiuzhekan.cbkj.controller.business.treatment.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel
public class KnowCheckPres {
    @ApiModelProperty(value = "处方内容，拼接规则：“中药名,药品ID,剂量,单位,用法;”，例：“当归,001,1,克,煎服;黄芪,002,4,克,煎服;党参,003,10,克,煎服;”，注意剂量区间检测以“克”为单位判断，如果是其他单位的请自行换算，除蜈蚣之类“条“单位的", required = true)
    private String pres;
    @ApiModelProperty(value = "中医病名ID")
    private String disId;
    @ApiModelProperty(value = "证型ID")
    private String symId;
    @ApiModelProperty(value = "是否怀孕：Y为怀孕，N为没有怀孕")
    private String gravidity;
}
