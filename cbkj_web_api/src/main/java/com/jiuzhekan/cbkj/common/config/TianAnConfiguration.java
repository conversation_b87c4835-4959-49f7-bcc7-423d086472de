package com.jiuzhekan.cbkj.common.config;

import com.jiuzhekan.cbkj.common.interceptor.tianan.TianAnDecryptIntercept;
import com.jiuzhekan.cbkj.common.interceptor.tianan.TianAnEncryptIntercept;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 天安加密配置
 */
@Configuration
@ConditionalOnProperty(name = "interceptor.tianan", havingValue = "true")
public class TianAnConfiguration {

    @Bean
    public TianAnDecryptIntercept tianAnDecryptIntercept() {
        return new TianAnDecryptIntercept();
    }

    @Bean
    public TianAnEncryptIntercept tianAnEncryptIntercept() {
        return new TianAnEncryptIntercept();
    }

}
