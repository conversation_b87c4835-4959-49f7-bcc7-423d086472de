package com.jiuzhekan.managementTCMSP.service;

import com.github.pagehelper.PageHelper;

import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.managementTCMSP.bean.*;
import com.jiuzhekan.cbkj.common.utils.*;
import com.jiuzhekan.managementTCMSP.beans.user.SysAdminInfoAssessmentEvaluation;
import com.jiuzhekan.managementTCMSP.beans.user.SysAdminInfoEdu;
import com.jiuzhekan.managementTCMSP.beans.user.SysAdminInfoExamine;
import com.jiuzhekan.managementTCMSP.mapper.AdminInfoMapper;
import com.jiuzhekan.managementTCMSP.mapper.user.*;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/8/13 14:04
 * @Version 1.0
 */
@Service
public class TCMSPAdminInfoService {

    private final AdminInfoMapper adminInfoMapper;
    private final SysAdminInfoexMapper sysAdminInfoexMapper;
    private final SysDoctorMultipointMapper sysDoctorMultipointMapper;

    private final SysAdminInfoEduMapper sysAdminInfoEduMapper;

    private final SysAdminInfoExamineMapper sysAdminInfoExamineMapper;

    private final SysAdminInfoAssessmentEvaluationMapper sysAdminInfoAssessmentEvaluationMapper;

    public TCMSPAdminInfoService(AdminInfoMapper adminInfoMapper, SysAdminInfoexMapper sysAdminInfoexMapper, SysDoctorMultipointMapper sysDoctorMultipointMapper, SysAdminInfoEduMapper sysAdminInfoEduMapper, SysAdminInfoExamineMapper sysAdminInfoExamineMapper, SysAdminInfoAssessmentEvaluationMapper sysAdminInfoAssessmentEvaluationMapper) {
        this.adminInfoMapper = adminInfoMapper;
        this.sysAdminInfoexMapper = sysAdminInfoexMapper;
        this.sysDoctorMultipointMapper = sysDoctorMultipointMapper;
        this.sysAdminInfoEduMapper = sysAdminInfoEduMapper;
        this.sysAdminInfoExamineMapper = sysAdminInfoExamineMapper;
        this.sysAdminInfoAssessmentEvaluationMapper = sysAdminInfoAssessmentEvaluationMapper;
    }

    public Object getPageDatas(TCMSPAdminInfoReq admin, Page page) {

        PageHelper.startPage(page.getPage(), page.getLimit());
        List<AppsInfo> lis = adminInfoMapper.getPageDatas(admin);
        Object result = Page.getLayUiTablePageData(lis);
        PageHelper.clearPage();
        return result;
    }

    /**
     * 保存或者编辑更新用户信息
     *
     * @param admin
     * @return
     */
    public ResEntity saveOrEdit(AppsInfo admin) {
        if (StringUtils.isBlank(admin.getUserId())) {

        } else {

        }
        return ResEntity.success();
    }

    public Object getAppList() {
        List<HashMap<String, String>> list = adminInfoMapper.getAppList();
        return ResEntity.success(list);
    }

    public Object getInsList(String appId) {
        List<HashMap<String, Object>> list = adminInfoMapper.getInsList(appId);
        //返回是所有的机构，但是需要重构层级，有上下级，需要用到树形结构，使用递归

        return ResEntity.success(list);
    }

    public Object getDeptList(String appId, String insCode) {
        List<HashMap<String, Object>> list = adminInfoMapper.getDeptList(appId, insCode);
        return ResEntity.success(list);
    }

    public Object getPersonDetail(String userId) {
        if (StringUtils.isBlank(userId)) {
            return ResEntity.error("参数不能为空");
        }
        AppsInfoDetails detail = adminInfoMapper.getPersonDetail(userId);
        if (null != detail) {

            List<SysDoctorMultipoint> sysDoctorMultipoints = adminInfoMapper.getPersonMultipointsByUserId(userId);
            detail.setSysDoctorMultipoints(sysDoctorMultipoints);

        }
        return ResEntity.success( detail);
    }

    /**
     * 保存人员信息
     *
     * @param appsInfoDetails
     * @return
     */
    public Object savePersonDetail(AppsInfoDetails appsInfoDetails) {
        ResEntity resEntity = checkParams(appsInfoDetails);
        if (!resEntity.getStatus()) {
            return resEntity;
        }
        appsInfoDetails.setUserId(IDUtil.getID());
        String encode = MD5Util.encode("123456");
        appsInfoDetails.setPassword(encode);
        appsInfoDetails.setIsQualifier("1");
        appsInfoDetails.setStatus("0");
        appsInfoDetails.setCreateDate(new Date());
        appsInfoDetails.setCreateUser(AdminUtils.getCurrentHr().getId());
        appsInfoDetails.setCreateUserName(AdminUtils.getCurrentHr().getNameZh());
        adminInfoMapper.insertPersonDetail(appsInfoDetails);

        List<SysDoctorMultipoint> sysDoctorMultipoints = appsInfoDetails.getSysDoctorMultipoints();
        sysDoctorMultipoints.forEach(sysDoctorMultipoint -> {
            sysDoctorMultipoint.setUserId(appsInfoDetails.getUserId());
            sysDoctorMultipoint.setCreateUser(AdminUtils.getCurrentHr().getId());
            sysDoctorMultipoint.setCreateUserName(AdminUtils.getCurrentHr().getNameZh());
            sysDoctorMultipoint.setCreateDate(new Date());
            sysDoctorMultipoint.setStatus("0");
            sysDoctorMultipoint.setIsQualifier(1);
            sysDoctorMultipoint.setPersonalShare("0");
            sysDoctorMultipoint.setUserName(appsInfoDetails.getUserName());
            sysDoctorMultipointMapper.insert(sysDoctorMultipoint);
        });
        //插入sys_admin_infoex
        //配置医生权限
        SysAdminInfoex sysAdminInfoex = new SysAdminInfoex();
        sysAdminInfoex.setSpecialDrugsUsePermission(Constant.BASIC_STRING_ONE);
        sysAdminInfoex.setPrescriptionShare(Constant.BASIC_STRING_ZERO + "," + Constant.BASIC_STRING_ONE);
        sysAdminInfoex.setDescriptionShare(Constant.BASIC_STRING_ZERO + "," + Constant.BASIC_STRING_ONE);
        sysAdminInfoex.setDefaultPrescriptionShare(Constant.BASIC_STRING_ONE);
        sysAdminInfoex.setUserId(appsInfoDetails.getUserId());
        sysAdminInfoexMapper.insert(sysAdminInfoex);
        return ResEntity.success(appsInfoDetails);
    }

    public Object editPersonDetail(AppsInfoDetails appsInfoDetails) {
        ResEntity resEntity = checkParams(appsInfoDetails);
        if (!resEntity.getStatus()) {
            return resEntity;
        }
        adminInfoMapper.updatePersonDetail(appsInfoDetails);
        return ResEntity.success(appsInfoDetails);
    }

    public ResEntity checkParams(AppsInfoDetails appsInfoDetails) {
        if (StringUtils.isBlank(appsInfoDetails.getCertificate())) {
            return ResEntity.error("证件号不能为空");
        }
        if (StringUtils.isBlank(appsInfoDetails.getUserName())) {
            return ResEntity.error("登录名不能为空");
        }
        if (StringUtils.isBlank(appsInfoDetails.getPhone())) {
            return ResEntity.error("手机号不能为空");
        }
        if (StringUtils.isBlank(appsInfoDetails.getNameZh())) {
            return ResEntity.error("中文名不能为空");
        }
        if (appsInfoDetails.getWorkStatus() == null) {
            return ResEntity.error("工作状态不能为空");
        }
        if (appsInfoDetails.getStaffType() == null) {
            return ResEntity.error("人员类别不能为空");
        }
        if (StringUtils.isBlank(appsInfoDetails.getProfessional())) {
            return ResEntity.error("职称不能为空");
        }
        if (null == appsInfoDetails.getSysDoctorMultipoints() || appsInfoDetails.getSysDoctorMultipoints().isEmpty()) {
            return ResEntity.error("请选择所属机构");
        }
        return ResEntity.success();
    }

    public Object getContinueEducationList(GetContinueEducationListReq req,Page page) {
        PageHelper.startPage(page.getPage(), page.getLimit());
        List<SysAdminInfoEdu> list = sysAdminInfoEduMapper.getContinueEducationList(req);
        Object layUiTablePageData = Page.getLayUiTablePageData(list);
        PageHelper.clearPage();
        return layUiTablePageData;
    }

    public Object deletePerson(String userId) {
        adminInfoMapper.deletePerson(userId);
        return ResEntity.success();
    }

    public Object saveContinueEducation(SysAdminInfoEdu sysAdminInfoEdu) {

        if (StringUtils.isBlank(sysAdminInfoEdu.getUserName())) {
            return ResEntity.error("人名不能为空");
        }
        if (StringUtils.isBlank(sysAdminInfoEdu.getUserId())) {
            return ResEntity.error("userId不能为空");
        }
        if (StringUtils.isBlank(sysAdminInfoEdu.getCourseName())) {
            return ResEntity.error("课程名称不能为空");
        }
        if (null == (sysAdminInfoEdu.getCourseType())) {
            return ResEntity.error("课程类型不能为空");
        }
        if (null == (sysAdminInfoEdu.getStudyStatus())) {
            return ResEntity.error("学习状态不能为空");
        }
        if (null == (sysAdminInfoEdu.getCredit())) {
            return ResEntity.error("学分不能为空");
        }

        sysAdminInfoEdu.setCreateUserName(AdminUtils.getCurrentHr().getNameZh());
        sysAdminInfoEdu.setCreateUserId(AdminUtils.getCurrentHr().getId());
        sysAdminInfoEdu.setInsertTime(new Date());
        if (sysAdminInfoEdu.getId() == null){
            sysAdminInfoEduMapper.insert(sysAdminInfoEdu);
        }else {
            sysAdminInfoEduMapper.updateByPrimaryKey(sysAdminInfoEdu);
        }

        return ResEntity.success();
    }

    public Object deleteContinueEducation(String id) {
        SysAdminInfoEdu objectById = sysAdminInfoEduMapper.getObjectById(id);
        if (objectById != null) {
            sysAdminInfoEduMapper.deleteByPrimaryKey(objectById);
            return ResEntity.success();
        }
        return ResEntity.success();
    }

    /**
     * 绩效考核管理-列表
     * @param getExamineList
     * @param page
     * @return
     */
    public Object getExamineList(GetExamineList getExamineList, Page page) {
        PageHelper.startPage(page.getPage(), page.getLimit());
        List<SysAdminInfoExamine> list = sysAdminInfoExamineMapper.getExamineList(getExamineList);
        Object layUiTablePageData = Page.getLayUiTablePageData(list);
        PageHelper.clearPage();
        return layUiTablePageData;
    }

    public Object saveExamine(SysAdminInfoExamine sysAdminInfoExamine) {
        //校验参数
        if (StringUtils.isBlank(sysAdminInfoExamine.getExamineCycle())) {
            return ResEntity.error("考核周期不能为空");
        }
        if (StringUtils.isBlank(sysAdminInfoExamine.getUserName())) {
            return ResEntity.error("考核人名不能为空");
        }
        if (StringUtils.isBlank(sysAdminInfoExamine.getUserId())) {
            return ResEntity.error("userId不能为空");
        }
        if (sysAdminInfoExamine.getExamineScore() == null) {
            return ResEntity.error("考核分数不能为空");
        }
        if (sysAdminInfoExamine.getExamineClass() == null) {
            return ResEntity.error("考核等级不能为空");
        }
        if (null == (sysAdminInfoExamine.getExamineTime())) {
            return ResEntity.error("工号的集合不能为空");
        }
        sysAdminInfoExamine.setInsertTime(new Date());
        sysAdminInfoExamine.setCreateUserId(AdminUtils.getCurrentHr().getId());
        sysAdminInfoExamine.setCreateUserName(AdminUtils.getCurrentHr().getNameZh());
        if (sysAdminInfoExamine.getId() == null){
            sysAdminInfoExamineMapper.insert(sysAdminInfoExamine);
        }else {
            sysAdminInfoExamineMapper.updateByPrimaryKey(sysAdminInfoExamine);
        }
        return ResEntity.success();
    }

    public Object deleteExamine(String id) {
        SysAdminInfoExamine objectById = sysAdminInfoExamineMapper.getObjectById(id);
        if (null == objectById){
            return ResEntity.error("数据不存在");
        }
        sysAdminInfoExamineMapper.deleteByPrimaryKey(objectById);
        return ResEntity.success();
    }

    public Object getAssessmentEvaluationList(GetAssessmentEvaluationList getAssessmentEvaluationList, Page page) {
        PageHelper.startPage(page.getPage(), page.getLimit());
        List<SysAdminInfoAssessmentEvaluation> list = sysAdminInfoAssessmentEvaluationMapper.getAssessmentEvaluationList(getAssessmentEvaluationList);
        Object layUiTablePageData = Page.getLayUiTablePageData(list);
        PageHelper.clearPage();
        return layUiTablePageData;
    }

    public Object saveAssessmentEvaluation(SysAdminInfoAssessmentEvaluation sysAdminInfoAssessmentEvaluation) {
        if (StringUtils.isBlank(sysAdminInfoAssessmentEvaluation.getAssessmentEvaluationName())) {
            return ResEntity.error("测评名称不能为空");
        }
        if (StringUtils.isBlank(sysAdminInfoAssessmentEvaluation.getUserName())) {
            return ResEntity.error("被测评人名不能为空");
        }
        if (StringUtils.isBlank(sysAdminInfoAssessmentEvaluation.getUserId())) {
            return ResEntity.error("userId不能为空");
        }
        if (sysAdminInfoAssessmentEvaluation.getAssessmentEvaluationScore() == null) {
            return ResEntity.error("测评分数不能为空");
        }
        if (sysAdminInfoAssessmentEvaluation.getAssessmentEvaluationClass() == null) {
            return ResEntity.error("测评等级不能为空");
        }
        if (null == (sysAdminInfoAssessmentEvaluation.getAssessmentEvaluationTime())) {
            return ResEntity.error("工号的集合不能为空");
        }
        if (StringUtils.isBlank(sysAdminInfoAssessmentEvaluation.getAssessmentEvaluationUserId())) {
            return ResEntity.error("测评人名不能为空");
        }
        if (StringUtils.isBlank(sysAdminInfoAssessmentEvaluation.getAssessmentEvaluationUserName())) {
            return ResEntity.error("测评人名不能为空");
        }
        sysAdminInfoAssessmentEvaluation.setInsertTime(new Date());
        sysAdminInfoAssessmentEvaluation.setCreateUserId(AdminUtils.getCurrentHr().getId());
        sysAdminInfoAssessmentEvaluation.setCreateUserName(AdminUtils.getCurrentHr().getNameZh());
        if (sysAdminInfoAssessmentEvaluation.getId() == null){
            sysAdminInfoAssessmentEvaluationMapper.insert(sysAdminInfoAssessmentEvaluation);
        }else {
            sysAdminInfoAssessmentEvaluationMapper.updateByPrimaryKey(sysAdminInfoAssessmentEvaluation);
        }
        return ResEntity.success();
    }

    public Object deleteAssessmentEvaluation(String id) {
        SysAdminInfoAssessmentEvaluation objectById = sysAdminInfoAssessmentEvaluationMapper.getObjectById(id);
        if (objectById != null) {
            sysAdminInfoAssessmentEvaluationMapper.deleteByPrimaryKey(objectById);
        }
        return ResEntity.success();
    }
}
