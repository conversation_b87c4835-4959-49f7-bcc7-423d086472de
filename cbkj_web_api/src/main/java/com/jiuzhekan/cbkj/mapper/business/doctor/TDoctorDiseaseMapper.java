package com.jiuzhekan.cbkj.mapper.business.doctor;

import com.jiuzhekan.cbkj.beans.business.doctor.TDoctorDisease;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public interface TDoctorDiseaseMapper extends BaseMapper<TDoctorDisease> {

    /**
     * 某医生30天内的常用疾病
     *
     * @param tDoctorDisease
     * @return java.util.List<com.jiuzhekan.cbkj.beans.business.doctor.TDoctorDisease>
     * <AUTHOR>
     * @date 2020/8/24
     */
    List<TDoctorDisease> getDoctorDiseaseInThirtyDay(TDoctorDisease tDoctorDisease);

    /**
     * 统计常用疾病
     *
     * @return java.util.List<com.jiuzhekan.cbkj.beans.business.doctor.TDoctorDisease>
     * <AUTHOR>
     * @date 2020/8/24
     */
//    List<TDoctorDisease> statisticDoctorDiseaseFromRecord();

    /**
     * 统计常用疾病
     *
     * @return java.util.List<com.jiuzhekan.cbkj.beans.business.doctor.TDoctorDisease>
     * <AUTHOR>
     * @date 2020/8/24
     */
    List<TDoctorDisease> statisticDoctorDiseaseFromGroup(Integer days);
}