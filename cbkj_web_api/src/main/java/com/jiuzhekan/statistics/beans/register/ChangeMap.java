package com.jiuzhekan.statistics.beans.register;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date $ $
 **/
@Data
@ApiModel("统计报表")
public class ChangeMap {
    private int key;
    private String appId;
    private String insCode;
    private String appName;
    private String insName;
    private String userId;
    private String userName;

//    @ApiModelProperty(value = "医共体总计")
//    private int numberAppTotal;

    @ApiModelProperty(value = "体质辨识")
    private int number;
    @ApiModelProperty(value = "协定方")
    private int sumPersonalPrescription;
    @ApiModelProperty(value = "适宜技术")
    private int sumStatisticsPrescription;
    @ApiModelProperty(value = "就诊人次")
    private int registerTimes;
    @ApiModelProperty(value = "电子病历")
    private int electronicRecordNum;
    @ApiModelProperty(value = "中药饮片处方数(内服+外用处方总和)")
    private int sumInnerAndExtPrescription;

    @ApiModelProperty(value = "门诊中药处方数")
    private int sumOutPatientPrescription;

    @ApiModelProperty(value = "住院中药处方数")
    private int sumInPatientPrescription;

    @ApiModelProperty(value = "中药饮片处方数(内服+外用处方总和)CDSS")
    private int sumInnerAndExtPrescriptionCDSS;

    private List<ChangeMap> insList;

    public static void main(String[] args) {
        ArrayList<String> strings = new ArrayList<>();
        strings.add("1");
        strings.add("2");
        strings.add("3");
        strings.add("4");
        strings.add("5");
        strings.add(1, "6");
        strings.forEach(System.out::println);
    }
}
