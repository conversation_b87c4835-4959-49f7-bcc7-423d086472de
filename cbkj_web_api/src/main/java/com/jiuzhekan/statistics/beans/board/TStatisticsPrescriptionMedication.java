package com.jiuzhekan.statistics.beans.board;

import com.jiuzhekan.statistics.utils.DateUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class TStatisticsPrescriptionMedication implements Serializable {

    @ApiModelProperty(value = "主键自增长")
    private Integer id;

    @ApiModelProperty(value = "")
    private String appId;

    @ApiModelProperty(value = "")
    private String appName;

    @ApiModelProperty(value = "")
    private String insCode;

    @ApiModelProperty(value = "")
    private String insName;

    @ApiModelProperty(value = "")
    private String deptId;

//    @ApiModelProperty(value = "医生ID")
//    private String userId;
//
//    @ApiModelProperty(value = "")
//    private String userName;

    @ApiModelProperty(value = "统计日期")
    private Date createDate;

    @ApiModelProperty(value = "插入时间")
    private Date insertDate;

    @ApiModelProperty(value = "提醒次数")
    private Integer number;



    public TStatisticsPrescriptionMedication initDate(TStatisticsPrescriptionMedication tStatisticsPrescriptionMedication) throws ParseException {
        this.appId = tStatisticsPrescriptionMedication.getAppId();
        this.insCode = tStatisticsPrescriptionMedication.getInsCode();
//        this.userId = tStatisticsPrescriptionMedication.getUserId();
        SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd");
        this.createDate = sdf2.parse(DateUtils.getWeekEndTim2e(0, true));
        this.insertDate = new Date();
        this.number = 0;
        return this;
    }
}
