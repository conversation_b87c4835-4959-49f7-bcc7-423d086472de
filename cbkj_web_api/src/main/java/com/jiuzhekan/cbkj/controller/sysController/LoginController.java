package com.jiuzhekan.cbkj.controller.sysController;

import com.alibaba.fastjson.JSON;
import com.jiuzhekan.cbkj.beans.ai.CueWordURLRequest;
import com.jiuzhekan.cbkj.beans.sysBeans.AdminInfo;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.beans.sysBeans.SysAdminPractice;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.common.config.CookieUtil;
import com.jiuzhekan.cbkj.common.config.security.TokenBo;
import com.jiuzhekan.cbkj.common.config.security.TokenUtil;
import com.jiuzhekan.cbkj.common.http.SsoUtil;
import com.jiuzhekan.cbkj.common.utils.AESPKCS7Util;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.HardwareUtil;
import com.jiuzhekan.cbkj.common.utils.encry.RSAEncryption;
import com.jiuzhekan.cbkj.service.ai.SySCueWordService;
import com.jiuzhekan.cbkj.service.business.his.DigitPadService;
import com.jiuzhekan.cbkj.service.myData.TPersonalRuleAuthService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

/**
 * LoginController
 * <p>
 * 杭州聪宝科技有限公司
 *
 * <AUTHOR>
 * @date 2021/7/12
 */
@Api(value = "登录接口", tags = "登录接口")
@Controller
@Slf4j
public class LoginController {

    @Autowired
    private SsoUtil ssoUtil;
    @Autowired
    private TPersonalRuleAuthService tPersonalRuleAuthService;
    @Autowired
    private TokenUtil tokenUtil;

    @Value(value = "${rsa.whitelist.privateKey}")
    private String whitelistPrivateKey;

    @Value(value = "${whitelist.machineCode:}")
    private String whitelistMachineCode;


    private final SySCueWordService sySCueWordService;

    public LoginController(SySCueWordService sySCueWordService) {
        this.sySCueWordService = sySCueWordService;
    }
//    @Autowired
//    private RSAEncryption rsaEncryption;
//    @Value("${rsa.privateKey}")
//    private String privateKey;

    //    @GetMapping(value = "/credentials/login")
//    @ApiOperation(value = "三方调用-获取登录token")
//    @ResponseBody
//    public Object hisLogin(String cbdata) {
//        return digitPadService.hisLogin(cbdata);
//    }
    @RequestMapping(value = "login", method = RequestMethod.POST)
    @ApiOperation(value = "登录", notes = "登录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "name", value = "用户名", defaultValue = "xiong", dataType = "String", paramType = "query", required = true),
            @ApiImplicitParam(name = "pwd", value = "密码", defaultValue = "c8a25c2f9ce8f5ffea79aa127cda5014", dataType = "String", paramType = "query", required = true),
            @ApiImplicitParam(name = "appId", value = "医共体ID", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "insCode", value = "医疗机构代码", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "login", value = "重新登陆", dataType = "String", paramType = "query")
    })
    @LogAnnotation(value = "登录", isWrite = true)
    @ResponseBody
    public Object login(String name, String pwd, String appId, String insCode, String login, HttpServletResponse response) {
        if (StringUtils.isNotEmpty(whitelistMachineCode)) {
            //设备码白名单校验
            ResEntity res = verify(whitelistMachineCode);
            if (!res.getStatus()) {
                return res;
            }
        }
        return ssoLogin(name, pwd, appId, insCode, login,response);
    }

    private ResEntity verify(String whitelistMachineCode) {
        //当前设备码
        String uniqueIdentifier = HardwareUtil.getUniqueIdentifier();

        if (StringUtils.isEmpty(uniqueIdentifier)) {
            return ResEntity.error("硬件设备异常、没有访问权限！");
        }

        RSAEncryption rsa = new RSAEncryption();
        try {
            String dencrypt = rsa.dencryptByPrivateKey(whitelistMachineCode, whitelistPrivateKey);
            if (!dencrypt.equals(uniqueIdentifier)) {
                return ResEntity.error("未认证、没有访问权限！");
            }
        } catch (Exception e) {
            return ResEntity.error(e.getMessage());
        }

        return ResEntity.success(null);
    }

    @RequestMapping(value = "exit", method = RequestMethod.GET)
    @ApiOperation(value = "登出", notes = "登出")
    @LogAnnotation(value = "登出", isWrite = true)
    @ResponseBody
    public ResEntity exit() {

        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (null != authentication) {
            Object obj = authentication.getPrincipal();
            if (obj instanceof TokenBo) {
                String tokenKey = ((TokenBo) obj).getTokenKey();
                tokenUtil.deleteToken(tokenKey);
                ssoUtil.logout(tokenKey);
            }
        }
        return ResEntity.success(null);
    }

    /**
     * sso登录
     *
     * @param name    name
     * @param pwd     pwd
     * @param appId   appId
     * @param insCode insCode
     * @param login   login
     * @return com.jiuzhekan.cbkj.beans.sysBeans.ResEntity
     * <AUTHOR>
     * @date 2022/3/10
     */
    private Object ssoLogin(String name, String pwd, String appId, String insCode, String login,HttpServletResponse response) {

        String extra = "";
        if (StringUtils.isNotBlank(appId)) {
            extra = extra.concat("appId:").concat(appId);
        }
        if (StringUtils.isNotBlank(insCode)) {
            extra = extra.concat("insCode:").concat(insCode);
        }

        ResEntity ssoEntity = ssoUtil.login(name, pwd, extra);

        if (!ssoEntity.getStatus()) {
            CookieUtil.addSecureCookie(response, "authorization", "", 3600);
            return ssoEntity;
        }

        String sessionId = ((Map<?, ?>) ssoEntity.getData()).get("sessionId").toString();
        //用户
        AdminInfo admin = new AdminInfo(ssoEntity.getData());
        //执业机构
        SysAdminPractice practice = new SysAdminPractice(ssoEntity.getData(), appId, insCode);
        if (practice.getAdminId() == null) {
            practice.setAdminId(admin.getId());
            practice.setAppId(Constant.BASIC_APP_ID);
            practice.setInsCode(Constant.BASIC_INS_CODE);
            practice.setDepId(Constant.BASIC_DEPT_ID);
        }

        //TODO 用户拓展信息
//        adminService.loadAdminInfoEx(admin);

        //TODO 用户协定方权限
        tPersonalRuleAuthService.savePersonalPuleAuth(practice);

        admin.setAppId(appId);
        admin.setInsCode(insCode);

        if (tokenUtil.getLoginOne()) {
            if (!"ok".equals(login)) {
                final TokenBo bo = tokenUtil.getTokenBo(sessionId);
                if (bo != null && admin.getId().equals(bo.getAdminId())) {
                    return ResEntity.error(555, "你的账号已登录，是否覆盖？");
                }
            } else {
                tokenUtil.deleteTokens(sessionId);
            }
        }

        TokenBo tokenBo = tokenUtil.createTokenBo(sessionId, admin, practice);
        tokenUtil.updateTokenBo(tokenBo);

        Map<String, Object> result = new HashMap<>();
        result.put("status", true);
        result.put("message", Constant.SUCCESS_DX);
        result.put("data", tokenBo.getAuthorization());
        result.put("needUpdatePwd", ((Map<?, ?>) ssoEntity.getData()).get("needUpdatePwd"));
        CookieUtil.addSecureCookie(response, "authorization", tokenBo.getAuthorization(), 3600);
        return result;
    }


    @GetMapping(value = "/get/his/cadata")
    @ApiOperation(value = "获取数字诊间登录cbdata", notes = "获取数字诊间登录cbdata")
    @LogAnnotation(value = "获取数字诊间登录cbdata", isWrite = true)
    @ResponseBody
    public Object getHISLoginData() {
        HashMap<String, Object> map = new HashMap<>();
        AdminInfo currentHr = AdminUtils.getCurrentHr();

        map.put("appId", AdminUtils.getCurrentAppId());
        map.put("insCode", AdminUtils.getCurrentHr().getInsCode());
        map.put("deptId", AdminUtils.getCurrentHr().getDeptId());
        map.put("userName", currentHr.getUsername());
        log.info("======获取数字诊间登录cbdata:{}", JSON.toJSONString(map));
        try {
            String base64 = AESPKCS7Util.encrypt(JSON.toJSONString(map), Constant.SYS_AES_KEY, "base64");
            return ResEntity.success(base64);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }


    @GetMapping(value = "/get/gpt/url")
    @ApiOperation(value = "获取GPT请求URL", notes = "获取GPT请求URL")
    @LogAnnotation(value = "获取GPT请求URL", isWrite = true)
    @ResponseBody
    public Object getGPTUrl() {

        return sySCueWordService.getGPBaseTUrl();
    }

    @PostMapping(value = "/get/gpt/urlByContent")
    @ApiOperation(value = "素问gpt通过组装病历等数据获取访问地址", notes = "素问gpt通过组装病历等数据获取访问地址")
    @LogAnnotation(value = "素问gpt通过组装病历等数据获取访问地址", isWrite = true)
    @ResponseBody
    public Object getGPTUrlByObj(@RequestBody CueWordURLRequest cueWordURLRequest) {
        //判断 cueWordURLRequest.getCueWordType() 值 不是 1 和 2 就返回错误
        if (cueWordURLRequest.getCueWordType() == null) {
            return ResEntity.error("缺少必要字段cueWordType");
        }
        if ("AI_ELE_RECORD_QUA_CON".equals(cueWordURLRequest.getCueWordType()) && null == cueWordURLRequest.getMedicalHistorySaveRequest()) {
            return ResEntity.error("缺少必要对象medicalHistorySaveRequest");
        }
//        if (cueWordURLRequest.getCueWordType() != 1 && cueWordURLRequest.getCueWordType() != 2) {
//            return ResEntity.error("cueWordURLRequest.cueWordType 值 不正确");
//        }
        return ResEntity.success(sySCueWordService.getCueWordURL(cueWordURLRequest));
    }


//    /**
//     * 本地登录
//     *
//     * @param name    name
//     * @param pwd     pwd
//     * @param appId   appId
//     * @param insCode insCode
//     * @param login   login
//     * @return com.jiuzhekan.cbkj.beans.sysBeans.ResEntity
//     * <AUTHOR>
//     * @date 2022/3/10
//     */
//    private ResEntity nativeLogin(String name, String pwd, String appId, String insCode, String login) {
//
//        AdminInfo admin = adminService.loadUserByUsername(name);
//
//        if (null == admin || pwd == null) {
//            return ResEntity.error("用户名或密码错误！");
//        }
//
//        try {
//            pwd = rsaEncryption.dencryptByPrivateKey(pwd, privateKey);
//        } catch (Exception e) {
//            log.error("RSA解密失败！");
//            return ResEntity.error("用户名或密码错误！");
//        }
//
//        if (!pwd.equals(admin.getPassword())) {
//            return ResEntity.error("用户名或密码错误！");
//        }
//
//        if (!admin.isEnabled()) {
//            return ResEntity.error("该用户被禁用！");
//        }
//
//        admin.setAppId(appId);
//        admin.setInsCode(insCode);
//
//        List<SysAdminPractice> practices = sysAdminPracticeService.getPracticeList(admin.getId(), appId, insCode);
//        if (practices == null || practices.size() == 0) {
//            return ResEntity.error("用户没有授权执业机构，请联系管理员！");
//        } else if (practices.size() > 1) {
//            return ResEntity.error("请选择执业机构！");
//        }
//
//        TokenBo tokenBo = null;
//        final SysAdminPractice practice = practices.get(0);
//        if (tokenUtil.getLoginOne()) {
//            String tokenKey = tokenUtil.createTokenKey(admin.getId(), practice.getAppId(), practice.getInsCode());
//            if (!"ok".equals(login)) {
//                final TokenBo bo = tokenUtil.getTokenBo(tokenKey);
//                if (bo != null && admin.getId().equals(bo.getAdminId())) {
//                    return ResEntity.error(555, "你的账号已登录，是否覆盖？");
//                }
//            } else {
//                tokenUtil.deleteTokens(tokenKey);
//            }
//        }
//        tokenBo = tokenUtil.createTokenBo(admin, practice);
//        tokenUtil.updateTokenBo(tokenBo);
//
//        return ResEntity.success(tokenBo.getAuthorization());
//    }
}
