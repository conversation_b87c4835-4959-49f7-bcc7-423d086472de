package com.jiuzhekan.healthpres.beans.heathPresControllerVo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description 获取中医保健患者列表
 * @Date 2025/5/27 10:53
 * @Version 1.0
 */
@ApiModel
@Data
public class GetChildIndexPageList {


    @ApiModelProperty(value = "患者id")
    private String patientId;

    @ApiModelProperty(value = "患者姓名")
    private String patientName;

    @ApiModelProperty(value = "创建人")
    private String createDoctorName;

    @ApiModelProperty(value = "创建人id")
    private String createDoctorId;

    @ApiModelProperty(value = "患者性别")
    private String patientSex;

    @ApiModelProperty(value = "患者年龄")
    private String patientAge;

    @ApiModelProperty(value = "患者出生日期")
    private Date patientBirthDay;

    @ApiModelProperty(value = "结果报告名称")
    private String resultReportName;

    @ApiModelProperty(value = "结果报告类型")
    private String resultReportTypeInfo;

    @ApiModelProperty(value = "结果报告时间")
    private Date resultReportTime;

    @ApiModelProperty(value = "1.儿童保健2.慢病调理 3健康处方")
    private Integer questionnaireMainType;

    @ApiModelProperty(value = "子分类 1-1(6、12月龄儿童)1-2(18、24月龄儿童)1-3(30、36月龄儿童) 等明儿")
    private String questionnaireMainSecondTypeName;

    @ApiModelProperty(value = "子分类 1-1(6、12月龄儿童)1-2(18、24月龄儿童)1-3(30、36月龄儿童) 等代码")
    private String questionnaireMainSecondTypeCode;


}
