package com.jiuzhekan.statistics.service.personal;

import com.github.pagehelper.PageHelper;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.statistics.beans.analysis.StatisticsResult;
import com.jiuzhekan.statistics.beans.personal.StatisticsPersonalPrescription;
import com.jiuzhekan.statistics.beans.personal.TStatisticsPersonalPrescription;
import com.jiuzhekan.statistics.beans.prescription.StatisticsPrescription;
import com.jiuzhekan.statistics.beans.prescription.TStatisticsPrescription;
import com.jiuzhekan.statistics.mapper.personal.TStatisticsPersonalPrescriptionMapper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.*;

@Service
public class TStatisticsPersonalPrescriptionService {

    @Autowired
    private TStatisticsPersonalPrescriptionMapper tStatisticsPersonalPrescriptionMapper;

    /**
     * 加载分页数据
     *
     * @param tStatisticsPersonalPrescription 协定方统计表
     * @param page 分页
     * @return Object
     * <AUTHOR>
     * @date 2022-05-23
     */
    public Object getPageDatas(TStatisticsPersonalPrescription tStatisticsPersonalPrescription, Page page) {

        PageHelper.startPage(page.getPage(), page.getLimit());
        List<TStatisticsPersonalPrescription> list = tStatisticsPersonalPrescriptionMapper.getPageListByObj(tStatisticsPersonalPrescription);
        return Page.getLayUiTablePageData(list);
    }

    /**
     * 加载某条数据
     *
     * @param id 协定方统计表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-05-23
     */
    public ResEntity findObj(String id) {

        if (StringUtils.isBlank(id)) {
            return ResEntity.entity(false, "参数不能为空哦", null);
        }
        TStatisticsPersonalPrescription tStatisticsPersonalPrescription = tStatisticsPersonalPrescriptionMapper.getObjectById(id);
        return ResEntity.entity(true, Constant.SUCCESS_DX, tStatisticsPersonalPrescription);
    }


    /**
     * 插入新数据
     *
     * @param tStatisticsPersonalPrescription 协定方统计表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-05-23
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insert(TStatisticsPersonalPrescription tStatisticsPersonalPrescription){

        long rows = tStatisticsPersonalPrescriptionMapper.insert(tStatisticsPersonalPrescription);

        return ResEntity.success(tStatisticsPersonalPrescription);
    }


    /**
     * 修改
     *
     * @param tStatisticsPersonalPrescription 协定方统计表
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-05-23
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity update(TStatisticsPersonalPrescription tStatisticsPersonalPrescription) {

        long rows = tStatisticsPersonalPrescriptionMapper.updateByPrimaryKey(tStatisticsPersonalPrescription);

        return ResEntity.success(tStatisticsPersonalPrescription);
    }

    /**
     * 删除
     *
     * @param ids ids
     * @return ResEntity
     * <AUTHOR>
     * @date 2022-05-23
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity deleteLis(String ids){
        if (StringUtils.isBlank(ids)) {
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }
        long rowsR = tStatisticsPersonalPrescriptionMapper.deleteBylist(ids.split(","));

        return ResEntity.success(rowsR);
    }

    /**
     * 协定方统计
     * @param timeDiff 传1是昨天。2前天。0今天。-1明天
     * @param shareType （0私有 1科室 2全院 3专家经验）
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity setPersonalPrescriptionResult(String curDate,String timeDiff,String shareType){
        Map<String, Object> param = new HashMap<>(16);
        //传1是昨天。2前天。0今天。-1明天
        if (StringUtils.isBlank(timeDiff)){
            timeDiff = "1";
        }
        param.put("timeDiff", timeDiff);
        param.put("shareType", shareType);
        if (StringUtils.isBlank(curDate)){
            param.put("curDate", new SimpleDateFormat("yyyy-MM-dd").format(new Date()).toString());
        }else {
            param.put("curDate",curDate);
        }
        List<StatisticsPersonalPrescription> list = tStatisticsPersonalPrescriptionMapper.selectPersonalPrescriptionResult(param);
        List<StatisticsPersonalPrescription> insertRe = new ArrayList<StatisticsPersonalPrescription>();
        List<StatisticsPersonalPrescription> updateRe = new ArrayList<StatisticsPersonalPrescription>();

        for (StatisticsPersonalPrescription statisticsResults : list) {
            if (Constant.BASIC_STRING_ZERO.equals(shareType)){
                statisticsResults.setSelfNum(statisticsResults.getNumber());
            }
            if (Constant.BASIC_STRING_ONE.equals(shareType)){
                statisticsResults.setDeptNum(statisticsResults.getNumber());
            }
            if (Constant.BASIC_STRING_TWO.equals(shareType)){
                statisticsResults.setInsNum(statisticsResults.getNumber());
            }
            if (Constant.BASIC_STRING_THREE.equals(shareType)){
                statisticsResults.setAppNum(statisticsResults.getNumber());
            }
            statisticsResults.setTotalNum(statisticsResults.getSelfNum()+statisticsResults.getDeptNum()+statisticsResults.getInsNum()+statisticsResults.getAppNum());
            if (null == statisticsResults.getId()){
                insertRe.add(statisticsResults);
            }else {
                updateRe.add(statisticsResults);
            }
        }
        //新增
        if (insertRe.size()>0){
            tStatisticsPersonalPrescriptionMapper.insertStatistics(insertRe);
        }
        if (updateRe.size()>0){
            for (StatisticsPersonalPrescription prescription : updateRe) {
                TStatisticsPersonalPrescription tStatisticsPrescription = new TStatisticsPersonalPrescription();
                BeanUtils.copyProperties(prescription, tStatisticsPrescription);
                tStatisticsPersonalPrescriptionMapper.updateByPrimaryKey(tStatisticsPrescription);
            }
        }
        return ResEntity.success(null);
    }
    public TStatisticsPersonalPrescription getLastData(){
        return tStatisticsPersonalPrescriptionMapper.getLastData();
    }
}
