package com.jiuzhekan.cbkj.controller.business.record;

import com.jiuzhekan.cbkj.beans.business.record.TRecord;
import com.jiuzhekan.cbkj.beans.business.record.VO.TBigRecordRespVO;
import com.jiuzhekan.cbkj.beans.business.record.VO.TRecordRespVO;
import com.jiuzhekan.cbkj.beans.business.record.VO.TodayPatientReqVO;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.common.annotaionUtil.StatisticsFunction;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.service.business.record.TRecordService;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("business")
@Api(value = "病例接口", tags = "病例接口")
public class TRecordController {

    @Autowired
    private TRecordService tRecordService;


    @RequestMapping(value = "tRecord/findObj", method = RequestMethod.GET)
    @ApiOperation(value = "加载病例信息详情", notes = "根据病历id查询病历主表信息", response = TRecord.class)
    @ApiImplicitParam(name = "recId", value = "病历id", paramType = "query")
    @LogAnnotation(value = "加载病例信息详情")
    @ResponseBody
    public Object getObj(String recId) {
        ResEntity result = tRecordService.findObj(recId);
        return result;
    }


    @RequestMapping(value = "lsRecord/getRecSByPatientId", method = RequestMethod.GET)
    @ApiOperation(value = "查询单个患者的病历", notes = "根据患者id单表查询病历主表,结果按照时间倒序", response = TRecord.class)
    @LogAnnotation(value = "根据患者id单表查询病历主表,结果按照时间倒序")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "patientId", value = "患者ID", dataType = "String", paramType = "query"),
    })
    @ResponseBody
    public Object getRecSByPatientId(String patientId) {
        Object obj = tRecordService.getRecSByPatientId(patientId);
        return obj;
    }

    @RequestMapping(value = "tRecord/getSingleRecord", method = RequestMethod.GET)
    @ApiOperation(value = "查询单份病历", notes = "根据病历id查询病历详情", response = TRecordRespVO.class)
    @ApiImplicitParam(name = "recId", value = "病历id", required = true, paramType = "query")
    @LogAnnotation(value = "根据病历id查询病历详情")
    @ResponseBody
    public Object getSingleRecord(String recId, String preNo) {
        Object obj = tRecordService.getSingleRecord(recId, preNo);
        return obj;
    }

    @RequestMapping(value = "tRecord/getAllPre", method = RequestMethod.GET)
    @ApiOperation(value = "查询单份病历", notes = "根据病历id查询病历详情", response = TRecordRespVO.class)
    @ApiImplicitParam(name = "recId", value = "病历id", required = true, paramType = "query")
    @LogAnnotation(value = "根据病历id查询病历详情")
    @ResponseBody
    public Object getAllPre(String recId, String preNo) {
        Object obj = tRecordService.getAllPre(recId, preNo);
        return obj;
    }

    @RequestMapping(value = "tRecord/getCheckRecord", method = RequestMethod.GET)
    @ApiOperation(value = "查询待审核病历", notes = "根据病历id查询待审核病历详情", response = TRecordRespVO.class)
    @ApiImplicitParam(name = "recId", value = "病历id", required = true, paramType = "query")
    @LogAnnotation(value = "根据病历id查询待审核病历详情")
    @ResponseBody
    public Object getCheckRecord(String recId) {
        Object obj = tRecordService.getCheckRecord(recId);
        return obj;
    }

    @RequestMapping(value = "tRecord/getBigRecord", method = RequestMethod.GET)
    @ApiOperation(value = "查询大病历", notes = "根据病历id找到诊次id,再根据诊次id查询所有病历", response = TBigRecordRespVO.class)
    @ApiImplicitParam(name = "recId", value = "病历id", required = true, paramType = "query")
    @LogAnnotation(value = "根据病历id查询大病历")
    @ResponseBody
    public Object getBigRecord(String recId) {
        return tRecordService.getBigRecord(recId);
    }


    @RequestMapping(value = "tRecord/getHistoryRec", method = RequestMethod.GET)
    @ApiOperation(value = "历史病历", notes = "根据患者id查出其一定时间内的病历列表", response = TRecord.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "patientId", value = "患者id", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orderName", value = "病人信息查询(0 病人姓名,1 就诊卡号,2 病人手机号,3 病人身份证号)", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orderValue", value = "输入框内容", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "startDate", value = "开始日期(日期都不选的话默认为1年以内)", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "endDate", value = "结束日期", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "page", value = "页码,默认1", dataType = "Int", paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "每页条数,默认10", dataType = "Int", paramType = "query")
    })
    @LogAnnotation(value = "历史病历")
    @ResponseBody
    public Object getHistoryRec(TodayPatientReqVO todayPatientReqVO, Page page) {
        Object obj = tRecordService.getHistoryRec(todayPatientReqVO, page);
        return obj;
    }


    @RequestMapping(value = "tRecord/recManage", method = RequestMethod.GET)
    @ApiOperation(value = "病历管理", notes = "查询病历主表(不管当前登录人的医联体id和inscode,看前端传递的参数)", response = TRecord.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "appId", value = "appId", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "insCode", value = "医疗机构id", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "recTreType", value = "病历类型（分两类：门诊、住院医嘱，对应值分别为1、2）", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "docId", value = "医生id", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "docName", value = "医生中文名", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orderName", value = "病人信息查询(0 病人姓名,1 就诊卡号,2 病人手机号,3 病人身份证号)", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orderValue", value = "输入框内容", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "startDate", value = "开始日期", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "endDate", value = "结束日期", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "page", value = "页码,默认1", dataType = "Int", paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "每页条数,默认10", dataType = "Int", paramType = "query")
    })
    @LogAnnotation(value = "病历管理")
    @StatisticsFunction(source = "病历管理", value = "病历查询次数")
    @ResponseBody
    public Object recManage(TodayPatientReqVO todayPatientReqVO, Page page) {
        Object obj = tRecordService.recManage(todayPatientReqVO, page);
        return obj;
    }


    @RequestMapping(value = "tRecord/updateIsPay", method = RequestMethod.GET)
    @ApiOperation(value = "微信支付修改状态", notes = "微信支付修改状态")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "recId", value = "病历id", dataType = "String", paramType = "query", required = true),
            @ApiImplicitParam(name = "registerId", value = "挂号id,用于修改挂号表状态", dataType = "String", paramType = "query", required = true),
            @ApiImplicitParam(name = "preId", value = "处方id,如果同时支付4个处方,用英文逗号将处方id拼接起来"
                    , dataType = "String", paramType = "query", required = true)
    })
    @LogAnnotation(value = "微信支付修改状态")
    @ResponseBody
    public Object updateIsPay(String recId, String registerId, String preId) {
        Object obj = tRecordService.updateIsPay(recId, registerId, preId);
        return obj;
    }

    @RequestMapping(value = "tRecord/myConsilia", method = RequestMethod.GET)
    @ApiOperation(value = "我的医案", notes = "根据当前医生id,疾病id,证型id在病历主表查找记录,挂号表对应就诊状态为已收费")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "disId", value = "疾病id", dataType = "String", paramType = "query", required = true),
            @ApiImplicitParam(name = "symId", value = "证型id", dataType = "String", paramType = "query", required = true)
    })
    @LogAnnotation(value = "根据当前医生id,疾病id,证型id在病历主表查找记录")
    @ResponseBody
    public Object myConsilia(String disId, String symId,Page page) {
        Object obj = tRecordService.myConsilia(disId, symId ,page);
        return obj;
    }



    @RequestMapping(value = "tRecord/getPreByPreNo", method = RequestMethod.GET)
    @ApiOperation(value = "根据处方编号获取处方信息", notes = "预览打印处方",response = TRecordRespVO.class)
    @ApiImplicitParams({
        @ApiImplicitParam(name = "preNo", value = "处方编号", dataType = "String", paramType = "query", required = true)
    })
    @LogAnnotation(value = "根据处方编号获取处方信息")
    @ResponseBody
    public Object getPreByPreNo(String preNo) {
        return tRecordService.getPreByPreNo(preNo);
    }
}