package com.jiuzhekan.cbkj.mapper.bs;

import com.jiuzhekan.cbkj.beans.bs.BsSelfPickupPoint;
import com.jiuzhekan.cbkj.beans.bs.designateddelivery.DesignatedDeliveryListRe;
import org.springframework.stereotype.Component;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;

import java.util.List;

@Component
public interface BsSelfPickupPointMapper extends BaseMapper<BsSelfPickupPoint>{


    List<BsSelfPickupPoint> getDeliveryDataList(DesignatedDeliveryListRe designatedDeliveryListRe);
}