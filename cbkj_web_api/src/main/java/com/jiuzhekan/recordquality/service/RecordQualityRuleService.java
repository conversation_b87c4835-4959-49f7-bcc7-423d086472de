package com.jiuzhekan.recordquality.service;

import com.github.pagehelper.PageHelper;
import com.jiuzhekan.cbkj.beans.sysBeans.AdminInfo;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.recordquality.beans.*;
import com.jiuzhekan.recordquality.mapper.TRecordQualityRuleItemsMapper;
import com.jiuzhekan.recordquality.mapper.TRecordQualityRuleItemsMappingMapper;
import com.jiuzhekan.recordquality.mapper.TRecordQualityRuleMainMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/6/26 13:55
 * @Version 1.0
 */
@Slf4j
@Service
public class RecordQualityRuleService {

    private final TRecordQualityRuleMainMapper tRecordQualityRuleMainMapper;
    private final TRecordQualityRuleItemsMapper tRecordQualityRuleItemsMapper;
    private final TRecordQualityRuleItemsMappingMapper tRecordQualityRuleItemsMappingMapper;

    public RecordQualityRuleService(TRecordQualityRuleMainMapper tRecordQualityRuleMainMapper, TRecordQualityRuleItemsMapper tRecordQualityRuleItemsMapper, TRecordQualityRuleItemsMappingMapper tRecordQualityRuleItemsMappingMapper) {
        this.tRecordQualityRuleMainMapper = tRecordQualityRuleMainMapper;
        this.tRecordQualityRuleItemsMapper = tRecordQualityRuleItemsMapper;
        this.tRecordQualityRuleItemsMappingMapper = tRecordQualityRuleItemsMappingMapper;
    }

    public Object list(RecordQualityListReq recordQualityListReq) {
        String userId = AdminUtils.getCurrentHr().getId();
        List<Object> adminRolePlatforms = AdminUtils.getCurrentHr().getAdminRolePlatforms();
        if (adminRolePlatforms != null) {
            for (int i = 0; i < adminRolePlatforms.size(); i++) {
                Object o = adminRolePlatforms.get(i);

                if (o instanceof LinkedHashMap) {
                    LinkedHashMap<String, String> rule = (LinkedHashMap<String, String>) o;
                    if (rule.get("rnameZh").contains(Constant.ADMINROLENAME)) {
                        userId = null;
                    }
                }
            }
        }

        PageHelper.startPage(recordQualityListReq.getPage(), recordQualityListReq.getLimit());
        List<TRecordQualityRuleMain> list = tRecordQualityRuleMainMapper.list(recordQualityListReq);

        if (userId != null) {
            String finalUserId = userId;
            list.forEach(tKnowledgeShare -> {
                if (StringUtils.isBlank(finalUserId) && tKnowledgeShare.getCreateUserId().equals(finalUserId) ) {
                    tKnowledgeShare.setCanDelete(1);
                    tKnowledgeShare.setCanUpdate(1);
                }else {
                    tKnowledgeShare.setCanDelete(0);
                    tKnowledgeShare.setCanUpdate(0);
                }
            });
        }


        Object layUiTablePageData = Page.getLayUiTablePageData(list);
        PageHelper.clearPage();
        return layUiTablePageData;
    }

    @Transactional(rollbackFor = Exception.class)
    public Object deleteById(String recordQualityRuleId) {
        TRecordQualityRuleMain objectById = tRecordQualityRuleMainMapper.getObjectById(recordQualityRuleId);
        if (objectById != null) {
            AdminInfo currentHr = AdminUtils.getCurrentHr();
            objectById.setRecordQualityRuleStatus(1);
            objectById.setDelTime(new Date());
            objectById.setDelUserId(currentHr.getId());
            objectById.setDelUserName(currentHr.getNameZh());
            tRecordQualityRuleMainMapper.updateByPrimaryKey(objectById);
            return ResEntity.success(objectById);
        }
        return ResEntity.error("删除失败");
    }

    /**
     * @param recordQualityRuleId
     * @return
     */
    public Object getObjectById(String recordQualityRuleId) {
        // 参数校验
        if (recordQualityRuleId == null || recordQualityRuleId.isEmpty()) {
            return ResEntity.error("缺少必要入参");
        }
        try {
            TRecordQualityRuleMain objectMapping = tRecordQualityRuleMainMapper.getObjectMapping(recordQualityRuleId);
            if (objectMapping == null) {
                return ResEntity.error("对象不存在");
            }
            /**
             * 处理返回给前端的需要格式、保持和保存的接口入参格式一致
             */
            TRecordQualityRuleMainGetOne tRecordQualityRuleMainGetOne = new TRecordQualityRuleMainGetOne();
            tRecordQualityRuleMainGetOne.setRecordQualityRuleId(objectMapping.getRecordQualityRuleId());
            tRecordQualityRuleMainGetOne.setRecordQualityRuleCalssName(objectMapping.getRecordQualityRuleCalssName());
            tRecordQualityRuleMainGetOne.setRecordQualityRuleCalssValue(objectMapping.getRecordQualityRuleCalssValue());
            tRecordQualityRuleMainGetOne.setRecordQualityRuleName(objectMapping.getRecordQualityRuleName());
            tRecordQualityRuleMainGetOne.setRecordQualityRuleCondition(objectMapping.getRecordQualityRuleCondition());
            tRecordQualityRuleMainGetOne.setRecordQualityRuleStatus(objectMapping.getRecordQualityRuleStatus());
            tRecordQualityRuleMainGetOne.setRecordQualityRuleDesc(objectMapping.getRecordQualityRuleDesc());
            tRecordQualityRuleMainGetOne.setCreateTime(objectMapping.getCreateTime());
            tRecordQualityRuleMainGetOne.setCreateUserName(objectMapping.getCreateUserName());
            tRecordQualityRuleMainGetOne.setCreateUserId(objectMapping.getCreateUserId());

            List<TRecordQualityRuleItemsSave> ruleInfoListGetOne = new ArrayList<>();
            List<TRecordQualityRuleItems> ruleInfoList = objectMapping.getRuleInfoList();
            for (TRecordQualityRuleItems ruleInfo : ruleInfoList) {
                TRecordQualityRuleItemsSave tRecordQualityRuleItemsSave = new TRecordQualityRuleItemsSave();
                tRecordQualityRuleItemsSave.setRecordQualityRuleId(ruleInfo.getRecordQualityRuleId());
                tRecordQualityRuleItemsSave.setQualityRuleItemsId(ruleInfo.getQualityRuleItemsId());
                tRecordQualityRuleItemsSave.setQualityRuleItemsClass(ruleInfo.getQualityRuleItemsClass());
                tRecordQualityRuleItemsSave.setSort(ruleInfo.getSort());
                List<TRecordQualityRuleItemsMapping> ruleInfoMapping = ruleInfo.getRuleInfoMapping();
                TRecordQualityRuleItemsMappingSave mappingSave = new TRecordQualityRuleItemsMappingSave();
                List<TRecordQualityRuleItemsMappingSelectSave> selectListOne = new ArrayList<>();
                List<TRecordQualityRuleItemsMappingSelectSave> selectListTwo = new ArrayList<>();

                // 使用有序的Map结构保持插入顺序
                Map<String, TRecordQualityRuleItemsMappingSelectSave> selectMapOne = new LinkedHashMap<>();
                Map<String, TRecordQualityRuleItemsMappingSelectSave> selectMapTwo = new LinkedHashMap<>();

                if (ruleInfoMapping != null) {
                    for (TRecordQualityRuleItemsMapping ruleInfoMapping1 : ruleInfoMapping) {
                        // 处理第一个选择列表
                        if (!selectMapOne.containsKey(ruleInfoMapping1.getId1())) {
                            selectMapOne.put(ruleInfoMapping1.getId1(),
                                    createSelectSave(ruleInfoMapping1.getId1(), ruleInfoMapping1.getName1()));
                        }

                        // 处理第二个选择列表
                        if (!selectMapTwo.containsKey(ruleInfoMapping1.getId2())) {
                            selectMapTwo.put(ruleInfoMapping1.getId2(),
                                    createSelectSave(ruleInfoMapping1.getId2(), ruleInfoMapping1.getName2()));
                        }
                    }

                    selectListOne.addAll(selectMapOne.values());
                    selectListTwo.addAll(selectMapTwo.values());
                }


                mappingSave.setSelectListOne(selectListOne);
                mappingSave.setSelectListTwo(selectListTwo);
                tRecordQualityRuleItemsSave.setRuleInfoSelectListInfo(mappingSave);
                ruleInfoListGetOne.add(tRecordQualityRuleItemsSave);
            }
            tRecordQualityRuleMainGetOne.setRuleInfoList(ruleInfoListGetOne);


            return tRecordQualityRuleMainGetOne;
        } catch (Exception e) {
            log.error("获取质量规则详情时发生异常", e);
            return ResEntity.error("服务器内部错误");
        }
    }

    /**
     * 创建selectSave对象的公共方法
     */
    private TRecordQualityRuleItemsMappingSelectSave createSelectSave(String id, String name) {
        TRecordQualityRuleItemsMappingSelectSave selectSave = new TRecordQualityRuleItemsMappingSelectSave();
        selectSave.setId(id);
        selectSave.setName(name);
        return selectSave;
    }

    @Transactional(rollbackFor = Exception.class)
    public Object updateStatus(String recordQualityRuleId, Integer recordQualityRuleStatus) {
        if (StringUtils.isBlank(recordQualityRuleId)) {
            return ResEntity.error("参数错误(缺少参数)！");
        }
        //获取单个数据
        TRecordQualityRuleMain objectById = tRecordQualityRuleMainMapper.getObjectById(recordQualityRuleId);
        if (objectById != null) {
            AdminInfo currentHr = AdminUtils.getCurrentHr();
            objectById.setRecordQualityRuleStatus(recordQualityRuleStatus);
            objectById.setUpdateTime(new Date());
            objectById.setUpdateUserId(currentHr.getId());
            objectById.setUpdateUserName(currentHr.getNameZh());
            tRecordQualityRuleMainMapper.updateByPrimaryKey(objectById);
            return ResEntity.success(objectById);
        }
        return ResEntity.error("修改失败");
    }

    /**
     * 判断是否已经维护互斥病
     *
     * @param name
     * @return
     */
    public ResEntity jugmentExist(TRecordQualityRuleMainSave tRecordQualityRuleMainSave) {
        HuChiList huChiList = new HuChiList();
        ArrayList<String> stringArrayList = new ArrayList<>();
        List<TRecordQualityRuleItemsSave> ruleInfoList = tRecordQualityRuleMainSave.getRuleInfoList();
        if (ruleInfoList != null) {
            for (TRecordQualityRuleItemsSave ruleInfo : ruleInfoList) {

                TRecordQualityRuleItemsMappingSave ruleInfoSelectListInfo = ruleInfo.getRuleInfoSelectListInfo();
                if (ruleInfoSelectListInfo != null) {
                    List<TRecordQualityRuleItemsMappingSelectSave> selectListOne = ruleInfoSelectListInfo.getSelectListOne();
                    if (selectListOne != null) {
                        selectListOne.forEach(selectListOne1 -> {
                            stringArrayList.add(selectListOne1.getId());
                        });
                    }
                    List<TRecordQualityRuleItemsMappingSelectSave> selectListTwo = ruleInfoSelectListInfo.getSelectListTwo();
                    if (selectListTwo != null) {
                        selectListTwo.forEach(selectListTwo1 -> {
                            stringArrayList.add(selectListTwo1.getId());
                        });
                    }
                }

            }
        }
        huChiList.setHuChiListOne(stringArrayList);
        huChiList.setHuChiListTwo(stringArrayList);
        huChiList.setRecordQualityRuleId(huChiList.getRecordQualityRuleId());
        List<TRecordQualityRuleItemsMapping> huChiList1 = tRecordQualityRuleItemsMappingMapper.jugmentExist(huChiList);
        huChiList1.addAll(tRecordQualityRuleItemsMappingMapper.getHuChiListF(huChiList));

        if (!huChiList1.isEmpty()) {
            StringBuilder msg = new StringBuilder();
            huChiList1.forEach(huChiList12 -> {
                msg.append(huChiList12.getName1()).append("-").append(huChiList12.getName2()).append(";").append("\n");
            });

            return ResEntity.error("请勿重复维护互斥关系：" + msg);
        }
        return ResEntity.success();

    }

    @Transactional(rollbackFor = Exception.class)
    public Object create(@Valid TRecordQualityRuleMainSave tRecordQualityRuleMainSave) {
        if (tRecordQualityRuleMainSave != null) {
            AdminInfo currentHr = AdminUtils.getCurrentHr();
            TRecordQualityRuleMain tRecordQualityRuleMain = new TRecordQualityRuleMain();
            BeanUtils.copyProperties(tRecordQualityRuleMainSave, tRecordQualityRuleMain);
            tRecordQualityRuleMain.setCreateTime(new Date());
            tRecordQualityRuleMain.setRecordQualityRuleId(IDUtil.getID());
            tRecordQualityRuleMain.setCreateUserId(currentHr.getId());
            tRecordQualityRuleMain.setCreateUserName(currentHr.getNameZh());
            List<TRecordQualityRuleItemsSave> ruleInfoList = tRecordQualityRuleMainSave.getRuleInfoList();

            ArrayList<TRecordQualityRuleItems> tRecordQualityRuleItemsArrayList = new ArrayList<>();
            ArrayList<TRecordQualityRuleItemsMapping> tRecordQualityRuleItemsMappings = new ArrayList<>();
            StringBuilder conditonStr = new StringBuilder();
            int temp = 0;
            for (TRecordQualityRuleItemsSave tRecordQualityRuleItemsSave : ruleInfoList) {
                TRecordQualityRuleItems tRecordQualityRuleItems = new TRecordQualityRuleItems();
                BeanUtils.copyProperties(tRecordQualityRuleItemsSave, tRecordQualityRuleItems);
                tRecordQualityRuleItems.setRecordQualityRuleId(tRecordQualityRuleMain.getRecordQualityRuleId());
                tRecordQualityRuleItems.setQualityRuleItemsId(IDUtil.getID());
                tRecordQualityRuleItems.setSort(temp++);
                tRecordQualityRuleItemsArrayList.add(tRecordQualityRuleItems);
                TRecordQualityRuleItemsMappingSave ruleInfoSelectListInfo = tRecordQualityRuleItemsSave.getRuleInfoSelectListInfo();
                if (ruleInfoSelectListInfo != null) {
                    ruleInfoSelectListInfo.getSelectListOne().forEach(a -> {
                        ruleInfoSelectListInfo.getSelectListTwo().forEach(b -> {
                            TRecordQualityRuleItemsMapping tRecordQualityRuleItemsMapping = new TRecordQualityRuleItemsMapping();
                            tRecordQualityRuleItemsMapping.setId1(a.getId());
                            tRecordQualityRuleItemsMapping.setName1(a.getName());
                            tRecordQualityRuleItemsMapping.setId2(b.getId());
                            tRecordQualityRuleItemsMapping.setName2(b.getName());
                            tRecordQualityRuleItemsMapping.setQualityRuleItemsId(tRecordQualityRuleItems.getQualityRuleItemsId());
                            tRecordQualityRuleItemsMappings.add(tRecordQualityRuleItemsMapping);
                            conditonStr.append(a.getName()).append("互斥").append(b.getName()).append(";");
                        });
                    });
                }
            }
            //入库
            tRecordQualityRuleMain.setRecordQualityRuleCondition(conditonStr.toString());
            tRecordQualityRuleMainMapper.insert(tRecordQualityRuleMain);
            tRecordQualityRuleItemsMapper.insertList(tRecordQualityRuleItemsArrayList);
            tRecordQualityRuleItemsMappingMapper.insertList(tRecordQualityRuleItemsMappings);
        }
        return ResEntity.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public Object update(@Valid TRecordQualityRuleMainSave tRecordQualityRuleMainSave) {
        if (tRecordQualityRuleMainSave != null) {
            AdminInfo currentHr = AdminUtils.getCurrentHr();
            TRecordQualityRuleMain tRecordQualityRuleMain = new TRecordQualityRuleMain();
            BeanUtils.copyProperties(tRecordQualityRuleMainSave, tRecordQualityRuleMain);
            tRecordQualityRuleMain.setUpdateTime(new Date());
            tRecordQualityRuleMain.setUpdateUserId(currentHr.getId());
            tRecordQualityRuleMain.setUpdateUserName(currentHr.getNameZh());
            List<TRecordQualityRuleItemsSave> ruleInfoList = tRecordQualityRuleMainSave.getRuleInfoList();

            //删除
            tRecordQualityRuleItemsMapper.deleteByRecordQualityRuleId(tRecordQualityRuleMainSave.getRecordQualityRuleId());


            ArrayList<TRecordQualityRuleItems> tRecordQualityRuleItemsArrayList = new ArrayList<>();
            ArrayList<TRecordQualityRuleItemsMapping> tRecordQualityRuleItemsMappings = new ArrayList<>();
            int temp = 0;
            StringBuilder conditonStr = new StringBuilder();
            for (TRecordQualityRuleItemsSave tRecordQualityRuleItemsSave : ruleInfoList) {
//                if (StringUtils.isBlank(tRecordQualityRuleItemsSave.getQualityRuleItemsId())) {
//                    throw new RuntimeException("参数错误(缺少参数)！");
//                }
                TRecordQualityRuleItems tRecordQualityRuleItems = new TRecordQualityRuleItems();
                BeanUtils.copyProperties(tRecordQualityRuleItemsSave, tRecordQualityRuleItems);
                tRecordQualityRuleItems.setRecordQualityRuleId(tRecordQualityRuleMain.getRecordQualityRuleId());
                tRecordQualityRuleItems.setQualityRuleItemsId(IDUtil.getID());
                tRecordQualityRuleItems.setSort(temp++);
                tRecordQualityRuleItemsArrayList.add(tRecordQualityRuleItems);
                TRecordQualityRuleItemsMappingSave ruleInfoSelectListInfo = tRecordQualityRuleItemsSave.getRuleInfoSelectListInfo();
//                tRecordQualityRuleItemsMappingMapper.deleteByQualityRuleItemsId(tRecordQualityRuleItems.getQualityRuleItemsId());
                if (ruleInfoSelectListInfo != null) {
                    ruleInfoSelectListInfo.getSelectListOne().forEach(a -> {
                        ruleInfoSelectListInfo.getSelectListTwo().forEach(b -> {
                            TRecordQualityRuleItemsMapping tRecordQualityRuleItemsMapping = new TRecordQualityRuleItemsMapping();
                            tRecordQualityRuleItemsMapping.setId1(a.getId());
                            tRecordQualityRuleItemsMapping.setName1(a.getName());
                            tRecordQualityRuleItemsMapping.setId2(b.getId());
                            tRecordQualityRuleItemsMapping.setName2(b.getName());
                            tRecordQualityRuleItemsMapping.setQualityRuleItemsId(tRecordQualityRuleItems.getQualityRuleItemsId());
                            tRecordQualityRuleItemsMappings.add(tRecordQualityRuleItemsMapping);
                            conditonStr.append(a.getName()).append("互斥").append(b.getName()).append(";");
                        });
                    });
                }
            }
            //入库
            tRecordQualityRuleMain.setRecordQualityRuleCondition(conditonStr.toString());
            tRecordQualityRuleMainMapper.updateByPrimaryKey(tRecordQualityRuleMain);
            tRecordQualityRuleItemsMapper.insertList(tRecordQualityRuleItemsArrayList);
            tRecordQualityRuleItemsMappingMapper.insertList(tRecordQualityRuleItemsMappings);
        }
        return ResEntity.success();
    }

    public Object getRuleType() {
        return QualityClass.getAllQualityClass();
    }

    public Object getSymptomList(String name) {
        return tRecordQualityRuleMainMapper.getQualitySymptom(name);
    }
}
