package com.jiuzhekan.cbkj.mapper.formula;

import com.jiuzhekan.cbkj.beans.formula.TFormula;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public interface TFormulaMapper extends BaseMapper<TFormula> {

    /**
     * 根据权限获取配方
     *
     * @param tFormula tFormula
     * @return java.util.List<com.jiuzhekan.cbkj.beans.formula.TFormula>
     * <AUTHOR>
     * @date 2021/7/21
     */
    List<TFormula> getFormulaByAuth(TFormula tFormula);

    /**
     * 判断配方是否有库存
     *
     * @param tFormula tFormula
     * @return java.lang.Integer
     * <AUTHOR>
     * @date 2021/7/21
     */
    Integer existStock(TFormula tFormula);

    /**
     * 根据ID获取配方和明细
     *
     * @param tFormula tFormula
     * @return com.jiuzhekan.cbkj.beans.formula.TFormula
     * <AUTHOR>
     * @date 2021/7/21
     */
    TFormula getFormulaDetail(TFormula tFormula);

}