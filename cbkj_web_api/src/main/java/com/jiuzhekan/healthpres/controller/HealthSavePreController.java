package com.jiuzhekan.healthpres.controller;

import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.healthpres.beans.TUserQuestionnaireResult;
import com.jiuzhekan.healthpres.beans.healthPrescribingControllerVo.GetTypesHealthPrescribing;
import com.jiuzhekan.healthpres.beans.healthPrescribingControllerVo.HealthPrescribingRequestVo;
import com.jiuzhekan.healthpres.beans.heathPresControllerVo.SaveHealthPreModelMainRequest;
import com.jiuzhekan.healthpres.service.FilesCreateService;
import com.jiuzhekan.healthpres.service.HealthSavePreService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/5/27 14:50
 * @Version 1.0
 */
@Controller
@Api(value = "健康处方（开方界面接口）", tags = "健康处方（开方界面接口）")
@RequestMapping("healthPrescribing")
public class HealthSavePreController {
    private final FilesCreateService filesCreateService;
    private final HealthSavePreService healthSavePreService;

    public HealthSavePreController(FilesCreateService filesCreateService, HealthSavePreService healthSavePreService) {
        this.filesCreateService = filesCreateService;
        this.healthSavePreService = healthSavePreService;
    }

    @RequestMapping(value = "getTypes", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(value = "获取健康类型下拉、处方内容以及明細", notes = "获取健康类型下拉、处方内容以及明細", response = GetTypesHealthPrescribing.class)
    public ResEntity getTypes(String patientId, String westDisId, String chineseDisId) {
        if (StringUtils.isBlank(patientId)) {
            return ResEntity.error("请选择患者");
        }
        if ((StringUtils.isBlank(westDisId))) {
            westDisId = "-1";
        }
        if (StringUtils.isBlank(chineseDisId)) {
            chineseDisId = "-1";
        }
        GetTypesHealthPrescribing getTypesHealthPrescribing = healthSavePreService.getTypes(patientId, westDisId, chineseDisId);
        return ResEntity.success(getTypesHealthPrescribing);
    }

//    @RequestMapping(value = "getTypeDetails", method = RequestMethod.GET)
//    @ResponseBody
//    @ApiOperation(value = "获取健康处方内容的明细", notes = "获取健康处方内容的明细", response = ResEntity.class)
//    public ResEntity getTypeDetails(String questionnaireMainId) {
//
//        return ResEntity.success();
//    }

    @RequestMapping(value = "saveHealthPre", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "开方界面保存中医保健", notes = "开方界面保存中医保健", response = ResEntity.class)
    public ResEntity saveHealthPre(@Valid @RequestBody HealthPrescribingRequestVo healthPrescribingRequestVo) {
        ResEntity o = healthSavePreService.saveHealthPre(healthPrescribingRequestVo);
        if (o.getStatus() && o.getData() instanceof TUserQuestionnaireResult) {
            TUserQuestionnaireResult tUserQuestionnaireResult = (TUserQuestionnaireResult) o.getData();
            filesCreateService.createWordPdf(tUserQuestionnaireResult.getResultId());
        }
        return o;
    }


}
