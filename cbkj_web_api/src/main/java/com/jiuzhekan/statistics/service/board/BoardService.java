package com.jiuzhekan.statistics.service.board;

import com.jiuzhekan.cbkj.beans.statistics.TStatistics;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.service.statistics.TStatisticsService;
import com.jiuzhekan.statistics.beans.register.QueryParam;
import com.jiuzhekan.statistics.service.function.TStatisticsFunction2Service;
import com.jiuzhekan.statistics.service.prescription.TStatisticsPrescriptionService;
import com.jiuzhekan.statistics.service.statistics.StatisticsService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.jiuzhekan.statistics.utils.DateUtils.getMonth;
import static com.jiuzhekan.statistics.utils.DateUtils.getWeekEndTim2e;

/**
 * <AUTHOR>
 * @Date $ $
 **/
@Service
public class BoardService {
    TStatisticsService tStatisticsService;
    private final StatisticsService statisticsService;
    private final TStatisticsPrescriptionMedicationService tStatisticsPrescriptionMedicationService;
    private final TStatisticsHealthCareService tStatisticsHealthCareService;
    private final TStatisticsFunction2Service tStatisticsFunction2Service;
    private final TStatisticsPrescriptionService tStatisticsPrescriptionService;
    private final TStatisticsPrescriptionTypeService tStatisticsPrescriptionTypeService;
    private final TStatisticsPrescriptionAuditService tStatisticsPrescriptionAuditService;

    public BoardService(TStatisticsService tStatisticsService, StatisticsService statisticsService,
                        TStatisticsPrescriptionMedicationService tStatisticsPrescriptionMedicationService,
                        TStatisticsHealthCareService tStatisticsHealthCareService,
                        TStatisticsPrescriptionTypeService tStatisticsPrescriptionTypeService,
                        TStatisticsFunction2Service tStatisticsFunction2Service, TStatisticsPrescriptionService tStatisticsPrescriptionService, TStatisticsPrescriptionAuditService tStatisticsPrescriptionAuditService) {
        this.tStatisticsService = tStatisticsService;
        this.statisticsService = statisticsService;
        this.tStatisticsPrescriptionMedicationService = tStatisticsPrescriptionMedicationService;
        this.tStatisticsHealthCareService = tStatisticsHealthCareService;
        this.tStatisticsPrescriptionTypeService = tStatisticsPrescriptionTypeService;
        this.tStatisticsFunction2Service = tStatisticsFunction2Service;
        this.tStatisticsPrescriptionService = tStatisticsPrescriptionService;
        this.tStatisticsPrescriptionAuditService = tStatisticsPrescriptionAuditService;
    }

    /**
     * @param timeType 1.昨天 6.本周 7.本月 8.本年 0全部
     * @param appId
     * @param insCode
     * @return
     * @throws ParseException
     */
    public List<Map<String,Object>> getIndexPlotDataOne(Integer timeType, String appId, String insCode) throws ParseException {
        ArrayList<Map<String,Object>> returnMap = new ArrayList<>();
        SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd");
        QueryParam queryParam = new QueryParam();
        queryParam.setAppId(appId);
        queryParam.setInsCode(insCode);
        String start = "";
        String end = "";
        if (null == timeType || 0 == timeType) {
            timeType = 0;
            start = "1970-01-01";
            end = getWeekEndTim2e(timeType, true);
        } else {
            start = getWeekEndTim2e(timeType, false);
            end = getWeekEndTim2e(timeType, true);
        }
        queryParam.setQueryStartDate(sdf2.parse(start));
        queryParam.setQueryEndDate(sdf2.parse(end));
        //中药处方量 张
//        StatisticsChangeResult data = statisticsService.getStatisticsPrescription(queryParam);
        int innerAndExternData = tStatisticsPrescriptionService.getInnerAndExterData(queryParam);
        //安全用药提醒 次
        int medicationServiceData = tStatisticsPrescriptionMedicationService.getData(queryParam);
        //医保控费提醒 次
        int healthCareServiceData = tStatisticsHealthCareService.getData(queryParam);
        //中药审方数量 张
        int prescriptionAuditServiceData = tStatisticsPrescriptionAuditService.getData(queryParam);
        //煎煮处方 张
        QueryParam queryParam2 = new QueryParam();
        BeanUtils.copyProperties(queryParam,queryParam2);
        queryParam2.setNumType(1);
        int typeServiceData = tStatisticsPrescriptionTypeService.getData(queryParam2);
        //已收费处方 张
        QueryParam queryParam3 = new QueryParam();
        BeanUtils.copyProperties(queryParam,queryParam3);
        queryParam3.setNumType(2);
        int prescriptionTypeServiceData = tStatisticsPrescriptionTypeService.getData(queryParam3);
        //知识库辅助  次
        int function2ServiceData = tStatisticsFunction2Service.getData(queryParam);
        //中医就诊人 次
        int statisticsServiceAllData = statisticsService.getAllData(queryParam);
        packageMap(returnMap,"张",innerAndExternData,1,"中药处方量","tcmNumber");
        packageMap(returnMap,"次",medicationServiceData,2,"安全用药提醒","safeMedicationNumber");
        packageMap(returnMap,"次",healthCareServiceData,3,"医保控费提醒","healthCareNumber");
        packageMap(returnMap,"张",prescriptionAuditServiceData,4,"中药审方数量","tcmPrescriptionsNumber");
        packageMap(returnMap,"张",typeServiceData,5,"煎煮中处方","decoctionPrescriptionNumber");
        packageMap(returnMap,"张",prescriptionTypeServiceData,6,"已签收处方","chargePrescriptionNumber");
        packageMap(returnMap,"次",function2ServiceData,7,"知识库辅助","knowAuxiliaryNumber");
        packageMap(returnMap,"次",statisticsServiceAllData,8,"中医就诊人次","patientsNumber");
        return returnMap;
    }
    public void packageMap(List<Map<String,Object>> sourceList,String unit,Integer number,Integer sort,String name,String key){
        HashMap<String, Object> map = new HashMap<>(8);
        map.put("unit",unit);
        map.put("number",number);
        map.put("sort",sort);
        map.put("name",name);
        sourceList.add(map);
    }

    public HashMap<Object, Object> getIndexPlotData(Integer timeType, String appId, String insCode) throws ParseException {
        HashMap<Object, Object> objectObjectHashMap = new HashMap<>(16);
        //门诊
        List<TStatistics> outTop5DisName = tStatisticsService.processData(timeType, Constant.BASIC_STRING_ONE, StringUtils.isBlank(appId) ? null : appId, StringUtils.isBlank(insCode) ? null : insCode);
        //住院
        List<TStatistics> inTop5DisName = tStatisticsService.processData(timeType, Constant.BASIC_STRING_TWO, StringUtils.isBlank(appId) ? null : appId, StringUtils.isBlank(insCode) ? null : insCode);
        QueryParam queryParam = new QueryParam();
        queryParam.setAppId( StringUtils.isBlank(appId) ? null : appId);
        queryParam.setInsCode(StringUtils.isBlank(insCode) ? null : insCode);
        SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        ArrayList<Map<String, Object>> objects = new ArrayList<>();
        ArrayList<Integer> objects2 = new ArrayList<>();
        int statisticMon = 6;
        if (timeType == 4) {
            statisticMon = 12;
        }
        List<String> month = getMonth(statisticMon);
        for (String s : month) {
            String[] split = s.split(",");
            queryParam.setQueryStartDate(sdf2.parse(split[0]+" 00:00:00"));
            queryParam.setQueryEndDate(sdf2.parse(split[1]+" 23:59:59"));
            //StatisticsChangeResult data = statisticsService.getStatisticsPrescription(queryParam);
//            int sumInnerAndExtPrescription = data.getSumInnerAndExtPrescription();
            HashMap<String, Object> stringStringHashMap = new HashMap<>(16);
            stringStringHashMap.put("month", split[0].split("-")[1]);
            stringStringHashMap.put("year", split[0].split("-")[0]);
            objects2.add(tStatisticsPrescriptionService.getInnerAndExterData(queryParam));
            objects.add(stringStringHashMap);
        }
        objectObjectHashMap.put("outTop5DisName", outTop5DisName);
        objectObjectHashMap.put("inTop5DisName", inTop5DisName);
        objectObjectHashMap.put("drugPrescriptionMonth", objects);
        objectObjectHashMap.put("drugPrescriptionDate", objects2);
        return objectObjectHashMap;
    }


}
