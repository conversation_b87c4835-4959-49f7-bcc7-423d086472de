package com.jiuzhekan.cbkj.controller.business.setting;

import com.jiuzhekan.cbkj.beans.business.record.TBusinessManual;
import com.jiuzhekan.cbkj.beans.business.setting.TBusinessAnnex;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.common.annotaionUtil.StatisticsFunction;
import com.jiuzhekan.cbkj.service.business.setting.TBusinessManualService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;

@Api(value = "操作手册接口", tags = "操作手册")
@RequestMapping(value = "tBusinessManual/")
@Controller
public class TBusinessManualController {

    @Autowired
    private TBusinessManualService tBusinessManualService;

    @RequestMapping(value = "getPages", method = RequestMethod.GET)
    @ApiOperation(value = "帮助维护 - 查询操作手册(不分页)",notes = "帮助维护里的查询操作手册(不分页,返回格式{status:true,message:'success',data:null})",response = TBusinessManual.class)
    @ResponseBody
    @LogAnnotation("帮助维护里的查询操作手册(不分页)")
    public Object getApps(TBusinessManual tBusinessManual) {
        Object obj = tBusinessManualService.getPageDatas(tBusinessManual);
        return obj;
    }

    @RequestMapping(value = "getManualAnnexS", method = RequestMethod.GET)
    @ApiOperation(value = "设置-帮助 查询操作手册附件",notes = " 设置-帮助 查询操作手册附件",response = TBusinessAnnex.class)
    @ResponseBody
    @LogAnnotation("设置-帮助 查询操作手册附件")
    public Object getManualAnnexS(TBusinessManual tBusinessManual) {
        Object obj = tBusinessManualService.getManualAnnexS(tBusinessManual);
        return obj;
    }

    @RequestMapping(value = "update/findObj",method = RequestMethod.GET)
    @LogAnnotation("加载操作手册详情")
    @ApiOperation(value = "加载操作手册详情",notes = "加载操作手册详情",response = TBusinessManual.class)
    @ResponseBody
    public Object getObj(String id) {
        ResEntity result = tBusinessManualService.findObj(id);
        return result;
    }

    @RequestMapping(value = "insert" , method = RequestMethod.POST)
    @LogAnnotation(isWrite = true, value = "新增操作手册")
    @ApiOperation(value = "新增操作手册",notes = "新增操作手册")
    @ResponseBody
    public Object insert(@RequestBody TBusinessManual tBusinessManual) {
        ResEntity result = tBusinessManualService.insert(tBusinessManual);
        return result;
    }

    @RequestMapping(value = "update",method = RequestMethod.POST)
    @LogAnnotation(isWrite = true, value = "修改操作手册")
    @ApiOperation(value = "修改操作手册",notes = "修改操作手册")
    @ResponseBody
    public Object update(@RequestBody TBusinessManual tBusinessManual) {
        ResEntity result = tBusinessManualService.update(tBusinessManual);
        return result;
    }

    @RequestMapping(value = "deleteLis",method = RequestMethod.GET)
    @LogAnnotation(value = "删除操作手册")
    @ApiOperation(value = "删除操作手册",notes = "删除操作手册")
    @ResponseBody
    public Object deleteLis(String ids) throws Exception {
        ResEntity result = tBusinessManualService.deleteLis(ids);
        return result;
    }

    @RequestMapping(value = "disableOrEnable",method = RequestMethod.GET)
    @LogAnnotation(value = "禁用/启用操作手册(单条数据操作,不支持批量)")
    @ApiOperation(value = "禁用/启用操作手册",notes = "禁用/启用操作手册(单条数据操作,不支持批量)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "操作手册主键id", dataType = "String", paramType = "query",required = true),
            @ApiImplicitParam(name = "manualType", value = "状态0启用，1禁用", dataType = "String", paramType = "query",required = true)
    })
    @ResponseBody
    public Object disableOrEnable(String id , String manualType) {
        ResEntity result = tBusinessManualService.disableOrEnable(id,manualType);
        return result;
    }

    @RequestMapping(value = "getManualFile", method = RequestMethod.GET)
    @ApiOperation(value = "根据url获取帮助手册文件流返回", notes = "根据url获取帮助手册文件流返回")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "fileUrl", value = "fileUrl", dataType = "String", paramType = "query", required = true),
            @ApiImplicitParam(name = "id", value = "帮助手册id,用于修改下载次数", dataType = "String", paramType = "query", required = true)
    })
    @LogAnnotation(value = "下载操作手册", isWrite = true)
    @StatisticsFunction(source="设置", value = "操作手册下载量")
    @ResponseBody
    public Object getFileByUrl(String fileUrl,String id, HttpServletResponse response) {
        return tBusinessManualService.getFileByUrl(fileUrl,id, response);
    }
}