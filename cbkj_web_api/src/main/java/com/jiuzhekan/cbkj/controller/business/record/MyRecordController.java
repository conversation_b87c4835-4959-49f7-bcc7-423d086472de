package com.jiuzhekan.cbkj.controller.business.record;

import com.jiuzhekan.cbkj.beans.business.record.VO.TRecordRespVO;
import com.jiuzhekan.cbkj.beans.business.record.VO.TodayPatientReqVO;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.common.annotaionUtil.StatisticsFunction;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.service.business.record.TRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@Api(value = "我的历史病历", tags = {"我的历史病历"})
public class MyRecordController {

    @Autowired
    private TRecordService tRecordService;


    @RequestMapping(value = "business/myRecord/getPages", method = RequestMethod.GET)
    @ApiOperation(value = "分页查询我的历史病例信息", notes = "病历主表中的就诊时间、疾病ID、证型ID、病人信息（病人姓名" +
            "、就诊卡号、联系方式、身份证号）。查询内容取自【病历主表】【患者表】【处方表】【配送表】，" +
            "一次就诊一条记录。年龄显示为：年龄1+年龄单位1+年龄2+年龄单位2。", response = TRecordRespVO.class)
    @ResponseBody
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orderName", value = "病人信息查询(0 病人姓名,1 就诊卡号,2 病人手机号,3 病人身份证号)", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orderValue", value = "输入框内容", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "disName", value = "疾病名称(关联病历表查询)", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "symName", value = "证型名称", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "startDate", value = "开始时间", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "endDate", value = "结束时间", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "page", value = "页码,默认1", dataType = "Int", paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "每页条数,默认10", dataType = "Int", paramType = "query")
    })
    @LogAnnotation("分页查询我的历史病例信息")
    @StatisticsFunction(source = "我的历史病历", value = "查看次数")
    public Object getApps(TodayPatientReqVO tRecord, Page page) {
        return tRecordService.getMyPageDatas(tRecord, page);
    }

    @RequestMapping(value = "business/myRecord/getMyRecDetail", method = RequestMethod.GET)
    @ApiOperation(value = "根据病历id查出单病历", notes = "根据病历id查出单病历", response = TRecordRespVO.class)
    @ApiImplicitParam(name = "recId", value = "病历id", dataType = "String", paramType = "query")
    @ResponseBody
    @LogAnnotation("分页查询患者历史病例信息")
    public Object getMyRecDetail(String recId) {
        Object obj = tRecordService.getSingleRecord(recId, null);
        return obj;
    }
}