package com.jiuzhekan.cbkj.mapper.business.record;

import com.jiuzhekan.cbkj.beans.business.record.TBusinessManual;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public interface TBusinessManualMapper extends BaseMapper<TBusinessManual> {

    long deleteByManualList(@Param("manual") TBusinessManual tBusinessManual, @Param("list") List<String> split);

    long updateDownTimes(String id);
}