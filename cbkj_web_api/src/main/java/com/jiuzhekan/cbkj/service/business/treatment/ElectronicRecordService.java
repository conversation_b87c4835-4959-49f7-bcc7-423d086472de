package com.jiuzhekan.cbkj.service.business.treatment;

import com.jiuzhekan.cbkj.beans.business.his.THisRecord;
import com.jiuzhekan.cbkj.beans.business.prescription.TRecord2Look;
import com.jiuzhekan.cbkj.beans.business.record.TRecord;
import com.jiuzhekan.cbkj.beans.business.record.TRecordDetail;
import com.jiuzhekan.cbkj.beans.business.record.TRegister;
import com.jiuzhekan.cbkj.beans.business.syndrome.TRecordMaster;
import com.jiuzhekan.cbkj.beans.business.syndrome.TRecordSyndromeAnswer;
import com.jiuzhekan.cbkj.beans.business.syndrome.TRecordSyndromeAsk;
import com.jiuzhekan.cbkj.beans.business.syndrome.TRecordSyndromeGroup;
import com.jiuzhekan.cbkj.beans.business.template.TRecordTemplate;
import com.jiuzhekan.cbkj.beans.business.template.TRecordTemplateDetail;
import com.jiuzhekan.cbkj.beans.sysBeans.AdminInfo;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import com.jiuzhekan.cbkj.mapper.business.his.THisRecordMapper;
import com.jiuzhekan.cbkj.mapper.business.prescription.TRecord2LookMapper;
import com.jiuzhekan.cbkj.mapper.business.record.TRecordDetailMapper;
import com.jiuzhekan.cbkj.mapper.business.record.TRecordMapper;
import com.jiuzhekan.cbkj.mapper.business.syndrome.TRecordMasterMapper;
import com.jiuzhekan.cbkj.mapper.business.syndrome.TRecordSyndromeGroupMapper;
import com.jiuzhekan.cbkj.mapper.business.template.TRecordTemplateDetailMapper;
import com.jiuzhekan.cbkj.mapper.business.template.TRecordTemplateMapper;
import com.jiuzhekan.cbkj.service.business.TSysParamService;
import com.jiuzhekan.cbkj.service.redis.ParameterRedisService;
import com.jiuzhekan.cbkj.service.redis.RedisService;
import com.jiuzhekan.cbkj.service.template.TRecordTemplateService;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 电子病历
 *
 * <AUTHOR>
 * @date 2020/2/18 11:31
 */
@Slf4j
@Service
public class ElectronicRecordService {

    private final TRecordMapper tRecordMapper;
    private final RecordService recordService;
    private final TRecordDetailMapper tRecordDetailMapper;
    private final TRecordTemplateMapper tRecordTemplateMapper;
    private final TRecordTemplateDetailMapper tRecordTemplateDetailMapper;
    private final TSysParamService tSysParamService;
    private final TRecordSyndromeGroupMapper tRecordSyndromeGroupMapper;
    private final TRecordTemplateService tRecordTemplateService;
    private final TRecordMasterMapper tRecordMasterMapper;
    private final THisRecordMapper tHisRecordMapper;
    private final RedisService redisService;
    private final ParameterRedisService parameterRedisService;

    private final TRecord2LookMapper tRecord2LookMapper;
    private final TempFromRecordSyndromeService tempFromRecordSyndromeService;

    @Autowired
    private ZkxcDiagnosticService zkxcDiagnosticService;
    public ElectronicRecordService(
            TempFromRecordSyndromeService tempFromRecordSyndromeService,
            THisRecordMapper tHisRecordMapper, TRecordMapper tRecordMapper, RecordService recordService, TRecordDetailMapper tRecordDetailMapper, TRecordTemplateMapper tRecordTemplateMapper, TRecordTemplateDetailMapper tRecordTemplateDetailMapper, TSysParamService tSysParamService, TRecordSyndromeGroupMapper tRecordSyndromeGroupMapper, TRecordTemplateService tRecordTemplateService, TRecordMasterMapper tRecordMasterMapper, RedisService redisService, TRecord2LookMapper tRecord2LookMapper, ParameterRedisService parameterRedisService) {
        this.tRecordMapper = tRecordMapper;
        this.recordService = recordService;
        this.tRecordDetailMapper = tRecordDetailMapper;
        this.tRecordTemplateMapper = tRecordTemplateMapper;
        this.tRecordTemplateDetailMapper = tRecordTemplateDetailMapper;
        this.tSysParamService = tSysParamService;
        this.tRecordSyndromeGroupMapper = tRecordSyndromeGroupMapper;
        this.tRecordTemplateService = tRecordTemplateService;
        this.tRecordMasterMapper = tRecordMasterMapper;
        this.tHisRecordMapper = tHisRecordMapper;
        this.redisService = redisService;
        this.tRecord2LookMapper = tRecord2LookMapper;
        this.parameterRedisService = parameterRedisService;
        this.tempFromRecordSyndromeService = tempFromRecordSyndromeService;
    }


    /**
     * 判断中医病历是否必须保存，是否已保存
     *
     * @param registerId
     * @return java.lang.Object
     * <AUTHOR>
     * @date 2020/7/8
     */
    public Object getTemplateHasSave(String registerId) {
        if (StringUtils.isBlank(registerId)) {
            return ResEntity.entity(false, "挂号ID不能为空！", null);
        }

        TRegister register = AdminUtils.getCurrentRegister();
        //2022.5.11 增加2、3
        //中医病历是否必填（1是0否 2门诊必填，住院不必填 3住院必填，门诊不必填）
        String RECORD_MUST_SAVE = tSysParamService.getSysParam(Constant.RECORD_MUST_SAVE).getParValues();
        if (Constant.BASIC_STRING_ONE.equals(RECORD_MUST_SAVE)
                || (Constant.BASIC_STRING_TWO.equals(RECORD_MUST_SAVE) && register.getClinicTypeId() == 1)
                || (Constant.BASIC_STRING_THREE.equals(RECORD_MUST_SAVE) && register.getClinicTypeId() == 2)) {
            String menuByUserId2 = parameterRedisService.getMenuByUserId2(AdminUtils.getCurrentHr().getId());
            if ("/diagnosis/questionnaire".equals(menuByUserId2)) {
                TRecord recordByRegisterId = tRecordMapper.getRecordByRegisterId(registerId);
                if (null == recordByRegisterId) {
                    return ResEntity.entity(true, Constant.SUCCESS_DX, "/diagnosis/questionnaire");
                }
                TRecord2Look byRecId = tRecord2LookMapper.findByRecId(recordByRegisterId.getRecId());
                if (null == byRecId) {
                    return ResEntity.entity(true, Constant.SUCCESS_DX, "/diagnosis/questionnaire");
                }
            } else {
                String tempId = tRecordDetailMapper.getTemplateIdByRegisterId(registerId);
                if (StringUtils.isBlank(tempId)) {
                    return ResEntity.entity(true, Constant.SUCCESS_DX, "/manage/case/record");
                }
            }

        }
        return ResEntity.entity(true, Constant.SUCCESS_DX, "");
    }

    /**
     * 获取默认电子病历模板
     *
     * @param disName
     * @return java.lang.Object
     * <AUTHOR>
     * @date 2020/2/18
     */
    public TRecordTemplate getDefalutTemplate(String disId, String disName,AdminInfo admin) {



        String appId = AdminUtils.getCurrentAppId();
        String insCode = AdminUtils.getCurrentInsCode();
        String deptId = AdminUtils.getCurrentDeptId();
        TRecordTemplate template = null;

        TRecordTemplate demo = new TRecordTemplate();
        demo.setCreateUser(admin.getId());
        demo.setIsDefault(Constant.BASIC_STRING_ONE);
        demo.setIsShare(Constant.BASIC_STRING_ZERO);
        demo.setTemplClassify(Constant.BASIC_STRING_TWO);

        demo.setDisId(disId);
        demo.setDisName(disName);
        template = tRecordTemplateMapper.getDefaultTemplate(demo);

        if (template == null) {
            demo.setIsDefault(null);
            template = tRecordTemplateMapper.getDefaultTemplate(demo);
        }
        if (template == null) {
            demo.setAppId(appId);
            demo.setInsCode(insCode);
            demo.setDeptId(deptId);
            demo.setIsShare(Constant.BASIC_STRING_ONE);
            demo.setCreateUser(null);
            demo.setIsDefault(Constant.BASIC_STRING_ONE);
            template = tRecordTemplateMapper.getDefaultTemplate(demo);
        }
        if (template == null) {
            demo.setIsDefault(null);
            template = tRecordTemplateMapper.getDefaultTemplate(demo);
        }
        if (template == null) {
            demo.setIsShare(Constant.BASIC_STRING_TWO);
            demo.setDeptId(null);
            demo.setIsDefault(Constant.BASIC_STRING_ONE);
            template = tRecordTemplateMapper.getDefaultTemplate(demo);
        }
        if (template == null) {
            demo.setIsDefault(null);
            template = tRecordTemplateMapper.getDefaultTemplate(demo);
        }
        if (template == null) {
            demo.setIsShare(Constant.BASIC_STRING_THREE);
            demo.setInsCode(null);
            demo.setIsDefault(Constant.BASIC_STRING_ONE);
            template = tRecordTemplateMapper.getDefaultTemplate(demo);
        }
        if (template == null) {
            demo.setIsDefault(null);
            template = tRecordTemplateMapper.getDefaultTemplate(demo);
        }
        if (template == null) {
            demo.setAppId(null);
            demo.setIsShare(null);
            demo.setTemplClassify(Constant.BASIC_STRING_ONE);
            demo.setIsDefault(Constant.BASIC_STRING_ONE);
            template = tRecordTemplateMapper.getDefaultTemplate(demo);
        }
        if (template == null) {
            demo.setIsDefault(null);
            template = tRecordTemplateMapper.getDefaultTemplate(demo);
        }
        if (StringUtils.isNotBlank(disId) || StringUtils.isNotBlank(disName)) {
            //疾病没查到，查询没有疾病条件的
            if (template == null) {
                demo.setCreateUser(admin.getId());
                demo.setIsDefault(Constant.BASIC_STRING_ONE);
                demo.setIsShare(Constant.BASIC_STRING_ZERO);
                demo.setTemplClassify(Constant.BASIC_STRING_TWO);
                demo.setDisId(null);
                demo.setDisName(null);
                template = tRecordTemplateMapper.getDefaultTemplate(demo);
            }
            if (template == null) {
                demo.setIsDefault(null);
                template = tRecordTemplateMapper.getDefaultTemplate(demo);
            }
            if (template == null) {
                demo.setAppId(appId);
                demo.setInsCode(insCode);
                demo.setDeptId(deptId);
                demo.setIsShare(Constant.BASIC_STRING_ONE);
                demo.setCreateUser(null);
                demo.setIsDefault(Constant.BASIC_STRING_ONE);
                template = tRecordTemplateMapper.getDefaultTemplate(demo);
            }
            if (template == null) {
                demo.setIsDefault(null);
                template = tRecordTemplateMapper.getDefaultTemplate(demo);
            }
            if (template == null) {
                demo.setIsShare(Constant.BASIC_STRING_TWO);
                demo.setDeptId(null);
                demo.setIsDefault(Constant.BASIC_STRING_ONE);
                template = tRecordTemplateMapper.getDefaultTemplate(demo);
            }
            if (template == null) {
                demo.setIsDefault(null);
                template = tRecordTemplateMapper.getDefaultTemplate(demo);
            }
            if (template == null) {
                demo.setIsShare(Constant.BASIC_STRING_THREE);
                demo.setInsCode(null);
                demo.setIsDefault(Constant.BASIC_STRING_ONE);
                template = tRecordTemplateMapper.getDefaultTemplate(demo);
            }
            if (template == null) {
                demo.setIsDefault(null);
                template = tRecordTemplateMapper.getDefaultTemplate(demo);
            }
            if (template == null) {
                demo.setAppId(null);
                demo.setIsShare(null);
                demo.setTemplClassify(Constant.BASIC_STRING_ONE);
                demo.setIsDefault(Constant.BASIC_STRING_ONE);
                template = tRecordTemplateMapper.getDefaultTemplate(demo);
            }
            if (template == null) {
                demo.setIsDefault(null);
                template = tRecordTemplateMapper.getDefaultTemplate(demo);
            }
        }

        if (template != null) {
            TRecordTemplateDetail detailModel = new TRecordTemplateDetail();
            detailModel.setTemplId(template.getTemplId());
            List<TRecordTemplateDetail> allList = tRecordTemplateDetailMapper.getPageListByObj(detailModel);

            template.setDetailList(getListToTree(allList));
        }
        return template;
    }


    /**
     * list组装成树
     *
     * @param allList
     * @return java.util.List<com.jiuzhekan.cbkj.beans.business.template.TRecordTemplateDetail>
     * <AUTHOR>
     * @date 2020/2/17
     */
    private List<TRecordTemplateDetail> getListToTree(List<TRecordTemplateDetail> allList) {
        Map<String, TRecordTemplateDetail> map = new HashMap<>();
        List<TRecordTemplateDetail> rootList = new ArrayList<>();

        for (TRecordTemplateDetail detail : allList) {
            map.put(detail.getDetailId(), detail);
        }

        for (TRecordTemplateDetail detail : allList) {

            TRecordTemplateDetail parent = map.get(detail.getDetailPid());

            if (parent != null) {

                if (parent.getChildren() == null) {
                    parent.setChildren(new ArrayList<>());
                }

                if (parent.getOptions() == null) {
                    parent.setOptions(new ArrayList<>());
                }

                if (detail.getDetailDisplay() == 10) {
                    parent.getOptions().add(detail);
                } else {
                    parent.getChildren().add(detail);
                }
            } else {
                rootList.add(detail);
            }
        }
        return rootList;
    }

    /**
     * 电子病历模板下拉列表
     *
     * @return java.lang.Object
     * <AUTHOR>
     * @date 2020/2/18
     */
    public Object getTemplateList() {

        AdminInfo admin = AdminUtils.getCurrentHr();
        TRecordTemplate tRecordTemplate = new TRecordTemplate();
        tRecordTemplate.setCreateUser(admin.getId());
        tRecordTemplate.setAppId(AdminUtils.getCurrentAppId());
        tRecordTemplate.setInsCode(AdminUtils.getCurrentInsCode());
        tRecordTemplate.setDeptId(AdminUtils.getCurrentDeptId());
        tRecordTemplate.setIsUsing(Constant.BASIC_STRING_ONE);

        List<TRecordTemplate> list = tRecordTemplateMapper.getPageListByObj(tRecordTemplate);
        List<TRecordTemplate> collect = list.stream().map(i -> {
            i.setSelfTemp(admin.getId().equals(i.getCreateUser()) ? true : false);
            return i;
        }).collect(Collectors.toList());
        return ResEntity.entity(true, Constant.SUCCESS_DX, collect);
    }


    /**
     * 保存中医电子病历
     *
     * @param tRecordTemplate
     * @param registerId
     * @param patientId
     * @return com.jiuzhekan.cbkj.beans.sysBeans.ResEntity
     * <AUTHOR>
     * @date 2020/2/24
     */
    public ResEntity save(TRecordTemplate tRecordTemplate, String registerId, String patientId) {

        if (StringUtils.isBlank(registerId)) {
            return ResEntity.entity(false, "挂号ID不能为空！", null);
        }
        if (StringUtils.isBlank(patientId)) {
            return ResEntity.entity(false, "患者ID不能为空！", null);
        }
        if (StringUtils.isBlank(tRecordTemplate.getTemplId())) {
            return ResEntity.entity(false, "模板ID不能为空！", null);
        }

        TRecord record = recordService.getRecordByRegisterId(registerId);

        List<TRecordTemplateDetail> rootList = tRecordTemplate.getDetailList();

        if (rootList == null || rootList.size() == 0) {
            return ResEntity.entity(false, "没有接收到电子病历明细！", null);
        }

        List<TRecordDetail> list = new ArrayList<>();

        iterTemplateTreeToDetailList(rootList, list, record.getRecId(), tRecordTemplate.getTemplId());

        record.setPatientContent("");
        record.setNowDesc("");
        record.setPastDesc("");
        record.setFamilyDesc("");
        record.setAllergyDesc("");
        record.setTongue("");
        record.setPulse("");
        record.setFourDiagnosis("");
        record.setPhysical("");
        record.setAuxiliaryExam("");

        for (TRecordTemplateDetail detail : rootList) {
            if (detail.getDetailType() != null) {
                if ("zs".equals(detail.getDetailType()) || "主诉".equals(replaceSpace(detail.getDetailName()))) {
                    record.setPatientContent(detail.getContent());
                } else if ("xbs".equals(detail.getDetailType()) || "现病史".equals(replaceSpace(detail.getDetailName()))) {
                    record.setNowDesc(detail.getContent());
                } else if ("jws".equals(detail.getDetailType()) || "既往史".equals(replaceSpace(detail.getDetailName()))) {
                    record.setPastDesc(detail.getContent());
                } else if ("jzs".equals(detail.getDetailType()) || "家族史".equals(replaceSpace(detail.getDetailName()))) {
                    record.setFamilyDesc(detail.getContent());
                } else if ("gms".equals(detail.getDetailType()) || "过敏史".equals(replaceSpace(detail.getDetailName()))) {
                    record.setAllergyDesc(detail.getContent());
                } else if ("wzs".equals(detail.getDetailType()) || "舌象".equals(replaceSpace(detail.getDetailName()))) {
                    record.setTongue(detail.getContent());
                } else if ("qzmz".equals(detail.getDetailType()) || "脉象".equals(replaceSpace(detail.getDetailName()))) {
                    record.setPulse(detail.getContent());
                } else if ("zysz".equals(detail.getDetailType()) || "中医四诊".equals(replaceSpace(detail.getDetailName()))) {
                    record.setFourDiagnosis(detail.getContent());
                } else if ("tgjc".equals(detail.getDetailType()) || "体格检查".equals(replaceSpace(detail.getDetailName()))) {
                    record.setPhysical(detail.getContent());
                } else if ("fzjcxm".equals(detail.getDetailType()) || "辅助检查".equals(replaceSpace(detail.getDetailName()))) {
                    record.setAuxiliaryExam(detail.getContent());
                } else if ("grs".equals(detail.getDetailType()) || "个人史".equals(replaceSpace(detail.getDetailName()))) {
                    record.setPersonalDesc(detail.getContent());
                } else if ("zlyj".equals(detail.getDetailType()) || "治疗意见".equals(replaceSpace(detail.getDetailName()))) {
                    record.setTreatmentAdvice(detail.getContent());
                }
            }
        }

        tRecordMapper.updateRecordWhenSaveElectronicRecord(record);

        tRecordDetailMapper.deleteByRecId(record.getRecId());
        if (list.size() > 0) {
            tRecordDetailMapper.insertList(list);
        }

        return ResEntity.entity(true, Constant.SUCCESS_DX, null);
    }

    private static String replaceSpace(String str) {
        return str.replaceAll("\\s+", "").replaceAll("　", "");
    }

    /**
     * 把树转化成List
     *
     * @param detailList
     * @param list
     * @return void
     * <AUTHOR>
     * @date 2020/2/12
     */
    private String iterTemplateTreeToDetailList(List<TRecordTemplateDetail> detailList, List<TRecordDetail> list, String recId, String templId) {

        StringBuilder content = new StringBuilder();

        for (TRecordTemplateDetail detail : detailList) {

            if (detail.getContent() == null) {
                detail.setContent("");
            }

            TRecordDetail recordDetail = new TRecordDetail();
            recordDetail.setRecDetailId(IDUtil.getID());
            recordDetail.setRecId(recId);
            recordDetail.setTemplId(templId);
            recordDetail.setDetailId(detail.getDetailId());
            recordDetail.setDetailName(detail.getDetailName());
            recordDetail.setDetailType(detail.getDetailType());
            recordDetail.setDetailContent(detail.getContent());
            list.add(recordDetail);


            if (detail.getDetailDisplay() == 10) {//选项

                if (Constant.BASIC_STRING_TRUE.equals(detail.getContent())) {

                    detail.setContent("");

                    if (detail.getChildren() != null && detail.getChildren().size() > 0) {

                        detail.setContent(iterTemplateTreeToDetailList(detail.getChildren(), list, recId, templId));

                        content.append(detail.getContent()).append("、");

                    } else if (detail.getOptions() != null && detail.getOptions().size() > 0) {

                        detail.setContent(iterTemplateTreeToDetailList(detail.getOptions(), list, recId, templId));

                        content.append(detail.getContent()).append("、");

                    } else {
                        content.append(replaceSpace(detail.getDetailName())).append("、");
                    }
                }
            } else if (detail.getDetailDisplay() == 4 || detail.getDetailDisplay() == 5) {

                if (detail.getOptions() != null && detail.getOptions().size() > 0) {
                    detail.setContent(iterTemplateTreeToDetailList(detail.getOptions(), list, recId, templId));
                }
                if (StringUtils.isNotBlank(detail.getContent())) {

                    if (StringUtils.isNotBlank(detail.getDetailName())) {
                        content.append(replaceSpace(detail.getDetailName())).append("：");
                    }
                    content.append(detail.getContent());
                }
            } else {

                if (detail.getChildren() != null && detail.getChildren().size() > 0) {
                    if (StringUtils.isNotBlank(detail.getContent())) {
                        String s = iterTemplateTreeToDetailList(detail.getChildren(), list, recId, templId);
                        if (StringUtils.isNotBlank(s)) {
                            s = "，" + s;
                        }
                        detail.setContent(detail.getContent() + s);
                    } else {
                        detail.setContent(iterTemplateTreeToDetailList(detail.getChildren(), list, recId, templId));
                    }
                }
                if (detail.getOptions() != null && detail.getOptions().size() > 0) {
                    iterTemplateTreeToDetailList(detail.getOptions(), list, recId, templId);
                }

                if (StringUtils.isNotBlank(detail.getContent())) {

                    if (StringUtils.isNotBlank(detail.getDetailName())) {
                        content.append(replaceSpace(detail.getDetailName())).append("：");
                    }

                    content.append(detail.getContent());

                    if (StringUtils.isNotBlank(detail.getDetailSuffix())) {
                        content.append(detail.getDetailSuffix());
                    }
                }
            }
        }

        if (content.toString().endsWith("，")) {
            content.deleteCharAt(content.length() - 1);
        }
        if (content.toString().endsWith("、")) {
            content.deleteCharAt(content.length() - 1);
        }
        if (content.length() > 0 && !content.toString().endsWith("；")) {
            content.append("；");
        }

        return content.toString();
    }

    /**
     * 病历回显
     *
     * @param recId
     * @return java.lang.Object
     * <AUTHOR>
     * @date 2020/2/19
     */
    public ResEntity getRecordDetailListByRecId(String recId) {
        if (StringUtils.isBlank(recId)) {
            return ResEntity.entity(false, "病历ID不能为空！", null);
        }
        String templId = tRecordDetailMapper.getTemplateIdByRecID(recId);

        if (StringUtils.isBlank(templId)) {
            return ResEntity.entity(false, "没有中医电子病历记录！", null);
        }

        TRecordTemplate template = tRecordTemplateMapper.getObjectById(templId);
        template.setRecId(recId);
        TRecordTemplateDetail detailModel = new TRecordTemplateDetail();
        detailModel.setTemplId(templId);
        detailModel.setRecId(recId);
        List<TRecordTemplateDetail> allList = tRecordTemplateDetailMapper.getDetailContentList(detailModel);
        if (allList != null && allList.size() > 0) {
            template.setDetailList(getListToTree(allList));
        }
        String menuByUserId2 = parameterRedisService.getMenuByUserId2(AdminUtils.getCurrentHr().getId());
        if (!StringUtils.isBlank(menuByUserId2)) {
            template.setUrl(menuByUserId2 + "/" + recId);
        }
        return ResEntity.entity(true, Constant.SUCCESS_DX, template);
    }

    /**
     * 病历回显
     *
     * @param registerId
     * @return java.lang.Object
     * <AUTHOR>
     * @date 2020/2/19
     */
    public ResEntity getRecordDetailListByRegisterId(String registerId, String disId,AdminInfo admin) {
        if (StringUtils.isBlank(registerId)) {
            return ResEntity.entity(false, "挂号ID不能为空！", null);
        }

        TRecord record = tRecordMapper.getRecordByRegisterId(registerId);

        if (record == null || StringUtils.isBlank(record.getRecId())) {
            //默认模板
            TRecordTemplate template = getDefalutTemplate(disId, null,admin);
            //智能辩证自动填充电子病历
            if (template != null) {
                tempFromRecordSyndromeService.setTempFromRecordSyndrome(template, registerId, disId);
                THisRecord hisRecordByToken = tHisRecordMapper.getHisRecordByToken(registerId);
                if (null != hisRecordByToken) {
                    for (TRecordTemplateDetail detail : template.getDetailList()) {
                        if ("主诉".equals(replaceSpace(detail.getDetailName())) && StringUtils.isNotBlank(hisRecordByToken.getPatientContent())) {
                            detail.setContent(hisRecordByToken.getPatientContent());
                        } else if ("现病史".equals(replaceSpace(detail.getDetailName())) && StringUtils.isNotBlank(hisRecordByToken.getNowDesc())) {
                            detail.setContent(hisRecordByToken.getNowDesc());
                        } else if ("既往史".equals(replaceSpace(detail.getDetailName())) && StringUtils.isNotBlank(hisRecordByToken.getPastDesc())) {
                            detail.setContent(hisRecordByToken.getPastDesc());
                        } else if ("中医四诊".equals(replaceSpace(detail.getDetailName())) && StringUtils.isNotBlank(hisRecordByToken.getFourDiagnosis())) {
                            detail.setContent(hisRecordByToken.getFourDiagnosis());
                        } else if ("体格检查".equals(replaceSpace(detail.getDetailName())) && StringUtils.isNotBlank(hisRecordByToken.getPhysical())) {
                            detail.setContent(hisRecordByToken.getPhysical());
                        } else if ("辅助检查".equals(replaceSpace(detail.getDetailName())) && StringUtils.isNotBlank(hisRecordByToken.getAuxiliaryExam())) {
                            detail.setContent(hisRecordByToken.getAuxiliaryExam());
                        }else if ("个人史".equals(replaceSpace(detail.getDetailName())) && StringUtils.isNotBlank(hisRecordByToken.getGrs())) {
                            detail.setContent(hisRecordByToken.getGrs());
                        }else if ("治疗意见".equals(replaceSpace(detail.getDetailName())) && StringUtils.isNotBlank(hisRecordByToken.getTreatmentAdvice())) {
                            detail.setContent(hisRecordByToken.getTreatmentAdvice());
                        }else if ("过敏史".equals(replaceSpace(detail.getDetailName())) && StringUtils.isNotBlank(hisRecordByToken.getGms())) {
                            detail.setContent(hisRecordByToken.getGms());
                        }
                    }
                }
                //优先级大于HIS
                for (TRecordTemplateDetail detail : template.getDetailList()) {
                    if ("中医四诊".equals(replaceSpace(detail.getDetailName()))) {
                        Object data = zkxcDiagnosticService.getData(registerId, "3_1", null,null);
                        if (null != data){
                            detail.setContent(data.toString());
                        }
                    }
                }
            }
            return ResEntity.entity(true, Constant.SUCCESS_DX, template);
        }

        String templId = tRecordDetailMapper.getTemplateIdByRecID(record.getRecId());

        if (StringUtils.isBlank(templId)) {
            //默认模板
            TRecordTemplate template = getDefalutTemplate(null, record.getDisName(),admin);
            //智能辩证自动填充电子病历
            if (template != null) {
                tempFromRecordSyndromeService.setTempFromRecordSyndrome(template, registerId, record.getDisId());
            }
            return ResEntity.entity(true, Constant.SUCCESS_DX, template);
        }

        return getRecordDetailListByRecId(record.getRecId());
    }

    /**
     * 电子病历模板详情，自动填充病历
     *
     * @param templId
     * @param registerId
     * @return com.jiuzhekan.cbkj.beans.sysBeans.ResEntity
     * <AUTHOR>
     * @date 2021/2/26
     */
    public ResEntity detail(String templId, String registerId) {

        if (StringUtils.isBlank(templId)) {
            return new ResEntity(false, "参数不能为空哦", null);
        }
        TRecordTemplate tRecordTemplate = tRecordTemplateMapper.getObjectById(templId);

        if (tRecordTemplate == null) {
            return ResEntity.error("模板不存在！");
        }

        tRecordTemplate.setDetailList(tRecordTemplateService.getTeplateDetailList(templId));

        tempFromRecordSyndromeService.setTempFromRecordSyndrome(tRecordTemplate, registerId, null);

        return new ResEntity(true, Constant.SUCCESS_DX, tRecordTemplate);

    }

















    /**
     * 保存处方时，没有填写病历时，自动填充智能辩证记录
     *
     * @param record     record
     * @param registerId registerId
     * <AUTHOR>
     * @date 2022/10/9 14:46
     */
    @Async
    public void peekSyndromeElectronicRecord(TRecord record, String registerId, String currentUserEleMenuStr,AdminInfo admin) {
        //判断是否保存电子病例
        if ("/diagnosis/questionnaire".equals(currentUserEleMenuStr)) {
            //处方一件事
        }else {
            String templId = tRecordDetailMapper.getTemplateIdByRecID(record.getRecId());
            if (StringUtils.isBlank(templId)) {
                //获取默认模板
                ResEntity tempRes = getRecordDetailListByRegisterId(registerId, record.getDisId(),admin);
                if (tempRes.getStatus() && tempRes.getData() != null && tempRes.getData() instanceof TRecordTemplate) {
                    //保存电子病例
                    TRecordTemplate temp = (TRecordTemplate) tempRes.getData();
                    save(temp, registerId, record.getPatientId());
                }
            }
        }
        //更新智能辩证记录为已保存
        tRecordSyndromeGroupMapper.hasSaveRecordByRegisterId(registerId);
    }

}