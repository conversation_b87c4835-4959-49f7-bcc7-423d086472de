package com.jiuzhekan.cbkj.controller.common;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jiuzhekan.cbkj.beans.bs.*;
import com.jiuzhekan.cbkj.beans.business.record.TRegister;
import com.jiuzhekan.cbkj.beans.drug.MatVo;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.common.http.DigitalMedicineTemplate;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.controller.common.vo.DigitalMedicineAddress;
import com.jiuzhekan.cbkj.service.common.AddressService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

@Controller
@Api(value = "地址接口", tags = "地址接口")
@RequestMapping("address")
public class AddressController {

    @Autowired
    private AddressService addressService;
    @Autowired
    private DigitalMedicineTemplate digitalMedicineTemplate;

    @RequestMapping(value = "province", method = RequestMethod.GET)
    @ApiOperation(value = "省", response = BsProvince.class)
    @ResponseBody
    @Cacheable(value = "parameter::address", keyGenerator = "cacheKeyGenerator")
    public Object getProvinceList() {
        BsProvince bsProvince = new BsProvince();
        return addressService.getProvinceList(bsProvince);
    }

    @RequestMapping(value = "city", method = RequestMethod.GET)
    @ApiOperation(value = "市", response = BsCity.class)
    @ResponseBody
    @Cacheable(value = "parameter::address", keyGenerator = "cacheKeyGenerator")
    public Object getCityList(String provinceCode) {
        BsCity bsCity = new BsCity();
        bsCity.setProvinceCode(provinceCode);
        return addressService.getCityList(bsCity);
    }

    @RequestMapping(value = "area", method = RequestMethod.GET)
    @ApiOperation(value = "区", response = BsArea.class)
    @ResponseBody
    @Cacheable(value = "parameter::address", keyGenerator = "cacheKeyGenerator")
    public Object getAreaList(String cityCode) {
        BsArea bsArea = new BsArea();
        bsArea.setCityCode(cityCode);
        return addressService.getAreaList(bsArea);
    }

    @RequestMapping(value = "street", method = RequestMethod.GET)
    @ApiOperation(value = "街道", response = BsStreet.class)
    @ResponseBody
    @Cacheable(value = "parameter::address", keyGenerator = "cacheKeyGenerator")
    public Object getStreetList(String areaCode) {
        BsStreet bsStreet = new BsStreet();
        bsStreet.setAreaCode(areaCode);
        return addressService.getStreetList(bsStreet);
    }
    @RequestMapping(value = "village", method = RequestMethod.GET)
    @ApiOperation(value = "获取行政村", notes = "获取行政村")
    @LogAnnotation(value = "获取行政村")
    @ResponseBody
    @Cacheable(value = "parameter::address", keyGenerator = "cacheKeyGenerator")
    public Object getVillages(String streetCode) {
        BsVillage bsVillage = new BsVillage();
        bsVillage.setStreetCode(streetCode);
        return addressService.getBsVillageList(bsVillage);
//        TRegister register = clientRedisService.getRegisterById(registerId);
//        TDcAddress address = tDcAddressMapper.getLastAddressByPatientId(patientId);
//
//        SysInstitution ins = orgRedisService.getSysInstitution(AdminUtils.getCurrentAppId(), AdminUtils.getCurrentInsCode());
//        return null;
    }

    @RequestMapping(value = "digital/medicine/address", method = RequestMethod.GET)
        @ApiOperation(value = "通过数字国医获取患者地址信息", response = BsStreet.class)
    @ResponseBody
    public Object getDigitalMedicineAddress() {
        TRegister register = AdminUtils.getCurrentRegister();
        if (register != null && null != register.getPatients() && "01".equals(register.getPatients().getPatientCertificateType())) {
            String patientCertificate = register.getPatients().getPatientCertificate();
            ResEntity resEntity = digitalMedicineTemplate.get("common/findAddressByCard?cardNumber="+patientCertificate);
            if (resEntity.getStatus()){
                List<DigitalMedicineAddress> addressesList = JSON.parseArray(JSON.toJSONString(resEntity.getData()), DigitalMedicineAddress.class);
                for (DigitalMedicineAddress digitalMedicineAddress : addressesList) {
                    if (!StringUtils.isBlank(digitalMedicineAddress.getDcMobile()) && !StringUtils.isBlank(digitalMedicineAddress.getDcMobile2())) {
                        digitalMedicineAddress.setDcMobile(digitalMedicineAddress.getDcMobile() + "," + digitalMedicineAddress.getDcMobile2());
                    }
                }
                return ResEntity.success(addressesList);
            }
            return ResEntity.error(resEntity.getMessage());
        }
        return ResEntity.success(null);
    }
}
