package com.jiuzhekan.managementTCMSP.service;

import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/8/18 10:24
 * @Version 1.0
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class TCMSPAdminInfoServiceTest extends TestCase {

    @Autowired
    private TCMSPAdminInfoService tcmspAdminInfoService;
    @Test
    public void testGetAppList() {
        //测试获取所有应用列表
        log.info("测试获取所有应用列表");
        Object appList = tcmspAdminInfoService.getAppList();
        log.info("获取所有应用列表成功：{}", appList);
    }
}