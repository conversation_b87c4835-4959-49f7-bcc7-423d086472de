package com.jiuzhekan.cbkj.mapper.business.record;

import com.jiuzhekan.cbkj.beans.business.record.TRegister;
import com.jiuzhekan.cbkj.beans.http.SMFParams;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

@Component
public interface TRegisterMapper extends BaseMapper<TRegister> {
    /**
     * @Description : 查询当前患者是否已经挂号了.
     * <AUTHOR> xhq
     * @updateTime : 2020/1/13 10:45
     */
    TRegister getExistRegByReg(TRegister tRegister);

    int addRegister(TRegister tRegister);

    SMFParams getSMFParams(String registerId);

    TRegister getRegisterByRegId(@Param("registerId") String registerId);

}