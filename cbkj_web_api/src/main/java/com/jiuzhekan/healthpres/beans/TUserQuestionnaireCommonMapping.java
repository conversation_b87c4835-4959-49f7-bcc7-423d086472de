package com.jiuzhekan.healthpres.beans;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class TUserQuestionnaireCommonMapping implements Serializable{

    @ApiModelProperty(value = "")
    private String commonWestId;

    @ApiModelProperty(value = "")
    private String commonDisId;

    @ApiModelProperty(value = "1.西医映射开方界面疾病2.")
    private Integer type;


}
