package com.jiuzhekan.cbkj.mapper.business.patients;

import com.jiuzhekan.cbkj.beans.business.patients.TDcAddress;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;
import org.springframework.stereotype.Component;

@Component
public interface TDcAddressMapper extends BaseMapper<TDcAddress> {

    /**
     * 根据患者街道和详细地址查询已有地址
     *
     * @param dcAddress dcAddress
     * @return com.jiuzhekan.cbkj.beans.business.patients.TDcAddress
     * <AUTHOR>
     * @date 2020/11/18
     */
    TDcAddress getDcByStreetAndAddress(TDcAddress dcAddress);

    /**
     * 查询患者最新地址
     *
     * @param patientId patientId
     * @return com.jiuzhekan.cbkj.beans.business.patients.TDcAddress
     * <AUTHOR>
     * @date 2020/11/18
     */
    TDcAddress getLastAddressByPatientId(String patientId);
    TDcAddress getLastAddressByPatientPre(String patientId);
}