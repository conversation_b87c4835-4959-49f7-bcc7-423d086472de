package com.jiuzhekan.cbkj.service.business.setting;

import com.github.pagehelper.PageHelper;
import com.jiuzhekan.cbkj.beans.business.record.TBusinessEdition;
import com.jiuzhekan.cbkj.beans.business.record.TBusinessManual;
import com.jiuzhekan.cbkj.beans.business.setting.TBusinessEditionRead;
import com.jiuzhekan.cbkj.beans.sysBeans.AdminInfo;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.mapper.business.record.TBusinessEditionMapper;
import com.jiuzhekan.cbkj.mapper.business.setting.TBusinessEditionReadMapper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
public class TBusinessEditionService {

    @Autowired
    private TBusinessEditionMapper tBusinessEditionMapper;
    @Autowired
    private TBusinessEditionReadMapper tBusinessEditionReadMapper;
    @Autowired
    private TBusinessEditionReadService tBusinessEditionReadService;

    /**
     * 加载分页数据
     *
     * @param tBusinessEdition
     * @return
     */
    public Object getPageDatas(TBusinessEdition tBusinessEdition) {
        tBusinessEdition.setIsDel((byte) 0);
        List<TBusinessEdition> list = tBusinessEditionMapper.getPageListByObj(tBusinessEdition);
        return ResEntity.entity(true, Constant.SUCCESS_DX, list);
    }

    /**
     * 插入新数据
     *
     * @param tBusinessEdition
     * @return
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insert(TBusinessEdition tBusinessEdition) {
        AdminInfo currentHr = AdminUtils.getCurrentHr();
        tBusinessEdition.setCreateUserId(currentHr.getId());
        tBusinessEdition.setCreateUserName(currentHr.getNameZh());
        tBusinessEdition.setCreateTime(new Date());
        tBusinessEdition.setIsDel((byte) 0);
        tBusinessEdition.setId(IDUtil.getID());
        long rows = tBusinessEditionMapper.insert(tBusinessEdition);
        if (rows > 0) {
            return ResEntity.entity(true, Constant.SUCCESS_DX, tBusinessEdition);
        }
        return new ResEntity(false, "保存失败，数据库异常！！", null);
    }


    /**
     * 修改
     *
     * @param tBusinessEdition
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity update(TBusinessEdition tBusinessEdition) {
        tBusinessEdition.setUpdateUserId(AdminUtils.getCurrentHr().getId());
        tBusinessEdition.setUpdateTime(new Date());
        long rows = tBusinessEditionMapper.updateByPrimaryKey(tBusinessEdition);
        if (rows > 0) {
            return ResEntity.entity(true, Constant.SUCCESS_DX, null);
        }
        return new ResEntity(false, "修改失败，数据库异常", null);
    }

    /**
     * 加载某条数据
     *
     * @param id
     * @return
     */
    public ResEntity findObj(String id) {

        if (StringUtils.isBlank(id)) {
            return new ResEntity(false, "参数不能为空哦", null);
        }
        TBusinessEdition tBusinessEdition = tBusinessEditionMapper.getObjectById(id);
        return new ResEntity(true, Constant.SUCCESS_DX, tBusinessEdition);
    }

    /**
     * 删除
     *
     * @param ids ids
     * @return ResEntity
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity deleteLis(String ids) {
        if (StringUtils.isBlank(ids)) {
            return new ResEntity(false, "参数错误(缺少参数)！", null);
        }
        List<String> split = Arrays.asList(ids.split(","));
        TBusinessEdition businessEdition = new TBusinessEdition();
        businessEdition.setDeleteTime(new Date());
        businessEdition.setDeleteUserId(AdminUtils.getCurrentHr().getId());
        businessEdition.setIsDel((byte) 1);
        long rowsR = tBusinessEditionMapper.deleteByEditionS(split, businessEdition);

        return new ResEntity(true, Constant.SUCCESS_DX, rowsR);
    }


    /**
     * 当前用户未读版本
     *
     * @return com.jiuzhekan.cbkj.beans.sysBeans.ResEntity
     * <AUTHOR>
     * @date 2021/7/16
     */
    public ResEntity unread() {

        String adminId = AdminUtils.getCurrentHr().getId();
        List<TBusinessEdition> editionList = tBusinessEditionMapper.unreadEdition(adminId);

        //异步保存阅读记录
        tBusinessEditionReadService.read(adminId, editionList);

        return ResEntity.success(editionList);
    }
}