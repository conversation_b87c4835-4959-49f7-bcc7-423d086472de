package com.jiuzhekan.recordquality.mapper;

import com.jiuzhekan.recordquality.beans.HuChiList;
import com.jiuzhekan.recordquality.beans.TRecordQualityRuleItemsMapping;
import org.springframework.stereotype.Component;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;

import java.util.List;

@Component
public interface TRecordQualityRuleItemsMappingMapper extends BaseMapper<TRecordQualityRuleItemsMapping>{


    void deleteByQualityRuleItemsId(String qualityRuleItemsId);

    /**
     * 查除了关键词之外的互斥关系 1疾病2西医3症状4关键词，疾病质控有：1-3 1-4 2-3 2-4 ，症状质控有：3-3
     * @param huChiList
     * @return
     */
    List<TRecordQualityRuleItemsMapping> getHuChiList(HuChiList huChiList);

    List<TRecordQualityRuleItemsMapping> jugmentExist(HuChiList huChiList);
    List<TRecordQualityRuleItemsMapping> getPageListByObj2();

    /**
     * 查除了关键词之外的互斥关系 1疾病2西医3症状4关键词，疾病质控有：1-3 1-4 2-3 2-4 ，症状质控有：3-3
     * 1-4 和 2-4 互斥，也适合4-4
     * @param huChiList
     * @return
     */
    List<TRecordQualityRuleItemsMapping> getHuChiListF(HuChiList huChiList);
}