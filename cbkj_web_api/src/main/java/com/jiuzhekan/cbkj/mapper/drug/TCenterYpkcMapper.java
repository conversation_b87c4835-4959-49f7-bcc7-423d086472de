package com.jiuzhekan.cbkj.mapper.drug;

import com.jiuzhekan.cbkj.beans.business.record.TPrescriptionItem;
import com.jiuzhekan.cbkj.beans.business.record.TPrescriptionPreparationItem;
import com.jiuzhekan.cbkj.beans.business.store.TDisplay;
import com.jiuzhekan.cbkj.beans.drug.CenterHisMappingVO;
import com.jiuzhekan.cbkj.beans.drug.TCenterYpkc;
import com.jiuzhekan.cbkj.beans.quartzVO.CenterInventory;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

@Component
public interface TCenterYpkcMapper extends BaseMapper<TCenterYpkc> {

    List<TCenterYpkc> getYpkcList(TCenterYpkc tCenterYpkc);

    // 药房药品库存同步使用
    void updateCenterInv(CenterInventory centerInventory);

    long insertCenterInv(List<CenterInventory> insertYpkcList);

    long updateCenterInvList(List<CenterInventory> updateYpkcList);

    /**
     * 开方检测药品库存
     *
     * @param item 处方药品明细
     * @return long
     * <AUTHOR>
     * @date 2021/1/26
     */
    BigDecimal checkCenterKcsl(TPrescriptionItem item);

    /**
     * 开方检测中药制剂库存
     *
     * @param item 中药制剂明细
     * @return long
     * <AUTHOR>
     * @date 2021/1/26
     */
    BigDecimal checkPreparationItemKcsl(TPrescriptionPreparationItem item);

    /**
     * 开方成功预扣库存
     *
     * @param item 处方药品明细
     * @return long
     * <AUTHOR>
     * @date 2021/1/26
     */
    long reduceCenterKcsl(TPrescriptionItem item);

    /**
     * 开方成功中药制剂预扣库存
     *
     * @param item 中药制剂明细
     * @return long
     * <AUTHOR>
     * @date 2021/1/26
     */
    long reducePreparationItemKcsl(TPrescriptionPreparationItem item);

    /**
     * 作废处方回退库存
     *
     * @param item 处方药品明细
     * @return long
     * <AUTHOR>
     * @date 2021/1/26
     */
    long addCenterKcsl(TPrescriptionItem item);

    /**
     * 作废处方中药制剂回退库存
     *
     * @param item 中药制剂明细
     * @return long
     * <AUTHOR>
     * @date 2021/1/26
     */
    long addPreparationItemKcsl(TPrescriptionPreparationItem item);


    /**
     * 获取药品能使用的药房ID和中药类型
     *
     * @param hisYpmlId hisYpmlId
     * @param matList   matList
     * @return List<TDisplay>
     */
    List<TDisplay> getDisplayInMatList(String hisYpmlId, List<String> matList);

    /**
     * 获取统一药品代码
     * <AUTHOR>
     * @date 2021/7/26
     */
    String getYpdmTy(String ypmlId,String ypdm);

    /**
     * 同步药品库存
     * <AUTHOR>
     * @date 2021/7/26
     */
    long insertOrUpdateList(List<TCenterYpkc> tCenterYpkc);
}