package com.jiuzhekan.statistics.utils;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @Date $ $
 **/
public class DateUtils {
    public static List<String> getMonth(int monthNum) {
        List<String> monthList = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        //1号就从上个月开始算
        int num = 1;
        if (isFirstDayOfMonth(calendar)){
            num = 0;
        }
        calendar.set(Calendar.MONTH,calendar.get(Calendar.MONTH)+num);
        for (int i = 0; i < monthNum; i++) {
            calendar.add(Calendar.MONTH, -1);
            String month = calendar.get(Calendar.YEAR)+"-"+fillZero(calendar.get(Calendar.MONTH)+1)+"-"+"01"+","+
            calendar.get(Calendar.YEAR)+"-"+fillZero(calendar.get(Calendar.MONTH)+1)+"-"+calendar.getActualMaximum(Calendar.DAY_OF_MONTH)
                    ;
            monthList.add(month);
        }
        Collections.reverse(monthList);
        return monthList;
    }

    public static List<String> getMonthOnly(int monthNum) {
        List<String> monthList = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        //1号就从上个月开始算
        int num = 1;
        if (isFirstDayOfMonth(calendar)){
            num = 0;
        }
        calendar.set(Calendar.MONTH,calendar.get(Calendar.MONTH)+num);
        for (int i = 0; i < monthNum; i++) {
            calendar.add(Calendar.MONTH, -1);
            String month = (calendar.get(Calendar.MONTH)+1)+"";
            monthList.add(month);
        }
        Collections.reverse(monthList);
        return monthList;
    }

    /**
     * 判断今天是否是1号
     * @param calendar  日历对象
     * @return          是否第一天
     */
    public static boolean isFirstDayOfMonth(Calendar calendar){
        calendar.setTime(new Date());
        calendar.set(Calendar.DATE,calendar.get(Calendar.DATE)+1);
        if(calendar.get(Calendar.DAY_OF_MONTH) == 2){
            return true;
        }else{
            return false;
        }
    }

    /**
     * 格式化月份
     */
    public static String fillZero(int i){
        String month = "";
        if(i<10){
            month = "0" + i;
        }else{
            month = String.valueOf(i);
        }
        return month;
    }

    /**
     * start
     * 本周开始时间戳 - 以星期一为本周的第一天
     * <AUTHOR> 2019-09-11
     */
    public static String getWeekStartTime(String dayStr) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat( "yyyy-MM-dd", Locale.getDefault());
        Calendar cal = Calendar.getInstance();
        int day_of_week = cal.get(Calendar. DAY_OF_WEEK) - 1;
        if (day_of_week == 0 ) {
            day_of_week = 7 ;
        }
        cal.add(Calendar.DATE , -day_of_week + 1 );
        return simpleDateFormat.format(cal.getTime());
    }

    /**
     * end
     * 本周结束时间戳 - 以星期一为本周的第一天
     */
    public static String getWeekEndTime(String type) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat( "yyyy-MM-dd", Locale.getDefault());
        Calendar cal = Calendar.getInstance();
//        cal.setTime(DateUtil.parse2Date2(dayStr));
        int day_of_week = cal.get(Calendar. DAY_OF_WEEK) - 1;
        if (day_of_week == 0 ) {
            day_of_week = 7 ;
        }
        cal.add(Calendar.DATE , -day_of_week + 7 );
        return simpleDateFormat.format(cal.getTime());
    }
    //1.昨天 6.本周 7.本月 8.本年 0全部
    public static String getWeekEndTim2e(int type,boolean isEnd) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat( "yyyy-MM-dd", Locale.getDefault());
        Calendar cal = Calendar.getInstance();
        if (type == 1){
            if (isEnd){
                cal.set(Calendar.HOUR, -13);
                cal.set(Calendar.MINUTE, 59);
                cal.set(Calendar.SECOND, 59);
            }else {
                cal.set(Calendar.HOUR, -24);
                cal.set(Calendar.MINUTE, 0);
                cal.set(Calendar.SECOND, 0);
            }
            return simpleDateFormat.format(cal.getTime());
        }
        else if (type == 6){
            int day_of_week = cal.get(Calendar.DAY_OF_WEEK) - 1;
            if (day_of_week == 0 ) {
                day_of_week = 7 ;
            }
            if (isEnd){
                cal.add(Calendar.DATE , -day_of_week + 7 );
            }else {
                cal.add(Calendar.DATE , -day_of_week + 1);
            }
            return simpleDateFormat.format(cal.getTime());
        }
        else if (type == 7){
            if (isEnd){
                cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DAY_OF_MONTH));
            }else {
                cal.set(Calendar.DAY_OF_MONTH, cal.getActualMinimum(Calendar.DAY_OF_MONTH));
            }
            return simpleDateFormat.format(cal.getTime());
        }else if (8 == type){
            if (isEnd){
                cal.set(Calendar.DAY_OF_YEAR , cal.getActualMaximum(Calendar.DAY_OF_YEAR));
            }else {
                cal.set(Calendar.DAY_OF_YEAR , cal.getActualMinimum(Calendar.DAY_OF_YEAR));
            }
            return simpleDateFormat.format(cal.getTime());
        }
        if (isEnd){
            cal.set(Calendar.HOUR, 11);
            cal.set(Calendar.MINUTE, 59);
            cal.set(Calendar.SECOND, 59);
            cal.set(Calendar.MILLISECOND, 999);
        }else {
            cal.set(Calendar.HOUR, -12);
            cal.set(Calendar.MINUTE, 0);
            cal.set(Calendar.SECOND, 0);
        }
        return simpleDateFormat.format(cal.getTime());

    }
    //1.昨天 6.本周 7.本月 8.本年 0全部
    public static void main(String[] args) {
        System.out.println("昨天");
        System.out.println(getWeekEndTim2e(1,false));
        System.out.println(getWeekEndTim2e(1,true));

        System.out.println("本周");
        System.out.println(getWeekEndTim2e(6,false));
        System.out.println(getWeekEndTim2e(6,true));
        System.out.println("本月");
        System.out.println(getWeekEndTim2e(7,false));
        System.out.println(getWeekEndTim2e(7,true));
        System.out.println("本年");
        System.out.println(getWeekEndTim2e(8,false));
        System.out.println(getWeekEndTim2e(8,true));

        System.out.println("今天");
        System.out.println(getWeekEndTim2e(0,false));
        System.out.println(getWeekEndTim2e(0,true));

        System.out.println(getMonth(6));

        System.out.println(new SimpleDateFormat("yyyy-MM-dd").format(new Date()));
    }
}
