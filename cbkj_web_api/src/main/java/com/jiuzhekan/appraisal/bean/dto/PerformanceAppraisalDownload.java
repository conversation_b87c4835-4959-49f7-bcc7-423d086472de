package com.jiuzhekan.appraisal.bean.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/27 16:29
 * @Version 1.0
 */
@ApiModel
@Data
public class PerformanceAppraisalDownload {


//    @ApiModelProperty(value = "机构appid")
//    private String appId;

    @ApiModelProperty(value = "机构代码")
    private String insCode;

    @ApiModelProperty(value = "科室代码")
    private String deptId;

    @ApiModelProperty(value = "子指标代码")
    private String zibiaodm;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date yeWuRQStart;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date yeWuRQEnd;

    @ApiModelProperty(value = "下载类型：1.主指标医疗机构2主指标科室3.子指标机构4子指标科室",required = true)
    private Integer type;


//    private Integer page;
//    private Integer limit;
}
