package com.jiuzhekan.statistics.beans.function;

import com.jiuzhekan.cbkj.beans.sysBeans.AdminInfo;
import com.jiuzhekan.cbkj.beans.sysBeans.SysAdminPractice;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@NoArgsConstructor
@ApiModel
public class TStatisticsFunction2 implements Serializable{

    @ApiModelProperty(value = "自增主键")
    private Integer id;

    @ApiModelProperty(value = "医联体ID")
    private String appId;

    @ApiModelProperty(value = "医联体")
    private String appName;

    @ApiModelProperty(value = "医疗机构代码")
    private String insCode;

    @ApiModelProperty(value = "医疗机构")
    private String insName;

    @ApiModelProperty(value = "科室ID")
    private String deptId;

    @ApiModelProperty(value = "科室")
    private String deptName;

    @ApiModelProperty(value = "用户ID")
    private String userId;

    @ApiModelProperty(value = "用户")
    private String userName;

    @ApiModelProperty(value = "分类")
    private String classify;

    @ApiModelProperty(value = "功能")
    private String function;

    @ApiModelProperty(value = "使用次数")
    private BigDecimal usageTimes;

    @ApiModelProperty(value = "统计日期")
    private Date createDate;

    @ApiModelProperty(value = "插入日期")
    private Date insertDate;



    @ApiModelProperty(value = "查询开始日期")
    private String beginDate;

    @ApiModelProperty(value = "查询结束日期")
    private String endDate;
    @ApiModelProperty(value = "是否展示图表")
    private boolean showChart;

    public TStatisticsFunction2(String appId,String insCode,String functionName, String functionSource, String beginDate, String endDate) {
        this.classify = functionName;
        this.function = functionSource;
        this.beginDate = beginDate;
        this.endDate = endDate;
        this.appId = appId;
        this.insCode = insCode;
    }
    public TStatisticsFunction2(String appId,String insCode,String functionName, String beginDate, String endDate) {
        this.classify = functionName;
        this.beginDate = beginDate;
        this.endDate = endDate;
        this.appId = appId;
        this.insCode = insCode;
    }


    public TStatisticsFunction2(String classify, String function, Date nowTime, Date nowDay, int usageTimes) {

        AdminInfo admin = AdminUtils.getCurrentHr();
        SysAdminPractice practice = AdminUtils.getCurrentPractice();

        this.appId = practice.getAppId();
        this.appName = practice.getAppName();
        this.insCode = practice.getInsCode();
        this.insName = practice.getInsName();
        this.deptId = practice.getDepId();
        this.deptName = practice.getDepName();
        this.userId = admin.getId();
        this.userName = admin.getNameZh();

        this.classify = classify;
        this.function = function;

        this.usageTimes = new BigDecimal(usageTimes);
        this.createDate = nowDay;
        this.insertDate = nowTime;
    }
}
