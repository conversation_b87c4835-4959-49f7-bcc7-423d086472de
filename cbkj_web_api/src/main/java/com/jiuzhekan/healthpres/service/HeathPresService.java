package com.jiuzhekan.healthpres.service;

import com.github.pagehelper.PageHelper;
import com.jiuzhekan.cbkj.beans.business.patients.TPatientAge;
import com.jiuzhekan.cbkj.beans.business.patients.TPatients;
import com.jiuzhekan.cbkj.beans.sysApp.SysInstitution;
import com.jiuzhekan.cbkj.beans.sysBeans.AdminInfo;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.http.KnowRestTemplate;
import com.jiuzhekan.cbkj.common.utils.*;
import com.jiuzhekan.cbkj.service.business.patients.TPatientsService;
import com.jiuzhekan.healthpres.beans.*;
import com.jiuzhekan.healthpres.beans.heathPresControllerVo.*;
import com.jiuzhekan.healthpres.mapper.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/5/26 17:30
 * @Version 1.0
 */
@Slf4j
@Service
public class HeathPresService {

    @Value("${file.address}")
    private String location;
    @Value("${root.preview}")
    private String preview;
    private final TPatientsService patientsService;

    private final TUserQuestionnaireCommonWestDisMapper tUserQuestionnaireCommonWestDisMapper;
    private final TUserQuestionnaireResultMapper tUserQuestionnaireResultMapper;
    private final TUserQuestionnaireMainMapper tUserQuestionnaireMainMapper;
    private final TUserQuestionnaireResultItemMapper tUserQuestionnaireResultItemMapper;
    private final KnowRestTemplate knowRestTemplate;
    private final TUserQuestionnaireResultAcuMapper tUserQuestionnaireResultAcuMapper;
    private final HealthTypeService healthTypeService;

    public HeathPresService(TPatientsService patientsService, TUserQuestionnaireCommonWestDisMapper tUserQuestionnaireCommonWestDisMapper, TUserQuestionnaireResultMapper tUserQuestionnaireResultMapper, TUserQuestionnaireMainMapper tUserQuestionnaireMainMapper, TUserQuestionnaireResultItemMapper tUserQuestionnaireResultItemMapper, KnowRestTemplate knowRestTemplate, TUserQuestionnaireResultAcuMapper tUserQuestionnaireResultAcuMapper, HealthTypeService healthTypeService) {
        this.patientsService = patientsService;
        this.tUserQuestionnaireCommonWestDisMapper = tUserQuestionnaireCommonWestDisMapper;
        this.tUserQuestionnaireResultMapper = tUserQuestionnaireResultMapper;
        this.tUserQuestionnaireMainMapper = tUserQuestionnaireMainMapper;
        this.tUserQuestionnaireResultItemMapper = tUserQuestionnaireResultItemMapper;
        this.knowRestTemplate = knowRestTemplate;
        this.tUserQuestionnaireResultAcuMapper = tUserQuestionnaireResultAcuMapper;
        this.healthTypeService = healthTypeService;
    }

    public ArrayList<Object> getChildHeathTypes(String patientId) {
        ArrayList<Object> objects = new ArrayList<>();
        TPatients tPatients = patientsService.findById(patientId);
        if (tPatients == null) {
            return objects;
        }
        //通过证件号获取年龄
        TPatientAge patientAge = TPatientsService.getAgeMonthByBirth(tPatients.getPatientBirthday());
        //获取年龄：12岁或者是12月
        Short age2 = patientAge.getAge2();


        HashMap<String, Object> re1 = new HashMap<>();
        HashMap<String, Object> re2 = new HashMap<>();
        HashMap<String, Object> re3 = new HashMap<>();
        re1.put("questionnaireMainSecondTypeCode", "1-1");
        re1.put("questionnaireMainSecondTypeName", "6、12月龄儿童");//0<age<15月
        re1.put("recommend", false);

        re2.put("questionnaireMainSecondTypeCode", "1-2");
        re2.put("questionnaireMainSecondTypeName", "18、24月龄儿童");//15<=age<27
        re2.put("recommend", false);
        re3.put("questionnaireMainSecondTypeCode", "1-3");
        re3.put("questionnaireMainSecondTypeName", "30、36月龄儿童");//27<=age<=39
        re3.put("recommend", false);

        if (age2 < 18) {
            re1.put("recommend", true);
        } else if (age2 < 30) {
            re2.put("recommend", true);
        } else if (age2 <= 39) {
            re3.put("recommend", true);
        }

        objects.add(re1);
        objects.add(re2);
        objects.add(re3);
        return objects;
    }

    //    private static HashMap<String, Object> getStringObjectHashMap(TUserQuestionnaireCommonWestDis tUserQuestionnaireCommonWestDis, List<GetRecommendByPatient> recommendByPatient) {
//        HashMap<String, Object> re1 = new HashMap<>();
//        re1.put("questionnaireMainSecondTypeCode", tUserQuestionnaireCommonWestDis.getCommonWestId());
//        re1.put("questionnaireMainSecondTypeName", tUserQuestionnaireCommonWestDis.getCommonWestName());//0<age<15月
//        //判断tUserQuestionnaireCommonWestDis.getCommonWestId()在recommendByPatient中是否存在
//        if (recommendByPatient != null && !recommendByPatient.isEmpty() && recommendByPatient.contains(tUserQuestionnaireCommonWestDis.getCommonWestId()))
//        {
//            re1.put("recommend", true);
//        }else{
//            re1.put("recommend", false);
//        }
//        return re1;
//    }
//    public static void main(String[] args) {
//        LinkedHashMap<String, String> healthType = healthTypeService.getHealthType();
//        healthType.forEach((k, v) -> {
//            System.out.print("k = " + k);
//            System.out.println("v = " + v);
//        });
//    }

    public ArrayList<Object> getManDisHeathTypes(String patientId) {
        //       ArrayList<Object> objects = new ArrayList<>();
//        TUserQuestionnaireCommonWestDis dis = new TUserQuestionnaireCommonWestDis();
//        dis.setStatus(0);
//        List<TUserQuestionnaireCommonWestDis> pageListByObj = tUserQuestionnaireCommonWestDisMapper.getPageListByObj(dis);
        //返回推荐
        RecommendSearchQuery recommendSearchQuery = new RecommendSearchQuery();
        recommendSearchQuery.setPatientId(patientId);
        List<com.jiuzhekan.healthpres.beans.GetRecommendByPatient> recommendByPatient = tUserQuestionnaireCommonWestDisMapper.getRecommendByPatient(recommendSearchQuery);
        LinkedHashMap<String, String> healthType = healthTypeService.getHealthType();
        healthType.remove("1-1");
        healthType.remove("1-2");
        healthType.remove("1-3");
        healthType.remove("3-1");
        ArrayList<Object> objects = new ArrayList<>();
        healthType.forEach((k, v) -> {
            HashMap<String, Object> re1 = new HashMap<>();
            re1.put("questionnaireMainSecondTypeCode", k);
            re1.put("questionnaireMainSecondTypeName", v);
            if (recommendByPatient != null && !recommendByPatient.isEmpty()) {
                recommendByPatient.forEach(item -> {
                    if (item.getClassType().equals(k)) {
                        re1.put("recommend", true);
                    }
                });
            }
            if (!re1.containsKey("recommend")) {
                re1.put("recommend", false);
            }
            objects.add(re1);
        });
        //        if (pageListByObj != null && !pageListByObj.isEmpty()) {
//            for (TUserQuestionnaireCommonWestDis tUserQuestionnaireCommonWestDis : pageListByObj) {
//                HashMap<String, Object> re1 = getStringObjectHashMap(tUserQuestionnaireCommonWestDis, recommendByPatient);
//                objects.add(re1);
//            }
//        }
        return objects;
    }

    /**
     * @param childIndexPageListVo
     * @return 获取中医保健患者列表
     */
    public Object getChildIndexPageList(ChildIndexPageListVo childIndexPageListVo) {
        PageHelper.startPage(childIndexPageListVo.getPage(), childIndexPageListVo.getLimit());

        List<GetChildIndexPageList> list = tUserQuestionnaireResultMapper.getChildIndexPageList(childIndexPageListVo);
        PageHelper.clearPage();
        if (list != null && !list.isEmpty()) {
            list.forEach(
                    getChildIndexPageList -> {
                        TPatientAge ageByBirth = TPatientsService.getAgeByBirth(getChildIndexPageList.getPatientBirthDay());
                        getChildIndexPageList.setPatientAge(ageByBirth.getAge());
                    }
            );
        }
        return Page.getLayUiTablePageData(list);
    }

    /**
     * @param getDetailPageListRequest
     * @return 获取中医保健患者详细列表
     */
    public Object getDetailPageList(GetDetailPageListRequest getDetailPageListRequest) {
        String id = AdminUtils.getCurrentHr().getId();

        PageHelper.startPage(getDetailPageListRequest.getPage(), getDetailPageListRequest.getLimit());
        List<GetDetailPageListResponse> list = tUserQuestionnaireResultMapper.getDetailPageList(getDetailPageListRequest);
        PageHelper.clearPage();

        if (list != null && !list.isEmpty()) {
            list.forEach(
                    getChildIndexPageList -> {
                        TPatientAge ageByBirth = TPatientsService.getAgeByBirth(getChildIndexPageList.getPatientBirthDay());
                        getChildIndexPageList.setPatientAge(ageByBirth.getAge());
                        if (StringUtils.isNotBlank(getChildIndexPageList.getCreateDoctorId())
                                && getChildIndexPageList.getCreateDoctorId().equals(id)
                        ) {
                            getChildIndexPageList.setCanUpdate(true);
                            getChildIndexPageList.setCanDelete(true);
                        } else {
                            getChildIndexPageList.setCanUpdate(false);
                            getChildIndexPageList.setCanDelete(false);
                        }
                    }
            );
        }

        return Page.getLayUiTablePageData(list);
    }

    /**
     * @param getHealthPreModel
     * @return 获取中医保健模板详情
     */
    public Object getHealthPreModel(GetHealthPreModel getHealthPreModel) {
        HealthPreModelMainResponse healthPreModelMainResponse = tUserQuestionnaireMainMapper.getHealthPreModel(getHealthPreModel);
        List<HealthPreModelMainItemResponse> questionnaireMainItemList = healthPreModelMainResponse.getQuestionnaireMainItemList();
        questionnaireMainItemList.forEach(item -> {
            if (item.getItemType() == 2 && StringUtils.isNotBlank(item.getItemSelectDefaultId())) {
                String[] split = item.getItemSelectDefaultId().split(",");
                List<TUserQuestionnaireResultAcu> a = getACU(split);
                item.setLookHealthPreMainItemAcuResponseList(a);
            }

        });
        return healthPreModelMainResponse;
    }

    public List<TUserQuestionnaireResultAcu> getACU(String[] acuid) {
        ArrayList<TUserQuestionnaireResultAcu> list = new ArrayList<>();
        for (int i = 0; i < acuid.length; i++) {
            Map<String, Object> map = new HashMap<>();
            map.put("acuid", acuid[i]);
            ResEntity<HashMap<String, Object>> resEntity = knowRestTemplate.postKnow("acupoint/detail", map);
            HashMap<String, Object> data = resEntity.getData();

            TUserQuestionnaireResultAcu tUserQuestionnaireResultAcu = new TUserQuestionnaireResultAcu();
            tUserQuestionnaireResultAcu.setAcuCode(data.get("acuCode").toString());
            tUserQuestionnaireResultAcu.setAcuName(data.get("acuname").toString());
            tUserQuestionnaireResultAcu.setAcuImg(data.get("acuimg").toString());
            tUserQuestionnaireResultAcu.setAcuPosition(data.get("acuposotion").toString());
            tUserQuestionnaireResultAcu.setAcuId(data.get("acuid").toString());
            list.add(tUserQuestionnaireResultAcu);
        }


        return list;
    }

    @Transactional(rollbackFor = Exception.class)
    public ResEntity createHealthPre(SaveHealthPreModelMainRequest saveHealthPreModelMainRequest) {
        AdminInfo currentHr = AdminUtils.getCurrentHr();

        String appId = AdminUtils.getCurrentAppId();
        String insCode = AdminUtils.getCurrentInsCode();
        String currentDeptId = AdminUtils.getCurrentDeptId();


        TPatients patients = patientsService.findById(saveHealthPreModelMainRequest.getPatientId());
        if (patients == null){
            return ResEntity.error("未找到患者信息");
        }
        SysInstitution sysInstitution = patientsService.getSysInstitution();

        List<SaveHealthPreModelMainItemRequest> itemList = saveHealthPreModelMainRequest.getSaveQuestionnaireMainItemList();

        TUserQuestionnaireResult tUserQuestionnaireResult = new TUserQuestionnaireResult();
        tUserQuestionnaireResult.setResultCode(MakeOrderNum.makeBzNum());
        tUserQuestionnaireResult.setResultId(IDUtil.getID());
        tUserQuestionnaireResult.setInsName(sysInstitution.getInsName());
        tUserQuestionnaireResult.setAppName(sysInstitution.getAppName());
        if (saveHealthPreModelMainRequest.getQuestionnaireMainType() == 1) {
            tUserQuestionnaireResult.setResultReportName(saveHealthPreModelMainRequest.getQuestionnaireMainSecondTypeName() + "中医保健报告");
            tUserQuestionnaireResult.setResultReportTypeInfo(saveHealthPreModelMainRequest.getQuestionnaireMainSecondTypeName() + "保健");

        } else if (saveHealthPreModelMainRequest.getQuestionnaireMainType() == 2) {
            tUserQuestionnaireResult.setResultReportName(saveHealthPreModelMainRequest.getQuestionnaireMainSecondTypeName() + "中医慢病调理报告");
            tUserQuestionnaireResult.setResultReportTypeInfo(saveHealthPreModelMainRequest.getQuestionnaireMainSecondTypeName() + "中医慢病调理");
        } else {
            return ResEntity.entity(false, "请选择正确的模板类型", null);
        }

        tUserQuestionnaireResult.setCreateDoctorId(currentHr.getId());
        tUserQuestionnaireResult.setCreateDoctorName(currentHr.getNameZh());
        tUserQuestionnaireResult.setInsertTime(new Date());
        tUserQuestionnaireResult.setStatus(0);
        tUserQuestionnaireResult.setPatientId(saveHealthPreModelMainRequest.getPatientId());
        tUserQuestionnaireResult.setPatientName(patients.getPatientName());
        tUserQuestionnaireResult.setQuestionnaireMainFirstLine(saveHealthPreModelMainRequest.getQuestionnaireMainFirstLine());
        tUserQuestionnaireResult.setQuestionnaireMainType(saveHealthPreModelMainRequest.getQuestionnaireMainType());
        tUserQuestionnaireResult.setQuestionnaireMainSecondTypeCode(saveHealthPreModelMainRequest.getQuestionnaireMainSecondTypeCode());
        tUserQuestionnaireResult.setQuestionnaireMainSecondTypeName(saveHealthPreModelMainRequest.getQuestionnaireMainSecondTypeName());
        tUserQuestionnaireResult.setQuestionnaireMainTitle(saveHealthPreModelMainRequest.getQuestionnaireMainTitle());
        tUserQuestionnaireResult.setQuestionnaireMainTips(saveHealthPreModelMainRequest.getQuestionnaireMainTips());
        tUserQuestionnaireResult.setResultReportTime(new Date());
        if (!Constant.BASIC_APP_ID.equals(appId)) {
            tUserQuestionnaireResult.setAppId(appId);
        }
        if (!Constant.BASIC_INS_CODE.equals(insCode)) {
            tUserQuestionnaireResult.setInsCode(insCode);
        }
        if (!Constant.BASIC_DEPT_ID.equals(currentDeptId)) {
            tUserQuestionnaireResult.setDeptId(currentDeptId);
        }


        tUserQuestionnaireResultMapper.insert(tUserQuestionnaireResult);

        ArrayList<TUserQuestionnaireResultItem> itemArrayList = new ArrayList<>();
        ArrayList<TUserQuestionnaireResultAcu> itemACUArrayList = new ArrayList<>();
        itemList.forEach(item -> {
            TUserQuestionnaireResultItem tUserQuestionnaireResultItem = new TUserQuestionnaireResultItem();
            tUserQuestionnaireResultItem.setResultId(tUserQuestionnaireResult.getResultId());
            tUserQuestionnaireResultItem.setResultItemContent(item.getItemContent());
            tUserQuestionnaireResultItem.setResultItemSort(item.getSort());
            tUserQuestionnaireResultItem.setResultItemTitle(item.getItemTitle());
            tUserQuestionnaireResultItem.setResultItemType(item.getItemType());
            tUserQuestionnaireResultItem.setResultItemId(IDUtil.getID());
            tUserQuestionnaireResultItem.setShowType(item.getShowType());
//            tUserQuestionnaireResultItem.setResultItemSelectId(item.getItemSelectDefaultId());
            if (item.getItemType() == 2 && item.getQuestionnaireResultAcuList() != null) {
                for (SaveResultAcu saveResultAcu : item.getQuestionnaireResultAcuList()) {
                    if (saveResultAcu.getAcuId() != null){
                        TUserQuestionnaireResultAcu resultItem = new TUserQuestionnaireResultAcu();
                        BeanUtils.copyProperties(saveResultAcu, resultItem);
                        resultItem.setResultAcuId(IDUtil.getID());
                        resultItem.setResultItemId(tUserQuestionnaireResultItem.getResultItemId());
                        resultItem.setResultId(tUserQuestionnaireResult.getResultId());
                        itemACUArrayList.add(resultItem);
                    }

                }

            }
            itemArrayList.add(tUserQuestionnaireResultItem);
        });
        tUserQuestionnaireResultItemMapper.insertList(itemArrayList);
        if (!itemACUArrayList.isEmpty()) {
            tUserQuestionnaireResultAcuMapper.insertList(itemACUArrayList);
        }
        return ResEntity.success(tUserQuestionnaireResult);
    }

    public Object getReportList(GetReportListRequestVo getReportListRequestVo) {
        PageHelper.startPage(getReportListRequestVo.getPage(), getReportListRequestVo.getLimit());
        List<GetDetailPageListResponse> reportList = tUserQuestionnaireResultMapper.getReportList(getReportListRequestVo);
        reportList.forEach(report -> {
            if (report.getPatientBirthDay() != null) {
                TPatientAge ageByBirth = TPatientsService.getAgeByBirth(report.getPatientBirthDay());
                if (ageByBirth != null) {
                    report.setPatientAge(ageByBirth.getAge());
                }
            }

        });
        Object layUiTablePageData = Page.getLayUiTablePageData(reportList);
        PageHelper.clearPage();
        return layUiTablePageData;
    }

    public Object getAllTypes() {
        LinkedHashMap<String, String> healthType = healthTypeService.getHealthType();
        //对于key不是1.1 1.2 1.3 的所有desc值加上“中医慢病调理”这六个字
        healthType.forEach((key, value) -> {
            if (!key.startsWith("1-")) {
                healthType.put(key, value + "中医慢病调理");
            }
        });
        return healthType;
    }

    public Object lookHealthPre(String resultId) {
        LookHealthPreMainResponse lookHealthPreMainResponse = tUserQuestionnaireResultMapper.lookHealthPre(resultId);
        return lookHealthPreMainResponse;
    }

    @Transactional(rollbackFor = Exception.class)
    public Object editHealthPre(SaveHealthPreModelMainRequest saveHealthPreModelMainRequest) {
        String resultId = saveHealthPreModelMainRequest.getResultId();
        if (StringUtils.isBlank(resultId)) {
            return ResEntity.entity(false, "缺少resultId", null);
        }
        AdminInfo currentHr = AdminUtils.getCurrentHr();
        TUserQuestionnaireResult objectById = tUserQuestionnaireResultMapper.getObjectById(resultId);
        if (objectById == null) {
            return ResEntity.entity(false, "没有此报告", null);
        }
        objectById.setLastUpdateDoctorId(currentHr.getId());
        objectById.setLastUpdateDoctorName(currentHr.getNameZh());
        objectById.setLastUpdateTime(new Date());
        objectById.setQuestionnaireMainFirstLine(saveHealthPreModelMainRequest.getQuestionnaireMainFirstLine());
        objectById.setQuestionnaireMainTips(saveHealthPreModelMainRequest.getQuestionnaireMainTips());
        tUserQuestionnaireResultMapper.updateByPrimaryKey(objectById);

        tUserQuestionnaireResultItemMapper.deleteByMainResultId(saveHealthPreModelMainRequest.getResultId());

        ArrayList<TUserQuestionnaireResultItem> itemArrayList = new ArrayList<>();
        ArrayList<TUserQuestionnaireResultAcu> itemACUArrayList = new ArrayList<>();
        tUserQuestionnaireResultAcuMapper.deleteByRersultId(objectById.getResultId());
        saveHealthPreModelMainRequest.getSaveQuestionnaireMainItemList().forEach(item -> {
            TUserQuestionnaireResultItem tUserQuestionnaireResultItem = new TUserQuestionnaireResultItem();
            tUserQuestionnaireResultItem.setResultId(saveHealthPreModelMainRequest.getResultId());
            tUserQuestionnaireResultItem.setResultItemContent(item.getItemContent());
            tUserQuestionnaireResultItem.setResultItemSort(item.getSort());
            tUserQuestionnaireResultItem.setResultItemTitle(item.getItemTitle());
            tUserQuestionnaireResultItem.setResultItemType(item.getItemType());
//            tUserQuestionnaireResultItem.setResultItemSelectId(item.getItemSelectDefaultId());
            tUserQuestionnaireResultItem.setResultItemId(IDUtil.getID());
            itemArrayList.add(tUserQuestionnaireResultItem);
            if (item.getItemType() == 2 && null != item.getQuestionnaireResultAcuList()) {
                for (SaveResultAcu saveResultAcu : item.getQuestionnaireResultAcuList()) {
                    TUserQuestionnaireResultAcu resultItem = new TUserQuestionnaireResultAcu();
                    BeanUtils.copyProperties(saveResultAcu, resultItem);
                    resultItem.setResultAcuId(IDUtil.getID());
                    resultItem.setResultItemId(tUserQuestionnaireResultItem.getResultItemId());
                    resultItem.setResultId(objectById.getResultId());
                    itemACUArrayList.add(resultItem);
                }

            }
        });
        if (!itemACUArrayList.isEmpty()) {
            tUserQuestionnaireResultAcuMapper.insertList(itemACUArrayList);
        }
        tUserQuestionnaireResultItemMapper.insertList(itemArrayList);
        return ResEntity.success();
    }

    public Object deleteHealthPre(String resultId) {
        if (StringUtils.isBlank(resultId)) {
            return ResEntity.entity(false, "缺少resultId", null);
        }
        AdminInfo currentHr = AdminUtils.getCurrentHr();
        TUserQuestionnaireResult objectById = tUserQuestionnaireResultMapper.getObjectById(resultId);
        if (objectById == null) {
            return ResEntity.entity(false, "没有此报告", null);
        }
        objectById.setDelDoctorId(currentHr.getId());
        objectById.setDelDoctorName(currentHr.getNameZh());
        objectById.setDelTime(new Date());
        objectById.setStatus(1);
        tUserQuestionnaireResultMapper.updateByPrimaryKey(objectById);
        return ResEntity.success();
    }

    public void downloadResult(HttpServletRequest req, HttpServletResponse resp, String resultId, String fileType) throws UnsupportedEncodingException {
        if (StringUtils.isBlank(resultId)){
            log.error("缺少resultId");
            return;
        }
        try {
            req.setCharacterEncoding("utf-8");
        } catch (UnsupportedEncodingException e) {
            log.error("设置请求编码失败", e);
            return;
        }
        String id = AdminUtils.getCurrentHr().getId()+resultId;
        synchronized (id.intern()) {
            TUserQuestionnaireResult objectById = tUserQuestionnaireResultMapper.getObjectById(resultId);
            if (objectById == null || StringUtils.isBlank(objectById.getWordPath())){
                log.warn("下载报告结果,未找到对应结果ID: {}", resultId);
            }
            // 增加空值检查
            if (objectById == null) {
                log.warn("未找到对应的结果ID: {}", resultId);
                return;
            }

            String name = objectById.getPatientName() + objectById.getResultReportName();

            String fileName;
            if ("PDF".equals(fileType)) {
                fileName = sanitizePath(objectById.getPdfPath().replace(preview, location));
                name += ".pdf";
            } else if ("Word".equals(fileType)) {
                fileName = sanitizePath(objectById.getWordPath().replace(preview, location));
                name += ".doc";
            } else {
                return;
            }
            String downloadFilenameNew = "filename*=utf-8''" +
                    URLEncoder.encode(name, "UTF-8")
                            .replaceAll("\\+", "%20");

            try (InputStream fin = Files.newInputStream(Paths.get(fileName));
                 ServletOutputStream out = resp.getOutputStream()) {
                // 先清空可能的缓冲
                resp.resetBuffer();
                String agent = req.getHeader("USER-AGENT");

                if (agent != null && (agent.contains("MSIE") || agent.contains("Trident"))) {
                    // IE浏览器
                    name = java.net.URLEncoder.encode(name, "UTF8");
                } else if (agent != null && agent.contains("Mozilla")) {
                    // 火狐,chrome等浏览器
                    name = java.net.URLEncoder.encode(name, "UTF-8");
                }

                resp.setCharacterEncoding("utf-8");
//                resp.setContentType("application/msword");
                resp.setContentType("application/octet-stream");
                resp.addHeader("Content-Disposition", "attachment;"+downloadFilenameNew+";filename=" + name);

                byte[] buffer = new byte[512];
                int bytesToRead;
                while ((bytesToRead = fin.read(buffer)) != -1) {
                    out.write(buffer, 0, bytesToRead);
                }
                out.flush();
            } catch (IOException e) {
                resp.reset();  // 发生异常时重置响应
                log.error("下载文件异常, 文件名: {}, 错误详情: {}", fileName, e.getMessage(), e);
                try {
                    resp.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                } catch (IOException ex) {
                    throw new RuntimeException(ex);
                }
            }
        }
    }

    // 防止路径遍历攻击
    private String sanitizePath(String path) {
        // 简单示例，实际应根据具体需求实现更严格的校验
        if (path.contains("..") || path.contains(File.separator + "." + File.separator)) {
            throw new IllegalArgumentException("非法路径: " + path);
        }
        return path;
    }

}
