package com.jiuzhekan.healthpres.beans;

import java.util.ArrayList;
import java.util.LinkedHashMap;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/6/3 11:14
 * @Version 1.0
 */
public enum HealthType {

    MAN_DIS_THREE_ONE("3-1", "中医健康处方"),
    CHILD_HEALTH_PRE_ONE("1-1", "6、12月龄儿童保健"),
    CHILD_HEALTH_PRE_TWO("1-2", "18、24月龄儿童保健"),
    //30、36月龄儿童保健
    CHILD_HEALTH_PRE_THREE("1-3", "30、36月龄儿童保健"),
    //高血压、糖尿病前期、慢性阻塞性肺疾病、非酒精性单纯性脂肪肝、围绝经期综合征（更年期综合征）、哮喘、慢性心力衰竭、缺血性脑卒中（大动脉粥样硬化型）、血管性痴呆、胃癌前病变、食管癌前病变、结直肠癌前病变
    MAN_DIS_ONE("2-1", "高血压"),
    MAN_DIS_TWO("2-2", "糖尿病前期"),
    MAN_DIS_THREE("2-3", "慢性阻塞性肺疾病"),
    MAN_DIS_FOUR("2-4", "非酒精性单纯性脂肪肝"),
    MAN_DIS_FIVE("2-5", "围绝经期综合征"),
    MAN_DIS_SIX("2-6", "哮喘"),
    MAN_DIS_SEVEN("2-7", "慢性心力衰竭"),
    MAN_DIS_EIGHT("2-8", "缺血性脑卒中"),
    MAN_DIS_NINE("2-9", "血管性痴呆"),
    MAN_DIS_TEN("2-10", "胃癌前病变"),
    MAN_DIS_ELEVEN("2-11", "食管癌前病变"),
    MAN_DIS_TWELVE("2-12", "结直肠癌前病变")
    ;


    private String type;
    private String desc;

    HealthType(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }
    //提供一个方法，按照顺序返回type和desc，用List返回
    public static LinkedHashMap<String, String> getHealthType() {
        LinkedHashMap<String, String> map = new LinkedHashMap<>();
        for (HealthType healthType : HealthType.values()) {
            map.put(healthType.type, healthType.desc);
         }
          return map;
    }
}
