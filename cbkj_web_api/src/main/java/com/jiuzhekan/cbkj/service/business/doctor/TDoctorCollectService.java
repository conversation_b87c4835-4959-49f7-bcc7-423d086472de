package com.jiuzhekan.cbkj.service.business.doctor;

import com.github.pagehelper.PageHelper;
import com.jiuzhekan.cbkj.beans.business.doctor.TDoctorCollect;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.mapper.business.doctor.TDoctorCollectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Slf4j
@Service
public class TDoctorCollectService {

    @Autowired
    private TDoctorCollectMapper tDoctorCollectMapper;

    /**
     * 加载分页数据
     *
     * @param tDoctorCollect
     * @param page
     * @return
     */
    public Object getPageDatas(TDoctorCollect tDoctorCollect, Page page) {
        PageHelper.startPage(page.getPage(), page.getLimit());
        List<TDoctorCollect> list = tDoctorCollectMapper.getPageListByObj(tDoctorCollect);
        Object result = Page.getLayUiTablePageData(list);
        return result;
    }

    public Object getMyCollectLitByModule(TDoctorCollect tDoctorCollect, Page page) {
        tDoctorCollect.setCreateUser(AdminUtils.getCurrentHr().getId());
        PageHelper.startPage(page.getPage(), page.getLimit());
        List<TDoctorCollect> list = tDoctorCollectMapper.getPageListByObj(tDoctorCollect);
        Object result = Page.getLayUiTablePageData(list);
        return result;
    }

    /**
     * 插入新数据
     *
     * @param tDoctorCollect
     * @return
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insert(TDoctorCollect tDoctorCollect) {
        if (StringUtils.isBlank(tDoctorCollect.getConnectId())) {
            return ResEntity.entity(false, "关联id为空!", tDoctorCollect);
        }
        if (null == tDoctorCollect.getCollectModule()) {
            return ResEntity.entity(false, "模块标志为空!", tDoctorCollect);
        }
        TDoctorCollect tDoctorCollect1 = new TDoctorCollect();
        tDoctorCollect1.setCollectModule(tDoctorCollect.getCollectModule());
        tDoctorCollect1.setCreateUser(AdminUtils.getCurrentHr().getId());
        tDoctorCollect1.setConnectId(tDoctorCollect.getConnectId());

        int size = tDoctorCollectMapper.getPageListByObj(tDoctorCollect1).size();
        if (size > 0) {
            return ResEntity.entity(false, "已经收藏了!", tDoctorCollect);
        }
        tDoctorCollect.setCollectId(IDUtil.getID());
        tDoctorCollect.setCreateUser(AdminUtils.getCurrentHr().getId());
        tDoctorCollect.setCreateDate(new Date());
        long row = tDoctorCollectMapper.insert(tDoctorCollect);
        if (StringUtils.isNotBlank(tDoctorCollect.getCollectId())) {
            return ResEntity.entity(true, Constant.SUCCESS_DX, tDoctorCollect);
        }
        return new ResEntity(false, "保存失败，数据库异常！！", null);
    }


    /**
     * 修改
     *
     * @param tDoctorCollect
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity update(TDoctorCollect tDoctorCollect) {
        if (StringUtils.isBlank(tDoctorCollect.getCollectId())) {
            return ResEntity.entity(false, "收藏id为空!", tDoctorCollect);
        }
        long rows = tDoctorCollectMapper.updateByPrimaryKey(tDoctorCollect);
        return ResEntity.entity(true, Constant.SUCCESS_DX, null);
    }

    /**
     * 加载某条数据
     *
     * @param collectId
     * @return
     */
    public ResEntity findObj(String collectId) {

        if (StringUtils.isBlank(collectId)) {
            return new ResEntity(false, "参数不能为空哦", null);
        }
        TDoctorCollect tDoctorCollect = tDoctorCollectMapper.getObjectById(collectId);
        return new ResEntity(true, Constant.SUCCESS_DX, tDoctorCollect);
    }

    /**
     * 删除
     *
     * @param ids
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity deleteLis(String ids) {
        if (StringUtils.isBlank(ids)) {
            return new ResEntity(false, "参数错误(缺少参数)！", null);
        }
        long rowsR = tDoctorCollectMapper.deleteBylist(ids.split(","));

        return new ResEntity(true, Constant.SUCCESS_DX, rowsR);
    }

    /**
     * @param tDoctorCollect :
     * @return : com.jiuzhekan.cbkj.beans.sysBeans.ResEntity
     * @Description :
     * <AUTHOR> xhq
     * @updateTime : 2020/6/18 17:24
     */
    public ResEntity deleteByObj(TDoctorCollect tDoctorCollect) {
        if (StringUtils.isBlank(tDoctorCollect.getCollectId())
                && (StringUtils.isBlank(tDoctorCollect.getConnectId()))) {
            // 如果主键id和关联id都为空,就无法删除
            return ResEntity.entity(false, "参数不全", tDoctorCollect);
        }
        tDoctorCollect.setCreateUser(AdminUtils.getCurrentHr().getId());
        long row = tDoctorCollectMapper.deleteByObj(tDoctorCollect);
        return ResEntity.entity(true, Constant.SUCCESS_DX, tDoctorCollect);
    }

    /**
     * 根据关联id和登录人查询是否收藏,后台获取登录人
     *
     * @param tDoctorCollect
     * @return
     */
    public Object getCollectByObj(TDoctorCollect tDoctorCollect) {
        if (StringUtils.isBlank(tDoctorCollect.getConnectId())) {
            return ResEntity.entity(false, "关联id没传", tDoctorCollect);
        }
        tDoctorCollect.setCreateUser(AdminUtils.getCurrentHr().getId());
        TDoctorCollect resCollect = tDoctorCollectMapper.getCollectByObj(tDoctorCollect);
        return ResEntity.entity(true, Constant.SUCCESS_DX, resCollect);
    }

    /**
     * 根据关联id和登录人查询是否收藏,后台获取登录人
     *
     * @param connectIds 关联ID逗号拼接
     * @return
     */
    public Object getCollectByConnectIds(String connectIds) {
        Map<String, Boolean> result = new HashMap<>();
        if (StringUtils.isNotBlank(connectIds)) {
            String[] split = connectIds.split(Constant.ENGLISH_COMMA);
            String[] split1 = split[0].split("-");
            List<String> existConnectIds = null;
            TDoctorCollect tDoctorCollect = new TDoctorCollect();
            tDoctorCollect.setCreateUser(AdminUtils.getCurrentHr().getId());
            tDoctorCollect.setConnectId(connectIds);
            if (split1.length == Constant.ADMIN_EXT_INTRODUCTION) {
                StringBuilder stringBuffer = new StringBuilder("");
                String temp = "";
                int tempNum = 0;
                for (String value : split) {
                    String s = value.split("-")[1];
                    if ("".equals(temp)) {
                        temp = s;
                    }
                    if (temp.equals(s)) {
                        tempNum++;
                    }
                    stringBuffer.append("-").append(s).append("|");
                }
                if (tempNum == split.length) {
                    //说明都是同一个疾病 不同诊疗方案
                    existConnectIds = tDoctorCollectMapper.getExistCollectByConnectIds(tDoctorCollect);
                } else {
                    String s = stringBuffer.toString();
                    tDoctorCollect.setConnectId(s.substring(0, s.length() - 1));
                    existConnectIds = tDoctorCollectMapper.getGuideExistCollectByConnectIds(tDoctorCollect);
                    List<String> existConnectIds2 = new ArrayList<>();
                    for (int i = 0; i < existConnectIds.size(); i++) {
                        String s1 = existConnectIds.get(i).split("-")[1];
                        for (String connectId : connectIds.split(Constant.ENGLISH_COMMA)) {
                            if (connectId.contains(s1)) {
                                existConnectIds2.add(connectId);
                                break;
                            }
                        }
                    }
                    //existConnectIds.clear();
                    existConnectIds.addAll(existConnectIds2);
                }
            } else {
                log.info("getCollectByConnectIds: connectId: " + connectIds);
                existConnectIds = tDoctorCollectMapper.getExistCollectByConnectIds(tDoctorCollect);
            }
            for (String connectId : connectIds.split(Constant.ENGLISH_COMMA)) {
                if (existConnectIds.contains(connectId)) {
                    result.put(connectId, true);
                } else {
                    result.put(connectId, false);
                }
            }
        }

        return ResEntity.entity(true, Constant.SUCCESS_DX, result);
    }


    public List<String> getByConnectIds(String connectIds) {
        List<String> existConnectIds = null;
        if (StringUtils.isNotBlank(connectIds)) {
            TDoctorCollect tDoctorCollect = new TDoctorCollect();
            tDoctorCollect.setCreateUser(AdminUtils.getCurrentHr().getId());
            tDoctorCollect.setConnectId(connectIds);
            existConnectIds = tDoctorCollectMapper.getExistCollectByConnectIds(tDoctorCollect);
        }
        return existConnectIds;
    }

    public List<TDoctorCollect> getByAllCollect(TDoctorCollect tDoctorCollect) {
        tDoctorCollect.setCreateUser(AdminUtils.getCurrentHr().getId());
        return tDoctorCollectMapper.getPageListByObj(tDoctorCollect);
    }


    /**
     * 判断知识库数据是否收藏（详情接口）
     *
     * @param resEntity 知识库详情
     * @param id        详情ID
     * @return com.jiuzhekan.cbkj.beans.sysBeans.ResEntity
     * <AUTHOR>
     * @date 2022/3/14
     */
    public ResEntity knowDataIsCollect(ResEntity resEntity, String id) {
        LinkedHashMap<String, Object> data = (LinkedHashMap<String, Object>) resEntity.getData();
        TDoctorCollect tDoctorCollect = new TDoctorCollect();
        tDoctorCollect.setCollectId(id);
        tDoctorCollect.setCreateUser(AdminUtils.getCurrentHr().getId());
        TDoctorCollect resCollect = tDoctorCollectMapper.getCollectByObj(tDoctorCollect);
        data.put("isCollect", resCollect == null ? 0 : 1);
        return resEntity;
    }

    /**
     * 判断知识库数据是否收藏（列表接口）
     *
     * @param resEntity 知识库数据列表
     * @param listName  列表字段名
     * @param idName    ID字段名
     * @return com.jiuzhekan.cbkj.beans.sysBeans.ResEntity
     * <AUTHOR>
     * @date 2022/3/14
     */
    public ResEntity knowDataIsCollect(ResEntity resEntity, String listName, String idName) {
        LinkedHashMap<String, Object> data1 = (LinkedHashMap<String, Object>) resEntity.getData();
        ArrayList<LinkedHashMap<String, Object>> dataList = (ArrayList<LinkedHashMap<String, Object>>) data1.get(listName);
        List<String> connectIds = new ArrayList<>();
        String[] split = idName.split(",");
        for (LinkedHashMap<String, Object> data : dataList) {
            if (split.length > 1) {
                connectIds.add(data.get(split[0]).toString() + "-" + data.get(split[1]).toString());
            } else {
                connectIds.add(data.get(idName).toString());
            }
        }
        Map<String, Integer> connectIdMap = getMapByConnectIds(connectIds);
        for (LinkedHashMap<String, Object> data : dataList) {
            if (split.length > 1) {
                data.put("isCollect", connectIdMap.get(data.get(split[0]).toString() + "-" + data.get(split[1]).toString()));
            } else {
                data.put("isCollect", connectIdMap.get(data.get(idName).toString()));
            }
        }
        return resEntity;
    }

    private Map<String, Integer> getMapByConnectIds(List<String> connectIds) {
        Map<String, Integer> map = new HashMap<>();
        if (connectIds != null && connectIds.size() > 0) {
            TDoctorCollect tDoctorCollect = new TDoctorCollect();
            tDoctorCollect.setCreateUser(AdminUtils.getCurrentHr().getId());
            tDoctorCollect.setConnectId(StringUtils.join(connectIds.toArray(), ","));
            List<String> existConnectIds = tDoctorCollectMapper.getExistCollectByConnectIds(tDoctorCollect);
            for (String connectId : existConnectIds) {
                map.put(connectId, 1);
            }

            for (String cId : connectIds) {
                map.putIfAbsent(cId, 0);
            }
        }
        return map;
    }

    public Object getListJoinTKnowledgeShare(HashMap<String, String> tDoctorCollect, Page page) {
        PageHelper.startPage(page.getPage(), page.getLimit());
        List<Map<String, String>> list = tDoctorCollectMapper.getListJoinTKnowledgeShare(tDoctorCollect);
        Object layUiTablePageData = Page.getLayUiTablePageData(list);
        PageHelper.clearPage();
        return layUiTablePageData;
    }
}