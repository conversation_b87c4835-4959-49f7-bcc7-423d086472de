package com.jiuzhekan.cbkj.service.business.record;

import com.jiuzhekan.cbkj.beans.business.record.TRecordDetail;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.mapper.business.record.TRecordDetailMapper;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class TRecordDetailService {

    @Autowired
    private TRecordDetailMapper tRecordDetailMapper;

    /**
     * 加载分页数据
     * @param tRecordDetail
     * @param page
     * @return
     */
    public Object getPageDatas(TRecordDetail tRecordDetail, Page page) {
        PageHelper.startPage(page.getPage(), page.getLimit());
        List<TRecordDetail> list = tRecordDetailMapper.getPageListByObj(tRecordDetail);
        Object result = Page.getLayUiTablePageData(list);
        return result;
    }

    /**
     * 插入新数据
     * @param tRecordDetail
     * @return
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insert(TRecordDetail tRecordDetail){
        tRecordDetail.setRecDetailId(IDUtil.getID());
        long rows = tRecordDetailMapper.insert(tRecordDetail);
        if(rows > 0){
            return ResEntity.entity(true, Constant.SUCCESS_DX,tRecordDetail);
        }
        return new ResEntity(false,"保存失败，数据库异常！！",null);
    }


    /**
     * 修改
     * @param tRecordDetail
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity update(TRecordDetail tRecordDetail) {

        long rows = tRecordDetailMapper.updateByPrimaryKey(tRecordDetail);
        if(rows >0){
            return ResEntity.entity(true,Constant.SUCCESS_DX,null);
        }
        return new ResEntity(false,"修改失败，数据库异常",null);
    }

    /**
     * 加载某条数据
     * @param recDetailId
     * @return
     */
    public ResEntity findObj(String recDetailId) {

        if(StringUtils.isBlank(recDetailId)){
            return new ResEntity(false,"参数不能为空哦",null);
        }
        TRecordDetail tRecordDetail = tRecordDetailMapper.getObjectById(recDetailId);
        return new ResEntity(true,Constant.SUCCESS_DX,tRecordDetail);
    }

    /**
     * 删除
     * @param ids
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity deleteLis(String ids){
        if(StringUtils.isBlank(ids)){
            return new ResEntity(false,"参数错误(缺少参数)！",null);
        }
        long rowsR = tRecordDetailMapper.deleteBylist(ids.split(","));

        return new ResEntity(true,Constant.SUCCESS_DX,rowsR);
    }
}