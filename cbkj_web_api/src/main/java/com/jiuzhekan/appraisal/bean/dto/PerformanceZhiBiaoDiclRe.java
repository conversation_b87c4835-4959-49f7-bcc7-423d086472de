package com.jiuzhekan.appraisal.bean.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/27 16:29
 * @Version 1.0
 */
@Data
public class PerformanceZhiBiaoDiclRe {
    @ApiModelProperty(value = "指标类型：1主指标2子指标")
    private String zhiBiaoType;

}
