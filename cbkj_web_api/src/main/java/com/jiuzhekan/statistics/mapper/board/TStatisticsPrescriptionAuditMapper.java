package com.jiuzhekan.statistics.mapper.board;

import com.jiuzhekan.statistics.beans.board.TStatisticsPrescriptionAudit;
import com.jiuzhekan.statistics.beans.board.TStatisticsPrescriptionType;
import com.jiuzhekan.statistics.beans.register.QueryParam;
import org.springframework.stereotype.Component;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;

import java.util.List;
import java.util.Map;

@Component
public interface TStatisticsPrescriptionAuditMapper extends BaseMapper<TStatisticsPrescriptionAudit>{
    List<TStatisticsPrescriptionAudit> getStaticsResult(Map<String, Object> param);

    Integer sumStatistics(QueryParam queryParam);
}