package com.jiuzhekan.statistics.mapper.prescription;

import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;
import com.jiuzhekan.statistics.beans.prescription.StatisticsPrescription;
import com.jiuzhekan.statistics.beans.prescription.TStatisticsPrescription;
import com.jiuzhekan.statistics.beans.register.QueryParam;
import com.jiuzhekan.statistics.beans.register.StatisticsRegister;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public interface TStatisticsPrescriptionMapper extends BaseMapper<TStatisticsPrescription>{
    /**
     * 统计
     * @param param
     * @return
     */
    List<StatisticsPrescription> selectStatisticsPrescriptionResult(Map<String, Object> param);
    List<StatisticsPrescription> selectStatisticsPrescriptionResultCDSS(Map<String, Object> param);
    /**
     * 返回昨日退费的操作所对应的处方。
     * @param param
     * @return
     */
    List<StatisticsPrescription> selectStatisticsRegisterSplit(Map<String, Object> param);
    /**
     * 插入统计
     * @param list
     * @return
     */
    int insertStatisticsPrescription(List<StatisticsPrescription> list);
    int insertStatisticsPrescriptionCDSS(List<StatisticsPrescription> list);
    int updateByPrimaryKeyCDSS(TStatisticsPrescription tStatisticsPrescription);

    /**
     * 获取最新一条
     * @return
     */
    TStatisticsPrescription getLastData();


    /**
     * 获取内服和外用数据
     * @param queryParam
     * @return
     */
    Integer getInnerAndExterData(QueryParam queryParam);

//    TStatisticsPrescription getChangeNumber(TStatisticsPrescription tStatisticsPrescription);
}