package com.jiuzhekan.appraisal.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/17 16:49
 * @Version 1.0
 */
@ApiModel
@Data
public class PerformanceSubClassIns {

    @ApiModelProperty(value = "机构代码")
    private String JiGouDM;

    @ApiModelProperty(value = "机构名称")
    private String JiGouMC;

    @ApiModelProperty(value = "子指标：机构下的所有指标以及指标值")
    List<JxkhZibiaoSubClassRes> zibiaoList;
}
