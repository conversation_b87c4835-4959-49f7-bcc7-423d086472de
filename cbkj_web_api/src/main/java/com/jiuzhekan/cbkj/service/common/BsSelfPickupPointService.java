package com.jiuzhekan.cbkj.service.common;

import com.github.pagehelper.PageHelper;
import com.jiuzhekan.cbkj.beans.bs.BsSelfPickupPoint;
import com.jiuzhekan.cbkj.beans.bs.designateddelivery.DesignatedDeliveryListRe;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.mapper.bs.BsSelfPickupPointMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/1 09:28
 * @Version 1.0
 */
@Service
public class BsSelfPickupPointService {

    private final BsSelfPickupPointMapper bsSelfPickupPointMapper;

    public BsSelfPickupPointService(BsSelfPickupPointMapper bsSelfPickupPointMapper) {
        this.bsSelfPickupPointMapper = bsSelfPickupPointMapper;
    }

    public Object getDeliveryDataList(DesignatedDeliveryListRe designatedDeliveryListRe, Page page) {
        PageHelper.startPage(page.getPage(), page.getLimit());

        List<BsSelfPickupPoint> list = bsSelfPickupPointMapper.getDeliveryDataList(designatedDeliveryListRe);

        return Page.getLayUiTablePageData(list);
    }
}
