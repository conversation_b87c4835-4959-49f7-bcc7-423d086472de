package com.jiuzhekan.knowledgeshare.service;

import com.github.pagehelper.PageHelper;
import com.jiuzhekan.cbkj.beans.sysBeans.AdminInfo;
import com.jiuzhekan.cbkj.beans.sysBeans.AdminRolePlatform;
import com.jiuzhekan.cbkj.beans.sysBeans.AdminRule;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.knowledgeshare.beans.ChineseMedicineListReq;
import com.jiuzhekan.knowledgeshare.beans.ShareListReq;
import com.jiuzhekan.knowledgeshare.beans.TKnowledgeShare;
import com.jiuzhekan.knowledgeshare.mapper.TKnowledgeShareMapper;
import lombok.Data;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.validation.Valid;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/7/1 10:05
 * @Version 1.0
 */
@Service
public class TKnowledgeShareService {

    private final TKnowledgeShareMapper tKnowledgeShareMapper;

    public TKnowledgeShareService(TKnowledgeShareMapper tKnowledgeShareMapper) {
        this.tKnowledgeShareMapper = tKnowledgeShareMapper;
    }

    public Object createShare(@Valid TKnowledgeShare tKnowledgeShare) {
        tKnowledgeShare.setKnowledgeShareId(IDUtil.getID());
        tKnowledgeShare.setShareTime(new Date());
        tKnowledgeShare.setShareStatus(0);
        tKnowledgeShare.setShareUserId(AdminUtils.getCurrentHr().getId());
        tKnowledgeShare.setShareUserName(AdminUtils.getCurrentHr().getNameZh());
        tKnowledgeShareMapper.insert(tKnowledgeShare);
        return ResEntity.success();
    }

    public Object getShareList(ShareListReq shareListReq) {
        String userId = AdminUtils.getCurrentHr().getId();
        List<Object> adminRolePlatforms = AdminUtils.getCurrentHr().getAdminRolePlatforms();
        if (adminRolePlatforms != null) {
            for (int i = 0; i < adminRolePlatforms.size(); i++) {
                Object o = adminRolePlatforms.get(i);

                if (o instanceof LinkedHashMap) {
                    LinkedHashMap<String, String> rule = (LinkedHashMap<String, String>) o;
                    if (rule.get("rnameZh").contains(Constant.ADMINROLENAME)) {
                        userId = null;
                    }
                }
            }
        }
        PageHelper.startPage(shareListReq.getPage(), shareListReq.getLimit());
        List<TKnowledgeShare> list = tKnowledgeShareMapper.getShareList(shareListReq);
        if (userId != null) {
            String finalUserId = userId;
            list.forEach(tKnowledgeShare -> {
                if (StringUtils.isBlank(finalUserId) && tKnowledgeShare.getShareUserId().equals(finalUserId) ) {
                    tKnowledgeShare.setCanDelete(1);
                    tKnowledgeShare.setCanUpdate(1);
                }else {
                    tKnowledgeShare.setCanDelete(0);
                    tKnowledgeShare.setCanUpdate(0);
                }
            });
        }
        Object layUiTablePageData = Page.getLayUiTablePageData(list);
        PageHelper.clearPage();
        return layUiTablePageData;
    }

    public Object getObjectById(String shareId) {
        TKnowledgeShare objectById = tKnowledgeShareMapper.getObjectById(shareId);
        return objectById;
    }

    public Object updateShare(@Valid TKnowledgeShare tKnowledgeShare) {
        TKnowledgeShare objectById = tKnowledgeShareMapper.getObjectById(tKnowledgeShare.getKnowledgeShareId());
        if (objectById == null) {
            return ResEntity.error("数据不存在");
        }
        AdminInfo currentHr = AdminUtils.getCurrentHr();
        String userId = currentHr.getId();
        //判断当前用户的role中是否有管理字段

        List<Object> adminRolePlatforms = currentHr.getAdminRolePlatforms();
        if (adminRolePlatforms != null) {
            for (int i = 0; i < adminRolePlatforms.size(); i++) {
                Object o = adminRolePlatforms.get(i);

                if (o instanceof LinkedHashMap) {
                    LinkedHashMap<String, String> rule = (LinkedHashMap<String, String>) o;
                    if (rule.get("rnameZh").contains(Constant.ADMINROLENAME)) {
                        userId = null;
                    }
                }
//                String roleName = adminRolePlatforms.get(i).getRoleName();
//                if (roleName.contains(Constant.ADMINROLENAME)) {
//                    userId = null;
//                }
            }
        }
        if (userId != null) {
            if (!userId.equals(objectById.getShareUserId())) {
                return ResEntity.error("权限不足");
            }
        }
//        if (adminRolePlatforms.stream().noneMatch(rule -> rule.getRnameZh().contains(Constant.ADMINROLENAME)) &&
//                !currentHr.getId().equals(objectById.getShareUserId())) {
//            return ResEntity.error("权限不足");
//        }
        BeanUtils.copyProperties(tKnowledgeShare, objectById);
        tKnowledgeShare.setUpdateUserId(AdminUtils.getCurrentHr().getId());
        tKnowledgeShare.setUpdateUserTime(new Date());
        tKnowledgeShare.setShareTime(new  Date());
        tKnowledgeShare.setUpdateUserName(AdminUtils.getCurrentHr().getNameZh());
        tKnowledgeShare.setShareStatus(tKnowledgeShare.getShareStatus());

        tKnowledgeShareMapper.updateByPrimaryKey(tKnowledgeShare);
        return ResEntity.success();
    }

    public Object deleteShare(String shareId) {
        TKnowledgeShare objectById = tKnowledgeShareMapper.getObjectById(shareId);
        if (objectById == null) {
            return ResEntity.error("数据不存在");
        }
        AdminInfo currentHr = AdminUtils.getCurrentHr();
        String userId = currentHr.getId();
//        //判断当前用户的role中是否有管理字段
//        if (AdminUtils.getCurrentHr().getRoles().stream().noneMatch(rule -> rule.getRnameZh().contains(Constant.ADMINROLENAME)) &&
//                !AdminUtils.getCurrentHr().getId().equals(objectById.getShareUserId())) {
//            return ResEntity.error("权限不足");
//        }
        List<Object> adminRolePlatforms = currentHr.getAdminRolePlatforms();
        if (adminRolePlatforms != null) {
            for (int i = 0; i < adminRolePlatforms.size(); i++) {
                Object o = adminRolePlatforms.get(i);

                if (o instanceof LinkedHashMap) {
                    LinkedHashMap<String, String> rule = (LinkedHashMap<String, String>) o;
                    if (rule.get("rnameZh").contains(Constant.ADMINROLENAME)) {
                        userId = null;
                    }
                }
//                String roleName = adminRolePlatforms.get(i).getRoleName();
//                if (roleName.contains(Constant.ADMINROLENAME)) {
//                    userId = null;
//                }
            }
        }
        if (userId != null) {
            if (!userId.equals(objectById.getShareUserId())) {
                return ResEntity.error("权限不足");
            }
        }
        objectById.setShareStatus(1);
        objectById.setDelUserId(AdminUtils.getCurrentHr().getId());
        objectById.setDelUserName(AdminUtils.getCurrentHr().getNameZh());
        objectById.setDelTime(new Date());
        tKnowledgeShareMapper.updateByPrimaryKey(objectById);
        return ResEntity.success();
    }

    public Object getChineseMedicineList(ChineseMedicineListReq bookName, Page page) {
        PageHelper.startPage(page.getPage(), page.getLimit());
        List<TKnowledgeShare> chineseMedicineList = tKnowledgeShareMapper.getChineseMedicineList(bookName);
        Object layUiTablePageData = Page.getLayUiTablePageData(chineseMedicineList);
        PageHelper.clearPage();
        return layUiTablePageData;
    }

    public Object getSharedLiteratureList(ChineseMedicineListReq bookName, Page page) {
        PageHelper.startPage(page.getPage(), page.getLimit());
        List<TKnowledgeShare> sharedLiteratureList = tKnowledgeShareMapper.getSharedLiteratureList(bookName);
        Object layUiTablePageData = Page.getLayUiTablePageData(sharedLiteratureList);
        PageHelper.clearPage();
        return layUiTablePageData;
    }
}
