package com.jiuzhekan.cbkj.common.utils.word;

import freemarker.template.Configuration;
import freemarker.template.Template;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;

public class WordGenerator {

    private static Configuration configuration;

    static {
        configuration = new Configuration(Configuration.DEFAULT_INCOMPATIBLE_IMPROVEMENTS);
        configuration.setDefaultEncoding("utf-8");
        configuration.setClassForTemplateLoading(WordGenerator.class, "/templates");
    }

    private WordGenerator() {
        throw new AssertionError();
    }

    public static File createDoc(Map<?, ?> dataMap) {
        Template template;
        try {
            template = configuration.getTemplate("analysisTemplate.ftl");
        } catch (IOException e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        String name = "analysisTemplate" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".doc";
        File f = new File(name);

        try {
            // 这个地方不能使用FileWriter因为需要指定编码类型否则生成的Word文档会因为有无法识别的编码而无法打开
            Writer w = new OutputStreamWriter(new FileOutputStream(f), StandardCharsets.UTF_8);
            template.process(dataMap, w);
            w.close();
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new RuntimeException(ex);
        }
        return f;
    }

    public static File createDoc(String filePath, Map<?, ?> dataMap) {
        Template template;
        try {
            template = configuration.getTemplate("analysisTemplate.ftl");
        } catch (IOException e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }

        File f = new File(filePath);

        try {
            // 这个地方不能使用FileWriter因为需要指定编码类型否则生成的Word文档会因为有无法识别的编码而无法打开
            Writer w = new OutputStreamWriter(new FileOutputStream(f), StandardCharsets.UTF_8);
            template.process(dataMap, w);
            w.close();
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new RuntimeException(ex);
        }
        return f;
    }
    public static File createDoc2(String filePath, Map<?, ?> dataMap) {
        Template template;
        try {
            template = configuration.getTemplate("analysisTemplate_2.xml");
        } catch (IOException e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }

        File f = new File(filePath);

        try {
            // 这个地方不能使用FileWriter因为需要指定编码类型否则生成的Word文档会因为有无法识别的编码而无法打开
            Writer w = new OutputStreamWriter(new FileOutputStream(f), StandardCharsets.UTF_8);
            template.process(dataMap, w);
            w.close();
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new RuntimeException(ex);
        }
        return f;
    }

    public static File createRecordDoc(String filePath, Map<?, ?> dataMap) {
        Template template;
        try {
            template = configuration.getTemplate("recordTemplate.ftl");
        } catch (IOException e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }

        File f = new File(filePath);

        try {
            // 这个地方不能使用FileWriter因为需要指定编码类型否则生成的Word文档会因为有无法识别的编码而无法打开
            Writer w = new OutputStreamWriter(new FileOutputStream(f), StandardCharsets.UTF_8);
            template.process(dataMap, w);
            w.close();
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new RuntimeException(ex);
        }
        return f;
    }


}
