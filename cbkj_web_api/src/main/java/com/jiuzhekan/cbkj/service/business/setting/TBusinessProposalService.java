package com.jiuzhekan.cbkj.service.business.setting;

import com.github.pagehelper.PageHelper;
import com.jiuzhekan.cbkj.beans.business.record.TBusinessProposal;
import com.jiuzhekan.cbkj.beans.business.setting.TBusinessAnnex;
import com.jiuzhekan.cbkj.beans.sysBeans.AdminInfo;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.mapper.business.record.TBusinessProposalMapper;
import com.jiuzhekan.cbkj.mapper.business.setting.TBusinessAnnexMapper;
import com.jiuzhekan.cbkj.service.sysService.AdminService;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Service
public class TBusinessProposalService {

    private final TBusinessProposalMapper tBusinessProposalMapper;
    private final TBusinessAnnexMapper tBusinessAnnexMapper;
    private final AdminService adminService;

    public TBusinessProposalService(TBusinessProposalMapper tBusinessProposalMapper, TBusinessAnnexMapper tBusinessAnnexMapper, AdminService adminService) {
        this.tBusinessProposalMapper = tBusinessProposalMapper;
        this.tBusinessAnnexMapper = tBusinessAnnexMapper;
        this.adminService = adminService;
    }

    /**
     * 加载分页数据
     *
     * @param tBusinessProposal
     * @param page
     * @return
     */
    public Object getPageDatas(TBusinessProposal tBusinessProposal, Page page) {
        tBusinessProposal.setIsDel((byte) 0);
        PageHelper.startPage(page.getPage(), page.getLimit());
        List<TBusinessProposal> list = tBusinessProposalMapper.getPageListByObj(tBusinessProposal);
        Object result = Page.getLayUiTablePageData(list);
        return result;
    }

    /**
     * 插入新数据
     *
     * @param tBusinessProposal
     * @return
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insert(TBusinessProposal tBusinessProposal) {

        AdminInfo currentHr = AdminUtils.getCurrentHr();
        String appId = AdminUtils.getCurrentAppIdIgnoreBasic();
        String insCode = AdminUtils.getCurrentInsCodeIgnoreBasic();

        String proId = IDUtil.getID();
        tBusinessProposal.setId(proId);
        tBusinessProposal.setCreateUserId(currentHr.getId());
        tBusinessProposal.setCreateTime(new Date());
        tBusinessProposal.setCreateUserName(currentHr.getNameZh());
        tBusinessProposal.setIsDel((byte) 0);
        // 未受理
        tBusinessProposal.setProposalReceiveState((byte) 1);
        tBusinessProposal.setAppId(appId);
        tBusinessProposal.setAppName(AdminUtils.getCurrentPractice().getAppName());
        tBusinessProposal.setInsCode(insCode);
        tBusinessProposal.setInsName(AdminUtils.getCurrentPractice().getInsName());
        long rows = tBusinessProposalMapper.insertProposal(tBusinessProposal);
        if (rows <= 0) {
            return ResEntity.entity(false, "建议数据插入失败", tBusinessProposal);
        }
        // 插入附件表
        List<TBusinessAnnex> annexList = tBusinessProposal.getAnnexList();
        if (null != annexList && !annexList.isEmpty()) {
            for (TBusinessAnnex tBusinessAnnex : annexList) {
                tBusinessAnnex.setId(IDUtil.getID());
                tBusinessAnnex.setAnnexForeignId(proId);
                tBusinessAnnex.setAnnexType((byte) 1);
                tBusinessAnnex.setIsDel((byte) 0);
            }
            int i = tBusinessAnnexMapper.insertList(annexList);
            if (i != annexList.size()) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return ResEntity.entity(false, "附件数据插入失败", tBusinessProposal);
            }
        }
        return new ResEntity(true, Constant.SUCCESS_DX, null);
    }


    /**
     * 修改
     *
     * @param tBusinessProposal
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity update(TBusinessProposal tBusinessProposal) {
        String proId = tBusinessProposal.getId();
        if (StringUtils.isBlank(proId)) {
            return ResEntity.entity(false, "建议修改失败,建议id为空", tBusinessProposal);
        }
        long rows = tBusinessProposalMapper.updateByPrimaryKey(tBusinessProposal);
        // 先作废掉所有附件数据
        TBusinessAnnex annex = new TBusinessAnnex();
        annex.setAnnexForeignId(proId);
        annex.setIsDel((byte) 1);
        int row = tBusinessAnnexMapper.updateByAnnex(annex);
        List<TBusinessAnnex> annexList = tBusinessProposal.getAnnexList();
        if (null != annexList && !annexList.isEmpty()) {
            for (TBusinessAnnex tBusinessAnnex : annexList) {
                if (StringUtils.isBlank(tBusinessAnnex.getId())) {
                    tBusinessAnnex.setId(IDUtil.getID());
                }
                if (StringUtils.isBlank(tBusinessAnnex.getAnnexForeignId())) {
                    tBusinessAnnex.setAnnexForeignId(proId);
                }
                if (null == tBusinessAnnex.getIsDel()) {
                    // 可以传作废标志来
                    tBusinessAnnex.setIsDel((byte) 0);
                }
            }
            tBusinessAnnexMapper.insertUpdateAnnex(annexList);
        }
        return new ResEntity(true, Constant.SUCCESS_DX, null);
    }

    /**
     * 加载某条数据
     *
     * @param id
     * @return
     */
    public ResEntity findObj(String id) {

        if (StringUtils.isBlank(id)) {
            return new ResEntity(false, "参数不能为空哦", null);
        }
        TBusinessProposal tBusinessProposal = tBusinessProposalMapper.getObjectById(id);
        if (null == tBusinessProposal) {
            return new ResEntity(false, "传入的id在数据库找不到!", id);
        }

        tBusinessProposal.setPhone(adminService.getAdmin(tBusinessProposal.getCreateUserId()).getPhone());
        tBusinessProposal.setEmployeeId(adminService.getAdmin(tBusinessProposal.getCreateUserId()).getEmployeeId());

        TBusinessAnnex tBusinessAnnex = new TBusinessAnnex();
        tBusinessAnnex.setAnnexForeignId(id);
        List<TBusinessAnnex> annexList = tBusinessAnnexMapper.getPageListByObj(tBusinessAnnex);
        tBusinessProposal.setAnnexList(annexList);
        return new ResEntity(true, Constant.SUCCESS_DX, tBusinessProposal);
    }

    /**
     * 删除
     *
     * @param ids
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity deleteLis(String ids) {
        if (StringUtils.isBlank(ids)) {
            return new ResEntity(false, "参数错误(缺少参数)！", null);
        }
        List<String> split = Arrays.asList(ids.split(","));
        TBusinessProposal pro = new TBusinessProposal();
        pro.setDeleteUserId(AdminUtils.getCurrentHr().getId());
        pro.setDeleteTime(new Date());
        pro.setIsDel((byte) 1);
        long rowsR = tBusinessProposalMapper.deleteByProArr(split, pro);
        if (rowsR != split.size()) {
            // 似乎有点多余啊
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return new ResEntity(false, "删除失败(数据库异常)！", ids);
        }
        tBusinessAnnexMapper.deleteByForeignIds(split);
        return new ResEntity(true, Constant.SUCCESS_DX, rowsR);
    }

    /**
     * @param tBusinessProposal :
     * @return : java.lang.Object
     * @Description :  我的建议列表
     * <AUTHOR> xhq
     * @updateTime : 2020/2/27 18:11
     */
    public Object getMyProposalS(TBusinessProposal tBusinessProposal, Page page) {
        tBusinessProposal.setCreateUserId(AdminUtils.getCurrentHr().getId());
        tBusinessProposal.setIsDel((byte) 0);
        PageHelper.startPage(page.getPage(), page.getLimit());
        List<TBusinessProposal> list = tBusinessProposalMapper.getPageListByObj(tBusinessProposal);
        if (null != list && !list.isEmpty()) {
            List<TBusinessAnnex> annexList = tBusinessAnnexMapper.getMyProposalS(list);
            for (TBusinessProposal businessProposal : list) {
                String proId = businessProposal.getId();
                List<TBusinessAnnex> aList = new ArrayList<>();
                for (TBusinessAnnex tBusinessAnnex : annexList) {
                    if (proId.equals(tBusinessAnnex.getAnnexForeignId())) {
                        aList.add(tBusinessAnnex);
                    }
                }
                businessProposal.setAnnexList(aList);
            }
        }
        Object result = Page.getLayUiTablePageData(list);
        return result;
    }
}