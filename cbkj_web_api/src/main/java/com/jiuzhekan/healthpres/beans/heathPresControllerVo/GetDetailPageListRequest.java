package com.jiuzhekan.healthpres.beans.heathPresControllerVo;

import com.jiuzhekan.cbkj.common.utils.Page;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/5/27 17:26
 * @Version 1.0
 */
@Data
public class GetDetailPageListRequest {
    private Integer page = 0;
    private Integer limit = 0;
    private String patientId;

    @ApiModelProperty(value = "1.儿童保健2.慢病调理3健康处方")
    private Integer questionnaireMainType;
}
