package com.jiuzhekan.cbkj.mapper.business.record;

import com.jiuzhekan.cbkj.beans.business.record.TPrescription;
import com.jiuzhekan.cbkj.beans.business.record.VO.TPreRespVO;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public interface TPrescriptionMapper extends BaseMapper<TPrescription> {

    List<TPrescription> getListByRecId(TPrescription tPrescription);

    List<TPreRespVO> getPreByPre(TPrescription tPrescription);

    /**
     * 物理删除
     */
    int physicalDelete(List<String> draftPreIds);

    int deleteByRecId(String recId);

    int deleteByPreIdList(List<String> preIdList);

    int deleteByPreNoList(List<String> preNoList);

    int saveCheckPre(TPrescription tPrescription);

    int updateIsPay(@Param("list") List<String> preArr, @Param("paramMap") Map<String, Object> payM);

    int updateIsSend(String preId);


    int updateRecExtType(TPrescription tPrescription);

    TPrescription getObjectByPreNo(String preNo);
    List<String> getObjectByPreSNo(String preNo);

    /**
     * 根据处方号查询所有未删除的处方，时间倒序
     * @param preNo 处方号
     * @param exceptPreId 排除的处方ID
     * @return List<TPrescription>
     */
    List<TPrescription> getPreListByPreNo(String preNo, String exceptPreId);
    /**
     * 根据处方号获取处方医联体、医疗机构、科室
     *
     * @param preNo preNo
     * @return
     */
    TPrescription getOrgInfoByPreNo(String preNo);

    /**
     * 只是获取preType 根据preNo
     *
     * @param split
     * @return
     */
    List<TPrescription> getPreTypeListByPreNo(String[] split);

    TPrescription getPreAndItemByPreNo(String preNo);


    List<TPrescription> getPreListByObj(TPrescription tPrescription, String visitNo, String registerId);

    /**
     * 定时作废未缴费处方(门诊)
     *
     * @param interval 间隔时间（单位小时）
     * @return int
     * <AUTHOR>
     * @date 2021/2/23
     */
    int delPreIfNoPayForMz(long interval);

    /**
     * 定时作废未缴费处方(住院)
     *
     * @param interval 间隔时间（单位小时）
     * @return int
     * <AUTHOR>
     * @date 2025/2/20
     */
    int delPreIfNoPayForZy(long interval);

    /**
     * 定时作废草稿处方
     *
     * @return int
     * <AUTHOR>
     * @date 2021/2/23
     */
    int delPreIfIsDraft();


    // List<preItem> getPreItem(String preId);

    String getPreType(String preId);

    /**
     * 获取目标时间与开始时间结束时间重复的处方数量
     *
     * @param mTime mTime
     * @return java.lang.Long
     * <AUTHOR>
     * @date 2021/12/2
     */
    List<TPrescription> getPreBetweenBeganEndTime(Map<String, String> params);

    /**
     * 根据病历获取草稿状态下的处方
     *
     * @param recId 病历ID
     * @return 处方ID集合
     */
    List<String> getDraftPreId(String recId);


    TPrescription getObjectByPreId(String pId);


    void updateIsCheckByPreId(String preId);
}