package com.jiuzhekan.managementTCMSP.controller;

import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.managementTCMSP.service.TCMSPAdminInfoService;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/8/15 15:59
 * @Version 1.0
 */
@RequestMapping("/tcmspAdminInfo")
@RestController
public class TCMSPAdminInfoController {

    public TCMSPAdminInfoController(TCMSPAdminInfoService tcmspAdminInfoService) {
        this.tcmspAdminInfoService = tcmspAdminInfoService;
    }
    private final TCMSPAdminInfoService tcmspAdminInfoService;
    /**
     * 获取医共体列表信息
     */
    @GetMapping("/getAppList")
    public Object getAppList() {
        return tcmspAdminInfoService.getAppList();
    }

    /**
     * 根据医共体appId获取Ins列表
     */
    @GetMapping("/getInsList")
    public Object getInsList(String appId) {
        if (StringUtils.isBlank(appId)){
            return ResEntity.error("医共体id不能为空");
        }
        return tcmspAdminInfoService.getInsList(appId);
    }
    /**
     * 根据医共体appId和insCode获取科室列表
     */
    @GetMapping("/getDeptList")
    public Object getDeptList(String appId, String insCode) {
        return null;
    }

    /**
     * 继续教育搜索人员-从人员信息管理中获取
     */
    @GetMapping("/getContinueEducationList")
    public Object getContinueEducationList(String appId, String insCode, String deptCode) {
        return null;
    }

}
