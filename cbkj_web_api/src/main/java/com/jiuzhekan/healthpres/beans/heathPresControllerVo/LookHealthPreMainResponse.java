package com.jiuzhekan.healthpres.beans.heathPresControllerVo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/6/3 16:26
 * @Version 1.0
 */
@Data
public class LookHealthPreMainResponse {

    @ApiModelProperty(value = "报告id")
    private String resultId;

    @ApiModelProperty(value = "患者id")
    private String patientId;

    @ApiModelProperty(value = "患者姓名（其他数据关联患者表查询）")
    private String patientName;

    @ApiModelProperty(value = "报告名称")
    private String resultReportName;

    @ApiModelProperty(value = "报告时间")
    private Date resultReportTime;

    @ApiModelProperty(value = "插入时间")
    private Date insertTime;

    @ApiModelProperty(value = "报告类型信息")
    private String resultReportTypeInfo;

    @ApiModelProperty(value = "标题")
    private String questionnaireMainTitle;

    @ApiModelProperty(value = "内容首行文本")
    private String questionnaireMainFirstLine;

    @ApiModelProperty(value = "内容第二行提示。")
    private String questionnaireMainTips;

    @ApiModelProperty(value = "1.儿童保健2.慢病调理")
    private Integer questionnaireMainType;

    @ApiModelProperty(value = "子分类：1-1(6、12月龄儿童)1-2(18、24月龄儿童)1-3(30、36月龄儿童) ")
    private String questionnaireMainSecondTypeCode;

    @ApiModelProperty(value = "子类型名称")
    private String questionnaireMainSecondTypeName;


    @ApiModelProperty(value = "word地址")
    private String wordPath;

    @ApiModelProperty(value = "pdf地址")
    private String pdfPath;
    private String resultCode;
    private String appName;
    private String deptName;
    private String insName;
    private String createDoctorName;

    private List<LookHealthPreMainItemResponse> questionnaireMainItemList;

}
