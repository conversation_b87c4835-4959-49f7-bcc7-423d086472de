package com.jiuzhekan.cbkj.controller.business.treatment.vo;

import com.jiuzhekan.cbkj.beans.drug.CenterHisMappingVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel
public class CenterHisMappingVORequest {

    @ApiModelProperty("产地ID")
    private String chanDi;

    @ApiModelProperty("转换后的中心药房药品类型")
    private String centerYplx;

    @ApiModelProperty("转换后的中心药房ID")
    private String storeId;

    @ApiModelProperty("转换后的中心药房药品类型")
    private String oldCenterYplx;

    @ApiModelProperty("转换后的中心药房ID")
    private String oldStoreId;

    @ApiModelProperty("药品目录ID")
    private String drugId;

    @ApiModelProperty("是否来自知识库 ")
    private boolean fromKnow;

    @ApiModelProperty("处方类型 1内服中药方 2外用中药方 3中成药方 4适宜技术方")
    private int type;

    @ApiModelProperty("原药品列表")
    private List<CenterHisMappingVO> voList;
    private Integer manySpeSwitch;
    private String registerId;
}
