package com.jiuzhekan.cbkj.common.constant;

/**
 * 处方状态常量
 *
 * <AUTHOR>
 * @date 2020/9/11
 */
public enum OrderStatusConstant {
    /**
     * 使用index
     */
    REGISTER("预约挂号", 3),
    REGISTER_PAY("挂号缴费", 5),
    CONSULT("咨询缴费", 7),
    PRES_SAVE("开方", 10),
    DELETE("删除", 20),
    CHECK_PASS("审核通过", 30),
    CHECK_FAIL("审核未通过", 40),
    PUSH_SUCCESS("推送成功", 44),
    PUSH_FAILED("推送失败", 45),
    PRES_PAY("处方缴费", 50),
    STORE_CHECK_PASS("药房审核通过", 61),
    STORE_CHECK_FAIL("药房审核未通过", 62),
    DOSAGE("配药", 80),
    RECHECK("复核", 82),
    BUBBLE("泡药", 85),
    SEND("发药", 90),
    SEND_CANCEL("取消发药", 100),
    CAN_RETURN("查询能否退费", 109),
    RETURN("退费", 110),
    RETURN_DRUG("退药", 111),
    RETURN_CANCEL("取消退药", 120),
    DECOCT("煎药", 130),
    PACK("包装", 135),
    EXPRESS("配送", 140),
    TAKEOVER("收货", 150);

    private String name;
    private int index;


    OrderStatusConstant(String name, int index) {
        this.name = name;
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public int getIndex() {
        return index;
    }

    @Override
    public String toString() {
        return Integer.toString(this.index);
    }

    public static String getName(Integer code) {
        for (OrderStatusConstant c : OrderStatusConstant.values()) {
            if (c.index == code) {
                return c.name;
            }
        }
        return "";
    }
}
