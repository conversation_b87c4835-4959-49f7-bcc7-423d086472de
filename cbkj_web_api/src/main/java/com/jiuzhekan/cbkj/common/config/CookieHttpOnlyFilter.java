package com.jiuzhekan.cbkj.common.config;

import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpServletResponseWrapper;
import java.io.IOException;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/8/4 09:50
 * @Version 1.0
 */
@Component
public class CookieHttpOnlyFilter implements Filter {

    @Override
    public void doFilter(ServletRequest request, ServletResponse response,
                         FilterChain chain) throws IOException, ServletException {

        HttpServletResponse httpServletResponse = (HttpServletResponse) response;

        // 创建一个包装器来拦截 addCookie 调用
        HttpServletResponseWrapper wrapper = new HttpServletResponseWrapper(httpServletResponse) {
            @Override
            public void addCookie(Cookie cookie) {
                cookie.setHttpOnly(true);  // 强制设置 HttpOnly
                cookie.setSecure(false);    // 如果是 HTTPS，也加上
                super.addCookie(cookie);
            }
        };

        chain.doFilter(request, wrapper);
    }
}

