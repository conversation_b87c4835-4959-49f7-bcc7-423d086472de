package com.jiuzhekan.appraisal.service;

import com.alibaba.fastjson.JSON;
import com.jiuzhekan.appraisal.bean.App;
import com.jiuzhekan.appraisal.bean.Dept;
import com.jiuzhekan.appraisal.bean.Ins;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.http.PlatformRestTemplate;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import lombok.Data;
import org.apache.commons.lang.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/4/17 15:51
 * @Version 1.0
 */
@Service
public class PerformanceInsService {
    private final PlatformRestTemplate platformRestTemplate;

    public PerformanceInsService(PlatformRestTemplate platformRestTemplate) {
        this.platformRestTemplate = platformRestTemplate;
    }

    @Cacheable(value = "org::ins", keyGenerator = "cacheKeyGenerator")
    public Map<String, String> getIns() {

        Map<String, Object> params = new HashMap<>();
        params.put("appId", AdminUtils.getCurrentAppId());

        //从综合平台获取医疗机构
        ResEntity post = platformRestTemplate.post("ins/tree", params);

        if (post.getStatus()) {
            List<App> apps = JSON.parseArray(JSON.toJSONString(post.getData()), App.class);
            return PerformanceAppraisalService.extractAppInfo(apps);
        }
        return null;
    }

    @Cacheable(value = "org::dept", keyGenerator = "cacheKeyGenerator")
    public HashMap<String, String> getDept2(String insCodes,String deptIds) {
        if (StringUtils.isBlank(insCodes)){
            insCodes = "";
        }
        String[] split = insCodes.split(",");
        ArrayList<App> apps1 = new ArrayList<>();
        for (int i = 0; i < split.length; i++) {
            String insCode = split[i];
            HashMap<String, Object> params = new HashMap<>();
            params.put("appId", AdminUtils.getCurrentAppId());
            params.put("insCode", StringUtils.isBlank(insCode) ? null : insCode);

            //从综合平台获取医疗机构
            ResEntity post = platformRestTemplate.post("dept/tree", params);
            if (post.getStatus()) {
                List<App> apps = JSON.parseArray(JSON.toJSONString(post.getData()), App.class);
                apps1.addAll(apps);
            }
        }


       // if (post.getStatus()) {
        //    List<App> apps = JSON.parseArray(JSON.toJSONString(post.getData()), App.class);
        if (!apps1.isEmpty())
        {
            return extractAppDeptInfo(apps1);
        }
      //  }
        return null;
    }
    @Cacheable(value = "org::dept", keyGenerator = "cacheKeyGenerator")
    public HashMap<String, String> getDept(String insCode) {

        HashMap<String, Object> params = new HashMap<>();
        params.put("appId", AdminUtils.getCurrentAppId());
        params.put("insCode", StringUtils.isBlank(insCode) ? null : insCode);

        //从综合平台获取医疗机构
        ResEntity post = platformRestTemplate.post("dept/tree", params);

        if (post.getStatus()) {
            List<App> apps = JSON.parseArray(JSON.toJSONString(post.getData()), App.class);
            return extractAppDeptInfo(apps);
        }
        return null;
    }


    public static HashMap<String, String> extractAppDeptInfo(List<App> apps) {
        HashMap<String, String> insMap = new HashMap<>();
        for (App app : apps) {
            insMap.putAll(extractInsDeptInfo(app.getInsList()));
        }
        return insMap;
    }

    private static HashMap<String, String> extractInsDeptInfo(List<Ins> insList) {
        HashMap<String, String> insMap = new HashMap<>();
        if (insList == null) {
            return insMap;
        }

        for (Ins ins : insList) {
            insMap.putAll(extractInsDeptInfo2(ins.getDeptList(),ins));
        }

        return insMap;
    }

    private static HashMap<String, String> extractInsDeptInfo2(List<Dept> deptList,Ins ins) {
        HashMap<String, String> insMap = new HashMap<>();
        for (Dept dept : deptList) {
            insMap.put(ins.getInsCode()+"-"+dept.getDeptId(), ins.getInsName()+"-"+dept.getDeptName());
        }
        return insMap;
    }
}
