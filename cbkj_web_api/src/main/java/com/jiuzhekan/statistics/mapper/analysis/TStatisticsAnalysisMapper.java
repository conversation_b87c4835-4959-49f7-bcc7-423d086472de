package com.jiuzhekan.statistics.mapper.analysis;

import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;
import com.jiuzhekan.statistics.beans.analysis.StatisticsResult;
import com.jiuzhekan.statistics.beans.analysis.TStatisticsAnalysis;
import com.jiuzhekan.statistics.beans.register.QueryParam;
import com.jiuzhekan.statistics.beans.register.QueryResult;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public interface TStatisticsAnalysisMapper extends BaseMapper<TStatisticsAnalysis>{
    /**
     * 获取体制辨识统计数据-包括已经统计的，如果已统计 id有值。
     * @param param
     */
    List<StatisticsResult> selectStatisticsResult(Map<String, Object> param);

    /**
     * 查昨天的删除
     * @param param
     * @return
     */
    List<StatisticsResult> selectStatisticsResult2();
    int insertStatistics(List<StatisticsResult> list);
    int replaceStatistics(List<StatisticsResult> list);

    TStatisticsAnalysis selectLastDate();

    List<QueryResult> sumResult(QueryParam queryParam);
    List<QueryResult> sumResultDoctor(QueryParam queryParam);

    /**
     * 获取协定放统计结果
     * @param queryParam
     * @return
     */
    List<QueryResult> sumPersonalPrescription(QueryParam queryParam);
    List<QueryResult> sumPersonalPrescriptionDoctor(QueryParam queryParam);

    /**
     * 适宜技术放
     * @param queryParam
     * @return
     */
    List<QueryResult> sumStatisticsPrescription(QueryParam queryParam);
    List<QueryResult> sumStatisticsPrescriptionDoctor(QueryParam queryParam);

    /**
     * 就诊人次\电子病历
     * @param queryParam
     * @return
     */
    List<QueryResult> sumStatisticsRegister(QueryParam queryParam);
    List<QueryResult> sumStatisticsRegisterDoc(QueryParam queryParam);

    /**
     * 获取数据
     * @param queryParam
     * @return
     */
    Integer sumStatistics(QueryParam queryParam);


    List<QueryResult> sumStatisticsPrescriptionCDSS(QueryParam queryParam2);
    List<QueryResult> sumStatisticsPrescriptionCDSSDoc(QueryParam queryParam2);

    List<QueryResult> sumStatisticsPrescriptionMzZy(QueryParam queryParam2);
    List<QueryResult> sumStatisticsPrescriptionMzZyDoc(QueryParam queryParam2);
}