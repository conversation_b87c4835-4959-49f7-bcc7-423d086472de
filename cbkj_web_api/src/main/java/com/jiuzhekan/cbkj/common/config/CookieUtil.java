package com.jiuzhekan.cbkj.common.config;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/8/4 10:35
 * @Version 1.0
 */
public class CookieUtil {
    public static void addSecureCookie(HttpServletResponse response, String name, String value, int maxAge) {
        Cookie cookie = new <PERSON>ie(name, value);
        cookie.setHttpOnly(true);
        cookie.setSecure(false);  // true = 仅 HTTPS
        cookie.setPath("/");
        cookie.setMaxAge(maxAge);
        response.addCookie(cookie);
    }
}

