package com.jiuzhekan.cbkj.mapper.business.record;

import com.jiuzhekan.cbkj.beans.business.record.TRecordDetail;
import org.springframework.stereotype.Component;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;

import java.util.List;

@Component
public interface TRecordDetailMapper extends BaseMapper<TRecordDetail> {

    List<TRecordDetail> getListByRecId(TRecordDetail tRecordDetail);

    int deleteByRecId(String recId);

    String getTemplateIdByRegisterId(String registerId);

    String getTemplateIdByRecID(String recId);

    int getCountTempDetailByRecId(String recId);
}