package com.jiuzhekan.cbkj.service.formula;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jiuzhekan.cbkj.beans.formula.TFormula;
import com.jiuzhekan.cbkj.beans.formula.TFormulaHttp;
import com.jiuzhekan.cbkj.beans.formula.TFormulaHttpToFormulaVo;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.http.DrugsRestTemplate;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.service.business.treatment.SearchMatService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

@Service
public class TFormulaService {

    @Autowired
    private SearchMatService searchMatService;
    @Autowired
    private DrugsRestTemplate drugsRestTemplate;
    /**
     * 搜索配方
     *
     * @param tFormula 配方
     * @param page     分页
     * @param preType     1.配方 2膏方
     * @return Object
     * <AUTHOR>
     * @date 2021-07-06
     */
    public Object search(TFormula tFormula, Page page,String preType,String payType) {

        List<TFormula> list = new ArrayList<>();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("preType", preType);
        jsonObject.put("payType", StringUtils.isBlank(payType) ? "0" : payType);
        jsonObject.put("category", tFormula.getPreType());
        jsonObject.put("appId", AdminUtils.getCurrentAppId());
        jsonObject.put("phaId", tFormula.getStoreId());
        jsonObject.put("preName", tFormula.getPreName());
        jsonObject.put("page", page.getPage());
        jsonObject.put("limit", page.getLimit());
        ResEntity post = drugsRestTemplate.post("get/agr/list", jsonObject);
        int code = post.getCode();
        if (200 == code) {
            ArrayList data1 = (ArrayList) post.getData();
            List<TFormulaHttp> matVos = JSON.parseArray(JSON.toJSONString(data1), TFormulaHttp.class);
            List<TFormula> formulaList = new ArrayList<>();
            for (int i = 0; i < matVos.size(); i++) {
                TFormula tFormula1 = TFormulaHttpToFormulaVo.transferFormulaBean(matVos.get(i), new TFormula());
                tFormula1.setFolderId(tFormula.getFolderId());
                if (null != tFormula1){
                    formulaList.add(tFormula1);
                }

            }
            //List<TFormula> formulaList = matVos.stream().map(m -> TFormulaHttpToFormulaVo.transferFormulaBean(m, new TFormula())).collect(Collectors.toList());
            post.setData(formulaList);
            return post;
        }
        return Page.getLayUiTablePageData(list);
    }


    /**
     * 加载某条数据
     *
     * @param formulaId 配方
     * @return ResEntity
     * <AUTHOR>
     * @date 2021-07-06
     */
    public ResEntity findObj(String formulaId,String storeId) {

        if (StringUtils.isBlank(formulaId)) {
            return ResEntity.entity(false, "参数不能为空哦", null);
        }

        TFormula tFormula = null;
        ResEntity resEntity = drugsRestTemplate.get("get/agr/info", "?agrId="+formulaId+"&phaId="+storeId);
        int code = resEntity.getCode();
        if (!resEntity.getStatus()){
            return new ResEntity(false, resEntity.getMessage(), null);
        }
        if (200 == code) {
            LinkedHashMap data = (LinkedHashMap) resEntity.getData();
            TFormulaHttp matVos = JSON.parseObject(JSON.toJSONString(data), TFormulaHttp.class);
            TFormula vo2 = new TFormula();
            vo2.setFormulaId(formulaId);
            tFormula = TFormulaHttpToFormulaVo.transferFormulaBean(matVos, vo2);
        }
        if (null == tFormula){
            return ResEntity.error("来源处方不存在，不可转方！");
        }
        return ResEntity.entity(true, Constant.SUCCESS_DX, tFormula);
    }


}
