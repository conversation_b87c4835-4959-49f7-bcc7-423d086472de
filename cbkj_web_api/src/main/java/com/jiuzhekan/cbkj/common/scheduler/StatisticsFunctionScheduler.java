package com.jiuzhekan.cbkj.common.scheduler;

import com.jiuzhekan.cbkj.mapper.statistics.TStatisticsFunctionMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * StatisticsFunction
 * <p>
 * 杭州聪宝科技有限公司
 * <p>
 *
 * <AUTHOR>
 * @date 2021/4/15 16:03
 */
@Slf4j
@Component
@EnableScheduling
@Transactional(rollbackFor = Exception.class)
public class StatisticsFunctionScheduler {

    @Autowired
    private TStatisticsFunctionMapper tStatisticsFunctionMapper;

    /**
     * 统计使用次数
     *
     * <AUTHOR>
     * @date 2021/4/16
     */
    @Scheduled(cron = "0 0 1 * * ?")
    public void statisticFunction() {

        /*****统计最后一次*****/

        //设置-五笔使用人数
//        tStatisticsFunctionMapper.statisticWb();
        //设置-拼音使用人数
//        tStatisticsFunctionMapper.statisticPy();

        //科室数
//        tStatisticsFunctionMapper.statisticDept();

        //专家经验共享-专家经验共享专家数
        tStatisticsFunctionMapper.statisticAppExpertNum();


        /*****统计每天的数量*****/

        //中医体质辨识-体质辨识数量
        tStatisticsFunctionMapper.statisticAnalysis();

        //总处方数
        tStatisticsFunctionMapper.statisticPreNum();
        //处方审核-人工审核的处方数
        tStatisticsFunctionMapper.statisticCheckPreNum();
        //智能开方-内服处方数量
        tStatisticsFunctionMapper.statisticInPreNum();
        //智能开方-内服膏方数量
        tStatisticsFunctionMapper.statisticInPreNumProduction();
        //智能开方-内服代煎数量
        tStatisticsFunctionMapper.statisticInPreNumDecoction();
        //智能开方-内服配送数量
        tStatisticsFunctionMapper.statisticInPreNumExpress();

        //协定方-协定方数量
        tStatisticsFunctionMapper.statisticPersonalNum();
        //协定方-个人协定方数量
        tStatisticsFunctionMapper.statisticPersonalSelfNum();
        //协定方-科室协定方数量
        tStatisticsFunctionMapper.statisticPersonalDeptNum();
        //协定方-全院协定方数量
        tStatisticsFunctionMapper.statisticPersonalInsNum();

        //电子病历模板配置-电子病历模板数
        tStatisticsFunctionMapper.statisticRecordTemplate();

        //专家经验共享-专家经验共享条数
        tStatisticsFunctionMapper.statisticAppExpertPreNum();
        //专家经验共享-专家经验共享内服处方数
        tStatisticsFunctionMapper.statisticAppExpertInPreNum();
        //专家经验共享-专家经验共享外用处方数
        tStatisticsFunctionMapper.statisticAppExpertExtPreNum();

        //名医验案-收藏次数
        tStatisticsFunctionMapper.statisticVerifyCollection();
        //中药查询-收藏次数
        tStatisticsFunctionMapper.statisticMatCollection();
        //方剂查询-收藏次数
        tStatisticsFunctionMapper.statisticPreCollection();
        //经络穴位查询-收藏次数
        tStatisticsFunctionMapper.statisticAcuCollection();
        //疾病查询-收藏次数
        tStatisticsFunctionMapper.statisticDisCollection();
    }

}
