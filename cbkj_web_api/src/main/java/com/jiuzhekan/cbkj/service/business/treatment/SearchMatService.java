package com.jiuzhekan.cbkj.service.business.treatment;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jiuzhekan.cbkj.beans.business.TSysParam;
import com.jiuzhekan.cbkj.beans.business.record.TRegister;
import com.jiuzhekan.cbkj.beans.business.store.TDisplay;
import com.jiuzhekan.cbkj.beans.drug.CenterHisMappingVO;
import com.jiuzhekan.cbkj.beans.drug.MatVo;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.http.DrugsRestTemplate;
import com.jiuzhekan.cbkj.common.http.KnowRestTemplate;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.common.utils.StringJudges;
import com.jiuzhekan.cbkj.controller.business.treatment.vo.CenterHisMappingVORequest;
import com.jiuzhekan.cbkj.service.business.TSysParamService;
import com.jiuzhekan.cbkj.service.redis.ClientRedisService;
import com.jiuzhekan.cbkj.service.redis.ParameterDisplayRedisService;
import com.jiuzhekan.cbkj.service.redis.ParameterRedisService;
import com.jiuzhekan.cbkj.service.redis.RedisService;
import com.jiuzhekan.statistics.service.board.TStatisticsHealthCareService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.UnknownHostException;
import java.util.*;
import java.util.stream.Collectors;

import static com.jiuzhekan.cbkj.beans.drug.TransCenterHisMappingVoToMatVo.transCenterHisMappingVoToMatVo;
import static com.jiuzhekan.cbkj.beans.drug.TransCenterHisMappingVoToMatVo.transferMatBean;

/**
 * 搜索药品
 * <p>
 * 杭州聪宝科技有限公司
 *
 * <AUTHOR>
 * @date 2020/10/26
 */
@Service
@Slf4j
public class SearchMatService {

    @Autowired
    private TSysParamService tSysParamService;
    @Autowired
    private KnowRestTemplate knowRestTemplate;
    @Autowired
    private RedisService redisService;
    @Autowired
    private ParameterRedisService parameterRedisService;
    @Autowired
    private ParameterDisplayRedisService parameterDisplayRedisService;
    @Autowired
    private ClientRedisService clientRedisService;
    @Autowired
    private DrugsRestTemplate drugsRestTemplate;
    @Autowired
    private TStatisticsHealthCareService tStatisticsHealthCareService;

    /**
     * 获取当前医疗机构配置的HIS药品目录
     *
     * @return
     */
    public ResEntity getHisYpmlId() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("appId", AdminUtils.getCurrentAppId());
        jsonObject.put("insCode", AdminUtils.getCurrentInsCode());
        return drugsRestTemplate.post("get/his/drug/ids", jsonObject);
    }


    /**
     * 中药方开方时选择药房中药类型
     *
     * @param register 挂号
     * <AUTHOR>
     * @date 2020/10/26
     */
    public List<TDisplay> getDisplayList(TRegister register) {

        if (register != null) {
            return parameterDisplayRedisService.showDisplayList2(register.getAppId(), register.getInsCode(), register.getDeptId(), String.valueOf(register.getClinicTypeId()));
        } else {
            return parameterDisplayRedisService.showDisplayList2(AdminUtils.getCurrentAppId(), AdminUtils.getCurrentInsCode(), AdminUtils.getCurrentDeptId(), Constant.BASIC_STRING_ONE);
        }
    }

    /**
     * 中药方开方时选择药房或者中药类型
     *
     * @param registerId 挂号ID
     * <AUTHOR>
     * @date 2020/10/26
     */
    public ResEntity getDisplayParams(String registerId) {
        TRegister register;
        if (StringUtils.isBlank(registerId)) {
            register = null;
        } else {
            register = clientRedisService.getRegisterById(registerId);
        }

        List<TDisplay> list = getDisplayList(register);

        if (list == null || list.isEmpty()) {
            return ResEntity.error("当前科室没有可使用的“中药类型”，请在联系管理员在“药房管理”中配置");
        }

        return ResEntity.entity(true, Constant.SUCCESS_DX, list);
    }


    /**
     * 空白方 搜索his药品
     *
     * @param val,ypmlId
     * @return
     */
    public Object searchMat(String val, String centerYplx, String centerStoreId, String drugId, String chanDi, Page page, Integer manySpeSwitch) throws UnknownHostException {
//        if (StringUtils.isBlank(centerYplx)){
//            return ResEntity.error("参数错误(缺少药品类型参数)！");
//        }
        //开方页面“医保”列显示数据来源(1his 2.医保外，默认1 )
        String par0 = tSysParamService.getSysParam(Constant.PRESCRIPTION_MEDICAL_INSURANCE_SOURCE).getParValues();
        String PRESCRIPTION_TCXL = tSysParamService.getSysParam(Constant.PRESCRIPTION_TCXL).getParValues();
        //颗粒剂处方是否限制为同一产地（1是  0否）
        String par = tSysParamService.getSysParam(Constant.PRESCRIPTION_GRANULE_SAME_PLACE).getParValues();


        String source = tSysParamService.getSysParam(Constant.MAT_SPECIAL_USAGE_SOURCE).getParValues();
        List<CenterHisMappingVO> mxList = new ArrayList<>();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("keyValue", val);
        jsonObject.put("matType", centerYplx);//中药类型（规格表中）
        jsonObject.put("phaId", centerStoreId);//药房id
        jsonObject.put("drugIdHis", drugId);//his药品目录id
        jsonObject.put("distinctOrigin", "1");//去重产地，0不去重 1 去重
        jsonObject.put("manySpeSwitch", null == manySpeSwitch ? 0 :manySpeSwitch);//药品开方多规格下拉开关 1是 0否
//        1拼音 2五笔
        String searchtype = AdminUtils.getCurrentShuruma();
        if (StringJudges.isContainChinese(val)) {
            searchtype = "";
        }
        jsonObject.put("inputType", searchtype);
        if ( StringUtils.isNotBlank(centerYplx) && (centerYplx.contains(Constant.BASIC_STRING_TWO) || centerYplx.contains(Constant.BASIC_STRING_FIVE))
//        if ((Constant.BASIC_STRING_TWO.equals(centerYplx) || Constant.BASIC_STRING_FIVE.equals(centerYplx))
                && Constant.BASIC_STRING_ONE.equals(par)) {
            //颗粒剂保持同一产地
            jsonObject.put("matOriginId", chanDi);

        }
        //jsonObject.put("containNoStock", "1");//是否搜索无库存药品 1是 0否
        jsonObject.put("page", page.getPage());
        jsonObject.put("limit", page.getLimit());
        long start = System.currentTimeMillis();
        log.info("--药品查询服务1--");
        ResEntity post = drugsRestTemplate.post("mat/searchMat", jsonObject);
        long start2 = System.currentTimeMillis();
        log.info("--药品查询服务2--" + (start2 - start));
        int code = post.getCode();
        if (200 == code) {
            //List<LinkedHashMap> data = (List<LinkedHashMap>) post.getData();
            List<MatVo> matVos = JSON.parseArray(JSON.toJSONString(post.getData()), MatVo.class);

            for (MatVo matVo : matVos) {
                CenterHisMappingVO vo1 = new CenterHisMappingVO();

                CenterHisMappingVO centerHisMappingVO = transferMatBean(matVo, vo1, source);
                mxList.add(centerHisMappingVO);
            }
            for (CenterHisMappingVO mappingVO : mxList) {
                setUniqueId(mappingVO);
                if (Constant.BASIC_STRING_TWO.equals(par0)) {
                    //医保外
                    String npa = "1".equals(mappingVO.getNotPayAlone()) ? "0" : "0".equals(mappingVO.getNotPayAlone()) ? "1" : null;
                    mappingVO.setIsInsurance(npa);
                }
            }
        }
        long start3 = System.currentTimeMillis();
        log.info("--药品查询服务3--" + (start3 - start2));

        //setDefaultUsage(centerYplx, centerStoreId, mxList);
        setDefaultUsage( centerStoreId, mxList);
        transMultiple(mxList);
        setDailyMaxDose(mxList);
        //此处判断是否开启提纯系数转换。
        if (Constant.BASIC_STRING_ONE.equals(PRESCRIPTION_TCXL)) {
            //开启。
            transTcxs(mxList);
        }
        long start4 = System.currentTimeMillis();

        setXZSM(mxList);
        long start5 = System.currentTimeMillis();
        log.info("--药品查询服务4--" + (start5 - start4));
        return Page.getResEntityPageData(mxList, post);
    }

    /**
     * 显示更多药品
     * 1 推出的处方的药对应多个当前中药类型的his的药品，默认一个
     * 2 搜索中药时，相同的中药（his给的规格ID一样）只随机显示一条记录
     *
     * @param matId         知识库药品ID
     * @param hisYpdm       HIS药品代码
     * @param centerYplx    中药类型
     * @param centerStoreId 药房ID
     * @param chanDi        产地
     * @param page          分页
     * <AUTHOR>
     * @date 2020/12/18
     */
    public Object getMoreMat(String matId, String hisYpdm, String centerYplx, String centerStoreId, String chanDi, Page page, Integer manySpeSwitch) {

        //CenterHisMappingVO vo = new CenterHisMappingVO(ypmlId, centerYplx, centerStoreId);
        JSONObject jsonObject = new JSONObject();
        String PRESCRIPTION_TCXL = tSysParamService.getSysParam(Constant.PRESCRIPTION_TCXL).getParValues();
        //开方页面“医保”列显示数据来源(1his 2.医保外，默认1 )
        String par0 = tSysParamService.getSysParam(Constant.PRESCRIPTION_MEDICAL_INSURANCE_SOURCE).getParValues();

        List<CenterHisMappingVO> mxList = new ArrayList<>();
//        //颗粒剂处方是否限制为同一产地（1是  0否）
        String par = tSysParamService.getSysParam(Constant.PRESCRIPTION_GRANULE_SAME_PLACE).getParValues();

        //获取更多药品
        if ((centerYplx.contains(Constant.BASIC_STRING_TWO) || centerYplx.contains(Constant.BASIC_STRING_FIVE))
                && Constant.BASIC_STRING_ONE.equals(par)) {
            //颗粒剂保持同一产地
            jsonObject.put("matOriginId", chanDi);

        }
        jsonObject.put("matPriceIdHis", hisYpdm);
        jsonObject.put("phaId", centerStoreId);
        //jsonObject.put("containNoStock", "1");
        jsonObject.put("page", page.getPage());
        jsonObject.put("limit", page.getLimit());
        jsonObject.put("manySpeSwitch", null == manySpeSwitch ? 0 :manySpeSwitch);
        ResEntity post = drugsRestTemplate.post("mat/getMoreMatInfo", jsonObject);
        //List<LinkedHashMap> data = (List<LinkedHashMap>) post.getData();
        String parValues = tSysParamService.getSysParam(Constant.MAT_SPECIAL_USAGE_SOURCE).getParValues();
        if (post.getStatus() && 200 == post.getCode()) {
            List<MatVo> matVos = JSON.parseArray(JSON.toJSONString(post.getData()), MatVo.class);
            for (MatVo matVo : matVos) {
                CenterHisMappingVO vo2 = new CenterHisMappingVO();
                CenterHisMappingVO centerHisMappingVO = transferMatBean(matVo, vo2, parValues);
                mxList.add(centerHisMappingVO);
            }
        }


        for (CenterHisMappingVO mappingVO : mxList) {
            mappingVO.setHasMore(mxList.size() > 1 ? 1 : 0);

            if (StringUtils.isNotBlank(matId)) {
                mappingVO.setHasMoreTip("该药品存在不同的产地或规格");
            } else if (StringUtils.isNotBlank(hisYpdm)) {
                mappingVO.setHasMoreTip("该药品存在不同的产地");
            }
        }

        for (CenterHisMappingVO mappingVO : mxList) {
            setUniqueId(mappingVO);
            if (Constant.BASIC_STRING_TWO.equals(par0)) {
                //医保外
                String npa = "1".equals(mappingVO.getNotPayAlone()) ? "0" : "0".equals(mappingVO.getNotPayAlone()) ? "1" : null;
                mappingVO.setIsInsurance(npa);
            }
        }

//        setDefaultUsage(centerYplx, centerStoreId, mxList);
        setDefaultUsage( centerStoreId, mxList);
        transMultiple(mxList);
        setDailyMaxDose(mxList);
        //此处判断是否开启提纯系数转换。
        if (Constant.BASIC_STRING_ONE.equals(PRESCRIPTION_TCXL)) {
            //开启。
            transTcxs(mxList);
        }
        setXZSM(mxList);
        return Page.getResEntityPageData(mxList, post);
    }

    public void setXZSM(List<CenterHisMappingVO> mxList) {
        TRegister register = AdminUtils.getCurrentRegister();
        String par0 = tSysParamService.getSysParam(Constant.INSURANCE_LIMIT_REMIND).getParValues();
        /**
         * 提醒患者范围控制:1门诊医保、2门诊自费、3住院医保、4住院自费、5门诊特病
         */
        String par1 = tSysParamService.getSysParam(Constant.INSURANCE_LIMIT_OBJECT).getParValues();
        if (!StringUtils.isBlank(par0) && Constant.BASIC_STRING_ONE.equals(par0)) {

            if (!StringUtils.isBlank(par1)) {
                boolean insuranceLimit = tSysParamService.insuranceLimitObject();
                for (CenterHisMappingVO centerHisMappingVO : mxList) {
                    if (!StringUtils.isBlank(centerHisMappingVO.getXzsm())) {
                        if (insuranceLimit) {
                            centerHisMappingVO.setXzsm(centerHisMappingVO.getHisYpmc() + " 限制说明：" + centerHisMappingVO.getXzsm());
                            tStatisticsHealthCareService.insertOrUpdate(centerHisMappingVO.getHisYpdm());
                        } else {
                            centerHisMappingVO.setXzsm(null);
                        }
                    }
                }

            }
        }
    }

    /**
     * 搜索中药制剂
     *
     * @param val  关键字
     * @param page 分页
     */
    public Object searchPreparationMat(String val, Page page) {
        ResEntity res = getHisYpmlId();
        if (!res.getStatus()) {
            return res;
        }
        String hisYpmlId = (String) res.getData();

        CenterHisMappingVO vo = new CenterHisMappingVO();
        vo.setHisYpmlId(hisYpmlId);


        List<CenterHisMappingVO> mxList = new ArrayList<>();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("keyValue", val);
        jsonObject.put("matType", Constant.BASIC_STRING_SEVEN);//中药类型（规格表中）
        //jsonObject.put("phaId", centerStoreId);//药房id
        jsonObject.put("drugIdHis", hisYpmlId);//his药品目录id
        jsonObject.put("distinctOrigin", "1");//去重产地，0不去重 1 去重
        jsonObject.put("page", page.getPage());
        jsonObject.put("limit", page.getLimit());
        //        1拼音 2五笔
        String searchtype = AdminUtils.getCurrentShuruma();
        if (StringJudges.isContainChinese(val)) {
            searchtype = "";
        }
        jsonObject.put("inputType", searchtype);
        ResEntity post = drugsRestTemplate.post("mat/searchMat", jsonObject);
        int code = post.getCode();
        if (200 == code) {
            String parValues = tSysParamService.getSysParam(Constant.MAT_SPECIAL_USAGE_SOURCE).getParValues();
            //List<LinkedHashMap> data = (List<LinkedHashMap>) post.getData();
            List<MatVo> matVos = JSON.parseArray(JSON.toJSONString(post.getData()), MatVo.class);
            for (MatVo matVo : matVos) {
                CenterHisMappingVO vo1 = new CenterHisMappingVO();
                CenterHisMappingVO centerHisMappingVO = transferMatBean(matVo, vo1, parValues);
                mxList.add(centerHisMappingVO);
            }

        }


        //开方页面“医保”列显示数据来源(1his 2.医保外，默认1 )
        String par0 = tSysParamService.getSysParam(Constant.PRESCRIPTION_MEDICAL_INSURANCE_SOURCE).getParValues();
        for (CenterHisMappingVO mappingVO : mxList) {
            setUniqueId(mappingVO);
            if (Constant.BASIC_STRING_TWO.equals(par0)) {
                //医保外
                String npa = "1".equals(mappingVO.getNotPayAlone()) ? "0" : "0".equals(mappingVO.getNotPayAlone()) ? "1" : null;
                mappingVO.setIsInsurance(npa);
            }
        }

        setPreparationMatUsage(mxList);
//        transMultiple(mxList);

        return Page.getResEntityPageData(mxList, post);
    }


    /**
     * 转换处方药品
     * HIS药品代码或知识库药品ID必须传一个
     */
    public ResEntity transMat(CenterHisMappingVORequest request) {

        //颗粒剂处方是否限制为同一产地（1是  0否）
        String par = tSysParamService.getSysParam(Constant.PRESCRIPTION_GRANULE_SAME_PLACE).getParValues();
        //开方页面“医保”列显示数据来源(1his接口数据IS_INSURANCE 2.医保外配置NOT_PAY_ALONE)
        String par1 = tSysParamService.getSysParam(Constant.PRESCRIPTION_MEDICAL_INSURANCE_SOURCE).getParValues();
        String PRESCRIPTION_TCXL = tSysParamService.getSysParam(Constant.PRESCRIPTION_TCXL).getParValues();
        //CenterHisMappingVO toVo = new CenterHisMappingVO();
        List<CenterHisMappingVO> list = new ArrayList<>();

        //调用接口获取转换药
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("distinctOrigin", par);//是否限制同一产地
        jsonObject.put("matType", request.getCenterYplx());//转换后的中药类型（规格表中）
        jsonObject.put("phaId", request.getStoreId());//转换后的药房id
        jsonObject.put("oldMatType", request.getOldCenterYplx());//转换前的中药类型（规格表中）
        jsonObject.put("oldPhaId", request.getOldStoreId());//转换前的药房id
        jsonObject.put("fromKnowleage", request.isFromKnow() ? 1 : 0);//是否来自知识库 0 否 1是
        jsonObject.put("drugIdHis", request.getDrugId());//his药品目录id
        jsonObject.put("manySpeSwitch", null == request.getManySpeSwitch() ? 0 :request.getManySpeSwitch() );//药品开方多规格下拉开关 1是 0否
        if ((Constant.BASIC_STRING_TWO.equals(request.getCenterYplx()) || Constant.BASIC_STRING_FIVE.equals(request.getCenterYplx()))
                && Constant.BASIC_STRING_ONE.equals(par)) {
            //颗粒剂保持同一产地
            jsonObject.put("matOriginId", request.getChanDi());
        }
        //原药品列表
        List<Object> objects = request.getVoList().stream().map(m -> JSONObject.toJSON(transCenterHisMappingVoToMatVo(new MatVo(), m))).collect(Collectors.toList());
        jsonObject.put("matList", objects);

        ResEntity post = drugsRestTemplate.post("mat/getChangeMat", jsonObject);

        if (!post.getStatus() || 200 != post.getCode()) {
            return ResEntity.error("【药房查询服务】" + post.getMessage());
        }
        //药品查询服务返回药品列表
        List<MatVo> matVos = JSON.parseArray(JSON.toJSONString(post.getData()), MatVo.class);
        //转换后药品列表
        String parValues = tSysParamService.getSysParam(Constant.MAT_SPECIAL_USAGE_SOURCE).getParValues();
        List<CenterHisMappingVO> toMatList = matVos.stream().map(m -> transferMatBean(m, new CenterHisMappingVO(), parValues,request.isFromKnow())).collect(Collectors.toList());

        for (int i = 0; i < toMatList.size(); i++) {

            //转换前药品
            CenterHisMappingVO fromVo = request.getVoList().get(i);
            //转换后药品
            CenterHisMappingVO toVo = toMatList.get(i);

            //setUniqueId(toVo);
            transMatDose(fromVo, toVo, request.isFromKnow() ? "1" : AdminUtils.getUserDoseIsDivisible());
            //增加manySpeSwitch判断是否开启多规格
            transDoseByManySpeSwitch(toVo,request.getManySpeSwitch(),toVo.getMatDoseList(),AdminUtils.getUserDoseIsDivisible());
            transDefaultUsage(request.getType(), fromVo, toVo);
            //此处判断是his还是医保外
            if (!Constant.BASIC_STRING_ONE.equals(par1)) {
                //医保外
                String npa = "1".equals(toVo.getNotPayAlone()) ? "0" : "0".equals(toVo.getNotPayAlone()) ? "1" : null;
                toVo.setIsInsurance(npa);
            }
            list.add(toVo);
        }


        transMultiple(list);
        setDailyMaxDose(list);
        //此处判断是否开启提纯系数转换。
        if (Constant.BASIC_STRING_ONE.equals(PRESCRIPTION_TCXL)) {
            //开启。
            transTcxs(list);
        }
        setXZSM(list);
        return new ResEntity(true, list);
    }

    /**
     * 根据oVo.getDose的值，向下匹配matDoseList数组中的最接近的数值
     * @param toVo t
     * @param manySpeSwitch
     * @param matDoseList
     */
    private void transDoseByManySpeSwitch(CenterHisMappingVO toVo, Integer manySpeSwitch,List<String> matDoseList,String doseIsDivisible) {
        if (
                manySpeSwitch == null || manySpeSwitch == 0 || toVo == null || toVo.getDose() == null
                    //    || Constant.BASIC_STRING_TWO.equals(doseIsDivisible)
                        || matDoseList == null || matDoseList.isEmpty()
        ){
            return;
        }else {

            BigDecimal dose = toVo.getDose();
            toVo.setDose( new BigDecimal( findClosestValue(toVo.getDose(),matDoseList)) );
            if (dose.compareTo( toVo.getDose()) != 0 && !Constant.BASIC_STRING_ONE.equals(doseIsDivisible)){
                toVo.setDoseIsDivisible(false);
            }
        }
    }

//    public static void main(String[] args) {
//        BigDecimal dose = new BigDecimal("10.00");
//        List<String> matDoseList = new ArrayList<>();
//        //matDoseList.add("6");
//        matDoseList.add("12.00");
//        matDoseList.add("8.00");
//        matDoseList.add("15.00");
//        matDoseList.add("7.00");
//
//        String closestValue = findClosestValue(dose, matDoseList);
//        System.out.println("Closest value: " + closestValue);
//    }

    /**
     * 计算向下匹配最近的值
     * @param dose
     * @param matDoseList
     * @return
     */
    private  String findClosestValue(BigDecimal dose, List<String> matDoseList) {
        String closestValue = null;
        BigDecimal minDiff = null;
        BigDecimal minNonAbsoluteDiff = null;

        for (String valueStr : matDoseList) {
            BigDecimal value = new BigDecimal(valueStr);
            BigDecimal diff = dose.subtract(value);

            if (minDiff == null || diff.abs().compareTo(minDiff) < 0) {
                minDiff = diff.abs();
                minNonAbsoluteDiff = value;
                closestValue = valueStr;
            } else if (diff.abs().compareTo(minDiff) == 0 && value.compareTo(minNonAbsoluteDiff) < 0) {
                minNonAbsoluteDiff = value;
                closestValue = valueStr;
            }
        }

        return closestValue;
    }

    /**
     * 使用提纯系数计算饮片或颗粒的值，计算结果都存入tcxsMatDose，不更改dose值。
     *
     * @param list
     */
    public void transTcxs(List<CenterHisMappingVO> list) {
        if (null == list) {
            return;
        }
        BigDecimal one = new BigDecimal("1");
        for (CenterHisMappingVO centerHisMappingVO : list) {
            String matType = centerHisMappingVO.getCenterYplx();
            BigDecimal tcxs = centerHisMappingVO.getTcxs();
            //dose不区分是颗粒还是饮片
            BigDecimal dose = centerHisMappingVO.getDose();
            BigDecimal tcxsMatDose = centerHisMappingVO.getTcxsMatDose();
            if (Constant.BASIC_STRING_ONE.equals(matType) || Constant.BASIC_STRING_FOUR.equals(matType)) {
                //散装饮片和小包装饮片
                if (null == tcxsMatDose && null != tcxs && null != dose) {
                    //这时dose是饮片
                    BigDecimal bigDecimal = dose.multiply(tcxs).setScale(2, RoundingMode.HALF_UP);
                    centerHisMappingVO.setTcxsMatDose(bigDecimal);
                }
            } else if (Constant.BASIC_STRING_TWO.equals(matType) || Constant.BASIC_STRING_FIVE.equals(matType)) {
                //散装颗粒和小包装颗粒
                if (null != tcxs && null != dose && null == tcxsMatDose) {
                    //颗粒转饮片，此时dose是颗粒
                    BigDecimal bigDecimal = dose.multiply(one.divide(tcxs, 6, RoundingMode.HALF_UP)).setScale(0, RoundingMode.HALF_UP);
                    centerHisMappingVO.setTcxsMatDose(bigDecimal);
                }
            }
        }
    }

    /**
     * 转换知识库制剂
     *
     * @param request CenterHisMappingVORequest
     * <AUTHOR>
     * @date 2021/6/22
     */
    public ResEntity transPreparationMat(CenterHisMappingVORequest request) {

        ResEntity res = getHisYpmlId();
        if (!res.getStatus()) {
            return res;
        }
        String ypmlId = (String) res.getData();

        //调用接口获取转换药
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("fromKnowleage", request.isFromKnow() ? 1 : 0);//是否来自知识库 0 否 1是
        jsonObject.put("drugIdHis", ypmlId);//his药品目录id
        //原药品列表
        List<Object> objects = request.getVoList().stream().map(m -> JSONObject.toJSON(transCenterHisMappingVoToMatVo(new MatVo(), m))).collect(Collectors.toList());
        jsonObject.put("matList", objects);

        ResEntity post = drugsRestTemplate.post("mat/getChangeMat", jsonObject);

        if (!post.getStatus() || 200 != post.getCode()) {
            return ResEntity.error("【药房查询服务】" + post.getMessage());
        }
        //药品查询服务返回药品列表
        List<MatVo> matVos = JSON.parseArray(JSON.toJSONString(post.getData()), MatVo.class);
        //转换后药品列表
        String parValues = tSysParamService.getSysParam(Constant.MAT_SPECIAL_USAGE_SOURCE).getParValues();
        List<CenterHisMappingVO> toMatList = matVos.stream().map(m -> transferMatBean(m, new CenterHisMappingVO(), parValues)).collect(Collectors.toList());


        return ResEntity.success(toMatList);
    }


    /**
     * 唯一标识（重复用药的判断）
     *
     * <AUTHOR>
     * @date 2021/3/4
     */
    private void setUniqueId(CenterHisMappingVO mat) {
        //处方同种草药判定规则：[0或1且][知识库ID][药房药品代码][HIS药品代码][上级药品代码][HIS规格ID]
        String par = tSysParamService.getSysParam(Constant.PRESCRIPTION_MEDICINE_COMPARE_RULE).getParValues();
        if (StringUtils.isBlank(par) || !StringUtils.isNumeric(par) || par.length() <= 1) {
            par = "0101";
        }

        String uniqueId;
        List<String> uniFields = new ArrayList<>();
        List<String> allFields = new ArrayList<>();
        allFields.add(mat.getMatId());
        allFields.add(mat.getCenterYpdm());
        allFields.add(mat.getHisYpdm());
        allFields.add(mat.getParentYpdm());
        allFields.add(mat.getHisGgid());

        String[] rule = par.split(Constant.BASIC_STRING_SPACE);

        for (int i = 1; i < rule.length; i++) {
            if (Constant.BASIC_STRING_ONE.equals(rule[i])) {
                uniFields.add(allFields.get(i - 1));
            }
        }

        if (Constant.BASIC_STRING_ONE.equals(rule[0])) {
            uniqueId = String.join("-", uniFields);
        } else {
            Optional<String> answer = uniFields.stream().filter(StringUtils::isNotBlank).findFirst();
            uniqueId = answer.orElseGet(() -> allFields.stream().filter(StringUtils::isNotBlank).collect(Collectors.joining("-")));
        }

        mat.setUniqueId(uniqueId);
    }

    /**
     * 转换剂量（保留一位小数）
     *
     * @param fromMat     来源药品
     * @param toMat       转换后药品
     * @param isDivisible "":提醒 1:四舍五入 2:自己填
     * <AUTHOR>
     * @date 2020/10/22
     */
    private void transMatDose(CenterHisMappingVO fromMat, CenterHisMappingVO toMat, String isDivisible) {
        toMat.setDoseIsDivisible(true);
        if (fromMat.getDose() != null) {
            BigDecimal dose = fromMat.getDose();
            //单位为克的不进行转换
            if (!Constant.UNIT_LIST_G.contains(fromMat.getCenterYpdw()) && StringUtils.isNotBlank(fromMat.getZhuanhuanxs())) {
                dose = dose.multiply(new BigDecimal(fromMat.getZhuanhuanxs()));
            }
            if (!Constant.UNIT_LIST_G.contains(toMat.getCenterYpdw()) && StringUtils.isNotBlank(toMat.getZhuanhuanxs())
                    && !"0".equals(toMat.getZhuanhuanxs())) {

                BigDecimal dose2 = dose.divide(new BigDecimal(toMat.getZhuanhuanxs()), 2, BigDecimal.ROUND_HALF_UP);
                dose = dose2.setScale(0, RoundingMode.HALF_UP);

                if (new BigDecimal(dose2.intValue()).compareTo(dose2) != 0) {
                    if (dose.compareTo(new BigDecimal("0")) == 0){
                        dose = new BigDecimal("1");
                    }
                    //剂量不能整除一个月内不提醒(1四舍五入2自己填)
                    if (StringUtils.isBlank(isDivisible)) {
                        toMat.setDoseIsDivisible(false);
                    } else if (Constant.BASIC_STRING_TWO.equals(isDivisible)) {
                        toMat.setDose(null);
                        return;
                    }
                }
            }
            toMat.setDose(dose);
        }
    }

    /**
     * 剂量限制倍数
     *
     * @param list list
     * <AUTHOR>
     * @date 2021/1/6
     */
    private void transMultiple(List<CenterHisMappingVO> list) {
        if (list == null) {
            return;
        }
        for (CenterHisMappingVO mappingVO : list) {
            if (StringUtils.isBlank(mappingVO.getZhuanhuanxs())) {
                mappingVO.setZhuanhuanxs(Constant.BASIC_STRING_ONE);
            }
            //单位为克时，限制倍数，倍数为转换系数（如当归10g*g，单位g，转换系数10）
            if (StringUtils.isNotBlank(mappingVO.getCenterYpdw()) && Constant.UNIT_LIST_G.contains(mappingVO.getCenterYpdw())) {
                mappingVO.setMultiple(mappingVO.getZhuanhuanxs());
            } else {
                mappingVO.setMultiple(Constant.BASIC_STRING_ONE);
            }
        }
    }

    /**
     * 控制日最大剂量
     *
     * @param list list
     * <AUTHOR>
     * @date 2021/1/6
     */
    private void setDailyMaxDose(List<CenterHisMappingVO> list) {
        if (list == null) {
            return;
        }
        TRegister register = AdminUtils.getCurrentRegister();
        if (register == null) {
            return;
        }

        //1门诊医保、2门诊自费、3住院医保、4住院自费、5门诊特病
        TSysParam param = tSysParamService.getSysParam(Constant.INSURANCE_LIMIT_OBJECT);
//        if (!((Constant.BASIC_STRING_ONE.equals(param.getParValues()) && (register.getClinicTypeId() != null && register.getClinicTypeId() == 2
//                || (register.getHisRecord() != null && Constant.BASIC_STRING_ONE.equals(register.getHisRecord().getIsSpecialDis()))))
//                || (Constant.BASIC_STRING_TWO.equals(param.getParValues()) && (register.getHisRecord() != null && !Constant.BASIC_STRING_ONE.equals(register.getHisRecord().getIsOwnExp()))))) {
//
//            for (CenterHisMappingVO mappingVO : list) {
//                mappingVO.setDailyMaxDoseIn(null);
//                mappingVO.setDailyMaxDoseExt(null);
//                mappingVO.setDailyMaxNumPrep(null);
//            }
//        }
        if (!tSysParamService.insuranceLimitObject()) {
            for (CenterHisMappingVO mappingVO : list) {
                mappingVO.setDailyMaxDoseIn(null);
                mappingVO.setDailyMaxDoseExt(null);
                mappingVO.setDailyMaxNumPrep(null);
            }
        }
    }


    /**
     * 默认用法
     *
     * @param centerYplx    中药类型
     * @param centerStoreId 药房ID
     * @param mxList        药品明细
     * <AUTHOR>
     * @date 2021/1/11
     */
    private void setDefaultUsage( String centerStoreId, List<CenterHisMappingVO> mxList) {

        String appId = AdminUtils.getCurrentAppId();
        String insCode = AdminUtils.getCurrentInsCode();

        String source = tSysParamService.getSysParam(Constant.MAT_SPECIAL_USAGE_SOURCE).getParValues();

        //获取知识库特殊用法
//        Map<String, String> specialUsageMat = new HashMap<>();
//
//        if ("know".equals(source)) {
//
//            StringBuilder sb = new StringBuilder();
//
//            for (CenterHisMappingVO mappingVO : mxList) {
//                if (StringUtils.isNotBlank(mappingVO.getMatId())) {
//                    sb.append(mappingVO.getMatId()).append(",");
//                }
//            }
//            if (sb.length() > 0) {
//                Map<String, Object> param = new HashMap<>();
//                param.put("pres", sb.substring(0, sb.length() - 1));
//                TRegister register = AdminUtils.getCurrentRegister();
//                if (register != null) {
//                    param.put("gravidity", register.getGravidity());
//                    param.put("age", register.getPatientAge().getYear().toString());
//                }
//                ResEntity res = knowRestTemplate.postKnow("check/pres", param);
//                if (res.getStatus()) {
//                    List<Map<String, Object>> array = (List<Map<String, Object>>) res.getData();
//                    for (Map<String, Object> map : array) {
//                        Object matId = map.get("matid");
//                        Object specialUsage = map.get("specialUsage");
//                        if (specialUsage != null && !"".equals(specialUsage)) {
//                            specialUsageMat.put(matId.toString(), specialUsage.toString());
//                        }
//                    }
//                }
//            }
//        }

        //中药用法选项
        LinkedHashMap<String, String> usageMap = parameterRedisService.getMatUsage(appId, insCode);

        //获取中药类型对应药房的默认用法
//        TDisplay display = redisService.getDisplayByMatTypeStoreId(
//                admin.getAppId(), admin.getInsCode(), AdminUtils.getCurrentDeptId(), centerYplx, centerStoreId);

        for (CenterHisMappingVO mappingVO : mxList) {
            //颗粒 默认空（冲服）
            if (Constant.BASIC_STRING_TWO.equals(mappingVO.getCenterYplx())
                    || Constant.BASIC_STRING_FIVE.equals(mappingVO.getCenterYplx())) {
                mappingVO.setUsageCode("");
                mappingVO.setUsage("");
                break;
            }

            //特殊用法取HIS的
            if ("his".equals(source) && StringUtils.isNotBlank(mappingVO.getUsage())) {

                mappingVO.setUsageCode(usageMap.get(mappingVO.getUsage()));

            } else if ("know".equals(source)
                    && StringUtils.isNotBlank(mappingVO.getMatId())) {

                //特殊用法取知识库的
                //注释掉2023-0707，
//                mappingVO.setUsage(specialUsageMat.get(mappingVO.getMatId()));
//                mappingVO.setUsageCode(usageMap.get(mappingVO.getUsage()));

            /*} else if (StringUtils.isNotBlank(display.getDefaultUsage())) {
                //药房默认用法
                mappingVO.setUsage(display.getDefaultUsage());
                mappingVO.setUsageCode(usageMap.get(mappingVO.getUsage()));
            */
            } else {
                mappingVO.setUsageCode("");
                mappingVO.setUsage("");
            }
        }
    }

    /**
     * 中药制剂默认用法
     *
     * @param mxList 药品明细
     * <AUTHOR>
     * @date 2021/6/9
     */
    private void setPreparationMatUsage(List<CenterHisMappingVO> mxList) {

        //中药用法选项
        LinkedHashMap<String, String> usageMap = parameterRedisService.getPreparationMatUsage(AdminUtils.getCurrentAppId(), AdminUtils.getCurrentInsCode());

        for (CenterHisMappingVO mappingVO : mxList) {

            //特殊用法取HIS的
            if (StringUtils.isNotBlank(mappingVO.getUsage())) {
                mappingVO.setUsageCode(usageMap.get(mappingVO.getUsage()));
            }
        }
    }

    /**
     * 转换用法
     *
     * @param type    处方类型
     * @param fromMat 转换前
     * @param toMat   转换后
     * <AUTHOR>
     * @date 2021/1/11
     */
    public void transDefaultUsage(int type, CenterHisMappingVO fromMat, CenterHisMappingVO toMat) {
        //2021.03.15改
        if (type == 2 && "外用".equals(fromMat.getUsage())) {
            //外用默认空
            toMat.setUsageCode("");
            toMat.setUsage("");
        } else if (Constant.BASIC_STRING_TWO.equals(toMat.getCenterYplx()) //2散装颗粒
                || Constant.BASIC_STRING_FIVE.equals(toMat.getCenterYplx())) { //5小包装颗粒
            //颗粒所有默认空（冲服）
            toMat.setUsageCode("");
            toMat.setUsage("");
        } else if ((Constant.BASIC_STRING_ONE.equals(toMat.getCenterYplx()) //1散装饮片
                || Constant.BASIC_STRING_FOUR.equals(toMat.getCenterYplx())) //4小包装饮片
                && type == 1 && "煎服".equals(fromMat.getUsage())) {
            //饮片煎服默认空
            toMat.setUsageCode("");
            toMat.setUsage("");
        } else if (StringUtils.isNotBlank(fromMat.getUsage())) {
            toMat.setUsage(fromMat.getUsage());
            toMat.setUsageCode(fromMat.getUsageCode());
        } else if (toMat.getUsage() == null) {
            toMat.setUsageCode("");
            toMat.setUsage("");
        }
    }


}
