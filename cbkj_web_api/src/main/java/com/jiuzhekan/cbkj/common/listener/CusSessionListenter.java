package com.jiuzhekan.cbkj.common.listener;

import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.annotation.WebListener;
import javax.servlet.http.HttpSessionEvent;
import javax.servlet.http.HttpSessionListener;

@Slf4j
@WebListener
public class CusSessionListenter implements HttpSessionListener {
    @Override
    public void sessionCreated(HttpSessionEvent httpSessionEvent) {
        log.info("session新增："+httpSessionEvent.getSession().getId());
    }

    @Override
    public void sessionDestroyed(HttpSessionEvent httpSessionEvent) {
        String sessionID = httpSessionEvent.getSession().getId();
        if(null != AdminUtils.getCurrentHr()){
            log.error("正常退出");
        }
    }
}