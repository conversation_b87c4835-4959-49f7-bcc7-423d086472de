package com.jiuzhekan.cbkj.service.myData;


import com.alibaba.fastjson.JSONArray;
import com.jiuzhekan.cbkj.beans.myData.TPersonalRuleAuth;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.beans.sysBeans.SysAdminPractice;
import com.jiuzhekan.cbkj.common.http.PlatformRestTemplate;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.controller.myData.TPersonalRuleAuthReq;
import com.jiuzhekan.cbkj.mapper.myData.TPersonalRuleAuthMapper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 协定方权限
 *
 * <AUTHOR>
 * @date 2021/05/20
 */
@Service
public class TPersonalRuleAuthService {

    @Autowired
    private TPersonalRuleAuthMapper tPersonalRuleAuthMapper;
    @Autowired
    private PlatformRestTemplate platformRestTemplate;


    /**
     * 根据处方ID获取权限树
     *
     * @param persPreId persPreId
     * @param ruleId    ruleId
     * @return
     */
    public ResEntity getTreeByPreId(String persPreId, String ruleId) {

        String appId = AdminUtils.getCurrentAppId();
        String insCode = AdminUtils.getCurrentInsCode();

        if (Constant.BASIC_INS_CODE.equals(insCode)) {
            return ResEntity.error("当前用户没有所属医疗机构，无法使用该功能");
        }

        TPersonalRuleAuth t = new TPersonalRuleAuth();
        t.setPersPreId(persPreId);
        t.setRuleId(ruleId);
        List<TPersonalRuleAuth> authList = tPersonalRuleAuthMapper.getListByPreId(t);
        Set<String> authSet = new HashSet<>();
        for (TPersonalRuleAuth auth : authList) {
            authSet.add(auth.getUserId());
        }

        Map<String, Object> params = new HashMap<>();
        params.put("appId", appId);
        params.put("insCode", insCode);
        //String deptId = AdminUtils.getCurrentDeptId();
        //if (!Constant.BASIC_DEPT_ID.equals(deptId)) {
        //    params.put("deptId", deptId);
        //}
        ResEntity resEntity = platformRestTemplate.post("user/tree", params);
        if (!resEntity.getStatus()) {
            return resEntity;
        }

        JSONArray insArr = new JSONArray();
        List<Map<String, Object>> appList = (List<Map<String, Object>>) resEntity.getData();

        for (Map<String, Object> app : appList) {

            List<Map<String, Object>> insList = (List<Map<String, Object>>) app.get("children");

            for (Map<String, Object> ins : insList) {

                List<Map<String, Object>> deptList = (List<Map<String, Object>>) ins.get("children");

                for (Map<String, Object> dept : deptList) {

                    List<Map<String, Object>> userList = (List<Map<String, Object>>) dept.get("children");

                    for (Map<String, Object> user : userList) {
                        user.put("checked", authSet.contains(user.get("userId")));
                    }
                }

                insArr.add(ins);
            }
        }

        return ResEntity.success(insArr);
    }


    /**
     * 保存权限
     *
     * @param req req
     * <AUTHOR>
     * @date 2021/05/20
     */
    public ResEntity saveAuth(TPersonalRuleAuthReq req) {

        if (StringUtils.isBlank(req.getPersPreId())) {
            return ResEntity.error("处方ID不能为空");
        }
        if (StringUtils.isBlank(req.getRuleId())) {
            return ResEntity.error("处方共享级别不能为空");
        }
        if (req.getAuthList() == null || req.getAuthList().size() == 0) {
            return ResEntity.error("参数不能为空");
        }

        Date currentDate = new Date();
        String adminId = AdminUtils.getCurrentHr().getId();

        for (TPersonalRuleAuth auth : req.getAuthList()) {
            if (StringUtils.isBlank(auth.getDeptId())) {
                return ResEntity.error("科室ID不能为空");
            }
            if (StringUtils.isBlank(auth.getUserId())) {
                return ResEntity.error("用户ID不能为空");
            }

            auth.setPersPreId(req.getPersPreId());
            auth.setRuleId(req.getRuleId());
            auth.setCreateDate(currentDate);
            auth.setCreateUser(adminId);
        }

        tPersonalRuleAuthMapper.deleteByPreAndRule(req.getPersPreId(), req.getRuleId());

        tPersonalRuleAuthMapper.insertList(req.getAuthList());

        return ResEntity.success(null);
    }

    @Async
    public void savePersonalPuleAuth(SysAdminPractice practice) {
        if (StringUtils.isNotBlank(practice.getDepId()) && StringUtils.isNotBlank(practice.getAdminId())) {
            tPersonalRuleAuthMapper.saveUserAuth(practice.getDepId(), practice.getAdminId());
        }
    }
}
