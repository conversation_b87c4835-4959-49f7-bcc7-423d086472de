package com.jiuzhekan.statistics.mapper.function;

import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;
import com.jiuzhekan.statistics.beans.function.SimpleP;
import com.jiuzhekan.statistics.beans.function.TStatisticsFunction2;
import com.jiuzhekan.statistics.beans.register.QueryParam;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public interface TStatisticsFunction2Mapper extends BaseMapper<TStatisticsFunction2> {

    Integer sumUsageTimes(TStatisticsFunction2 tStatisticsFunction2);

    /**
     * 查所某机构下 所有功能统计数据 或者是 某个功能统计数据
     *
     * @param tStatisticsFunction2
     * @return
     */
    List<TStatisticsFunction2> sumUsageTimes2(QueryParam queryParam);

    /**
     *  GROUP BY app_id,`function` 计算医共体总数
     * @param queryParam
     * @return
     */
    List<TStatisticsFunction2> sumUsageTimes3(QueryParam queryParam);

    /**
     * 统计分类下所有总和
     *
     * @param queryParam
     * @return
     */
    List<SimpleP> sumUsageTimesAll2(QueryParam queryParam);

    /**
     * 根据用户、功能、创建时间查询已有记录
     *
     * @param tStatisticsFunction2 tStatisticsFunction2
     * @return TStatisticsFunction2
     * <AUTHOR>
     * @date 2022/5/31 10:39
     */
    TStatisticsFunction2 getByDetail(TStatisticsFunction2 tStatisticsFunction2);

    /**
     * 更新使用次数、插入时间
     * @param tStatisticsFunction2 tStatisticsFunction2
     * <AUTHOR>
     * @date 2022/5/31 10:48
     */
    void updateUsageTimesInsertDate(TStatisticsFunction2 tStatisticsFunction2);

    /**
     * 获取统计数据
     * @param queryParam
     * @return
     */
    Integer sumStatistics(QueryParam queryParam);
}