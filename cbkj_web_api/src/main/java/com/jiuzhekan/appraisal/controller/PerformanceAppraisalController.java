package com.jiuzhekan.appraisal.controller;

import com.jiuzhekan.appraisal.bean.*;
import com.jiuzhekan.appraisal.bean.dto.*;
import com.jiuzhekan.appraisal.service.PerformanceAppraisalService;
import com.jiuzhekan.appraisal.service.PerformanceInsService;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.excel.ExportExcel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/3/27 16:20
 * @Version 1.0
 */
@Api(value = "业绩考核", tags = "业绩考核")
@RestController
public class PerformanceAppraisalController {
    private final PerformanceAppraisalService performanceAppraisalService;
    private final PerformanceInsService performanceInsService;
    private final HttpServletResponse response;

    public PerformanceAppraisalController(PerformanceAppraisalService performanceAppraisalService, PerformanceInsService performanceInsService, HttpServletResponse response) {
        this.performanceAppraisalService = performanceAppraisalService;
        this.performanceInsService = performanceInsService;
        this.response = response;
    }

    @ApiOperation(value = "获取机构列表")
    @GetMapping("getInsList")
    @ResponseBody
    public ResEntity getIns() {
        return ResEntity.success(performanceInsService.getIns());
    }

    @ApiOperation(value = "获取机构下所有科室列表")
    @GetMapping("getDeptList")
    @ResponseBody
    public ResEntity getDeptList(String insCode) {
        HashMap<String, String> dept = performanceInsService.getDept(insCode);
        HashMap<String, String> deptNew = new HashMap<>();
        //处理dept键值对 只要 - 符号后边内容
        for (Map.Entry<String, String> entry : dept.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            deptNew.put(key.substring(key.indexOf("-") + 1), value.substring(value.indexOf("-") + 1));
        }

        return ResEntity.success(deptNew);

    }


    @ApiOperation(value = "获取指标代码列表")
    @GetMapping("getZhiBiaoList")
    @ResponseBody
    public ResEntity getZhiBiaoList(PerformanceZhiBiaoDiclRe performanceZhiBiaoDiclRe) {
        return ResEntity.success(performanceAppraisalService.getZhiBiaoList(performanceZhiBiaoDiclRe));
    }

    @ApiOperation(value = "子指标-查询业绩考核", response = JxkhZibiaoSubClassRes.class)
    @GetMapping("getZZBPageDatas")
    @ResponseBody
    public ResEntity getZZBPageDatas(PerformanceAppraisalRe performanceAppraisalRe) {
//        Object zzbPageDatas = performanceAppraisalService.getZZBPageDatas(performanceAppraisalRe);
        return ResEntity.success(performanceAppraisalService.getZZBPageDatas(performanceAppraisalRe));
    }

    @ApiOperation(value = "子指标-查询业绩考核科室", response = JxkhZibiaoSubClassRes.class)
    @GetMapping("getZZBDeptPageDatas")
    @ResponseBody
    public ResEntity getZZBDeptPageDatas(PerformanceAppraisalDeptRe performanceAppraisalRe) {
//        if (StringUtils.isBlank(performanceAppraisalRe.getInsCode())){
//            return ResEntity.error("当前查询行的机构代码不能为空");
//        }
//        if (StringUtils.isBlank(performanceAppraisalRe.getZibiaodm())){
//            return ResEntity.error("当前查询行的子指标代码不能为空");
//        }
       if (StringUtils.isBlank(performanceAppraisalRe.getInsCode())){
           Map<String, String> ins = performanceInsService.getIns();
           Set<String> strings = ins.keySet();
           StringBuilder temp = new StringBuilder();
           for (String s : strings) {
               temp.append(",").append(s);
           }
           performanceAppraisalRe.setInsCode(temp.substring(1, temp.length()));
       }
        return ResEntity.success(performanceAppraisalService.getZZBDeptPageDatas(performanceAppraisalRe));
    }


    @ApiOperation(value = "主指标-查询业绩考核", response = JxkhZibiaoRes.class)
    @GetMapping("getMZBPageDatas")
    @ResponseBody
    public ResEntity getMZBPageDatas(PerformanceAppraisalZhuRe performanceAppraisalZhuRe) {

        return ResEntity.success(performanceAppraisalService.getMZBPageDatas(performanceAppraisalZhuRe));
    }

    @ApiOperation(value = "主指标-查询业绩考核科室", response = PerformanceDept.class)
    @GetMapping("getMZBDeptPageDatas")
    @ResponseBody
    public ResEntity getMZBDeptPageDatas(PerformanceAppraisalDeptZhuRe performanceAppraisalRe) {
//        if (StringUtils.isBlank(performanceAppraisalRe.getInsCode())){
//            return ResEntity.error("当前查询行的机构代码不能为空");
//        }
        if (StringUtils.isBlank(performanceAppraisalRe.getInsCode())){
            Map<String, String> ins = performanceInsService.getIns();
            Set<String> strings = ins.keySet();
            StringBuilder temp = new StringBuilder();
            for (String s : strings) {
                temp.append(",").append(s);
            }
            performanceAppraisalRe.setInsCode(temp.substring(1, temp.length()));
        }
        return ResEntity.success(performanceAppraisalService.getMZBDeptPageDatas(performanceAppraisalRe));
    }

    @ApiOperation(value = "主指标-指标说明", response = JxkhZibiao.class)
    @GetMapping("getZhiBiaoInfo")
    @ResponseBody
    public ResEntity getZhiBiaoInfo(String zibiaodm) {

        return ResEntity.success(performanceAppraisalService.getZhiBiaoInfo(zibiaodm));
    }

    @ApiOperation(value = "指标-数据下载")
    @GetMapping(value = "getZhiBiaoDownload",produces = "application/vnd.ms-excel")
//    @ResponseBody
    public String getZhiBiaoInfo(PerformanceAppraisalDownload p) {
        if (p.getType() == null) {
            return "下载类型不能为空";
        }
        LinkedHashMap<String, String> headers = new LinkedHashMap<>();
        if (p.getType() == 1) {
            PerformanceAppraisalZhuRe performanceAppraisalRe = new PerformanceAppraisalZhuRe();
            BeanUtils.copyProperties(p, performanceAppraisalRe);
            ArrayList<JxkhZibiaoRes> mzbPageDatas = performanceAppraisalService.getMZBPageDatas(performanceAppraisalRe);
            headers.put("jigoudmmc", "机构名称");
            headers.put("num", "国考序号");
            headers.put("zibiaomc", "指标名称");
            headers.put("jisuangsValue", "指标值");
            headers.put("jiliangdw", "单位");
            headers.put("gongshi", "计算公式与指标值");
            List<Object> performancZhuExcels = new ArrayList<Object>();
            int temp = 0;
            for (JxkhZibiaoRes mzbPageData : mzbPageDatas) {
                temp ++;
                PerformancZhuExcel excel = new PerformancZhuExcel();
                excel.setJigoudmmc(mzbPageData.getJigoudmmc());
                excel.setNum(temp+"");
                excel.setZibiaomc(mzbPageData.getZibiaomc());
                excel.setJisuangsValue(mzbPageData.getJisuangsValue());
                excel.setJiliangdw(mzbPageData.getJiliangdw());
                excel.setGongshi(mzbPageData.getJisuanff() + "=" + mzbPageData.getJisuangs());
                performancZhuExcels.add(excel);
            }
             excelCreate(headers, performancZhuExcels, "主指标数据_机构维度_");
        } else if (p.getType() == 2) {
            headers.put("jigoudmmc", "机构名称");
            headers.put("keshidmmc", "科室");
            headers.put("num", "国考序号");
            headers.put("zibiaomc", "指标名称");
            headers.put("jisuangsValue", "指标值");
            headers.put("jiliangdw", "单位");
            headers.put("gongshi", "计算公式与指标值");
            PerformanceAppraisalDeptZhuRe performanceAppraisalDeptRe = new PerformanceAppraisalDeptZhuRe();
            BeanUtils.copyProperties(p, performanceAppraisalDeptRe);
            ArrayList<JxkhZibiaoRes> mzbDeptPageDatas = performanceAppraisalService.getMZBDeptPageDatas(performanceAppraisalDeptRe);
            ArrayList<Object> list = new ArrayList<>();
            int temp = 0;
            for (JxkhZibiaoRes mzbPageData : mzbDeptPageDatas) {
                temp ++;
                PerformancZhuDeptExcel excel = new PerformancZhuDeptExcel();
                excel.setKeshidmmc(mzbPageData.getKeshidmmc());
                excel.setJigoudmmc(mzbPageData.getJigoudmmc());
                excel.setNum(temp+"");
                excel.setZibiaomc(mzbPageData.getZibiaomc());
                excel.setJisuangsValue(mzbPageData.getJisuangsValue());
                excel.setJiliangdw(mzbPageData.getJiliangdw());
                excel.setGongshi(mzbPageData.getJisuanff() + "=" + mzbPageData.getJisuangs());
                list.add(excel);
            }
            excelCreate(headers, list, "主指标数据_科室维度_");
        } else if (p.getType() == 3) {
            headers.put("jigoudmmc", "机构名称");
            headers.put("zibiaodm", "指标编码");
            headers.put("zibiaomc", "指标名称");
            headers.put("jisuangsValue", "指标值");
            headers.put("jiliangdw", "单位");
            PerformanceAppraisalRe performanceAppraisalRe = new PerformanceAppraisalRe();
            ArrayList<JxkhZibiaoSubClassRes> zzbPageDatas = performanceAppraisalService.getZZBPageDatas(performanceAppraisalRe);
            ArrayList<Object> list = new ArrayList<>();
            for (JxkhZibiaoSubClassRes mzbPageData : zzbPageDatas) {

                PerformancZiExcel excel = new PerformancZiExcel();
                excel.setZibiaodm(mzbPageData.getZibiaodm());
                excel.setJigoudmmc(mzbPageData.getJigoudmmc());
                excel.setZibiaomc(mzbPageData.getZibiaomc());
                excel.setJisuangsValue(mzbPageData.getJisuangsValue());
                excel.setJiliangdw(mzbPageData.getJiliangdw());
                list.add(excel);
            }
            excelCreate(headers, list, "子指标数据_机构维度_");
        } else if (p.getType() == 4) {
            headers.put("jigoudmmc", "机构名称");
            headers.put("keshimc", "科室名称");
            headers.put("zibiaodm", "指标编码");
            headers.put("zibiaomc", "指标名称");
            headers.put("jisuangsValue", "指标值");
            headers.put("jiliangdw", "单位");
            PerformanceAppraisalDeptRe performanceAppraisalDeptRe = new PerformanceAppraisalDeptRe();
            ArrayList<JxkhZibiaoSubClassRes> zzbDeptPageDatas = performanceAppraisalService.getZZBDeptPageDatas(performanceAppraisalDeptRe);
            ArrayList<Object> list = new ArrayList<>();
            for (JxkhZibiaoSubClassRes mzbPageData : zzbDeptPageDatas) {

                PerformancZiDeptExcel excel = new PerformancZiDeptExcel();
                excel.setKeshimc(mzbPageData.getKeshidmmc());
                excel.setZibiaodm(mzbPageData.getZibiaodm());
                excel.setJigoudmmc(mzbPageData.getJigoudmmc());
                excel.setZibiaomc(mzbPageData.getZibiaomc());
                excel.setJisuangsValue(mzbPageData.getJisuangsValue());
                excel.setJiliangdw(mzbPageData.getJiliangdw());
                list.add(excel);
            }
            excelCreate(headers, list, "子指标数据_科室维度_");
        }
        return "";
    }

    public void excelCreate(LinkedHashMap<String, String> headers, List<Object> list, String fileName){
        SXSSFWorkbook workbook = new SXSSFWorkbook(100);
        ExportExcel<Object> exportExcel = new ExportExcel<>();
        // 生成一个表格
        SXSSFSheet sheet = workbook.createSheet();
        exportExcel.packDataCell(sheet, 0, headers, list);
        //每创建完成一个sheet页就把数据刷新到磁盘
        try {
            sheet.flushRows();
            exportExcel.getExportedFile(workbook, fileName, response);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

}
