package com.jiuzhekan.healthpres.service;

import com.jiuzhekan.cbkj.beans.business.patients.TPatientAge;
import com.jiuzhekan.cbkj.beans.business.patients.TPatients;
import com.jiuzhekan.cbkj.beans.sysApp.SysInstitution;
import com.jiuzhekan.cbkj.beans.sysBeans.AdminInfo;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import com.jiuzhekan.cbkj.common.utils.MakeOrderNum;
import com.jiuzhekan.cbkj.service.business.patients.TPatientsService;
import com.jiuzhekan.healthpres.beans.*;
import com.jiuzhekan.healthpres.beans.healthPrescribingControllerVo.*;
import com.jiuzhekan.healthpres.beans.heathPresControllerVo.HealthPreModelMainItemResponse;
import com.jiuzhekan.healthpres.beans.heathPresControllerVo.HealthPreModelMainResponse;
import com.jiuzhekan.healthpres.beans.heathPresControllerVo.RecommendSearchQuery;
import com.jiuzhekan.healthpres.mapper.*;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/5/30 10:02
 * @Version 1.0
 */
@Service
public class HealthSavePreService {
    private final TUserQuestionnaireCommonWestDisMapper tUserQuestionnaireCommonWestDisMapper;
    private final TUserQuestionnaireMainMapper tUserQuestionnaireMainMapper;
    private final TPatientsService patientsService;
    private final HealthTypeService healthTypeService;
    private final TUserQuestionnaireResultMapper tUserQuestionnaireResultMapper;
    private final TUserQuestionnaireResultItemMapper tUserQuestionnaireResultItemMapper;
    private final TUserQuestionnaireResultAcuMapper tUserQuestionnaireResultAcuMapper;


    public HealthSavePreService(TUserQuestionnaireCommonWestDisMapper tUserQuestionnaireCommonWestDisMapper, TUserQuestionnaireMainMapper tUserQuestionnaireMainMapper, TPatientsService patientsService, HealthTypeService healthTypeService, TUserQuestionnaireResultMapper tUserQuestionnaireResultMapper, TUserQuestionnaireResultItemMapper tUserQuestionnaireResultItemMapper, TUserQuestionnaireResultAcuMapper tUserQuestionnaireResultAcuMapper) {
        this.tUserQuestionnaireCommonWestDisMapper = tUserQuestionnaireCommonWestDisMapper;
        this.tUserQuestionnaireMainMapper = tUserQuestionnaireMainMapper;
        this.patientsService = patientsService;
        this.healthTypeService = healthTypeService;
        this.tUserQuestionnaireResultMapper = tUserQuestionnaireResultMapper;
        this.tUserQuestionnaireResultItemMapper = tUserQuestionnaireResultItemMapper;
        this.tUserQuestionnaireResultAcuMapper = tUserQuestionnaireResultAcuMapper;
    }

    /**
     * 获取健康类型下拉、处方内容以及明細
     *
     * @param patientId
     * @param disId
     * @return
     */
    public GetTypesHealthPrescribing getTypes(String patientId, String westDisId, String chineseDisId) {
        //获取推荐
        RecommendSearchQuery query = new RecommendSearchQuery();
        query.setPatientId(patientId);
        query.setWestDisId(westDisId);
        query.setChineseDisId(chineseDisId);
        //根据中西医病获取推荐病
        List<GetRecommendByPatient> bianZhengRecommendByPatient = tUserQuestionnaireCommonWestDisMapper.getBianZhengRecommendByPatientNew(query);
        LinkedHashMap<String, String> healthType = healthTypeService.getHealthType();


        TPatients tPatients = patientsService.findById(patientId);
        //通过证件号获取年龄
        TPatientAge patientAge = TPatientsService.getAgeMonthByBirth(tPatients.getPatientBirthday());
        //获取出生到现在几个月了
        Short age2 = patientAge.getAge2();
        if (age2 > 39) {
            //去掉儿童的下拉选项
            healthType.remove("1-1");
            healthType.remove("1-2");
            healthType.remove("1-3");
        }
        if (!bianZhengRecommendByPatient.isEmpty()) {
            //有推荐的，放到最后
            healthType.remove("3-1");
            healthType.put("3-1", "中医健康处方");
        }

        //healthType 改成ArrayList
        ArrayList<GetRecommendByPatient> getRecommendByPatients = new ArrayList<>();
        healthType.forEach((k, v) -> {
            GetRecommendByPatient getRecommendByPatient = new GetRecommendByPatient();
            getRecommendByPatient.setClassType(k);
            getRecommendByPatient.setClassTypeName(v);
            getRecommendByPatients.add(getRecommendByPatient);
        });


        List<HealthPreModelMainResponse> healthPreModelAllList = tUserQuestionnaireMainMapper.getHealthPreModelAllList();
        GetTypesHealthPrescribing getTypesHealthPrescribing = new GetTypesHealthPrescribing();
        ArrayList<GetTypesHealthPrescribingSelect> selects = new ArrayList<>();

        getRecommendByPatients.forEach((getRecommendByPatient) -> {
            GetTypesHealthPrescribingSelect select = new GetTypesHealthPrescribingSelect();
            select.setTypeName(getRecommendByPatient.getClassTypeName());
            select.setTypeCode(getRecommendByPatient.getClassType());



            boolean b = false;
            b = bianZhengRecommendByPatient.stream().anyMatch(item -> item.getClassType().equals(getRecommendByPatient.getClassType()));
            //是否是推荐的第二判断，年龄
            if (age2 <= 18 && "1-1".equals(getRecommendByPatient.getClassType())) {
                b = true;
            } else if ( age2 >= 18 &&age2 < 30 && "1-2".equals(getRecommendByPatient.getClassType())) {
                b = true;
            } else if (age2 >= 30 && age2 <= 39 && "1-3".equals(getRecommendByPatient.getClassType())) {
                b = true;
            }
            select.setRecommend(b);

            healthPreModelAllList.forEach(item -> {
                if (item.getQuestionnaireMainSecondTypeCode().equals(getRecommendByPatient.getClassType())) {
                    //匹配上了
                    ArrayList<GetTypesHealthPrescribingContent> getTypesHealthPrescribingContents = new ArrayList<>();
                    List<HealthPreModelMainItemResponse> questionnaireMainItemList = item.getQuestionnaireMainItemList();
                    select.setQuestionnaireMainSecondTypeName(getRecommendByPatient.getClassTypeName());
                    select.setQuestionnaireMainSecondTypeCode(getRecommendByPatient.getClassType());
                    select.setQuestionnaireMainId(item.getQuestionnaireMainId());
                    select.setQuestionnaireMainFirstLine(item.getQuestionnaireMainFirstLine());
                    select.setQuestionnaireMainTips(item.getQuestionnaireMainTips());
                    select.setQuestionnaireMainTitle(item.getQuestionnaireMainTitle());
                    select.setQuestionnaireMainType( Integer.valueOf(getRecommendByPatient.getClassType().substring(0,1)) );


                    questionnaireMainItemList.forEach(questionnaireMainItem -> {
                        GetTypesHealthPrescribingContent content = new GetTypesHealthPrescribingContent();
                        content.setOptionTypeId(questionnaireMainItem.getItemId() + "");
                        content.setOptionTypeName(questionnaireMainItem.getItemTitle());
                        GetTypesHealthPrescribingItem getTypesHealthPrescribingItem = new GetTypesHealthPrescribingItem();
                        BeanUtils.copyProperties(questionnaireMainItem, getTypesHealthPrescribingItem);
                        content.setItem(getTypesHealthPrescribingItem);



                        getTypesHealthPrescribingContents.add(content);
                    });
                    select.setContents(getTypesHealthPrescribingContents);
                }
            });
            if (b) {
                if (!selects.isEmpty()){
//                    GetTypesHealthPrescribingSelect select1 = selects.get(0);
                    if (select.getTypeName().contains("儿童")){
                        selects.add(0, select);
                    }else {
                        GetTypesHealthPrescribingSelect select1 = selects.get(0);
                        if (select1.getTypeName().contains("儿童")){
                            selects.add(1, select);
                        }else {
                            selects.add(0, select);
                        }
                    }
                }else {
                    selects.add(0, select);
                }

            } else {
                selects.add(select);
            }
        });
        getTypesHealthPrescribing.setTypes(selects);
        //把getTypesHealthPrescribing中的types推荐的放到最前面


        return getTypesHealthPrescribing;
    }

    @Transactional(rollbackFor = Exception.class)
    public ResEntity saveHealthPre(@Valid HealthPrescribingRequestVo healthPrescribingRequestVo) {

        String re = healthPrescribingRequestVo.getQuestionnaireMainSecondTypeName().replace("儿童保健", "");
        healthPrescribingRequestVo.setQuestionnaireMainSecondTypeName( re );
        String mainSecondTypeCode = healthPrescribingRequestVo.getQuestionnaireMainSecondTypeCode();
        String substring = mainSecondTypeCode.substring(0, 1);
        AdminInfo currentHr = AdminUtils.getCurrentHr();

        String appId = AdminUtils.getCurrentAppId();
        String insCode = AdminUtils.getCurrentInsCode();
        String currentDeptId = AdminUtils.getCurrentDeptId();
        TPatients patients = patientsService.findById(healthPrescribingRequestVo.getPatientId());
        SysInstitution sysInstitution = patientsService.getSysInstitution();
        TUserQuestionnaireResult tUserQuestionnaireResult = new TUserQuestionnaireResult();
        tUserQuestionnaireResult.setResultId(IDUtil.getID());
        tUserQuestionnaireResult.setAppName(sysInstitution.getAppName());
        tUserQuestionnaireResult.setInsName(sysInstitution.getInsName());
        if ("1".equals(substring)) {
            tUserQuestionnaireResult.setResultReportName(healthPrescribingRequestVo.getQuestionnaireMainSecondTypeName() + "中医保健报告");
            tUserQuestionnaireResult.setResultReportTypeInfo(healthPrescribingRequestVo.getQuestionnaireMainSecondTypeName() + "儿童保健");
        } else if ("2".equals(substring)) {
            tUserQuestionnaireResult.setResultReportName(healthPrescribingRequestVo.getQuestionnaireMainSecondTypeName() + "中医慢病调理报告");
            tUserQuestionnaireResult.setResultReportTypeInfo(healthPrescribingRequestVo.getQuestionnaireMainSecondTypeName() + "中医慢病调理");
        } else if ("3".equals(substring)) {
            tUserQuestionnaireResult.setResultReportName(healthPrescribingRequestVo.getQuestionnaireMainSecondTypeName() + "健康处方");
            tUserQuestionnaireResult.setResultReportTypeInfo("中医健康处方");
        }
        tUserQuestionnaireResult.setCreateDoctorId(currentHr.getId());
        tUserQuestionnaireResult.setCreateDoctorName(currentHr.getNameZh());
        tUserQuestionnaireResult.setInsertTime(new Date());
        tUserQuestionnaireResult.setStatus(0);
        tUserQuestionnaireResult.setPatientId(healthPrescribingRequestVo.getPatientId());
        tUserQuestionnaireResult.setPatientName(patients.getPatientName());
        tUserQuestionnaireResult.setQuestionnaireMainFirstLine(healthPrescribingRequestVo.getQuestionnaireMainFirstLine());
        tUserQuestionnaireResult.setQuestionnaireMainType(Integer.parseInt(substring));
        tUserQuestionnaireResult.setQuestionnaireMainSecondTypeCode(healthPrescribingRequestVo.getQuestionnaireMainSecondTypeCode());
        tUserQuestionnaireResult.setQuestionnaireMainSecondTypeName(healthPrescribingRequestVo.getQuestionnaireMainSecondTypeName());
        tUserQuestionnaireResult.setQuestionnaireMainTitle(healthPrescribingRequestVo.getQuestionnaireMainTitle());
        tUserQuestionnaireResult.setQuestionnaireMainTips(healthPrescribingRequestVo.getQuestionnaireMainTips());
        tUserQuestionnaireResult.setResultReportTime(new Date());
        if (!Constant.BASIC_APP_ID.equals(appId)) {
            tUserQuestionnaireResult.setAppId(appId);
        }
        if (!Constant.BASIC_INS_CODE.equals(insCode)) {
            tUserQuestionnaireResult.setInsCode(insCode);
        }
        if (!Constant.BASIC_DEPT_ID.equals(currentDeptId)) {
            tUserQuestionnaireResult.setDeptId(currentDeptId);
        }
        tUserQuestionnaireResult.setResultCode(MakeOrderNum.makeBzNum());
        tUserQuestionnaireResultMapper.insert(tUserQuestionnaireResult);
        ArrayList<TUserQuestionnaireResultAcu> itemACUArrayList = new ArrayList<>();
        List<HealthPrescribingRequestItemVO> itemList = healthPrescribingRequestVo.getSaveQuestionnaireMainItemList();
        ArrayList<TUserQuestionnaireResultItem> itemArrayList = new ArrayList<>();
        itemList.forEach(item -> {
            TUserQuestionnaireResultItem tUserQuestionnaireResultItem = new TUserQuestionnaireResultItem();
            tUserQuestionnaireResultItem.setResultId(tUserQuestionnaireResult.getResultId());
            tUserQuestionnaireResultItem.setResultItemContent(item.getItemContent());
            tUserQuestionnaireResultItem.setResultItemSort(item.getSort());
            tUserQuestionnaireResultItem.setResultItemTitle(item.getItemTitle());
            tUserQuestionnaireResultItem.setResultItemType(item.getItemType());
            tUserQuestionnaireResultItem.setResultItemSelectId(item.getItemSelectId());
            tUserQuestionnaireResultItem.setResultItemId(IDUtil.getID());
            tUserQuestionnaireResultItem.setShowType(item.getShowType());
            itemArrayList.add(tUserQuestionnaireResultItem);
            if (item.getItemType() == 2 && item.getHealthSavePreResultAcuList() != null) {
                for (HealthSavePreResultAcu saveResultAcu : item.getHealthSavePreResultAcuList()) {
                    if (saveResultAcu.getAcuId() != null){
                        TUserQuestionnaireResultAcu resultItem = new TUserQuestionnaireResultAcu();
                        BeanUtils.copyProperties(saveResultAcu, resultItem);
                        resultItem.setResultAcuId(IDUtil.getID());
                        resultItem.setResultItemId(tUserQuestionnaireResultItem.getResultItemId());
                        resultItem.setResultId(tUserQuestionnaireResult.getResultId());
                        itemACUArrayList.add(resultItem);
                    }
                }

            }
        });
        if (!itemACUArrayList.isEmpty()) {
            tUserQuestionnaireResultAcuMapper.insertList(itemACUArrayList);
        }
        tUserQuestionnaireResultItemMapper.insertList(itemArrayList);

        return ResEntity.success(tUserQuestionnaireResult);
    }

    public static void main(String[] args) {
        System.out.println("123儿童保健".replace("儿童保健",""));
    }
}
