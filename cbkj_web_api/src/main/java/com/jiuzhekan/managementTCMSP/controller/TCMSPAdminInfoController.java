package com.jiuzhekan.managementTCMSP.controller;

import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.healthpres.beans.healthPrescribingControllerVo.GetTypesHealthPrescribing;
import com.jiuzhekan.managementTCMSP.bean.*;
import com.jiuzhekan.managementTCMSP.beans.user.SysAdminInfoAssessmentEvaluation;
import com.jiuzhekan.managementTCMSP.beans.user.SysAdminInfoEdu;
import com.jiuzhekan.managementTCMSP.beans.user.SysAdminInfoExamine;
import com.jiuzhekan.managementTCMSP.service.TCMSPAdminInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/8/15 15:59
 * @Version 1.0
 */
@Api(value = "人员信息管理", tags = "人员信息管理")
@RequestMapping("/tcmspAdminInfo")
@RestController
public class TCMSPAdminInfoController {

    public TCMSPAdminInfoController(TCMSPAdminInfoService tcmspAdminInfoService) {
        this.tcmspAdminInfoService = tcmspAdminInfoService;
    }

    private final TCMSPAdminInfoService tcmspAdminInfoService;

    /**
     * 获取医共体列表信息
     */
    @GetMapping("/getAppList")
    @ApiOperation(value = "获取医共体列表信息", notes = "获取医共体列表信息")
    public Object getAppList() {
        return tcmspAdminInfoService.getAppList();
    }

    /**
     * 根据医共体appId获取Ins列表
     */
    @GetMapping("/getInsList")
    @ApiOperation(value = "根据医共体appId获取Ins列表", notes = "根据医共体appId获取Ins列表")
    public Object getInsList(String appId) {
        if (StringUtils.isBlank(appId)) {
            return ResEntity.error("医共体id不能为空");
        }
        return tcmspAdminInfoService.getInsList(appId);
    }

    /**
     * 根据医共体appId和insCode获取科室列表
     */
    @GetMapping("/getDeptList")
    @ApiOperation(value = "根据医共体appId和insCode获取科室列表", notes = "根据医共体appId和insCode获取科室列表")
    public Object getDeptList(String appId, String insCode) {
        if (StringUtils.isBlank(appId) || StringUtils.isBlank(insCode)) {
            return ResEntity.error("参数不能为空");
        }
        return tcmspAdminInfoService.getDeptList(appId, insCode);
    }

    /**
     * 获取人员信息管理的列表数据
     */
    @GetMapping("/getPersonList")
    @ApiOperation(value = "人员信息-获取人员信息管理的列表数据", notes = "人员信息-获取人员信息管理的列表数据", response = AppsInfo.class)
    public Object getPersonList(TCMSPAdminInfoReq admin, Page page) {
        return tcmspAdminInfoService.getPageDatas(admin, page);
    }


    /**
     * 获取人员信息详情
     */
    @GetMapping("/getPersonDetail")
    @ApiOperation(value = "人员信息-获取人员信息详情", notes = "人员信息-获取人员信息详情", response = AppsInfoDetails.class)
    public Object getPersonDetail(String userId) {
        return (tcmspAdminInfoService.getPersonDetail(userId));
    }

    /**
     * 保存人员信息详情
     */
    @PostMapping("/savePersonDetail")
    @ApiOperation(value = "人员信息-保存人员信息详情", notes = "人员信息-保存人员信息详情")
    public Object savePersonDetail(@RequestBody AppsInfoDetails appsInfoDetails) {
        return (tcmspAdminInfoService.savePersonDetail(appsInfoDetails));
    }

    /**
     * 编辑人员信息详情
     */
    @PostMapping("/editPersonDetail")
    @ApiOperation(value = "人员信息-编辑人员信息详情", notes = "人员信息-编辑人员信息详情")
    public Object editPersonDetail(@RequestBody AppsInfoDetails appsInfoDetails) {
        return (tcmspAdminInfoService.editPersonDetail(appsInfoDetails));
    }
    /**
     * 删除人员信息
     */
    @GetMapping("/deletePerson")
    @ApiOperation(value = "人员信息-删除人员信息", notes = "人员信息-删除人员信息")
    public Object deletePerson(String userId) {
        if (StringUtils.isBlank(userId)) {
            return ResEntity.error("参数不能为空");
        }
        return (tcmspAdminInfoService.deletePerson(userId));
    }


    /**
     * 继续教育搜索人员列表-从人员信息管理中获取
     */
    @GetMapping("/getContinueEducationList")
    @ApiOperation(value = "继续教育-继续教育搜索人员列表", notes = "继续教育-继续教育搜索人员列表", response = SysAdminInfoEdu.class)
    public Object getContinueEducationList(GetContinueEducationListReq req,Page page) {
        return tcmspAdminInfoService.getContinueEducationList(req,page);
    }

    /**
     * 保存继续教育信息
     */
    @PostMapping("/saveContinueEducation")
    @ApiOperation(value = "继续教育-保存或修改继续教育信息", notes = "继续教育-保存或修改继续教育信息")
    public Object saveContinueEducation(@RequestBody SysAdminInfoEdu sysAdminInfoEdu) {
        return tcmspAdminInfoService.saveContinueEducation(sysAdminInfoEdu);
    }

    /**
     * 删除继续教育信息
     */
    @GetMapping("/deleteContinueEducation")
    @ApiOperation(value = "继续教育-删除继续教育信息", notes = "继续教育-删除继续教育信息")
    public Object deleteContinueEducation(String id) {
        if (StringUtils.isBlank(id)) {
            return ResEntity.error("参数不能为空");
        }
        return tcmspAdminInfoService.deleteContinueEducation(id);
    }

    /**
     * 绩效考核管理-列表
     */
    @GetMapping("/getExamineList")
    @ApiOperation(value = "绩效考核管理-列表", notes = "绩效考核管理-列表", response = SysAdminInfoExamine.class)
    public Object getExamineList(GetExamineList getExamineList, Page page) {
        return tcmspAdminInfoService.getExamineList(getExamineList,page);
    }

    /**
     * 绩效考核管理-保存
     */
    @PostMapping("/saveExamine")
    @ApiOperation(value = "绩效考核管理-保存或者修改", notes = "绩效考核管理-保存或者修改")
    public Object saveExamine(@RequestBody SysAdminInfoExamine sysAdminInfoExamine) {
        return tcmspAdminInfoService.saveExamine(sysAdminInfoExamine);
    }
    /**
     * 绩效考核管理-删除
     */
    @GetMapping("/deleteExamine")
    @ApiOperation(value = "绩效考核管理-删除", notes = "绩效考核管理-删除")
    public Object deleteExamine(String id) {
        if (StringUtils.isBlank(id)) {
            return ResEntity.error("参数不能为空");
        }
        return tcmspAdminInfoService.deleteExamine(id);
    }

    /**
     * 考核测评管理-列表
     */
    @GetMapping("/getAssessmentEvaluationList")
    @ApiOperation(value = "考核测评管理-列表", notes = "考核测评管理-列表", response = SysAdminInfoAssessmentEvaluation.class)
    public Object getAssessmentEvaluationList(GetAssessmentEvaluationList getAssessmentEvaluationList, Page page) {
        return tcmspAdminInfoService.getAssessmentEvaluationList(getAssessmentEvaluationList,page);
    }

    /**
     * 考核测评管理-保存或者修改
     */
    @PostMapping("/saveAssessmentEvaluation")
    @ApiOperation(value = "考核测评管理-保存或者修改", notes = "考核测评管理-保存或者修改")
    public Object saveAssessmentEvaluation(@RequestBody SysAdminInfoAssessmentEvaluation sysAdminInfoAssessmentEvaluation) {
        return tcmspAdminInfoService.saveAssessmentEvaluation(sysAdminInfoAssessmentEvaluation);
    }
    /**
     * 考核测评管理-删除
     */
    @GetMapping("/deleteAssessmentEvaluation")
    @ApiOperation(value = "考核测评管理-删除", notes = "考核测评管理-删除")
    public Object deleteAssessmentEvaluation(String id) {
        if (StringUtils.isBlank(id)) {
            return ResEntity.error("参数不能为空");
        }
        return tcmspAdminInfoService.deleteAssessmentEvaluation(id);
    }



}
