package com.jiuzhekan.knowledgeshare.beans;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/7/1 10:08
 * @Version 1.0
 */
@Data
@ApiModel
public class ShareListReq {

    private Integer page;
    private Integer limit;
    @ApiModelProperty(value = "共享类型 1中医古籍2.共享文献")
    private String shareType;

    @ApiModelProperty(value = "共享知识名称")
    private String shareTitle;

    @ApiModelProperty(value = "共享人")
    private String shareUserName;

    @ApiModelProperty(value = "共享状态0正常 2 停用")
    private Integer shareStatus;

}
