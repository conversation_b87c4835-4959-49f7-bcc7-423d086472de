package com.jiuzhekan.recordquality.beans;

import lombok.Getter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/6/25 16:09
 * @Version 1.0
 */
@Getter
public enum QualityClass {
    //规则类型：全部（默认），病症质控，病症质控
    ALL("全部", "-1"),
    DIS("病症质控", "1"),
    MED("症状质控", "2");
    private String name;
    private String value;

    QualityClass(String keyName, String keyValue) {
        this.name = keyName;
        this.value = keyValue;
    }

    //创建返回所有的List<Map<String,String>>的所有数据集合，无需入参
    public static List<HashMap<String,String>> getAllQualityClass() {
        List<HashMap<String,String>> list = new ArrayList<>();
        for (QualityClass qualityClass : QualityClass.values()) {
            HashMap<String,String> map = new HashMap<>();
            map.put("name", qualityClass.getName());
            map.put("value", qualityClass.getValue());
            list.add(map);
        }
        return list;
    }


}
