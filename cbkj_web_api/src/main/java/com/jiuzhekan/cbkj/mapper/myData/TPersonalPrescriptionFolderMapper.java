package com.jiuzhekan.cbkj.mapper.myData;

import com.jiuzhekan.cbkj.beans.myData.TPersonalPrescriptionFolder;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public interface TPersonalPrescriptionFolderMapper extends BaseMapper<TPersonalPrescriptionFolder> {

    /**
     * 该文件夹之后的序号全部加一
     *
     * @param tPersonalPrescriptionFolder 文件夹
     * <AUTHOR>
     * @date 2021/4/8
     */
    void folderNumIncrement(TPersonalPrescriptionFolder tPersonalPrescriptionFolder);

    /**
     * 批量排序
     *
     * @param list list
     * <AUTHOR>
     * @date 2021/4/8
     */
    void sortFolderNumList(List<TPersonalPrescriptionFolder> list);
}