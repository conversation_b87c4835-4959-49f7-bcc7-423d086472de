package com.jiuzhekan.cbkj.controller.sysApp;

import com.jiuzhekan.cbkj.beans.sysApp.SysInstitution;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.common.http.PlatformRestTemplate;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.Constant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.*;

@Api(value = "医疗机构接口", tags = "医疗机构接口")
@Controller
@RequestMapping("sys")
public class SysInstitutionController {

    @Autowired
    private PlatformRestTemplate platformRestTemplate;

//    @RequestMapping(value = "sysInstitution/getPages", method = RequestMethod.GET)
//    @ApiOperation(value = "分页查询医疗机构信息", notes = "分页查询医疗机构信息", response = SysInstitution.class)
//    @LogAnnotation(value = "分页查询医疗机构信息")
//    @ResponseBody
//    public Object getApps(SysInstitution sysInstitution, Page page) {
//        Object obj = sysInstitutionService.getPageDatas(sysInstitution, page);
//        return obj;
//    }
//
//    @RequestMapping(value = "sysInstitution/findObj", method = RequestMethod.GET)
//    @ApiOperation(value = "加载医疗机构信息详情", notes = "加载医疗机构信息详情", response = SysInstitution.class)
//    @LogAnnotation(value = "加载医疗机构信息详情")
//    @ResponseBody
//    public Object getObj(String id) {
//        ResEntity result = sysInstitutionService.findObj(id);
//        return result;
//    }
//
//    @RequestMapping(value = "sysInstitution/insert", method = RequestMethod.POST)
//    @ApiOperation(value = "新增医疗机构信息", notes = "新增医疗机构信息")
//    @LogAnnotation(value = "新增医疗机构信息", isWrite = true)
//    @ResponseBody
//    public Object insert(@RequestBody SysInstitution sysInstitution) {
//        ResEntity result = sysInstitutionService.insert(sysInstitution);
//        return result;
//    }
//
//    @RequestMapping(value = "sysInstitution/update", method = RequestMethod.POST)
//    @ApiOperation(value = "修改医疗机构信息", notes = "修改医疗机构信息")
//    @LogAnnotation(value = "修改医疗机构信息", isWrite = true)
//    @ResponseBody
//    public Object update(@RequestBody SysInstitution sysInstitution) {
//        ResEntity result = sysInstitutionService.update(sysInstitution);
//        return result;
//    }
//
//    @RequestMapping(value = "sysInstitution/deleteLis", method = RequestMethod.GET)
//    @ApiOperation(value = "删除医疗机构信息", notes = "删除医疗机构信息")
//    @LogAnnotation(value = "删除医疗机构信息", isWrite = true)
//    @ResponseBody
//    public Object deleteLis(String ids) throws Exception {
//        ResEntity result = sysInstitutionService.deleteLis(ids);
//        return result;
//    }
//
//    @RequestMapping(value = "sysInstitution/getInsLList", method = RequestMethod.GET)
//    @ApiOperation(value = "获取上级医疗机构", notes = "获取上级医疗机构", response = SysInstitution.class)
//    @LogAnnotation(value = "获取上级医疗机构")
//    @ResponseBody
//    public Object getInsList(SysInstitution sysInstitution) {
//        List<SysInstitution> sysInstitutionList = sysInstitutionService.getSysInstitutionList(sysInstitution);
//        return ResEntity.entity(true, Constant.SUCCESS_DX, sysInstitutionList);
//    }
//
//    @RequestMapping(value = "sysInstitution/getInsLListTree", method = RequestMethod.GET)
//    @ApiOperation(value = "获取上级医疗机构", notes = "获取上级医疗机构", response = SysInstitution.class)
//    @LogAnnotation(value = "获取上级医疗机构")
//    @ResponseBody
//    public Object getInsLListTree() {
//        return ResEntity.entity(true, Constant.SUCCESS_DX, sysInstitutionService.getSysInstitutionListTree(sysAppService.getApplist()));
//    }
//
//
//    @RequestMapping(value = "sysInstitution/changeStatus", method = RequestMethod.GET)
//    @ApiOperation(value = "禁用启用医疗机构", notes = "禁用启用医疗机构")
//    @LogAnnotation(value = "禁用启用医疗机构", isWrite = true)
//    @ResponseBody
//    public Object changeStatus(SysInstitution sysInstitution) {
//        ResEntity result = sysInstitutionService.changeUpdate(sysInstitution);
//        return result;
//    }
//
//    @RequestMapping(value = "sysInstitution/getInsListByIns", method = RequestMethod.GET)
//    @ApiOperation(value = "获取三级医疗机构列表", notes = "获取三级医疗机构列表", response = SysInstitution.class)
//    @LogAnnotation(value = "获取三级医疗机构列表")
//    @ResponseBody
//    public Object getInsListByIns(SysInstitution sysInstitution) {
//        List<SysInstitution> sysInstitutionList = sysInstitutionService.getInsListByIns(sysInstitution);
//        return ResEntity.entity(true, Constant.SUCCESS_DX, sysInstitutionList);
//    }

    @RequestMapping(value = "sysInstitution/getInsTree", method = RequestMethod.GET)
    @ApiOperation(value = "获取医疗机构树", notes = "获取医疗机构树", response = SysInstitution.class)
    @LogAnnotation(value = "获取医疗机构树")
    @ResponseBody
    public Object getInsTree(String appId) {

        Map<String, Object> params = new HashMap<>();
        params.put("appId", appId);
        String insCode = AdminUtils.getCurrentInsCode();
        if (!Constant.BASIC_INS_CODE.equals(insCode)) {
            params.put("insCode", insCode);
        }
        //从综合平台获取医疗机构
        ResEntity res = platformRestTemplate.post("ins/list", params);
        if (!res.getStatus()) {
            return res;
        }

        List<LinkedHashMap<String, Object>> rootList = new ArrayList<>();
        List<LinkedHashMap<String, Object>> insList = (List<LinkedHashMap<String, Object>>) res.getData();

        Map<String, LinkedHashMap<String, Object>> allMap = new HashMap<>();

        for (LinkedHashMap<String, Object> insMap : insList) {
            insMap.put("insList", new ArrayList<>());
            allMap.put((String) insMap.get("insCode"), insMap);
        }

        for (LinkedHashMap<String, Object> insMap : insList) {
            LinkedHashMap<String, Object> parent;
            Object pCode = insMap.get("insParentCode");
            insMap.put("insPcode", pCode);
            if (!(pCode instanceof String) || (parent = allMap.get(pCode)) == null) {
                rootList.add(insMap);
            } else {
                List<LinkedHashMap<String, Object>> chilren = (List<LinkedHashMap<String, Object>>) parent.get("insList");
                chilren.add(insMap);
            }

        }

        return ResEntity.success(rootList);
//        List<SysInstitution> sysInstitutionList = sysInstitutionService.getInsTree(appId);
//        return ResEntity.entity(true, Constant.SUCCESS_DX, sysInstitutionList);
    }
}