package com.jiuzhekan.cbkj.mapper.business.record;

import com.jiuzhekan.cbkj.beans.business.record.TPrescriptionPreparationItem;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

@Component
public interface TPrescriptionPreparationItemMapper extends BaseMapper<TPrescriptionPreparationItem> {

    /**
     * 根据处方ID获取中药制剂明细
     *
     * @param preId 处方ID
     * @return TPrescriptionPreparationItem
     * <AUTHOR>
     * @date 2021/6/9
     */
    List<TPrescriptionPreparationItem> getListByPreId(String preId);

    /**
     * 获取患者中药制剂日用量
     *
     * @param item 中药制剂明细
     * @return 已使用数量
     * <AUTHOR>
     * @date 2021/6/9
     */
    BigDecimal getPreparationDailyNumSum(TPrescriptionPreparationItem item);

    /**
     * 物理删除
     */
    long physicalDeleteByPreId(List<String> draftPreIds);

}