package com.jiuzhekan.recordquality.beans;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.Date;
import java.util.List;




@Data
@NoArgsConstructor
@ApiModel
public class TRecordQualityRuleMainGetOne implements Serializable{

    @ApiModelProperty(value = "规则主表id")
    private String recordQualityRuleId;

    @ApiModelProperty(value = "规则类型:代码")
    private String recordQualityRuleCalssValue;

    @ApiModelProperty(value = "规则类型:名称")
    private String recordQualityRuleCalssName;

    @ApiModelProperty(value = "规则名")
    private String recordQualityRuleName;

    @ApiModelProperty(value = "规则条件")
    private String recordQualityRuleCondition;

    @ApiModelProperty(value = "0启用1不启用")
    private Integer recordQualityRuleStatus;

    @ApiModelProperty(value = "规则描述")
    private String recordQualityRuleDesc;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "")
    private String createUserName;

    @ApiModelProperty(value = "")
    private String createUserId;

    @ApiModelProperty(value = "")
    private String updateUserName;

    @ApiModelProperty(value = "")
    private String updateUserId;

    @ApiModelProperty(value = "")
    private Date updateTime;

    @ApiModelProperty(value = "")
    private String delUserName;

    @ApiModelProperty(value = "")
    private String delUserId;

    @ApiModelProperty(value = "")
    private Date delTime;

    @ApiModelProperty(value = "")
    private String appId;

    @ApiModelProperty(value = "")
    private String appName;

    @ApiModelProperty(value = "")
    private String insCode;

    @ApiModelProperty(value = "")
    private String insName;

    @ApiModelProperty(value = "")
    private String deptId;

    @ApiModelProperty(value = "")
    private String deptName;


    @ApiModelProperty(value = "规则条件分组List")
    private List<TRecordQualityRuleItemsSave> ruleInfoList;


}
