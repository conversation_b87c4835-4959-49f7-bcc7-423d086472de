package com.jiuzhekan.statistics.aop.annotation;

import java.lang.annotation.*;

/**
 * 统计注解
 * <p>
 * 杭州聪宝科技有限公司
 * <p>
 *
 * <AUTHOR>
 * @date 2021/4/14 15:02
 */
@Target({ElementType.PARAMETER, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface StatisticsFunction2 {

    /**
     * 来源
     *
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021/4/14
     */
    String classify() default "";

    /**
     * 默认值
     *
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021/4/13
     */
    String function() default "";

    /**
     * 是否统计
     *
     * <AUTHOR>
     * @date 2021/4/13
     */
    boolean isStatistic() default true;
}
