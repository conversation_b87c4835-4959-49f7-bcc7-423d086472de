package com.jiuzhekan.cbkj.mapper.business.record;

import com.jiuzhekan.cbkj.beans.business.record.TBusinessEdition;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public interface TBusinessEditionMapper extends BaseMapper<TBusinessEdition> {

    long deleteByEditionS(@Param("list") List<String> split, @Param("edtion") TBusinessEdition businessEdition);


    List<TBusinessEdition> unreadEdition(String adminId);
}