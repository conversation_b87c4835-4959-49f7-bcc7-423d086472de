package com.jiuzhekan.recordquality.beans;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/7/2 15:47
 * @Version 1.0
 */
@Data
public class TRecordQualityStatisticsExccel {
    @ApiModelProperty(value = "目标名")
    private String qualityGoalName;
    @ApiModelProperty(value = "时间点名")
    private String qualityTimePointName;
    @ApiModelProperty(value = "关键点名")
    private String qualityKeyPonitName;
    @ApiModelProperty(value = "医生名")
    private String qualityDoctorName;
    @ApiModelProperty(value = "质控时间")
    private String qualityTime;
    @ApiModelProperty(value = "质控关键要素分析")
    private String qualityKeyElement;
    @ApiModelProperty(value = "")
    private String qualityScore;
}
