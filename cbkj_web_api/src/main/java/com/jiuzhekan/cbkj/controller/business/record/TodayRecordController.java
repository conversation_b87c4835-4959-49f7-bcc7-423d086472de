package com.jiuzhekan.cbkj.controller.business.record;

import com.jiuzhekan.cbkj.beans.business.patients.TPatients;
import com.jiuzhekan.cbkj.beans.business.patients.VO.TPatientsReqVO;
import com.jiuzhekan.cbkj.beans.business.record.VO.TodayPatientReqVO;
import com.jiuzhekan.cbkj.beans.business.record.VO.TodayPatientRespVO;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.beans.business.record.TRecord;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.service.business.patients.TPatientsService;
import com.jiuzhekan.cbkj.service.business.record.TRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@Api(value = "今日病人", tags = {"今日病人"})
public class TodayRecordController {

    @Autowired
    private TRecordService tRecordService;
    @Autowired
    private TPatientsService tPatientsService;


    @RequestMapping(value = "patients/todayRecord/getPages", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(value = "分页查询今日病人信息", notes = "分页查询今日病人信息", response = TodayPatientRespVO.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "time", value = "选择上下午(1上午, 2 下午,不选查全天)", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "recDiagnosisStatus", value = "就诊状态（0挂号未缴费，2待就诊，4已就诊，5已付款，6已退款，8已发药）", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orderName", value = "`", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orderValue", value = "输入框内容", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "page", value = "页码,默认1", dataType = "Int", paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "每页条数,默认10", dataType = "Int", paramType = "query")
    })
    @LogAnnotation("分页查询今日病人信息")
    public Object gettodayApps(TodayPatientReqVO tRecord, Page page) {
        return tRecordService.getTodayPageDatas(tRecord, page);
    }

    @RequestMapping(value = "todayRecord/tPatients/update/findObj", method = RequestMethod.GET)
    @ApiOperation(value = "加载患者信息详情", notes = "加载患者信息详情")
    @LogAnnotation("加载患者信息详情")
    @ResponseBody
    public Object getObj(String id) {
        return tPatientsService.findObj(id);
    }

    @RequestMapping(value = "todayRecord/getRegisterById", method = RequestMethod.GET)
    @ApiOperation(value = "获取挂号记录", notes = "获取挂号记录")
    @LogAnnotation("获取挂号记录")
    @ResponseBody
    public Object getRegisterById(String id) {
        ResEntity result = tPatientsService.getRegisterById(id);
        return result;
    }


    @RequestMapping(value = "todayRecord/tPatients/getPages", method = RequestMethod.GET)
    @LogAnnotation(value = "新建里的列表")
    @ApiOperation(value = "新建里的患者列表", notes = "新建里的患者列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orderName", value = "病人信息查询(0 病人姓名,1 就诊卡号,2 病人手机号,3 病人身份证号)", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "orderValue", value = "输入框内容", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "page", value = "页码,默认1", dataType = "Int", paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "每页条数,默认10", dataType = "Int", paramType = "query")
    })
    @ResponseBody
    public Object patientsPageDatas(TPatientsReqVO tPatients, Page page) {
        Object result = tPatientsService.getTodayPatients(tPatients, page);
        return result;
    }

    @RequestMapping(value = "todayRecord/tPatients/insert", method = RequestMethod.POST)
    @LogAnnotation(value = "病人建档", isWrite = true)
    @ApiOperation(value = "建档", notes = "病人建档")
    @ResponseBody
    public Object patientsInsert(@RequestBody TPatients tPatients) {
        return tPatientsService.patientsInsert(tPatients);
    }

    @RequestMapping(value = "todayRecord/tPatients/addRegister", method = RequestMethod.GET)
    @LogAnnotation(value = "新建里的添加", isWrite = true)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "deptId", value = "科室id", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "patientId", value = "患者id", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "patientName", value = "患者姓名", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "gravidity", value = "患者是否怀孕（Y为是，N为否）", dataType = "String", paramType = "query")
    })
    @ApiOperation(value = "新建里的添加", notes = "新建里的添加,将病人信息插入挂号表中去")
    @ResponseBody
    public Object addRegister(String deptId, String deptName, String patientId, String patientName, String gravidity) {
        return tRecordService.addRegister(deptId, deptName, patientId, patientName, gravidity);
    }

}