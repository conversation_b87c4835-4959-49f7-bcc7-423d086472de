package com.jiuzhekan.statistics.mapper.register;

import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;
import com.jiuzhekan.statistics.beans.prescription.StatisticsPrescription;
import com.jiuzhekan.statistics.beans.register.StatisticsRegister;
import com.jiuzhekan.statistics.beans.register.TStatisticsRegister;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public interface TStatisticsRegisterMapper extends BaseMapper<TStatisticsRegister>{
    /**
     * 统计统计就诊人次
     * @param param
     * @return
     */
    List<StatisticsRegister> selectStatisticsRegisterResult(Map<String, Object> param);

    /**
     * #统计中医电子病历
     * @param param
     * @return
     */
    List<StatisticsRegister> selectStatisticsRegisterEle(Map<String, Object> param);


    /**
     * 插入统计
     * @param list
     * @return
     */
    int insertStatisticsRegister(List<StatisticsRegister> list);

    int replaceinsertStatisticsRegister(List<StatisticsRegister> list);
    TStatisticsRegister selectLastDate();
}