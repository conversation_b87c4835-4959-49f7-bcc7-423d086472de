package com.jiuzhekan.statistics.beans.prescription;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date $ $
 **/
@Data
public class StatisticsPrescription {
    private Integer id;
    private String appId;
    private String insCode;
    private String userId;
    private String userName;
    private Date dataDate;
    @ApiModelProperty(value = "总开方数量")
    private Integer totalNum;

    @ApiModelProperty(value = "内服方数量")
    private Integer innerNum;

    @ApiModelProperty(value = "外用方数量")
    private Integer exterNum;
    @ApiModelProperty(value = "中成药数量")
    private Integer patentNum;
    @ApiModelProperty(value = "适宜技术方数量")
    private Integer acuNum;



    @ApiModelProperty(value = "制剂数量")
    private Integer prepareNum;
    /**
     * 当前类型的查询统计数
     */
    private Integer number;
    /**
     * 处方类型（1内服中药方 2外用中药方 3中成药方 4适宜技术方 5制剂）
     */
    private String preType;
}
