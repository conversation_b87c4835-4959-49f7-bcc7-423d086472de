package com.jiuzhekan.recordquality.beans;

import lombok.Getter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/6/30 17:14
 * @Version 1.0
 */
@Getter

public enum QualityTimePoint {

    //诊疗中
// 归档指控
    ARCHIVE_CLAIM_MAIN("1", "诊疗中"),

    ARCHIVE_CLAIM_DETAIL("2", "归档指控");
    private String code;
    private String name;

    QualityTimePoint(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static List<HashMap<String, String>> getAllQualityTimePoint() {
        List<HashMap<String, String>> list = new ArrayList<>();
        for (QualityTimePoint value : QualityTimePoint.values()) {
            HashMap<String, String> map = new HashMap<>();
            map.put("code", value.getCode());
            map.put("name", value.getName());
            list.add(map);
        }
        return list;
    }

}
