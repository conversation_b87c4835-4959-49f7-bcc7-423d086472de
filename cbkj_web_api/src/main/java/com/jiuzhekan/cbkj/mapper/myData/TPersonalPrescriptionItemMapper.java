package com.jiuzhekan.cbkj.mapper.myData;

import com.jiuzhekan.cbkj.beans.myData.TPersonalPrescriptionItem;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public interface TPersonalPrescriptionItemMapper extends BaseMapper<TPersonalPrescriptionItem>{


    long deleteByPersPreId(String persPreId);

    List<TPersonalPrescriptionItem> getPersItems(String persPreId);

    List<Map> getPerItems(String persPreId);
}