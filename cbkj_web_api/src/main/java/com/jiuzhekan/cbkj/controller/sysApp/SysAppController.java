package com.jiuzhekan.cbkj.controller.sysApp;

import io.swagger.annotations.Api;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

@Api(value = "医联体接口", tags = "医联体接口")
@Controller
@RequestMapping(value = "sys")
public class SysAppController {


//    @ApiOperation(value = "分页查询医联体信息", notes = "分页查询医联体信息", response = SysApp.class)
//    @RequestMapping(value = "sysApp/getPages", method = RequestMethod.GET)
//    @ResponseBody
//    @LogAnnotation("分页查询医联体信息")
//    public Object getApps(@ApiParam("医联体名称") String appName, Page page) {
//        SysApp sysApp = new SysApp();
//        sysApp.setAppName(appName);
//        Object obj = sysAppService.getPageDatas(sysApp, page);
//        return obj;
//    }
//
//    @ApiOperation(value = "加载医联体信息详情", notes = "加载医联体信息详情", response = SysApp.class)
//    @RequestMapping(value = "sysApp/findObj", method = RequestMethod.GET)
//    @LogAnnotation("加载医联体信息详情")
//    @ResponseBody
//    public Object getObj(@ApiParam("医联体ID") String appId) {
//        ResEntity result = sysAppService.findObj(appId);
//        return result;
//    }
//
//    @ApiOperation(value = "新增医联体信息", notes = "新增医联体信息")
//    @RequestMapping(value = "sysApp/insert", method = RequestMethod.POST)
//    @LogAnnotation(value = "新增医联体信息", isWrite = true)
//    @ResponseBody
//    public Object insert(@RequestBody SysApp sysApp) {
//        ResEntity result = sysAppService.insert(sysApp);
//        return result;
//    }
//
//    @ApiOperation(value = "修改医联体信息", notes = "修改医联体信息")
//    @RequestMapping(value = "sysApp/update", method = RequestMethod.POST)
//    @LogAnnotation(value = "修改医联体信息", isWrite = true)
//    @ResponseBody
//    public Object update(@RequestBody SysApp sysApp) {
//        ResEntity result = sysAppService.update(sysApp);
//        return result;
//    }
//
//    @ApiOperation(value = "删除医联体信息", notes = "删除医联体信息")
//    @RequestMapping(value = "sysApp/deleteLis", method = RequestMethod.POST)
//    @LogAnnotation(value = "删除医联体信息", isWrite = true)
//    @ResponseBody
//    public Object deleteLis(@ApiParam("医联体ID") String ids) throws Exception {
//        ResEntity result = sysAppService.deleteLis(ids);
//        return result;
//    }
//
//    @ApiOperation(value = "禁用启用医联体信息", notes = "禁用启用医联体信息")
//    @RequestMapping(value = "sysApp/changeStatus", method = RequestMethod.GET)
//    @LogAnnotation(value = "禁用启用医联体信息", isWrite = true)
//    @ResponseBody
//    public Object changeStatus(String appId, String isDisable) {
//        SysApp sysApp = new SysApp();
//        sysApp.setAppId(appId);
//        sysApp.setIsDisable(isDisable);
//        if (sysApp.getIsDisable() != null && "1".equals(sysApp.getIsDisable())) {
//            sysApp.setDisableDate(new Date());
//            sysApp.setDisableUser(AdminUtils.getCurrentHr().getId());
//            sysApp.setDisableUsername(AdminUtils.getCurrentHr().getUsername());
//        }
//        ResEntity result = sysAppService.changeUpdate(sysApp);
//        return result;
//    }

//    @RequestMapping(value = "sysApp/getApplist", method = RequestMethod.GET)
//    @LogAnnotation(value = "医联体下拉列表获取")
//    @ApiOperation(value = "sysApp/getApplist", notes = "医联体下拉列表获取")
//    @ResponseBody
//    public Object getApplist() {
//        return ResEntity.entity(true, Constant.SUCCESS_DX, sysAppService.getApplist());
//    }
}