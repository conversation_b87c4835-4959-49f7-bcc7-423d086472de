package com.jiuzhekan.cbkj.mapper.business.template;

import com.jiuzhekan.cbkj.beans.business.template.TRecordTemplateDetail;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public interface TRecordTemplateDetailMapper extends BaseMapper<TRecordTemplateDetail> {

    int deleteByTempId(String tempId);

    /**
     * 获取病历模板明细和明细内容
     * @param templateDetail 必须recId,templId
     * @return java.util.List<com.jiuzhekan.cbkj.beans.business.template.TRecordTemplateDetail>
     * <AUTHOR>
     * @date 2020/2/19
     */
    List<TRecordTemplateDetail> getDetailContentList(TRecordTemplateDetail templateDetail);

}