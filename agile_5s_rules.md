
# 📘 敏捷开发 5S 个人规范

为了提升团队敏捷开发效率，保证交付质量，制定本 **5S 个人规范**。  
5S 包含：**自律（Self-discipline）、标准（Standard）、速度（Speed）、分享（Share）、满意（Satisfaction）**。  

---

## 1. 🧘 Self-discipline - 自律
- 严格遵守团队约定的开发规范与工作流程。  
- 及时更新任务状态，避免任务遗忘或拖延。  
- 养成良好的编码习惯：提交小步快跑，保持代码整洁。  

---

## 2. 📏 Standard - 标准
- 遵循统一的 **代码规范、命名规则、文档模板**。  
- 提交代码必须通过 **自动化测试与静态检查**。  
- 严格执行 **代码 Review 流程**，保持质量一致性。  

---

## 3. ⚡ Speed - 速度
- 任务分解细化，保证短周期可交付。  
- 优先处理阻塞项，避免影响团队进度。  
- 保持每日小进展，迭代中持续交付价值。  

---

## 4. 🤝 Share - 分享
- 积极在团队内分享经验、工具与最佳实践。  
- 出现问题或踩坑时，记录并分享避免重复错误。  
- 参与项目 Wiki/知识库建设，促进团队成长。  

---

## 5. 😊 Satisfaction - 满意
- 追求 **代码可维护性** 和 **交付物的用户体验**。  
- 保持积极沟通，主动协助团队成员。  
- 在每个迭代中获得成就感和成长感，提升个人与团队满意度。  

---

## 📌 附录
- **每日工作清单（建议）**
  ```
  [ ] 更新任务进度到看板
  [ ] 代码提交规范化（commit message 清晰）
  [ ] 自测 & 单元测试通过
  [ ] 参与代码 Review 或验收
  [ ] 分享当天遇到的经验 / 问题
  ```

---

✍️ 本规范旨在帮助每位成员在敏捷开发中保持高效与协作，请严格执行并提出改进建议。
