package com.jiuzhekan.knowledgeshare.beans;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class TKnowledgeShare implements Serializable{

    @ApiModelProperty(value = "")
    private String knowledgeShareId;

    @ApiModelProperty(value = "1中医古籍2.共享文献")
    private String shareType;

    @ApiModelProperty(value = "")
    private String shareUserName;

    @ApiModelProperty(value = "")
    private String shareUserId;

    @ApiModelProperty(value = "")
    private Date shareTime;

    @ApiModelProperty(value = "")
    private Integer shareSort;

    @ApiModelProperty(value = "共享知识名称")
    private String shareTitle;

    @ApiModelProperty(value = "共享知识说明")
    private String shareInfo;

    @ApiModelProperty(value = "删除人id")
    private String delUserId;

    @ApiModelProperty(value = "")
    private String delUserName;

    @ApiModelProperty(value = "删除时间")
    private Date delTime;

    @ApiModelProperty(value = "上次更新人id")
    private String updateUserId;

    private Date updateUserTime;

    @ApiModelProperty(value = "")
    private String updateUserName;

    @ApiModelProperty(value = "0正常 1 删除  2停用")
    private Integer shareStatus;

    @ApiModelProperty(value = "文件地址")
    private String shareFilePath;

    @ApiModelProperty(value = "是否可删除 0 不能 1可以")
    private Integer canDelete;
    @ApiModelProperty(value = "是否可修改 0 不能 1可以")
    private Integer canUpdate;


}
