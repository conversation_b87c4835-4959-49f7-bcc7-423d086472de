package com.jiuzhekan.cbkj.service.business.analysis;

import com.jiuzhekan.cbkj.beans.business.analysis.TUserAnalysisResult;
import com.jiuzhekan.cbkj.beans.business.analysis.TUserAnalysisResultAcu;
import com.jiuzhekan.cbkj.beans.business.patients.TPatientAge;
import com.jiuzhekan.cbkj.common.utils.Base64OfSunMisc;
import com.jiuzhekan.cbkj.common.utils.DateUtil;
import com.jiuzhekan.cbkj.common.utils.word.WordGenerator;
import com.jiuzhekan.cbkj.common.utils.word.WordToPdfByAspose;
import com.jiuzhekan.cbkj.mapper.business.analysis.TUserAnalysisResultAcuMapper;
import com.jiuzhekan.cbkj.mapper.business.analysis.TUserAnalysisResultMapper;
import com.jiuzhekan.cbkj.service.business.patients.TPatientsService;
import com.jiuzhekan.cbkj.service.redis.KnowRedisService;
import com.jiuzhekan.cbkj.service.sysService.AdminService;
import com.jiuzhekan.cbkj.service.redis.RedisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;
import java.util.*;

/**
 * TUserAnalysisResultFileService
 * <p>
 * 杭州聪宝科技有限公司
 *
 * <AUTHOR>
 * @date 2021/6/25
 */
@Service
public class TUserAnalysisResultFileService {

    @Autowired
    private TUserAnalysisResultMapper tUserAnalysisResultMapper;
    @Autowired
    private TUserAnalysisResultAcuMapper tUserAnalysisResultAcuMapper;
    @Autowired
    private KnowRedisService knowRedisService;
    @Autowired
    private AdminService adminService;
    @Value("${file.address}")
    private String location;
    @Value("${root.preview}")
    private String preview;
    @Value("${root.upload.relative}")
    private String relative;
    private static final String ANALYSIS_PDF_FOLDER = "analysis/";


    /**
     * 生成体质辨识结果的Word和PDF
     *
     * @param analyId analyId
     * <AUTHOR>
     * @date 2021/6/25
     */
    @Async
    public void createWordPdf(String analyId) {

        synchronized (analyId.intern()) {

            Map<String, Object> dataMap = getAnalyDataMap(analyId);

            String dateFolder = DateUtil.getDateFormats(DateUtil.YYYYMMDD, null);

            String realFolder = location + relative + ANALYSIS_PDF_FOLDER + dateFolder + "/";
            String fileFolder = preview + relative + ANALYSIS_PDF_FOLDER + dateFolder + "/";

            String analyName = "analysisReport" + DateUtil.getDateFormats(DateUtil.YYYYMMDDHHMMSS, new Date());
            String wordName = analyName + ".doc";
            String pdfName = analyName + ".pdf";

            //Word访问路径
            String wordFilePath = fileFolder + wordName;
            //Word存储路径
            String wordRealPath = realFolder + wordName;
            //PDF访问路径
            String pdfFilePath = fileFolder + pdfName;
            //PDF存储路径
            String pdfRealPath = realFolder + pdfName;

            File folder = new File(realFolder);
            if (!folder.exists()) {
                folder.mkdirs();
            }

            try {
                //生成Word文档
                File word = WordGenerator.createDoc(wordRealPath, dataMap);
                //Word转PDF
                WordToPdfByAspose.wordToPdf(pdfRealPath, new FileInputStream(word));

            } catch (IOException e) {
                e.printStackTrace();
            }

            TUserAnalysisResult result = new TUserAnalysisResult();
            result.setAnalyId(analyId);
            result.setWordPath(wordFilePath);
            result.setPdfPath(pdfFilePath);
            tUserAnalysisResultMapper.updateWordPdf(result);
        }
    }


    /**
     * 体质辨识模版数据
     *
     * @param analyId analyId
     * @return Map
     * <AUTHOR>
     * @date 2021/6/24
     */
    private Map<String, Object> getAnalyDataMap(String analyId) {

        TUserAnalysisResult ar = tUserAnalysisResultMapper.getAnalysisResultById(analyId);
        ar.setAcupointList(tUserAnalysisResultAcuMapper.getAcuListByAnalyId(analyId));

        TPatientAge patientAge = TPatientsService.getAgeByBirth(ar.getPatientBirthday());
        ar.setPatientAge(patientAge.getAge());

        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("patientName", ar.getPatientName());
        dataMap.put("patientGender", ar.getPatientGender());
        dataMap.put("patientAge", ar.getPatientAge());
        dataMap.put("analyCode", ar.getAnalyCode());
        dataMap.put("analyTime", DateUtil.getDateFormats(DateUtil.YYYY_MM_DD, ar.getAnalyTime()));
        dataMap.put("analyTime2", DateUtil.getDateFormats(DateUtil.YYYY_MM_DD_, ar.getAnalyTime()));
        dataMap.put("analyTime3", DateUtil.getDateFormats(DateUtil.YYYY_MM_DD_HH_MM_SS, ar.getAnalyTime()));
        dataMap.put("subResult", ar.getSubResult());
        dataMap.put("description", transLine(ar.getDescription()));
        dataMap.put("ttRule", transLine(ar.getTtRule()));
        dataMap.put("ttPoint", transLine(ar.getTtPoint()));
        dataMap.put("emotionalAdjustment", transLine(ar.getEmotionalAdjustment()));
        dataMap.put("dietRecuperation", transLine(ar.getDietRecuperation()));
        dataMap.put("dailyLifeAdjustment", transLine(ar.getDailyLifeAdjustment()));
        dataMap.put("sports", transLine(ar.getSports()));
        dataMap.put("acupointHealthCare", transLine(ar.getAcupointHealthCare()));
        dataMap.put("medicatedDiet", transLine(ar.getMedicatedDiet()));
        dataMap.put("flowerTea", transLine(ar.getFlowerTea()));

        dataMap.put("doctorName", adminService.getAdminName(ar.getDoctorId()));
        dataMap.put("insName", adminService.getInsName(ar.getAppId(), ar.getInsCode()));

        dataMap.put("scoreImg", Base64OfSunMisc.getImageStr(ar.getScoreImg().replace(preview, location)));

        List<Map<String, Object>> acuList = new ArrayList<>();

        for (TUserAnalysisResultAcu acu : ar.getAcupointList()) {
            Map<String, Object> acuMap = new HashMap<>();
            acuMap.put("acuId", acu.getAcuId());
            acuMap.put("acuName", acu.getAcuName());
            acuMap.put("acuPosition", acu.getAcuPosition());
            String acuImg = knowRedisService.getInitInterfaceKnowledge().getUrl().replace("interface/", acu.getAcuImg());
            acuMap.put("acuImg", Base64OfSunMisc.netImageToBase64(acuImg));
            acuList.add(acuMap);
        }
        dataMap.put("acuList", acuList);

        return dataMap;
    }

    /**
     * 处理换行
     *
     * @param str str
     * @return String
     * <AUTHOR>
     * @date 2021/6/24
     */
    private String transLine(String str) {
        if (str == null) {
            return "";
        }

        return str.replaceAll("\r", "").replaceAll("\n",
                "</w:t>\n" +
                        "                        </w:r>\n" +
                        "                    </w:p>\n" +
                        "                    <w:p w:rsidR=\"000B3C8C\" w:rsidRDefault=\"007B3854\">\n" +
                        "                        <w:pPr>\n" +
                        "                            <w:widowControl/>\n" +
                        "                            <w:ind w:firstLine=\"315\"/>\n" +
                        "                            <w:rPr>\n" +
                        "                                <w:rFonts w:ascii=\"宋体\" w:hAnsi=\"宋体\" w:cs=\"宋体\"/>\n" +
                        "                                <w:color w:val=\"000000\"/>\n" +
                        "                                <w:kern w:val=\"0\"/>\n" +
                        "                                <w:sz w:val=\"28\"/>\n" +
                        "                                <w:szCs w:val=\"28\"/>\n" +
                        "                            </w:rPr>\n" +
                        "                        </w:pPr>\n" +
                        "                        <w:r>\n" +
                        "                            <w:rPr>\n" +
                        "                                <w:rFonts w:ascii=\"仿宋\" w:eastAsia=\"仿宋\" w:hAnsi=\"仿宋\" w:cs=\"仿宋\" w:hint=\"eastAsia\"/>\n" +
                        "                                <w:color w:val=\"000000\"/>\n" +
                        "                                <w:kern w:val=\"0\"/>\n" +
                        "                                <w:sz w:val=\"28\"/>\n" +
                        "                                <w:szCs w:val=\"28\"/>\n" +
                        "                            </w:rPr>\n" +
                        "                            <w:t>");
    }


    /**
     * https请求忽略证书
     */
    static {
        try {
            trustAllHttpsCertificates();
            HttpsURLConnection.setDefaultHostnameVerifier
                    (
                            (urlHostName, session) -> true
                    );
        } catch (Exception e) {
        }
    }

    private static void trustAllHttpsCertificates()
            throws NoSuchAlgorithmException, KeyManagementException {
        TrustManager[] trustAllCerts = new TrustManager[1];
        trustAllCerts[0] = new TrustAllManager();
        SSLContext sc = SSLContext.getInstance("SSL");
        sc.init(null, trustAllCerts, null);
        HttpsURLConnection.setDefaultSSLSocketFactory(
                sc.getSocketFactory());
    }

    private static class TrustAllManager implements X509TrustManager {
        @Override
        public X509Certificate[] getAcceptedIssuers() {
            return null;
        }

        @Override
        public void checkServerTrusted(X509Certificate[] certs,
                                       String authType) {
        }

        @Override
        public void checkClientTrusted(X509Certificate[] certs,
                                       String authType) {
        }
    }
}
