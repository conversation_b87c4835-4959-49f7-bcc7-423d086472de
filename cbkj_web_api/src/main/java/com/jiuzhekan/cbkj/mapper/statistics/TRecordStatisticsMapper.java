package com.jiuzhekan.cbkj.mapper.statistics;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 描述
 *
 * <AUTHOR>
 * @Date 2020/2/18
 */
@Component
public interface TRecordStatisticsMapper {

    /**
     * 根据时间段、年龄获取前几位疾病病名
     * @param map
     *  appId 医联体id
     *  insCode 医疗机构id
     *  recTreTimeBegin 开始时间
     *  recTreTimeEnd 结束时间
     *  recAgeBegin 年龄开始时间
     *  recAgeEnd   年龄结束时间
     *  num 前几位
     * @return
     */
    List<Map> getDisNameGroupDisNameAge(Map map);

    /**
     * 根据时间段，获取某个性别患病数量
     * @param map
     *  appId 医联体id
     *  insCode 医疗机构id
     *  recTreTimeBegin 开始时间
     *  recTreTimeEnd 结束时间
     *  recGender 性别
     *  recAgeBegin 年龄开始时间
     *  recAgeEnd   年龄结束时间
     * @return
     */
    Integer getCountByGenderAndAge(Map map);


    /**
     * 按照实际段，统计每个月的就诊人次
     * @param map
     * @return
     */
    List<Map> getCountByMonth(Map map);


    List<Map> getDisTop5ByRecTreTime(Map map);

    List<Map> getSynTop5ByRecTreTime(Map map);

    List<Map> getDisNameTop5ByGender(Map map);

    /**
     * 统计某一时间段每一种疾病的发病数量
     * @param map
     * @return
     */
    List<Map> getDisCountGrupByDisName(Map map);
}