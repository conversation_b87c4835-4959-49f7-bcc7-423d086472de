//package com.jiuzhekan.cbkj.service.formula.center;
//
//import com.alibaba.fastjson.JSONObject;
//import com.jiuzhekan.cbkj.beans.formula.TFormula;
//import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
//import com.jiuzhekan.cbkj.common.exception.ExceptionUtils;
//import com.jiuzhekan.cbkj.mapper.formula.TFormulaAuthMapper;
//import com.jiuzhekan.cbkj.mapper.formula.TFormulaMapper;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang.StringUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.http.HttpEntity;
//import org.springframework.http.HttpHeaders;
//import org.springframework.http.MediaType;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//import org.springframework.web.client.RestTemplate;
//
//import java.math.BigDecimal;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//import java.util.stream.Collectors;
//
///**
// * TFormulaInterface
// * <p>
// * 杭州聪宝科技有限公司
// *
// * <AUTHOR>
// * @date 2021/8/11
// */
//@Service
//@Slf4j
//public class TFormulaInterface {
//
//    @Autowired
//    private TFormulaMapper tFormulaMapper;
//    @Autowired
//    private TFormulaAuthMapper tFormulaAuthMapper;
//    @Autowired
//    private RestTemplate restTemplate;
//
//    @Value("${address.cbkj.core.port}cbkj/pre/addRecipe")
//    private String addRecipe;
//
//    @Value("${address.cbkj.core.port}cbkj/pre/getPrepPescriptionKuCun1")
//    private String getPrepPescriptionKuCun1;
//
//    /**
//     * 推送配方
//     *
//     * @param formula formula
//     * @return com.jiuzhekan.cbkj.beans.sysBeans.ResEntity
//     * <AUTHOR>
//     * @date 2021/8/12
//     */
//    @Transactional(rollbackFor = Exception.class)
//    public ResEntity pushFormula(TFormula formula) {
//        if (formula == null) {
//            return ResEntity.error("配方不能为空");
//        }
//
//        CenterFormula centerFormula = new CenterFormula(formula);
//
//        ResEntity entity = postForObject(addRecipe, centerFormula);
//
//        if (entity.getData() instanceof String && StringUtils.isNotBlank((String) entity.getData())) {
//
//            String gaoFangId = (String) entity.getData();
//            formula.setStorePriceId(gaoFangId);
//            //推送成功后，保存药房系统的价格ID
//            TFormula formula1 = new TFormula();
//            formula1.setFormulaId(formula.getFormulaId());
//            formula1.setStorePriceId(gaoFangId);
//            tFormulaMapper.updateByPrimaryKey(formula1);
//            return ResEntity.success(formula1);
//        }
//        return ResEntity.success(null);
//    }
//
//    /**
//     * 同步配方库存
//     *
//     * @return com.jiuzhekan.cbkj.beans.sysBeans.ResEntity
//     * <AUTHOR>
//     * @date 2021/8/12
//     */
//    @Transactional(rollbackFor = Exception.class)
//    public ResEntity syncFormulaStock() {
//
//        ResEntity entity = postForObject(getPrepPescriptionKuCun1, new HashMap<>());
//
//        List<CenterFormulaStock> stockList = saveFormulaAuth(entity.getData());
//
//        return ResEntity.success(stockList);
//    }
//
//    /**
//     * 检查配方库存
//     *
//     * @param formulaId formulaId
//     * @return com.jiuzhekan.cbkj.beans.sysBeans.ResEntity
//     * <AUTHOR>
//     * @date 2021/8/12
//     */
//    public BigDecimal checkStock(String formulaId) {
//
//        Map<String, Object> param = new HashMap<>();
//
//        TFormula formula = tFormulaMapper.getObjectById(formulaId);
//        if (formula != null && StringUtils.isNotBlank(formula.getStorePriceId())) {
//            param.put("gaofangid", formula.getStorePriceId());
//        }
//
//        BigDecimal sumStock = new BigDecimal("0");
//        ResEntity entity = postForObject(getPrepPescriptionKuCun1, param);
//
//        if (entity.getData() != null && entity.getData() instanceof List) {
//
//            List<CenterFormulaStock> stockList = saveFormulaAuth(entity.getData());
//
//            sumStock = stockList.stream().map(CenterFormulaStock::getKucunslToNumber).reduce(BigDecimal.ZERO, BigDecimal::add);
//        }
//
//        return sumStock;
//    }
//
//    @Transactional(rollbackFor = Exception.class)
//    public List<CenterFormulaStock> saveFormulaAuth(Object data) {
//
//        List<Map<String, Object>> centerList = (List<Map<String, Object>>) data;
//        List<CenterFormulaStock> stockList = centerList.stream().map(CenterFormulaStock::new).collect(Collectors.toList());
//
//        if (stockList.size() > 0) {
//            tFormulaAuthMapper.deleteByCenterFormulaStocks(stockList);
//            tFormulaAuthMapper.insertListByCenterFormulaStocks(stockList);
//            tFormulaAuthMapper.deleteFormulaIdNotExists();
//        }
//
//        return stockList;
//    }
//
//
//    private ResEntity postForObject(String url, Object param) {
//
//        ResEntity entity = null;
//        try {
//
//            log.info("【药房系统-配方】" + url + " 接口入参：" + JSONObject.toJSONString(param));
//
//            HttpHeaders headers = new HttpHeaders();
//            headers.setContentType(MediaType.APPLICATION_JSON);
//            HttpEntity request1 = new HttpEntity<>(param, headers);
//            entity = restTemplate.postForObject(url, request1, ResEntity.class);
//
//            log.info("【药房系统-配方】" + url + " 接口出参：" + JSONObject.toJSONString(entity));
//
//        } catch (Exception e) {
//            ExceptionUtils.throwErrorCustomRuntimeException("【药房系统-配方】" + url + " 接口异常：" + e.getMessage());
//        }
//
//        if (entity == null) {
//            ExceptionUtils.throwErrorCustomRuntimeException("【药房系统-配方】" + url + " 接口异常");
//        } else if (!entity.getStatus()) {
//            ExceptionUtils.throwErrorCustomRuntimeException("【药房系统-配方】" + url + " 接口失败：" + entity.getMessage());
//        }
//        return entity;
//    }
//}
