package com.jiuzhekan.cbkj.common.utils.excel;

import com.jiuzhekan.cbkj.beans.statistics.TAnalysisNationalIndexVo;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.DateUtil;
import com.jiuzhekan.statistics.beans.function.SimpleP;
import com.jiuzhekan.statistics.beans.function.TStatisticsFunction2;
import com.jiuzhekan.statistics.beans.register.ChangeStaticsFunction2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFCell;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.URLEncoder;
import java.util.*;

@Slf4j
@Component
public class ExportExcel<T> {

    /**
     * 创建表格标题样式
     *
     * @param workbook workbook
     * @return org.apache.poi.xssf.usermodel.CellStyle
     * <AUTHOR>
     * @date 2022/1/14
     */
    public static CellStyle createTitleCellStyle(SXSSFWorkbook workbook) {
        CellStyle cellStyle = workbook.createCellStyle();
        // 字体样式
        Font fontStyle = workbook.createFont();
        // 加粗
        // fontStyle.setBold(true);
        // 字体
        fontStyle.setFontName("黑体");
        // 大小
        fontStyle.setFontHeightInPoints((short) 12);
        cellStyle.setFont(fontStyle);
        // 水平居中
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        // 垂直居中
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        return cellStyle;
    }

    /**
     * 创建表格普通样式
     *
     * @param workbook workbook
     * @return org.apache.poi.xssf.usermodel.CellStyle
     * <AUTHOR>
     * @date 2022/1/14
     */
    public static CellStyle createNormalCellStyle(SXSSFWorkbook workbook) {
        CellStyle cellStyle = workbook.createCellStyle();
        // 字体样式
        Font fontStyle = workbook.createFont();
        // 加粗
        // fontStyle.setBold(true);
        // 字体
        fontStyle.setFontName("宋体");
        // 大小
        fontStyle.setFontHeightInPoints((short) 12);
        cellStyle.setFont(fontStyle);
        // 水平居中
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        // 垂直居中
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        return cellStyle;
    }

    /**
     * 组装标题行与数据行
     *
     * @param sheet   sheet
     * @param rowNum  从第几行开始填充
     * @param headers 标题集合 LinkedHashMap<T.field, title>
     * @param dataset 数值集合 Collection<T.field, value>
     * <AUTHOR>
     * @date 2022/1/14
     */
//    public void packDataCell(SXSSFSheet sheet, int rowNum, LinkedHashMap<String, String> headers, Collection<T> dataset, Map<String, List<Integer>> mergeMap) {
//        packDataCell(sheet, rowNum, headers, dataset);
//
//        if (null != mergeMap && !mergeMap.isEmpty()) {
//            mergeMap.forEach((k, v) -> {
//                //合并单元格
//                if (null != v && v.size() == 4 && (!v.get(0).equals(v.get(1)) || !v.get(2).equals(v.get(3)))) {
//                    CellRangeAddress mergeAddress = new CellRangeAddress(v.get(0), v.get(1), v.get(2), v.get(3));
//                    sheet.addMergedRegion(mergeAddress);
//                }
//            });
//        }
    /**
     * 组装标题行与数据行
     *
     * @param sheet   sheet
     * @param rowNum  从第几行开始填充
     * @param headers 标题集合 LinkedHashMap<T.field, title>
     * @param dataset 数值集合 Collection<T.field, value>
     * <AUTHOR>
     * @date 2022/1/14
     */
    public void packDataCell(SXSSFSheet sheet, int rowNum, LinkedHashMap<String, String> headers, Collection<T> dataset) {
        SXSSFRow row = sheet.createRow(rowNum);
        SXSSFCell cell;
        CellStyle normalCellStyle = ExportExcel.createNormalCellStyle(sheet.getWorkbook());

        Iterator<Map.Entry<String, String>> headerIt = headers.entrySet().iterator();
        int colIndex = 0;
        while (headerIt.hasNext()) {
            cell = row.createCell(colIndex);
            cell.setCellValue(new XSSFRichTextString(headerIt.next().getValue()));
            colIndex++;
        }

        //数据行
        Iterator<T> it = dataset.iterator();
        int rowIndex = rowNum + 1;
        while (it.hasNext()) {
            row = sheet.createRow(rowIndex);
            T t = it.next();

            Iterator<String> headerKeys = headers.keySet().iterator();
            colIndex = 0;
            while (headerKeys.hasNext()) {
                try {

                    cell = row.createCell(colIndex);
                    String fieldName = headerKeys.next();
                    //反射，动态调用getXxx()方法得到属性值
                    String getMethodName = "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
                    Method getMethod = t.getClass().getMethod(getMethodName);
                    Object value = getMethod.invoke(t);
                    if (value != null && value != "") {
                        String textValue = value.toString();
                        XSSFRichTextString richString = new XSSFRichTextString(textValue);
                        cell.setCellValue(richString);
                        cell.setCellStyle(normalCellStyle);
                    }
                    colIndex++;

                } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
                    e.printStackTrace();
                }
            }
            rowIndex++;
        }

    }

//    }

    /**
     * 指定路径下生成EXCEL文件
     */
    public void getExportedFile(SXSSFWorkbook workbook, String name, HttpServletResponse response) {
        BufferedOutputStream fos = null;
        try {
            String fileName = name + "-" + DateUtil.getDateFormats(DateUtil.YYYYMMDD, null) + ".xlsx";
            response.setContentType("application/x-msdownload");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            fos = new BufferedOutputStream(response.getOutputStream());
            workbook.write(fos);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            if (fos != null) {
                try {
                    // 此方法能够删除导出过程中生成的xml临时文件
                    workbook.dispose();
                    fos.close();
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
    }

    public void getExportedFilePath(SXSSFWorkbook workbook, String name) {
        String fileName = name + "-" + DateUtil.getDateFormats(DateUtil.YYYYMMDD, null) + ".xlsx";
        //文件存储路径
        String realPath = "/app/uploadFile/cbkjFile/" + fileName;
        FileOutputStream fos = null;
        try {
            fos = new FileOutputStream(realPath);
            workbook.write(fos);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 大数据分析模块-处方金额分析数据导出
     *
     * @param dataset  数据集合
     * @param fileName 标题
     * @param response
     * @param type
     */
    public void getPreAmountExcel(Collection<T> dataset, String fileName, HttpServletResponse response, Integer type) {
        // 声明一个工作薄
        SXSSFWorkbook workbook = new SXSSFWorkbook();
        // 生成一个表格
        SXSSFSheet sheet = workbook.createSheet(fileName);
        // 设置表格默认列宽度为15个字节
        sheet.setDefaultColumnWidth((short) 20);

        // 产生表格标题行
        SXSSFRow row = sheet.createRow(0);//行
        SXSSFCell cell1 = row.createCell(0);//创建一列
        XSSFRichTextString text1 = new XSSFRichTextString(fileName);//第1行标题
        cell1.setCellValue(text1);

        //为标题添加格式
        CellStyle cellStyle = workbook.createCellStyle();
        Font fontStyle = workbook.createFont(); // 字体样式
        // fontStyle.setBold(true); // 加粗
        fontStyle.setFontName("黑体"); // 字体
        fontStyle.setFontHeightInPoints((short) 12); // 大小
        cellStyle.setAlignment(HorizontalAlignment.CENTER); // 创建一个居中格式
        cellStyle.setFont(fontStyle);
        cell1.setCellStyle(cellStyle);

        // 合并单元格
        CellRangeAddress cra = new CellRangeAddress(0, 0, 0, 10); // 起始行, 终止行, 起始列, 终止列
        sheet.addMergedRegion(cra);

        //给表格内容创建样式
        CellStyle cellStyle2 = workbook.createCellStyle();
        Font fontStyle2 = workbook.createFont(); // 字体样式
        fontStyle2.setFontName("宋体"); // 字体
        fontStyle2.setFontHeightInPoints((short) 12); // 大小
        cellStyle2.setAlignment(HorizontalAlignment.CENTER); // 创建一个居中格式
        cellStyle2.setFont(fontStyle2);

        //第2行,第0列
        SXSSFRow row2 = sheet.createRow(1);
        SXSSFCell cell20 = row2.createCell(0);//创建一列
        XSSFRichTextString text20 = new XSSFRichTextString("");
        cell20.setCellValue(text20);
        cell20.setCellStyle(cellStyle2);

        //第2行，第1列
        SXSSFCell cell21 = row2.createCell(1);//创建一列
        XSSFRichTextString text21 = new XSSFRichTextString("单次就诊金额");
        cell21.setCellValue(text21);
        cell21.setCellStyle(cellStyle2);

        //第2行，第3列
        SXSSFCell cell23 = row2.createCell(3);//创建一列
        XSSFRichTextString text23 = new XSSFRichTextString("内服中药方");
        cell23.setCellValue(text23);
        cell23.setCellStyle(cellStyle2);

        //第2行，第5列
        SXSSFCell cell25 = row2.createCell(5);//创建一列
        XSSFRichTextString text25 = new XSSFRichTextString("外用中药方");
        cell25.setCellValue(text25);
        cell25.setCellStyle(cellStyle2);

        //第2行，第7列
        SXSSFCell cell27 = row2.createCell(7);//创建一列
        XSSFRichTextString text27 = new XSSFRichTextString("中成药方");
        cell27.setCellValue(text27);
        cell27.setCellStyle(cellStyle2);

        //第2行，第7列
        SXSSFCell cell29 = row2.createCell(9);//创建一列
        XSSFRichTextString text29 = new XSSFRichTextString("适宜技术方");
        cell29.setCellValue(text29);
        cell29.setCellStyle(cellStyle2);
        //合并单元格
        CellRangeAddress cra21 = new CellRangeAddress(1, 1, 1, 2); // 起始行, 终止行, 起始列, 终止列
        sheet.addMergedRegion(cra21);

        CellRangeAddress cra23 = new CellRangeAddress(1, 1, 3, 4);
        sheet.addMergedRegion(cra23);

        CellRangeAddress cra25 = new CellRangeAddress(1, 1, 5, 6);
        sheet.addMergedRegion(cra25);

        CellRangeAddress cra27 = new CellRangeAddress(1, 1, 7, 8);
        sheet.addMergedRegion(cra27);

        CellRangeAddress cra29 = new CellRangeAddress(1, 1, 9, 10);
        sheet.addMergedRegion(cra29);

        //第3行，第1列
        SXSSFRow row3 = sheet.createRow(2);
        SXSSFCell cell30 = row3.createCell(0);//创建一列
        XSSFRichTextString text30 = new XSSFRichTextString("");
        cell30.setCellValue(text30);
        cell30.setCellStyle(cellStyle2);

        //第3行，第2列
        SXSSFCell cell31 = row3.createCell(1);//创建一列
        XSSFRichTextString text31 = new XSSFRichTextString("次数");
        cell31.setCellValue(text31);
        cell21.setCellStyle(cellStyle2);

        //第3行，第3列
        SXSSFCell cell32 = row3.createCell(2);//创建一列
        XSSFRichTextString text32 = new XSSFRichTextString("占比（%）");
        cell32.setCellValue(text32);
        cell32.setCellStyle(cellStyle2);

        //第3行，第4列
        SXSSFCell cell33 = row3.createCell(3);//创建一列
        XSSFRichTextString text33 = new XSSFRichTextString("处方数");
        cell33.setCellValue(text33);
        cell33.setCellStyle(cellStyle2);

        //第3行，第5列
        SXSSFCell cell34 = row3.createCell(4);//创建一列
        XSSFRichTextString text34 = new XSSFRichTextString("占比（%）");
        cell34.setCellValue(text34);
        cell34.setCellStyle(cellStyle2);

        //第3行，第6列
        SXSSFCell cell35 = row3.createCell(5);//创建一列
        XSSFRichTextString text35 = new XSSFRichTextString("处方数");
        cell35.setCellValue(text35);
        cell35.setCellStyle(cellStyle2);

        //第3行，第7列
        SXSSFCell cell36 = row3.createCell(6);//创建一列
        XSSFRichTextString text36 = new XSSFRichTextString("占比（%）");
        cell36.setCellValue(text36);
        cell36.setCellStyle(cellStyle2);

        //第3行，第8列
        SXSSFCell cell37 = row3.createCell(7);//创建一列
        XSSFRichTextString text37 = new XSSFRichTextString("处方数");
        cell37.setCellValue(text37);
        cell37.setCellStyle(cellStyle2);

        //第3行，第9列
        SXSSFCell cell38 = row3.createCell(8);//创建一列
        XSSFRichTextString text38 = new XSSFRichTextString("占比（%）");
        cell38.setCellValue(text38);
        cell38.setCellStyle(cellStyle2);

        //第3行，第10列
        SXSSFCell cell39 = row3.createCell(9);//创建一列
        XSSFRichTextString text39 = new XSSFRichTextString("处方数");
        cell39.setCellValue(text39);
        cell39.setCellStyle(cellStyle2);

        //第3行，第11列
        SXSSFCell cell310 = row3.createCell(10);//创建一列
        XSSFRichTextString text310 = new XSSFRichTextString("占比（%）");
        cell310.setCellValue(text310);
        cell310.setCellStyle(cellStyle2);

        //合并出“处方金额”单元格
        CellRangeAddress cra11 = new CellRangeAddress(1, 2, 0, 0);
        sheet.addMergedRegion(cra11);

        //第2行,第0列
        XSSFRichTextString text20New = new XSSFRichTextString("处方金额");
        cell20.setCellValue(text20New);
        cell20.setCellStyle(cellStyle2);

        try {
            if (null != dataset && !dataset.isEmpty()) {
                Iterator<T> it = dataset.iterator();// 遍历集合数据，产生数据行
                int index = 2;
                while (it.hasNext()) {
                    index++;
                    row = sheet.createRow(index);
                    T t = (T) it.next();
                    // 利用反射，根据javabean属性的先后顺序，动态调用getXxx()方法得到属性值
                    Field[] fields = t.getClass().getDeclaredFields();
                    for (short i = 0; i < 11; i++) {
                        SXSSFCell cell = row.createCell(i);
                        Field field = fields[i];
                        String fieldName = field.getName();
                        if (type == 1) {
                            String[] split = fieldName.split("cglib_prop_");
                            fieldName = split[1];
                        }
                        String getMethodName = "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
                        Class tCls = t.getClass();
                        Method getMethod = tCls.getMethod(getMethodName, new Class[]{});
                        Object value = getMethod.invoke(t, new Object[]{});
                        // 判断值的类型后进行强制类型转换
                        String textValue = null;
                        // 其它数据类型都当作字符串简单处理
                        if (value != null && value != "") {
                            textValue = value.toString();
                        }
                        if (textValue != null) {
                            XSSFRichTextString richString = new XSSFRichTextString(textValue);
                            cell.setCellValue(richString);
                            cell.setCellStyle(cellStyle2);
                        }
                    }
                }
            }
            getExportedFile(workbook, fileName, response);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    public void getCountByPreTimeGroupPreOriginExcel(Collection<T> dataset, String fileName, HttpServletResponse response, String recTreTimeBegin,String recTreTimeEnd,int type) {
        SXSSFWorkbook workbook = new SXSSFWorkbook();
        SXSSFSheet sheet = workbook.createSheet(fileName);
        sheet.setDefaultColumnWidth((short) 20);
        SXSSFRow row = sheet.createRow(0);//行
        SXSSFCell cell1 = row.createCell(0);//创建一列
        XSSFRichTextString text1 = new XSSFRichTextString(fileName);//第1行标题
        cell1.setCellValue(text1);
        //为标题添加格式
        CellStyle cellStyle = workbook.createCellStyle();
        Font fontStyle = workbook.createFont(); // 字体样式
        fontStyle.setFontName("黑体"); // 字体
        fontStyle.setFontHeightInPoints((short) 12); // 大小
        cellStyle.setAlignment(HorizontalAlignment.CENTER); // 创建一个居中格式
        cellStyle.setFont(fontStyle);
        cell1.setCellStyle(cellStyle);
        CellRangeAddress cra =
                new CellRangeAddress(0, 0, 0, 2); // 起始行, 终止行, 起始列, 终止列
        sheet.addMergedRegion(cra);

        //给表格内容创建样式
        CellStyle cellStyle2 = workbook.createCellStyle();
        Font fontStyle2 = workbook.createFont(); // 字体样式
        fontStyle2.setFontName("宋体"); // 字体
        fontStyle2.setFontHeightInPoints((short) 12); // 大小
        cellStyle2.setAlignment(HorizontalAlignment.CENTER); // 创建一个居中格式
        cellStyle2.setFont(fontStyle2);

        //第2行,第0列
        SXSSFRow row2 = sheet.createRow(1);
        SXSSFCell cell20 = row2.createCell(1);//创建二列
        StringBuilder monthStr = new StringBuilder("统计时间：");
        monthStr.append(recTreTimeBegin).append("~").append(recTreTimeEnd);
        XSSFRichTextString text20 = new XSSFRichTextString(monthStr.toString());
        cell20.setCellValue(text20);
        cell20.setCellStyle(cellStyle2);
        //合并
        // 起始行, 终止行, 起始列, 终止列
        CellRangeAddress cra21 =
                new CellRangeAddress(1, 1, 1, 10);

        //第3行，第1列
        SXSSFRow row3 = sheet.createRow(2);
        SXSSFCell cell30 = row3.createCell(0);//创建一列
        XSSFRichTextString text30 = new XSSFRichTextString("类型");
        cell30.setCellValue(text30);
        cell30.setCellStyle(cellStyle2);

        //第3行，第2列
        SXSSFCell cell31 = row3.createCell(1);//创建一列
        XSSFRichTextString text31 = new XSSFRichTextString("占比");
        cell31.setCellValue(text31);
        cell31.setCellStyle(cellStyle2);

        //第3行，第3列
        SXSSFCell cell32 = row3.createCell(2);//创建一列
        XSSFRichTextString text32 = new XSSFRichTextString("数量");
        cell32.setCellValue(text32);
        cell32.setCellStyle(cellStyle2);
        try {
            if (null != dataset && !dataset.isEmpty()) {
                Iterator<T> it = dataset.iterator();// 遍历集合数据，产生数据行
                int index = 2;
                while (it.hasNext()) {
                    index++;
                    row = sheet.createRow(index);
                    T t = (T) it.next();
                    // 利用反射，根据javabean属性的先后顺序，动态调用getXxx()方法得到属性值
                    Field[] fields = t.getClass().getDeclaredFields();
                    for (short i = 0; i < fields.length; i++) {
                        SXSSFCell cell = row.createCell(i);
                        Field field = fields[i];
                        String fieldName = field.getName();
                        if (type == 1) {
                            String[] split = fieldName.split("cglib_prop_");
                            fieldName = split[1];
                        }
                        String getMethodName = "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
                        Class tCls = t.getClass();
                        Method getMethod = tCls.getMethod(getMethodName, new Class[]{});
                        Object value = getMethod.invoke(t, new Object[]{});
                        // 判断值的类型后进行强制类型转换
                        String textValue = null;
                        // 其它数据类型都当作字符串简单处理
                        if (value != null && value != "") {
                            textValue = value.toString();
                        }
                        if (textValue != null) {
                            XSSFRichTextString richString = new XSSFRichTextString(textValue);
                            cell.setCellValue(richString);
                            cell.setCellStyle(cellStyle2);
                        }
                    }
                }
            }
            getExportedFile(workbook, fileName.toString(), response);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 大数据分析模块-全县医生工作量分析导出
     *
     * @param dataset  数据集合
     * @param response
     * @param type
     * @param name     第一列标题
     * @param year     年
     * @param month    月
     */
    public void getDocutorWorkloadExcel(Collection<T> dataset,
                                        HttpServletResponse response,
                                        Integer type,
                                        String name,
                                        String year,
                                        String month) {
        //Excel标题
        StringBuilder fileName = new StringBuilder(year);
        fileName.append("年");
        if (StringUtils.isNotBlank(month)) {
            fileName.append(month).append("月");
        }
        fileName.append("医生工作量");

        // 声明一个工作薄
        SXSSFWorkbook workbook = new SXSSFWorkbook();
        // 生成一个表格
        SXSSFSheet sheet = workbook.createSheet(fileName.toString());
        // 设置表格默认列宽度为15个字节
        sheet.setDefaultColumnWidth((short) 20);

        // 产生表格标题行
        SXSSFRow row = sheet.createRow(0);//行
        SXSSFCell cell1 = row.createCell(0);//创建一列
        XSSFRichTextString text1 = new XSSFRichTextString(fileName.toString());//第1行标题
        cell1.setCellValue(text1);

        //为标题添加格式
        CellStyle cellStyle = workbook.createCellStyle();
        Font fontStyle = workbook.createFont(); // 字体样式
        // fontStyle.setBold(true); // 加粗
        fontStyle.setFontName("黑体"); // 字体
        fontStyle.setFontHeightInPoints((short) 12); // 大小
        cellStyle.setAlignment(HorizontalAlignment.CENTER); // 创建一个居中格式
        cellStyle.setFont(fontStyle);
        cell1.setCellStyle(cellStyle);

        // 合并单元格
        CellRangeAddress cra = new CellRangeAddress(0, 0, 0, 12); // 起始行, 终止行, 起始列, 终止列
        sheet.addMergedRegion(cra);


        //给表格内容创建样式
        CellStyle cellStyle2 = workbook.createCellStyle();
        Font fontStyle2 = workbook.createFont(); // 字体样式
        fontStyle2.setFontName("宋体"); // 字体
        fontStyle2.setFontHeightInPoints((short) 12); // 大小
        cellStyle2.setAlignment(HorizontalAlignment.CENTER); // 创建一个居中格式
        cellStyle2.setFont(fontStyle2);

        //第2行,第0列
        SXSSFRow row2 = sheet.createRow(1);
        SXSSFCell cell20 = row2.createCell(1);//创建二列
        StringBuilder monthStr = new StringBuilder(month);
        monthStr.append("月");
        XSSFRichTextString text20 = new XSSFRichTextString(monthStr.toString());
        cell20.setCellValue(text20);
        cell20.setCellStyle(cellStyle2);

        //第2行，第1列
        SXSSFCell cell21 = row2.createCell(7);//创建一列
        StringBuilder yearStr = new StringBuilder(year);
        yearStr.append("年总计");
        XSSFRichTextString text21 = new XSSFRichTextString(yearStr.toString());
        cell21.setCellValue(text21);
        cell21.setCellStyle(cellStyle2);

        //合并单元格
        CellRangeAddress cra21 = new CellRangeAddress(1, 1, 1, 6); // 起始行, 终止行, 起始列, 终止列
        sheet.addMergedRegion(cra21);
        CellRangeAddress cra23 = new CellRangeAddress(1, 1, 7, 12);
        sheet.addMergedRegion(cra23);

        //第3行，第1列
        SXSSFRow row3 = sheet.createRow(2);
        SXSSFCell cell30 = row3.createCell(0);//创建一列
        XSSFRichTextString text30 = new XSSFRichTextString(name);
        cell30.setCellValue(text30);
        cell30.setCellStyle(cellStyle2);

        //第3行，第2列
        SXSSFCell cell31 = row3.createCell(1);//创建一列
        XSSFRichTextString text31 = new XSSFRichTextString("就诊人次");
        cell31.setCellValue(text31);
        cell31.setCellStyle(cellStyle2);

        //第3行，第3列
        SXSSFCell cell32 = row3.createCell(2);//创建一列
        XSSFRichTextString text32 = new XSSFRichTextString("内服中药方");
        cell32.setCellValue(text32);
        cell32.setCellStyle(cellStyle2);

        //第3行，第4列
        SXSSFCell cell33 = row3.createCell(3);//创建一列
        XSSFRichTextString text33 = new XSSFRichTextString("外用中药方");
        cell33.setCellValue(text33);
        cell33.setCellStyle(cellStyle2);

        //第3行，第5列
        SXSSFCell cell34 = row3.createCell(4);//创建一列
        XSSFRichTextString text34 = new XSSFRichTextString("中成药方");
        cell34.setCellValue(text34);
        cell34.setCellStyle(cellStyle2);

        //第3行，第6列
        SXSSFCell cell35 = row3.createCell(5);//创建一列
        XSSFRichTextString text35 = new XSSFRichTextString("适宜技术方");
        cell35.setCellValue(text35);
        cell35.setCellStyle(cellStyle2);

        //第3行，第7列
        SXSSFCell cell36 = row3.createCell(6);//创建一列
        XSSFRichTextString text36 = new XSSFRichTextString("总开方数");
        cell36.setCellValue(text36);
        cell36.setCellStyle(cellStyle2);

        //第3行，第8列
        SXSSFCell cell37 = row3.createCell(7);//创建一列
        XSSFRichTextString text37 = new XSSFRichTextString("就诊人次");
        cell37.setCellValue(text37);
        cell37.setCellStyle(cellStyle2);

        //第3行，第9列
        SXSSFCell cell38 = row3.createCell(8);//创建一列
        XSSFRichTextString text38 = new XSSFRichTextString("内服中药方");
        cell38.setCellValue(text38);
        cell38.setCellStyle(cellStyle2);

        //第3行，第10列
        SXSSFCell cell39 = row3.createCell(9);//创建一列
        XSSFRichTextString text39 = new XSSFRichTextString("外用中药方");
        cell39.setCellValue(text39);
        cell39.setCellStyle(cellStyle2);

        //第3行，第11列
        SXSSFCell cell310 = row3.createCell(10);//创建一列
        XSSFRichTextString text310 = new XSSFRichTextString("中成药方");
        cell310.setCellValue(text310);
        cell310.setCellStyle(cellStyle2);

        //第3行，第12列
        SXSSFCell cell311 = row3.createCell(11);//创建一列
        XSSFRichTextString text311 = new XSSFRichTextString("适宜技术方");
        cell311.setCellValue(text311);
        cell311.setCellStyle(cellStyle2);

        //第3行，第13列
        SXSSFCell cell312 = row3.createCell(12);//创建一列
        XSSFRichTextString text312 = new XSSFRichTextString("总开方数");
        cell312.setCellValue(text312);
        cell312.setCellStyle(cellStyle2);

        try {
            if (null != dataset && !dataset.isEmpty()) {
                Iterator<T> it = dataset.iterator();// 遍历集合数据，产生数据行
                int index = 2;
                while (it.hasNext()) {
                    index++;
                    row = sheet.createRow(index);
                    T t = (T) it.next();
                    // 利用反射，根据javabean属性的先后顺序，动态调用getXxx()方法得到属性值
                    Field[] fields = t.getClass().getDeclaredFields();
                    for (short i = 0; i < 13; i++) {
                        SXSSFCell cell = row.createCell(i);
                        Field field = fields[i];
                        String fieldName = field.getName();
                        if (type == 1) {
                            String[] split = fieldName.split("cglib_prop_");
                            fieldName = split[1];
                        }
                        String getMethodName = "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
                        Class tCls = t.getClass();
                        Method getMethod = tCls.getMethod(getMethodName, new Class[]{});
                        Object value = getMethod.invoke(t, new Object[]{});
                        // 判断值的类型后进行强制类型转换
                        String textValue = null;
                        // 其它数据类型都当作字符串简单处理
                        if (value != null && value != "") {
                            textValue = value.toString();
                        }
                        if (textValue != null) {
                            XSSFRichTextString richString = new XSSFRichTextString(textValue);
                            cell.setCellValue(richString);
                            cell.setCellStyle(cellStyle2);
                        }
                    }
                }
            }
            getExportedFile(workbook, fileName.toString(), response);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导出收费处方数量统计
     *
     * @param dataset      dataset
     * @param response     response
     * @param preTimeBegin preTimeBegin
     * @param preTimeEnd   preTimeEnd
     * @return void
     * <AUTHOR>
     * @date 2021/12/27
     */
    public void getPreChargedNumExcel(Collection<T> dataset, Map<String, List<Integer>> mergeMap, HttpServletResponse response, String preTimeBegin, String preTimeEnd) {
        //Excel标题
        String fileName = "医共体处方统计分析";

        // 声明一个工作薄
        SXSSFWorkbook workbook = new SXSSFWorkbook();
        // 生成一个表格
        SXSSFSheet sheet = workbook.createSheet(fileName);
        // 设置表格默认列宽度为15个字节
        sheet.setDefaultColumnWidth((short) 20);

        // 产生表格标题行
        SXSSFRow row = sheet.createRow(0);//行
        SXSSFCell cell1 = row.createCell(0);//创建一列
        XSSFRichTextString text1 = new XSSFRichTextString(fileName);//第1行标题
        cell1.setCellValue(text1);

        //为标题添加格式
        CellStyle cellStyle = workbook.createCellStyle();
        Font fontStyle = workbook.createFont(); // 字体样式
        // fontStyle.setBold(true); // 加粗
        fontStyle.setFontName("黑体"); // 字体
        fontStyle.setFontHeightInPoints((short) 12); // 大小
        cellStyle.setAlignment(HorizontalAlignment.CENTER); // 创建一个居中格式
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle.setFont(fontStyle);
        cell1.setCellStyle(cellStyle);

        // 合并单元格
        CellRangeAddress cra = new CellRangeAddress(0, 0, 0, 5); // 起始行, 终止行, 起始列, 终止列
        sheet.addMergedRegion(cra);


        //给表格内容创建样式
        CellStyle cellStyle2 = workbook.createCellStyle();
        Font fontStyle2 = workbook.createFont(); // 字体样式
        fontStyle2.setFontName("宋体"); // 字体
        fontStyle2.setFontHeightInPoints((short) 12); // 大小
        cellStyle2.setAlignment(HorizontalAlignment.CENTER); // 创建一个居中格式
        cellStyle2.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle2.setFont(fontStyle2);

        //第2行
        SXSSFRow row2 = sheet.createRow(1);
        SXSSFCell cell20 = row2.createCell(0);//创建二列
        XSSFRichTextString text20 = new XSSFRichTextString("统计时间：" + preTimeBegin + " - " + preTimeEnd);
        cell20.setCellValue(text20);
        cell20.setCellStyle(cellStyle);

        //合并单元格
        CellRangeAddress cra21 = new CellRangeAddress(1, 1, 0, 5); // 起始行, 终止行, 起始列, 终止列
        sheet.addMergedRegion(cra21);

        //第3行，第1列
        SXSSFRow row3 = sheet.createRow(2);
        SXSSFCell cell30 = row3.createCell(0);//创建一列
        XSSFRichTextString text30 = new XSSFRichTextString("医疗机构");
        cell30.setCellValue(text30);
        cell30.setCellStyle(cellStyle);

        //第3行，第2列
        SXSSFCell cell31 = row3.createCell(1);//创建一列
        XSSFRichTextString text31 = new XSSFRichTextString("科室类型");
        cell31.setCellValue(text31);
        cell31.setCellStyle(cellStyle);

        //第3行，第3列
        SXSSFCell cell32 = row3.createCell(2);//创建一列
        XSSFRichTextString text32 = new XSSFRichTextString("科室");
        cell32.setCellValue(text32);
        cell32.setCellStyle(cellStyle);

        //第3行，第4列
        SXSSFCell cell33 = row3.createCell(3);//创建一列
        XSSFRichTextString text33 = new XSSFRichTextString("医生");
        cell33.setCellValue(text33);
        cell33.setCellStyle(cellStyle);

        //第3行，第5列
        SXSSFCell cell34 = row3.createCell(4);//创建一列
        XSSFRichTextString text34 = new XSSFRichTextString("已收费处方数量");
        cell34.setCellValue(text34);
        cell34.setCellStyle(cellStyle);

        //第3行，第6列
        SXSSFCell cell35 = row3.createCell(5);//创建一列
        XSSFRichTextString text35 = new XSSFRichTextString("合计");
        cell35.setCellValue(text35);
        cell35.setCellStyle(cellStyle);


        try {
            if (null != dataset && !dataset.isEmpty()) {
                Iterator<T> it = dataset.iterator();// 遍历集合数据，产生数据行
                int index = 2;
                while (it.hasNext()) {
                    index++;
                    row = sheet.createRow(index);
                    T t = (T) it.next();
                    // 利用反射，根据javabean属性的先后顺序，动态调用getXxx()方法得到属性值
                    Field[] fields = t.getClass().getDeclaredFields();
                    for (short i = 0; i < 6; i++) {
                        SXSSFCell cell = row.createCell(i);
                        Field field = fields[i];
                        String fieldName = field.getName();
                        String getMethodName = "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
                        Class tCls = t.getClass();
                        Method getMethod = tCls.getMethod(getMethodName, new Class[]{});
                        Object value = getMethod.invoke(t, new Object[]{});
                        // 都当作字符串简单处理
                        if (value != null && value != "") {
                            String textValue = value.toString();
                            XSSFRichTextString richString = new XSSFRichTextString(textValue);
                            cell.setCellValue(richString);
                            cell.setCellStyle(cellStyle2);
                        }
                    }
                }
            }

            if (null != mergeMap && !mergeMap.isEmpty()) {

                mergeMap.forEach((k, v) -> {
                    //合并单元格
                    if (null != v && v.size() == 4 && !v.get(0).equals(v.get(1))) {
                        CellRangeAddress mergeAddress = new CellRangeAddress(3 + v.get(0), 3 + v.get(1), v.get(2), v.get(3)); // 起始行, 终止行, 起始列, 终止列
                        sheet.addMergedRegion(mergeAddress);
                    }
                });
            }

            getExportedFile(workbook, fileName, response);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 国考指标分析数据导出（按数量金额统计）
     *
     * @param tAnalysisNationalIndexVos
     * @param response
     * @param startTimeStr
     * @param endTimeStr
     */
    public void getNationalAnalysisExcel(List<TAnalysisNationalIndexVo> tAnalysisNationalIndexVos,
                                         HttpServletResponse response,
                                         String startTimeStr, String endTimeStr) {

        //获取当前时间的年份及月份
        Calendar cal = Calendar.getInstance();
        int year = cal.get(Calendar.YEAR);
        int month = cal.get(Calendar.MONTH);

        //Excel标题
        StringBuilder fileName = new StringBuilder();
        if (StringUtils.isNotBlank(startTimeStr)) {
            Date startTime = DateUtil.getDateFormatd(startTimeStr, DateUtil.YYYY_MM_DD);
            fileName.append(DateUtil.getDateFormats(DateUtil.YYYY_MM_DD, startTime));
        } else {
            fileName.append((year + "") + "-0" + (month + 1 + "") + "-01");
        }

        if (StringUtils.isNotBlank(endTimeStr)) {
            Date endTime = DateUtil.getDateFormatd(endTimeStr, DateUtil.YYYY_MM_DD);
            fileName.append("~" + DateUtil.getDateFormats(DateUtil.YYYY_MM_DD, endTime));
        } else {
            fileName.append("~" + DateUtil.getDateFormats(DateUtil.YYYY_MM_DD, new Date()));
        }
        fileName.append(" 国考指标统计（按数量金额统计）");

        //声明一个工作薄
        SXSSFWorkbook workbook = new SXSSFWorkbook();
        //生成一个表格
        SXSSFSheet sheet = workbook.createSheet(fileName.toString());
        //设置表格默认列宽度为15个字节
        sheet.setDefaultColumnWidth((short) 20);

        //产生表格标题行
        SXSSFRow row = sheet.createRow(0);//行
        SXSSFCell cell1 = row.createCell(0);//创建一列
        XSSFRichTextString text1 = new XSSFRichTextString(fileName.toString());//第一行标题
        cell1.setCellValue(text1);

        //为标题添加样式
        CellStyle cellStyle = workbook.createCellStyle();
        Font fontStyle = workbook.createFont();//字体样式
        fontStyle.setFontName("黑体");//字体
        fontStyle.setFontHeightInPoints((short) 12); //大小
        cellStyle.setAlignment(HorizontalAlignment.CENTER);//居中
        cellStyle.setFont(fontStyle);
        cell1.setCellStyle(cellStyle);

        //合并单元格
        CellRangeAddress cra = new CellRangeAddress(0, 0, 0, 6);
        sheet.addMergedRegion(cra);

        //给表格内容创建样式
        CellStyle cellStyle2 = workbook.createCellStyle();
        Font fontStyle2 = workbook.createFont();//字体样式
        fontStyle2.setFontName("宋体");//字体
        fontStyle2.setFontHeightInPoints((short) 12);//大小
        cellStyle2.setAlignment(HorizontalAlignment.CENTER);//居中
        cellStyle2.setFont(fontStyle2);

        //第2行,第0列
        SXSSFRow row2 = sheet.createRow(1);
        SXSSFCell cell20 = row2.createCell(0);//创建一列
        XSSFRichTextString text20 = new XSSFRichTextString("科室");
        cell20.setCellValue(text20);
        cell20.setCellStyle(cellStyle2);

        //第2行,第1列
        SXSSFCell cell21 = row2.createCell(1);//创建一列
        XSSFRichTextString text21 = new XSSFRichTextString("门诊中药处方数");
        cell21.setCellValue(text21);
        cell21.setCellStyle(cellStyle2);

        //第2行,第2列
        SXSSFCell cell22 = row2.createCell(2);//创建一列
        XSSFRichTextString text22 = new XSSFRichTextString("门诊散装中药饮片和小包装中药饮片处方数");
        cell22.setCellValue(text22);
        cell22.setCellStyle(cellStyle2);

        //第2行,第3列
        SXSSFCell cell23 = row2.createCell(3);//创建一列
        XSSFRichTextString text23 = new XSSFRichTextString("门诊患者中药饮片使用人次");
        cell23.setCellValue(text23);
        cell23.setCellStyle(cellStyle2);

        //第2行,第4列
        SXSSFCell cell24 = row2.createCell(4);//创建一列
        XSSFRichTextString text24 = new XSSFRichTextString("门诊患者使用中医非药物疗法人次");
        cell24.setCellValue(text24);
        cell24.setCellStyle(cellStyle2);

        //第2行,第5列
        SXSSFCell cell25 = row2.createCell(5);//创建一列
        XSSFRichTextString text25 = new XSSFRichTextString("中药收入");
        cell25.setCellValue(text25);
        cell25.setCellStyle(cellStyle2);

        //第2行,第6列
        SXSSFCell cell26 = row2.createCell(6);//创建一列
        XSSFRichTextString text26 = new XSSFRichTextString("中药饮片收入");
        cell26.setCellValue(text26);
        cell26.setCellStyle(cellStyle2);

        try {

            if (null != tAnalysisNationalIndexVos && tAnalysisNationalIndexVos.size() > 0) {
                for (int i = 0; i < tAnalysisNationalIndexVos.size(); i++) {
                    row = sheet.createRow(i + 2);
                    String deptName = tAnalysisNationalIndexVos.get(i).getDeptName();
                    String prescriptionNum = tAnalysisNationalIndexVos.get(i).getPrescriptionNum();
                    String preSZOrXBZNum = tAnalysisNationalIndexVos.get(i).getPreSZOrXBZNum();
                    String peopleUseNum = tAnalysisNationalIndexVos.get(i).getPeopleUseNum();
                    String peopleNonUseNum = tAnalysisNationalIndexVos.get(i).getPeopleNonUseNum();
                    String cMedicineIncome = tAnalysisNationalIndexVos.get(i).getCMedicineIncome();
                    String cMedicinePiecesIncome = tAnalysisNationalIndexVos.get(i).getCMedicinePiecesIncome();
                    if (StringUtils.isBlank(deptName)) {
                        deptName = "";
                    }
                    if (StringUtils.isBlank(prescriptionNum)) {
                        prescriptionNum = "";
                    }
                    if (StringUtils.isBlank(preSZOrXBZNum)) {
                        preSZOrXBZNum = "";
                    }
                    if (StringUtils.isBlank(peopleUseNum)) {
                        peopleUseNum = "";
                    }
                    if (StringUtils.isBlank(peopleNonUseNum)) {
                        peopleNonUseNum = "";
                    }
                    if (StringUtils.isBlank(cMedicineIncome)) {
                        cMedicineIncome = "";
                    }
                    if (StringUtils.isBlank(cMedicinePiecesIncome)) {
                        cMedicinePiecesIncome = "";
                    }
                    SXSSFCell celli0 = row.createCell(0);
                    celli0.setCellValue(deptName);
                    celli0.setCellStyle(cellStyle2);
                    SXSSFCell celli1 = row.createCell(1);
                    celli1.setCellValue(prescriptionNum);
                    celli1.setCellStyle(cellStyle2);
                    SXSSFCell celli2 = row.createCell(2);
                    celli2.setCellValue(preSZOrXBZNum);
                    celli2.setCellStyle(cellStyle2);
                    SXSSFCell celli3 = row.createCell(3);
                    celli3.setCellValue(peopleUseNum);
                    celli3.setCellStyle(cellStyle2);
                    SXSSFCell celli4 = row.createCell(4);
                    celli4.setCellValue(peopleNonUseNum);
                    celli4.setCellStyle(cellStyle2);
                    SXSSFCell celli5 = row.createCell(5);
                    celli5.setCellValue(cMedicineIncome);
                    celli5.setCellStyle(cellStyle2);
                    SXSSFCell celli6 = row.createCell(6);
                    celli6.setCellValue(cMedicinePiecesIncome);
                    celli6.setCellStyle(cellStyle2);
                }
            }
            getExportedFile(workbook, fileName.toString(), response);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    /**
     * 国考指标分析数据导出（科室数量占比统计）
     *
     * @param tAnalysisNationalIndexVos
     * @param response
     * @param startTimeStr
     * @param endTimeStr
     */
    public void getDeptProAnalysisExcel(List<TAnalysisNationalIndexVo> tAnalysisNationalIndexVos,
                                        HttpServletResponse response,
                                        String startTimeStr, String endTimeStr) {

        //获取当前时间的年份及月份
        Calendar cal = Calendar.getInstance();
        int year = cal.get(Calendar.YEAR);
        int month = cal.get(Calendar.MONTH);

        //Excel标题
        StringBuilder fileName = new StringBuilder();
        if (StringUtils.isNotBlank(startTimeStr)) {
            Date startTime = DateUtil.getDateFormatd(startTimeStr, DateUtil.YYYY_MM_DD);
            fileName.append(DateUtil.getDateFormats(DateUtil.YYYY_MM_DD, startTime));
        } else {
            fileName.append((year + "") + "-0" + (month + 1 + "") + "-01");
        }

        if (StringUtils.isNotBlank(endTimeStr)) {
            Date endTime = DateUtil.getDateFormatd(endTimeStr, DateUtil.YYYY_MM_DD);
            fileName.append("~" + DateUtil.getDateFormats(DateUtil.YYYY_MM_DD, endTime));
        } else {
            fileName.append("~" + DateUtil.getDateFormats(DateUtil.YYYY_MM_DD, new Date()));
        }
        fileName.append(" 国考指标统计（按照科室数量占比统计）");

        //声明一个工作薄
        SXSSFWorkbook workbook = new SXSSFWorkbook();
        //生成一个表格
        SXSSFSheet sheet = workbook.createSheet(fileName.toString());
        //设置表格默认列宽度为15个字节
        sheet.setDefaultColumnWidth((short) 20);

        //产生表格标题行
        SXSSFRow row = sheet.createRow(0);//行
        SXSSFCell cell1 = row.createCell(0);//创建一列
        XSSFRichTextString text1 = new XSSFRichTextString(fileName.toString());//第一行标题
        cell1.setCellValue(text1);

        //为标题添加样式
        CellStyle cellStyle = workbook.createCellStyle();
        Font fontStyle = workbook.createFont();//字体样式
        fontStyle.setFontName("黑体");//字体
        fontStyle.setFontHeightInPoints((short) 12); //大小
        cellStyle.setAlignment(HorizontalAlignment.CENTER);//居中
        cellStyle.setFont(fontStyle);
        cell1.setCellStyle(cellStyle);

        //合并单元格
        CellRangeAddress cra = new CellRangeAddress(0, 0, 0, 6);
        sheet.addMergedRegion(cra);

        //给表格内容创建样式
        CellStyle cellStyle2 = workbook.createCellStyle();
        Font fontStyle2 = workbook.createFont();//字体样式
        fontStyle2.setFontName("宋体");//字体
        fontStyle2.setFontHeightInPoints((short) 12);//大小
        cellStyle2.setAlignment(HorizontalAlignment.CENTER);//居中
        cellStyle2.setFont(fontStyle2);

        //第2行,第0列
        SXSSFRow row2 = sheet.createRow(1);
        SXSSFCell cell20 = row2.createCell(0);//创建一列
        XSSFRichTextString text20 = new XSSFRichTextString("科室");
        cell20.setCellValue(text20);
        cell20.setCellStyle(cellStyle2);

        //第2行,第1列
        SXSSFCell cell21 = row2.createCell(1);//创建一列
        XSSFRichTextString text21 = new XSSFRichTextString("门诊中药处方比例");
        cell21.setCellValue(text21);
        cell21.setCellStyle(cellStyle2);

        //第2行,第2列
        SXSSFCell cell22 = row2.createCell(2);//创建一列
        XSSFRichTextString text22 = new XSSFRichTextString("门诊散装中药饮片和小包装中药饮片处方比例");
        cell22.setCellValue(text22);
        cell22.setCellStyle(cellStyle2);

        //第2行,第3列
        SXSSFCell cell23 = row2.createCell(3);//创建一列
        XSSFRichTextString text23 = new XSSFRichTextString("门诊患者中药饮片使用率");
        cell23.setCellValue(text23);
        cell23.setCellStyle(cellStyle2);

        //第2行,第4列
        SXSSFCell cell24 = row2.createCell(4);//创建一列
        XSSFRichTextString text24 = new XSSFRichTextString("门诊患者使用中医非药物疗法比例");
        cell24.setCellValue(text24);
        cell24.setCellStyle(cellStyle2);

        //第2行,第5列
        SXSSFCell cell25 = row2.createCell(5);//创建一列
        XSSFRichTextString text25 = new XSSFRichTextString("中药收入占药品收入比例");
        cell25.setCellValue(text25);
        cell25.setCellStyle(cellStyle2);

        //第2行,第6列
        SXSSFCell cell26 = row2.createCell(6);//创建一列
        XSSFRichTextString text26 = new XSSFRichTextString("中药饮片收入占药品收入比例");
        cell26.setCellValue(text26);
        cell26.setCellStyle(cellStyle2);

        try {

            if (null != tAnalysisNationalIndexVos && tAnalysisNationalIndexVos.size() > 0) {
                for (int i = 0; i < tAnalysisNationalIndexVos.size(); i++) {
                    row = sheet.createRow(i + 2);
                    String deptName = tAnalysisNationalIndexVos.get(i).getDeptName();
                    String prescriptionNum = tAnalysisNationalIndexVos.get(i).getPrescriptionPro().toString();
                    String preSZOrXBZNum = tAnalysisNationalIndexVos.get(i).getPreSZOrXBZPro().toString();
                    String peopleUseNum = tAnalysisNationalIndexVos.get(i).getPeopleUsePro().toString();
                    String peopleNonUseNum = tAnalysisNationalIndexVos.get(i).getPeopleNonUsePro().toString();
                    String cMedicineIncome = tAnalysisNationalIndexVos.get(i).getCMedicineIncomePro().toString();
                    String cMedicinePiecesIncome = tAnalysisNationalIndexVos.get(i).getCMedicinePiecesIncomePro().toString();
                    if (StringUtils.isBlank(deptName)) {
                        deptName = "";
                    }
                    if (StringUtils.isBlank(prescriptionNum)) {
                        prescriptionNum = "";
                    }
                    if (StringUtils.isBlank(preSZOrXBZNum)) {
                        preSZOrXBZNum = "";
                    }
                    if (StringUtils.isBlank(peopleUseNum)) {
                        peopleUseNum = "";
                    }
                    if (StringUtils.isBlank(peopleNonUseNum)) {
                        peopleNonUseNum = "";
                    }
                    if (StringUtils.isBlank(cMedicineIncome)) {
                        cMedicineIncome = "";
                    }
                    if (StringUtils.isBlank(cMedicinePiecesIncome)) {
                        cMedicinePiecesIncome = "";
                    }
                    SXSSFCell celli0 = row.createCell(0);
                    celli0.setCellValue(deptName);
                    celli0.setCellStyle(cellStyle2);
                    SXSSFCell celli1 = row.createCell(1);
                    celli1.setCellValue(prescriptionNum);
                    celli1.setCellStyle(cellStyle2);
                    SXSSFCell celli2 = row.createCell(2);
                    celli2.setCellValue(preSZOrXBZNum);
                    celli2.setCellStyle(cellStyle2);
                    SXSSFCell celli3 = row.createCell(3);
                    celli3.setCellValue(peopleUseNum);
                    celli3.setCellStyle(cellStyle2);
                    SXSSFCell celli4 = row.createCell(4);
                    celli4.setCellValue(peopleNonUseNum);
                    celli4.setCellStyle(cellStyle2);
                    SXSSFCell celli5 = row.createCell(5);
                    celli5.setCellValue(cMedicineIncome);
                    celli5.setCellStyle(cellStyle2);
                    SXSSFCell celli6 = row.createCell(6);
                    celli6.setCellValue(cMedicinePiecesIncome);
                    celli6.setCellStyle(cellStyle2);
                }
            }
            getExportedFile(workbook, fileName.toString(), response);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    /**
     * 国考指标分析数据导出（科室目标统计）
     *
     * @param tAnalysisNationalIndexVos
     * @param response
     * @param startTimeStr
     * @param endTimeStr
     */
    public void getDeptGoalPro(List<TAnalysisNationalIndexVo> tAnalysisNationalIndexVos,
                               HttpServletResponse response,
                               String startTimeStr, String endTimeStr) {
        //获取当前时间的年份及月份
        Calendar cal = Calendar.getInstance();
        int year = cal.get(Calendar.YEAR);
        int month = cal.get(Calendar.MONTH);

        //Excel标题
        StringBuilder fileName = new StringBuilder();
        if (StringUtils.isNotBlank(startTimeStr)) {
            Date startTime = DateUtil.getDateFormatd(startTimeStr, DateUtil.YYYY_MM_DD);
            fileName.append(DateUtil.getDateFormats(DateUtil.YYYY_MM_DD, startTime));
        } else {
            fileName.append((year + "") + "-0" + (month + 1 + "") + "-01");
        }

        if (StringUtils.isNotBlank(endTimeStr)) {
            Date endTime = DateUtil.getDateFormatd(endTimeStr, DateUtil.YYYY_MM_DD);
            fileName.append("~" + DateUtil.getDateFormats(DateUtil.YYYY_MM_DD, endTime));
        } else {
            fileName.append("~" + DateUtil.getDateFormats(DateUtil.YYYY_MM_DD, new Date()));
        }
        fileName.append(" 国考指标统计（按照科室目标统计）");

        //声明一个工作薄
        SXSSFWorkbook workbook = new SXSSFWorkbook();
        //生成一个表格
        SXSSFSheet sheet = workbook.createSheet(fileName.toString());
        //设置表格默认列宽度为15个字节
        sheet.setDefaultColumnWidth((short) 20);

        //产生表格标题行
        SXSSFRow row = sheet.createRow(0);//行
        SXSSFCell cell1 = row.createCell(0);//创建一列
        XSSFRichTextString text1 = new XSSFRichTextString(fileName.toString());//第一行标题
        cell1.setCellValue(text1);

        //为标题添加样式
        CellStyle cellStyle = workbook.createCellStyle();
        Font fontStyle = workbook.createFont();//字体样式
        fontStyle.setFontName("黑体");//字体
        fontStyle.setFontHeightInPoints((short) 12); //大小
        cellStyle.setAlignment(HorizontalAlignment.CENTER);//居中
        cellStyle.setFont(fontStyle);
        cell1.setCellStyle(cellStyle);

        //合并单元格
        CellRangeAddress cra = new CellRangeAddress(0, 0, 0, 12);
        sheet.addMergedRegion(cra);

        //给表格内容创建样式
        CellStyle cellStyle2 = workbook.createCellStyle();
        Font fontStyle2 = workbook.createFont();//字体样式
        fontStyle2.setFontName("宋体");//字体
        fontStyle2.setFontHeightInPoints((short) 12);//大小
        cellStyle2.setAlignment(HorizontalAlignment.CENTER);//居中
        cellStyle2.setFont(fontStyle2);

        //第2行,第0列
        SXSSFRow row2 = sheet.createRow(1);
        SXSSFCell cell20 = row2.createCell(0);//创建一列
        XSSFRichTextString text20 = new XSSFRichTextString("科室");
        cell20.setCellValue(text20);
        cell20.setCellStyle(cellStyle2);

        //第2行,第1列
        SXSSFCell cell21 = row2.createCell(1);//创建一列
        XSSFRichTextString text21 = new XSSFRichTextString("门诊中药处方比例");
        cell21.setCellValue(text21);
        cell21.setCellStyle(cellStyle2);

        //第2行,第2列
        SXSSFCell cell22 = row2.createCell(3);//创建一列
        XSSFRichTextString text22 = new XSSFRichTextString("门诊散装中药饮片和小包装中药饮片处方比例");
        cell22.setCellValue(text22);
        cell22.setCellStyle(cellStyle2);

        //第2行,第3列
        SXSSFCell cell23 = row2.createCell(5);//创建一列
        XSSFRichTextString text23 = new XSSFRichTextString("门诊患者中药饮片使用率");
        cell23.setCellValue(text23);
        cell23.setCellStyle(cellStyle2);

        //第2行,第4列
        SXSSFCell cell24 = row2.createCell(7);//创建一列
        XSSFRichTextString text24 = new XSSFRichTextString("门诊患者使用中医非药物疗法比例");
        cell24.setCellValue(text24);
        cell24.setCellStyle(cellStyle2);

        //第2行,第5列
        SXSSFCell cell25 = row2.createCell(9);//创建一列
        XSSFRichTextString text25 = new XSSFRichTextString("中药收入占药品收入比例");
        cell25.setCellValue(text25);
        cell25.setCellStyle(cellStyle2);

        //第2行,第6列
        SXSSFCell cell26 = row2.createCell(11);//创建一列
        XSSFRichTextString text26 = new XSSFRichTextString("中药饮片收入占药品收入比例");
        cell26.setCellValue(text26);
        cell26.setCellStyle(cellStyle2);

        //合并单元格
        CellRangeAddress cra21 = new CellRangeAddress(1, 1, 1, 2);
        sheet.addMergedRegion(cra21);
        //合并单元格
        CellRangeAddress cra22 = new CellRangeAddress(1, 1, 3, 4);
        sheet.addMergedRegion(cra22);
        //合并单元格
        CellRangeAddress cra23 = new CellRangeAddress(1, 1, 5, 6);
        sheet.addMergedRegion(cra23);
        //合并单元格
        CellRangeAddress cra24 = new CellRangeAddress(1, 1, 7, 8);
        sheet.addMergedRegion(cra24);
        //合并单元格
        CellRangeAddress cra25 = new CellRangeAddress(1, 1, 9, 10);
        sheet.addMergedRegion(cra25);
        //合并单元格
        CellRangeAddress cra26 = new CellRangeAddress(1, 1, 11, 12);
        sheet.addMergedRegion(cra26);

        //创建第三行
        SXSSFRow row3 = sheet.createRow(2);

        //创建第三行单元格
        for (int i = 0; i < 6; i++) {

            int num1 = 2 * i + 1;
            int num2 = 2 * i + 2;

            //第3行,第1列
            SXSSFCell cell3num1 = row3.createCell(num1);//创建一列
            XSSFRichTextString text3num1 = new XSSFRichTextString("目标");
            cell3num1.setCellValue(text3num1);
            cell3num1.setCellStyle(cellStyle2);
            //第3行,第1列
            SXSSFCell cell3num2 = row3.createCell(num2);//创建一列
            XSSFRichTextString text3num2 = new XSSFRichTextString("实际");
            cell3num2.setCellValue(text3num2);
            cell3num2.setCellStyle(cellStyle2);
        }

        //合并单元格
        CellRangeAddress cra20 = new CellRangeAddress(1, 2, 0, 0);
        sheet.addMergedRegion(cra20);

        try {

            if (null != tAnalysisNationalIndexVos && tAnalysisNationalIndexVos.size() > 0) {
                for (int i = 0; i < tAnalysisNationalIndexVos.size(); i++) {
                    row = sheet.createRow(i + 3);
                    String deptName = tAnalysisNationalIndexVos.get(i).getDeptName();
                    String prescriptionNumPro = "";
                    if (null != tAnalysisNationalIndexVos.get(i).getPrescriptionPro()) {
                        prescriptionNumPro = tAnalysisNationalIndexVos.get(i).getPrescriptionPro().toString();
                    }
                    String preSZOrXBZNumPro = "";
                    if (null != tAnalysisNationalIndexVos.get(i).getPreSZOrXBZPro()) {
                        preSZOrXBZNumPro = tAnalysisNationalIndexVos.get(i).getPreSZOrXBZPro().toString();
                    }
                    String peopleUseNumPro = "";
                    if (null != tAnalysisNationalIndexVos.get(i).getPeopleUsePro()) {
                        peopleUseNumPro = tAnalysisNationalIndexVos.get(i).getPeopleUsePro().toString();
                    }
                    String peopleNonUseNumPro = "";
                    if (null != tAnalysisNationalIndexVos.get(i).getPeopleNonUsePro()) {
                        peopleNonUseNumPro = tAnalysisNationalIndexVos.get(i).getPeopleNonUsePro().toString();
                    }
                    String cMedicineIncomePro = "";
                    if (null != tAnalysisNationalIndexVos.get(i).getCMedicineIncomePro()) {
                        cMedicineIncomePro = tAnalysisNationalIndexVos.get(i).getCMedicineIncomePro().toString();
                    }
                    String cMedicinePiecesIncomePro = "";
                    if (null != tAnalysisNationalIndexVos.get(i).getCMedicinePiecesIncomePro()) {
                        cMedicinePiecesIncomePro = tAnalysisNationalIndexVos.get(i).getCMedicinePiecesIncomePro().toString();
                    }
                    String prescriptionNum = "";
                    if (null != tAnalysisNationalIndexVos.get(i).getPrescriptionProportion()) {
                        prescriptionNum = tAnalysisNationalIndexVos.get(i).getPrescriptionProportion().toString();
                    }
                    String preSZOrXBZNum = "";
                    if (null != tAnalysisNationalIndexVos.get(i).getPreSZOrXBZProportion()) {
                        preSZOrXBZNum = tAnalysisNationalIndexVos.get(i).getPreSZOrXBZProportion().toString();
                    }
                    String peopleUseNum = "";
                    if (null != tAnalysisNationalIndexVos.get(i).getPeopleUseProportion()) {
                        peopleUseNum = tAnalysisNationalIndexVos.get(i).getPeopleUseProportion().toString();
                    }
                    String peopleNonUseNum = "";
                    if (null != tAnalysisNationalIndexVos.get(i).getPeopleNonUseProportion()) {
                        peopleNonUseNum = tAnalysisNationalIndexVos.get(i).getPeopleNonUseProportion().toString();
                    }
                    String cMedicineIncome = "";
                    if (null != tAnalysisNationalIndexVos.get(i).getCMedicineIncomeProportion()) {
                        cMedicineIncome = tAnalysisNationalIndexVos.get(i).getCMedicineIncomeProportion().toString();
                    }
                    String cMedicinePiecesIncome = "";
                    if (null != tAnalysisNationalIndexVos.get(i).getCMedicinePiecesIncomeProportion()) {
                        cMedicinePiecesIncome = tAnalysisNationalIndexVos.get(i).getCMedicinePiecesIncomeProportion().toString();
                    }
                    if (StringUtils.isBlank(deptName)) {
                        deptName = "";
                    }
                    SXSSFCell celli0 = row.createCell(0);
                    celli0.setCellValue(deptName);
                    celli0.setCellStyle(cellStyle2);
                    SXSSFCell celli1 = row.createCell(1);
                    celli1.setCellValue(prescriptionNum);
                    celli1.setCellStyle(cellStyle2);
                    SXSSFCell celli2 = row.createCell(2);
                    celli2.setCellValue(prescriptionNumPro);
                    celli2.setCellStyle(cellStyle2);
                    SXSSFCell celli3 = row.createCell(3);
                    celli3.setCellValue(preSZOrXBZNum);
                    celli3.setCellStyle(cellStyle2);
                    SXSSFCell celli4 = row.createCell(4);
                    celli4.setCellValue(preSZOrXBZNumPro);
                    celli4.setCellStyle(cellStyle2);
                    SXSSFCell celli5 = row.createCell(5);
                    celli5.setCellValue(peopleUseNum);
                    celli5.setCellStyle(cellStyle2);
                    SXSSFCell celli6 = row.createCell(6);
                    celli6.setCellValue(peopleUseNumPro);
                    celli6.setCellStyle(cellStyle2);
                    SXSSFCell celli7 = row.createCell(7);
                    celli7.setCellValue(peopleNonUseNum);
                    celli7.setCellStyle(cellStyle2);
                    SXSSFCell celli8 = row.createCell(8);
                    celli8.setCellValue(peopleNonUseNumPro);
                    celli8.setCellStyle(cellStyle2);
                    SXSSFCell celli9 = row.createCell(9);
                    celli9.setCellValue(cMedicineIncome);
                    celli9.setCellStyle(cellStyle2);
                    SXSSFCell celli10 = row.createCell(10);
                    celli10.setCellValue(cMedicineIncomePro);
                    celli10.setCellStyle(cellStyle2);
                    SXSSFCell celli11 = row.createCell(11);
                    celli11.setCellValue(cMedicinePiecesIncome);
                    celli11.setCellStyle(cellStyle2);
                    SXSSFCell celli12 = row.createCell(12);
                    celli12.setCellValue(cMedicinePiecesIncomePro);
                    celli12.setCellStyle(cellStyle2);
                }
            }
            getExportedFile(workbook, fileName.toString(), response);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 国考指标分析数据导出（按医生统计）
     *
     * @param tAnalysisNationalIndexVos
     * @param response
     * @param startTimeStr
     * @param endTimeStr
     */
    public void getDocPro(List<TAnalysisNationalIndexVo> tAnalysisNationalIndexVos,
                          HttpServletResponse response,
                          String startTimeStr, String endTimeStr) {
        //获取当前时间的年份及月份
        Calendar cal = Calendar.getInstance();
        int year = cal.get(Calendar.YEAR);
        int month = cal.get(Calendar.MONTH);

        //Excel标题
        StringBuilder fileName = new StringBuilder();
        if (StringUtils.isNotBlank(startTimeStr)) {
            Date startTime = DateUtil.getDateFormatd(startTimeStr, DateUtil.YYYY_MM_DD);
            fileName.append(DateUtil.getDateFormats(DateUtil.YYYY_MM_DD, startTime));
        } else {
            fileName.append((year + "") + "-0" + (month + 1 + "") + "-01");
        }

        if (StringUtils.isNotBlank(endTimeStr)) {
            Date endTime = DateUtil.getDateFormatd(endTimeStr, DateUtil.YYYY_MM_DD);
            fileName.append("~" + DateUtil.getDateFormats(DateUtil.YYYY_MM_DD, endTime));
        } else {
            fileName.append("~" + DateUtil.getDateFormats(DateUtil.YYYY_MM_DD, new Date()));
        }
        fileName.append(" 国考指标统计（按照科室数量占比统计）");

        //声明一个工作薄
        SXSSFWorkbook workbook = new SXSSFWorkbook();
        //生成一个表格
        SXSSFSheet sheet = workbook.createSheet(fileName.toString());
        //设置表格默认列宽度为15个字节
        sheet.setDefaultColumnWidth((short) 20);

        //产生表格标题行
        SXSSFRow row = sheet.createRow(0);//行
        SXSSFCell cell1 = row.createCell(0);//创建一列
        XSSFRichTextString text1 = new XSSFRichTextString(fileName.toString());//第一行标题
        cell1.setCellValue(text1);

        //为标题添加样式
        CellStyle cellStyle = workbook.createCellStyle();
        Font fontStyle = workbook.createFont();//字体样式
        fontStyle.setFontName("黑体");//字体
        fontStyle.setFontHeightInPoints((short) 12); //大小
        cellStyle.setAlignment(HorizontalAlignment.CENTER);//居中
        cellStyle.setFont(fontStyle);
        cell1.setCellStyle(cellStyle);

        //合并单元格
        CellRangeAddress cra = new CellRangeAddress(0, 0, 0, 7);
        sheet.addMergedRegion(cra);

        //给表格内容创建样式
        CellStyle cellStyle2 = workbook.createCellStyle();
        Font fontStyle2 = workbook.createFont();//字体样式
        fontStyle2.setFontName("宋体");//字体
        fontStyle2.setFontHeightInPoints((short) 12);//大小
        cellStyle2.setAlignment(HorizontalAlignment.CENTER);//居中
        cellStyle2.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle2.setFont(fontStyle2);

        //第2行,第0列
        SXSSFRow row2 = sheet.createRow(1);
        SXSSFCell cell20 = row2.createCell(0);//创建一列
        XSSFRichTextString text20 = new XSSFRichTextString("科室");
        cell20.setCellValue(text20);
        cell20.setCellStyle(cellStyle2);

        //第2行,第1列
        SXSSFCell cell21 = row2.createCell(1);//创建一列
        XSSFRichTextString text21 = new XSSFRichTextString("医生");
        cell21.setCellValue(text21);
        cell21.setCellStyle(cellStyle2);

        //第2行,第1列
        SXSSFCell cell22 = row2.createCell(2);//创建一列
        XSSFRichTextString text22 = new XSSFRichTextString("门诊中药处方比例");
        cell22.setCellValue(text22);
        cell22.setCellStyle(cellStyle2);

        //第2行,第2列
        SXSSFCell cell23 = row2.createCell(3);//创建一列
        XSSFRichTextString text23 = new XSSFRichTextString("门诊散装中药饮片和小包装中药饮片处方比例");
        cell23.setCellValue(text23);
        cell23.setCellStyle(cellStyle2);

        //第2行,第3列
        SXSSFCell cell24 = row2.createCell(4);//创建一列
        XSSFRichTextString text24 = new XSSFRichTextString("门诊患者中药饮片使用率");
        cell24.setCellValue(text24);
        cell24.setCellStyle(cellStyle2);

        //第2行,第4列
        SXSSFCell cell25 = row2.createCell(5);//创建一列
        XSSFRichTextString text25 = new XSSFRichTextString("门诊患者使用中医非药物疗法比例");
        cell25.setCellValue(text25);
        cell25.setCellStyle(cellStyle2);

        //第2行,第5列
        SXSSFCell cell26 = row2.createCell(6);//创建一列
        XSSFRichTextString text26 = new XSSFRichTextString("中药收入占药品收入比例");
        cell26.setCellValue(text26);
        cell26.setCellStyle(cellStyle2);

        //第2行,第6列
        SXSSFCell cell27 = row2.createCell(7);//创建一列
        XSSFRichTextString text27 = new XSSFRichTextString("中药饮片收入占药品收入比例");
        cell27.setCellValue(text27);
        cell27.setCellStyle(cellStyle2);

        //合并单元格
        CellRangeAddress cra20 = new CellRangeAddress(1, 1, 0, 1);
        sheet.addMergedRegion(cra20);

        try {

            if (null != tAnalysisNationalIndexVos && tAnalysisNationalIndexVos.size() > 0) {
                for (int i = 0; i < tAnalysisNationalIndexVos.size(); i++) {
                    int num = 0;
                    for (int k = 0; k < i; k++) {
                        num = num + tAnalysisNationalIndexVos.get(k).getList().size();
                    }
                    row = sheet.createRow(num + 2);
                    String deptName = tAnalysisNationalIndexVos.get(i).getDeptName();
                    if (StringUtils.isBlank(deptName)) {
                        deptName = "";
                    }
                    SXSSFCell celli0 = row.createCell(0);
                    celli0.setCellValue(deptName);
                    celli0.setCellStyle(cellStyle2);


                    List<TAnalysisNationalIndexVo> list =
                            tAnalysisNationalIndexVos.get(i).getList();

                    if (StringUtils.isNotBlank(deptName) && "全院".equals(deptName)) {
                        if (null != list && list.size() > 0) {
                            for (int j = 0; j < list.size(); j++) {
                                String prescriptionNumPro = "";
                                if (null != list.get(j).getPrescriptionPro()) {
                                    prescriptionNumPro = list.get(j).getPrescriptionPro().toString();
                                }
                                String preSZOrXBZNumPro = "";
                                if (null != list.get(j).getPreSZOrXBZPro()) {
                                    preSZOrXBZNumPro = list.get(j).getPreSZOrXBZPro().toString();
                                }
                                String peopleUseNumPro = "";
                                if (null != list.get(j).getPeopleUsePro()) {
                                    peopleUseNumPro = list.get(j).getPeopleUsePro().toString();
                                }
                                String peopleNonUseNumPro = "";
                                if (null != list.get(j).getPeopleNonUsePro()) {
                                    peopleNonUseNumPro = list.get(j).getPeopleNonUsePro().toString();
                                }
                                String cMedicineIncomePro = "";
                                if (null != list.get(j).getCMedicineIncomePro()) {
                                    cMedicineIncomePro = list.get(j).getCMedicineIncomePro().toString();
                                }
                                String cMedicinePiecesIncomePro = "";
                                if (null != list.get(j).getCMedicinePiecesIncomePro()) {
                                    cMedicinePiecesIncomePro = list.get(j).getCMedicinePiecesIncomePro().toString();
                                }
                                SXSSFCell celli2 = row.createCell(2);
                                celli2.setCellValue(prescriptionNumPro);
                                celli2.setCellStyle(cellStyle2);
                                SXSSFCell celli3 = row.createCell(3);
                                celli3.setCellValue(preSZOrXBZNumPro);
                                celli3.setCellStyle(cellStyle2);
                                SXSSFCell celli4 = row.createCell(4);
                                celli4.setCellValue(peopleUseNumPro);
                                celli4.setCellStyle(cellStyle2);
                                SXSSFCell celli5 = row.createCell(5);
                                celli5.setCellValue(peopleNonUseNumPro);
                                celli5.setCellStyle(cellStyle2);
                                SXSSFCell celli6 = row.createCell(6);
                                celli6.setCellValue(cMedicineIncomePro);
                                celli6.setCellStyle(cellStyle2);
                                SXSSFCell celli7 = row.createCell(7);
                                celli7.setCellValue(cMedicinePiecesIncomePro);
                                celli7.setCellStyle(cellStyle2);
                            }
                            //合并单元格
                            CellRangeAddress cra99 = new CellRangeAddress((2 + num), (2 + num), 0, 1);
                            sheet.addMergedRegion(cra99);
                        }
                    } else {
                        if (null != list && list.size() > 0) {
                            for (int j = 0; j < list.size(); j++) {
                                String docName = list.get(j).getDoctorName();
                                if (StringUtils.isBlank(docName)) {
                                    docName = "";
                                }
                                String prescriptionNumPro = "";
                                if (null != list.get(j).getPrescriptionPro()) {
                                    prescriptionNumPro = list.get(j).getPrescriptionPro().toString();
                                }
                                String preSZOrXBZNumPro = "";
                                if (null != list.get(j).getPreSZOrXBZPro()) {
                                    preSZOrXBZNumPro = list.get(j).getPreSZOrXBZPro().toString();
                                }
                                String peopleUseNumPro = "";
                                if (null != list.get(j).getPeopleUsePro()) {
                                    peopleUseNumPro = list.get(j).getPeopleUsePro().toString();
                                }
                                String peopleNonUseNumPro = "";
                                if (null != list.get(j).getPeopleNonUsePro()) {
                                    peopleNonUseNumPro = list.get(j).getPeopleNonUsePro().toString();
                                }
                                String cMedicineIncomePro = "";
                                if (null != list.get(j).getCMedicineIncomePro()) {
                                    cMedicineIncomePro = list.get(j).getCMedicineIncomePro().toString();
                                }
                                String cMedicinePiecesIncomePro = "";
                                if (null != list.get(j).getCMedicinePiecesIncomePro()) {
                                    cMedicinePiecesIncomePro = list.get(j).getCMedicinePiecesIncomePro().toString();
                                }
                                if (j <= 0) {
                                    SXSSFCell celli1 = row.createCell(1);
                                    celli1.setCellValue(docName);
                                    celli1.setCellStyle(cellStyle2);
                                    SXSSFCell celli2 = row.createCell(2);
                                    celli2.setCellValue(prescriptionNumPro);
                                    celli2.setCellStyle(cellStyle2);
                                    SXSSFCell celli3 = row.createCell(3);
                                    celli3.setCellValue(preSZOrXBZNumPro);
                                    celli3.setCellStyle(cellStyle2);
                                    SXSSFCell celli4 = row.createCell(4);
                                    celli4.setCellValue(peopleUseNumPro);
                                    celli4.setCellStyle(cellStyle2);
                                    SXSSFCell celli5 = row.createCell(5);
                                    celli5.setCellValue(peopleNonUseNumPro);
                                    celli5.setCellStyle(cellStyle2);
                                    SXSSFCell celli6 = row.createCell(6);
                                    celli6.setCellValue(cMedicineIncomePro);
                                    celli6.setCellStyle(cellStyle2);
                                    SXSSFCell celli7 = row.createCell(7);
                                    celli7.setCellValue(cMedicinePiecesIncomePro);
                                    celli7.setCellStyle(cellStyle2);
                                } else {
                                    row = sheet.createRow(2 + num + j);
                                    SXSSFCell celli1 = row.createCell(1);
                                    celli1.setCellValue(docName);
                                    celli1.setCellStyle(cellStyle2);
                                    SXSSFCell celli2 = row.createCell(2);
                                    celli2.setCellValue(prescriptionNumPro);
                                    celli2.setCellStyle(cellStyle2);
                                    SXSSFCell celli3 = row.createCell(3);
                                    celli3.setCellValue(preSZOrXBZNumPro);
                                    celli3.setCellStyle(cellStyle2);
                                    SXSSFCell celli4 = row.createCell(4);
                                    celli4.setCellValue(peopleUseNumPro);
                                    celli4.setCellStyle(cellStyle2);
                                    SXSSFCell celli5 = row.createCell(5);
                                    celli5.setCellValue(peopleNonUseNumPro);
                                    celli5.setCellStyle(cellStyle2);
                                    SXSSFCell celli6 = row.createCell(6);
                                    celli6.setCellValue(cMedicineIncomePro);
                                    celli6.setCellStyle(cellStyle2);
                                    SXSSFCell celli7 = row.createCell(7);
                                    celli7.setCellValue(cMedicinePiecesIncomePro);
                                    celli7.setCellStyle(cellStyle2);
                                }
                            }
                            if (list.size() > 1) {
                                CellRangeAddress crai0 = new CellRangeAddress((2 + num), (2 + num + list.size() - 1), 0, 0);
                                sheet.addMergedRegion(crai0);
                            }
                        }

                    }
                }
            }
            getExportedFile(workbook, fileName.toString(), response);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void getStatistics(Collection<T> dataset, String fileName, HttpServletResponse response,String parValues) {
        SXSSFWorkbook workbook = new SXSSFWorkbook();
        SXSSFSheet sheet = workbook.createSheet(fileName);
        sheet.setDefaultColumnWidth((short) 20);
        SXSSFRow row = sheet.createRow(0);//行
        SXSSFCell cell1 = row.createCell(0);//创建一列
        XSSFRichTextString text1 = new XSSFRichTextString("系统使用统计");//第1行标题
        cell1.setCellValue(text1);
        //为标题添加格式
        CellStyle cellStyle = workbook.createCellStyle();
        Font fontStyle = workbook.createFont(); // 字体样式
        fontStyle.setFontName("黑体"); // 字体
        fontStyle.setFontHeightInPoints((short) 12); // 大小
        cellStyle.setAlignment(HorizontalAlignment.CENTER); // 创建一个居中格式
        cellStyle.setFont(fontStyle);
        cell1.setCellStyle(cellStyle);

        CellRangeAddress cra =
                new CellRangeAddress(0, 0, 0, ((Constant.BASIC_STRING_ONE.equals(parValues))?7:6)  ); // 起始行, 终止行, 起始列, 终止列
        sheet.addMergedRegion(cra);

        //给表格内容创建样式
        CellStyle cellStyle2 = workbook.createCellStyle();
        Font fontStyle2 = workbook.createFont(); // 字体样式
        fontStyle2.setFontName("宋体"); // 字体
        fontStyle2.setFontHeightInPoints((short) 12); // 大小
        cellStyle2.setAlignment(HorizontalAlignment.CENTER); // 创建一个居中格式
        cellStyle2.setFont(fontStyle2);

        CellStyle cellStyle21 = workbook.createCellStyle();
        Font fontStyle21 = workbook.createFont(); // 字体样式
        fontStyle21.setFontName("宋体"); // 字体
        fontStyle21.setFontHeightInPoints((short) 12); // 大小
        cellStyle21.setAlignment(HorizontalAlignment.LEFT); // 创建一个居中格式
        cellStyle21.setFont(fontStyle21);

        //第2行,第0列
//        SXSSFRow row2 = sheet.createRow(1);
//        SXSSFCell cell20 = row2.createCell(1);//创建二列
//        StringBuilder monthStr = new StringBuilder("统计时间：");
//        //monthStr.append(recTreTimeBegin).append("~").append(recTreTimeEnd);
//        XSSFRichTextString text20 = new XSSFRichTextString(monthStr.toString());
//        cell20.setCellValue(text20);
//        cell20.setCellStyle(cellStyle2);
        //合并
        // 起始行, 终止行, 起始列, 终止列
        //CellRangeAddress cra21 = new CellRangeAddress(1, 1, 1, 10);

        //第3行，第1列
        SXSSFRow row3 = sheet.createRow(1);
        SXSSFCell cell30 = row3.createCell(0);//创建一列
        XSSFRichTextString text30 = new XSSFRichTextString("医院名称");
        cell30.setCellValue(text30);
        cell30.setCellStyle(cellStyle2);

        //第3行，第2列
        SXSSFCell cell31 = row3.createCell(1);//创建一列
        XSSFRichTextString text31 = new XSSFRichTextString("就诊人次");
        cell31.setCellValue(text31);
        cell31.setCellStyle(cellStyle2);

        //第3行，第3列
        SXSSFCell cell32 = row3.createCell(2);//创建一列
        XSSFRichTextString text32 = new XSSFRichTextString("中药饮片处方数");
        cell32.setCellValue(text32);
        cell32.setCellStyle(cellStyle2);

        //第3行，第4列
        SXSSFCell cell33 = row3.createCell(3);//创建一列
        XSSFRichTextString text33 = new XSSFRichTextString("适宜技术方");
        cell33.setCellValue(text33);
        cell33.setCellStyle(cellStyle2);

        //第3行，第5列
        SXSSFCell cell34 = row3.createCell(4);//创建一列
        XSSFRichTextString text34 = new XSSFRichTextString("中医电子病历");
        cell34.setCellValue(text34);
        cell34.setCellStyle(cellStyle2);

        //第3行，第6列
        SXSSFCell cell35 = row3.createCell(5);//创建一列
        XSSFRichTextString text35 = new XSSFRichTextString("体质辨识报告");
        cell35.setCellValue(text35);
        cell35.setCellStyle(cellStyle2);

        //第3行，第7列
        SXSSFCell cell36 = row3.createCell(6);//创建一列
        XSSFRichTextString text36 = new XSSFRichTextString("协定方");
        cell36.setCellValue(text36);
        cell36.setCellStyle(cellStyle2);

        if(Constant.BASIC_STRING_ONE.equals(parValues)){
            SXSSFCell cell37 = row3.createCell(7);//创建一列
            XSSFRichTextString text37 = new XSSFRichTextString("中药饮片处方数CDSS");
            cell37.setCellValue(text37);
            cell37.setCellStyle(cellStyle2);
        }

        try {
            if (null != dataset && !dataset.isEmpty()) {
                Iterator<T> it = dataset.iterator();// 遍历集合数据，产生数据行
                int index = 1;
                while (it.hasNext()) {
                    index++;
                    row = sheet.createRow(index);
                    T t = (T) it.next();
                    // 利用反射，根据javabean属性的先后顺序，动态调用getXxx()方法得到属性值
                    Field[] fields = t.getClass().getDeclaredFields();
                    for (short i = 0; i < fields.length; i++) {
                        SXSSFCell cell = row.createCell(i);
                        Field field = fields[i];
                        String fieldName = field.getName();
//                        if (type == 1) {
//                            String[] split = fieldName.split("cglib_prop_");
//                            fieldName = split[1];
//                        }
                        String getMethodName = "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
                        Class tCls = t.getClass();
                        Method getMethod = tCls.getMethod(getMethodName, new Class[]{});
                        Object value = getMethod.invoke(t, new Object[]{});
                        // 判断值的类型后进行强制类型转换
                        String textValue = null;
                        // 其它数据类型都当作字符串简单处理
                        if (value != null && value != "") {
                            textValue = value.toString();
                        }
                        if (textValue != null) {
                            XSSFRichTextString richString = new XSSFRichTextString(textValue);
                            cell.setCellValue(richString);
                            if (i == 0){
                                cell.setCellStyle(cellStyle21);
                            }else {
                                cell.setCellStyle(cellStyle2);
                            }
                        }
                    }
                }
            }
            getExportedFile(workbook, fileName.toString(), response);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void getStatisticsFunction(List<ChangeStaticsFunction2> dataset, String fileName, HttpServletResponse response, List<SimpleP> function2List) {
        SXSSFWorkbook workbook = new SXSSFWorkbook();
        SXSSFSheet sheet = workbook.createSheet(fileName);
        sheet.setDefaultColumnWidth((short) 20);
        SXSSFRow row = sheet.createRow(0);//行
        SXSSFCell cell1 = row.createCell(0);//创建一列
        XSSFRichTextString text1 = new XSSFRichTextString("系统学习统计");//第1行标题
        cell1.setCellValue(text1);
        //为标题添加格式
        CellStyle cellStyle = workbook.createCellStyle();
        Font fontStyle = workbook.createFont(); // 字体样式
        fontStyle.setFontName("黑体"); // 字体
        fontStyle.setFontHeightInPoints((short) 12); // 大小
        cellStyle.setAlignment(HorizontalAlignment.CENTER); // 创建一个居中格式
        cellStyle.setFont(fontStyle);
        cell1.setCellStyle(cellStyle);
        CellRangeAddress cra =
                new CellRangeAddress(0, 0, 0, function2List.size()); // 起始行, 终止行, 起始列, 终止列
        sheet.addMergedRegion(cra);

        //给表格内容创建样式
        CellStyle cellStyle2 = workbook.createCellStyle();
        Font fontStyle2 = workbook.createFont(); // 字体样式
        fontStyle2.setFontName("宋体"); // 字体
        fontStyle2.setFontHeightInPoints((short) 12); // 大小
        cellStyle2.setAlignment(HorizontalAlignment.CENTER); // 创建一个居中格式
        cellStyle2.setFont(fontStyle2);

        CellStyle cellStyle21 = workbook.createCellStyle();
        Font fontStyle21 = workbook.createFont(); // 字体样式
        fontStyle21.setFontName("宋体"); // 字体
        fontStyle21.setFontHeightInPoints((short) 12); // 大小
        cellStyle21.setAlignment(HorizontalAlignment.LEFT); // 创建一个居中格式
        cellStyle21.setFont(fontStyle21);

        //第2行,第0列
        //       SXSSFRow row2 = sheet.createRow(1);
//        SXSSFCell cell20 = row2.createCell(1);//创建二列
//        StringBuilder monthStr = new StringBuilder("统计时间：");
        //monthStr.append(recTreTimeBegin).append("~").append(recTreTimeEnd);
        //XSSFRichTextString text20 = new XSSFRichTextString(monthStr.toString());
        //cell20.setCellValue(text20);
        //cell20.setCellStyle(cellStyle2);
        //合并
        // 起始行, 终止行, 起始列, 终止列
//        CellRangeAddress cra21 =
//                new CellRangeAddress(1, 1, 1, 10);

        //第3行，第1列
        SXSSFRow row3 = sheet.createRow(1);
        SXSSFCell cell30 = row3.createCell(0);//创建一列
        XSSFRichTextString text30 = new XSSFRichTextString("医院名称");
        cell30.setCellValue(text30);
        cell30.setCellStyle(cellStyle2);

        /**
         * 动态列
         */
        for (int i = 0; i < function2List.size(); i++) {
            SXSSFCell cell = row3.createCell(i+1);//创建一列
            XSSFRichTextString text = new XSSFRichTextString(function2List.get(i).getFunction());
            cell.setCellValue(text);
            cell.setCellStyle(cellStyle2);
        }


        try {
            if (null != dataset && !dataset.isEmpty()) {
//                Iterator<T> it = dataset.iterator();// 遍历集合数据，产生数据行
                int index = 1;
//                while (it.hasNext()) {
//                    index++;
//                    row = sheet.createRow(index);
//                    T t = (T) it.next();
//                    // 利用反射，根据javabean属性的先后顺序，动态调用getXxx()方法得到属性值
//                    Field[] fields = t.getClass().getDeclaredFields();
//                    for (short i = 0; i < fields.length; i++) {
//                        SXSSFCell cell = row.createCell(i);
//                        Field field = fields[i];
//                        String fieldName = field.getName();
////                        if (type == 1) {
////                            String[] split = fieldName.split("cglib_prop_");
////                            fieldName = split[1];
////                        }
//                        String getMethodName = "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
//                        Class tCls = t.getClass();
//                        Method getMethod = tCls.getMethod(getMethodName, new Class[]{});
//                        Object value = getMethod.invoke(t, new Object[]{});
//                        // 判断值的类型后进行强制类型转换
//                        String textValue = null;
//                        // 其它数据类型都当作字符串简单处理
//                        if (value != null && value != "") {
//                            textValue = value.toString();
//                        }
//                        if (textValue != null) {
//                            XSSFRichTextString richString = new XSSFRichTextString(textValue);
//                            cell.setCellValue(richString);
//                            cell.setCellStyle(cellStyle2);
//                        }
//                    }
//                }
                for (int i = 0; i < dataset.size(); i++) {

                    List<TStatisticsFunction2> function2List1 = dataset.get(i).getFunction2List();
                    index++;
                    row = sheet.createRow(index);
                    XSSFRichTextString richString = new XSSFRichTextString(dataset.get(i).getAppName());
                    SXSSFCell cell = row.createCell(0);
                    cell.setCellValue(richString);
                    cell.setCellStyle(cellStyle21);
                    for (int i1 = 0; i1 < function2List1.size(); i1++) {
                        SXSSFCell cell2 = row.createCell(i1+1);
                        XSSFRichTextString richString2 = new XSSFRichTextString(function2List1.get(i1).getUsageTimes().intValue()+"");
                        cell2.setCellValue(richString2);
                        cell2.setCellStyle(cellStyle2);
                    }


                    List<ChangeStaticsFunction2> insList = dataset.get(i).getInsList();
                    for (int i1 = 0; i1 < insList.size(); i1++) {
                        List<TStatisticsFunction2> function2List2 = insList.get(i1).getFunction2List();
                        index++;
                        row = sheet.createRow(index);
                        XSSFRichTextString richString2 = new XSSFRichTextString("  "+insList.get(i1).getInsName());
                        SXSSFCell cell2 = row.createCell(0);
                        cell2.setCellValue(richString2);
                        cell2.setCellStyle(cellStyle21);
                        for (int i12 = 0; i12 < function2List2.size(); i12++) {
                            SXSSFCell cell22 = row.createCell(i12+1);
                            XSSFRichTextString richString22 = new XSSFRichTextString(function2List2.get(i12).getUsageTimes().intValue()+"");
                            cell22.setCellValue(richString22);
                            cell22.setCellStyle(cellStyle2);
                        }
                        List<ChangeStaticsFunction2> insList1 = insList.get(i1).getInsList();
                        for (int i2 = 0; i2 < insList1.size(); i2++) {
                            List<TStatisticsFunction2> function2List3 = insList1.get(i2).getFunction2List();
                            index++;
                            row = sheet.createRow(index);
                            XSSFRichTextString richString22 = new XSSFRichTextString("    "+insList1.get(i2).getInsName());
                            SXSSFCell cell22 = row.createCell(0);
                            cell22.setCellValue(richString22);
                            cell22.setCellStyle(cellStyle21);
                            for (int i12 = 0; i12 < function2List3.size(); i12++) {
                                SXSSFCell cell222 = row.createCell(i12+1);
                                XSSFRichTextString richString222 = new XSSFRichTextString(function2List3.get(i12).getUsageTimes().intValue()+"");
                                cell222.setCellValue(richString222);
                                cell222.setCellStyle(cellStyle2);
                            }
                            List<ChangeStaticsFunction2> insList2 = insList1.get(i2).getInsList();
                            for (int i3 = 0; i3 < insList2.size(); i3++) {
                                List<TStatisticsFunction2> function2List4 = insList2.get(i3).getFunction2List();

                                index++;
                                row = sheet.createRow(index);
                                XSSFRichTextString richString222 = new XSSFRichTextString("      "+insList2.get(i3).getInsName());
                                SXSSFCell cell222 = row.createCell(0);
                                cell222.setCellValue(richString222);
                                cell222.setCellStyle(cellStyle21);
                                for (int i12 = 0; i12 < function2List4.size(); i12++) {
                                    SXSSFCell cell2222 = row.createCell(i12+1);
                                    XSSFRichTextString richString2222 = new XSSFRichTextString(function2List4.get(i12).getUsageTimes().intValue()+"");
                                    cell2222.setCellValue(richString2222);
                                    cell2222.setCellStyle(cellStyle2);
                                }

                            }
                        }
                    }
                }
                index++;
                row = sheet.createRow(index);
                XSSFRichTextString richString = new XSSFRichTextString("合计");
                SXSSFCell cell = row.createCell(0);
                cell.setCellValue(richString);
                cell.setCellStyle(cellStyle2);
                for (int i = 0; i < function2List.size(); i++) {
//                    index++;
//                    row = sheet.createRow(index);
                    XSSFRichTextString richString2 = new XSSFRichTextString(function2List.get(i).getUsageTimes().intValue()+"");
                    SXSSFCell cell2 = row.createCell(i+1);
                    cell2.setCellValue(richString2);
                    cell2.setCellStyle(cellStyle2);
                }

            }
            getExportedFile(workbook, fileName.toString(), response);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}