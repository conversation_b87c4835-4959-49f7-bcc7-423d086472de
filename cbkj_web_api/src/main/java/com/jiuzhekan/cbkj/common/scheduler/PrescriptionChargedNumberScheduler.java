package com.jiuzhekan.cbkj.common.scheduler;

import com.jiuzhekan.cbkj.common.utils.RedisLockUtil;
import com.jiuzhekan.cbkj.service.statistics.TPrescriptionChargedNumberService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * @program: pre_api
 * @description: 统计收费处方数量
 * @author: wangtao
 * @create: 2021-03-16 12:02
 **/
@Service
@Slf4j
public class PrescriptionChargedNumberScheduler {
    @Autowired
    private RedisLockUtil redisLockUtil;
    @Autowired
    TPrescriptionChargedNumberService tPrescriptionChargedNumberService;
    @Scheduled(cron = "  0 10 0 * * ?")
   //@Scheduled(cron = "  50 * * * * ?")
    void schedulerPreNumber() {
        String lockKey = "QUARTZ_SYNCHRONIZED";
        String lockValue = String.valueOf(Thread.currentThread().getId());
        int lockTime = 30 * 60;
        log.info("-------QUARTZ_SYNCHRONIZED------");
        if (redisLockUtil.lock(lockKey, lockValue, lockTime)) {
            log.info("-------QUARTZ_SYNCHRONIZED  BEGAN------");
            System.out.println("凌晨0点50分统计收费处方量定时器启动.... ....");
            //tPrescriptionChargedNumberService.deletePrescriptionChargedNumber();
            tPrescriptionChargedNumberService.insertPrescriptionChargedNumber();
            log.info("-------QUARTZ_SYNCHRONIZED  END------");
        }else {
            log.info("-------定时任务没有启动------");
        }


    }



}
