package com.jiuzhekan.cbkj.mapper.statistics;

import com.jiuzhekan.cbkj.beans.statistics.TStatisticsPrescriptionExpand;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;
import org.springframework.stereotype.Component;

import java.util.List;


@Component
public interface TStatisticsPrescriptionExpandMapper extends BaseMapper<TStatisticsPrescriptionExpand> {

    /**
     * 统计数据
     *
     * @param doctorId
     * @return
     */
    List<TStatisticsPrescriptionExpand> getStatisticsPrescriptionListbyDoctorId(String doctorId);

    /**
     * 获取协定方的医生list
     *
     * @return
     */
    List<TStatisticsPrescriptionExpand> getStatisticsPrescriptionDoctorList();

    /**
     * 删除医生数据
     *
     * @param doctorList
     * @return
     */
    int delStatisticsPrescriptionByDoctorList(List<String> doctorList);


}