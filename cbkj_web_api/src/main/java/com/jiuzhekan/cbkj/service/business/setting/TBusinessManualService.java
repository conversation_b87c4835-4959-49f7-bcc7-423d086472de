package com.jiuzhekan.cbkj.service.business.setting;

import com.jiuzhekan.cbkj.beans.business.record.TBusinessManual;
import com.jiuzhekan.cbkj.beans.business.setting.TBusinessAnnex;
import com.jiuzhekan.cbkj.beans.sysBeans.AdminInfo;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import com.jiuzhekan.cbkj.mapper.business.record.TBusinessManualMapper;
import com.jiuzhekan.cbkj.mapper.business.setting.TBusinessAnnexMapper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Service
public class TBusinessManualService {

    @Autowired
    private TBusinessManualMapper tBusinessManualMapper;
    @Autowired
    private TBusinessAnnexMapper tBusinessAnnexMapper;

    /**
     * 加载数据
     *
     * @param tBusinessManual
     * @return
     */
    public Object getPageDatas(TBusinessManual tBusinessManual) {
        tBusinessManual.setIsDel((byte) 0);
        List<TBusinessManual> list = tBusinessManualMapper.getPageListByObj(tBusinessManual);
        return ResEntity.entity(true, Constant.SUCCESS_DX, list);
    }

    /**
     * 插入新数据
     *
     * @param tBusinessManual
     * @return
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insert(TBusinessManual tBusinessManual) {
        String manualId = IDUtil.getID();
        tBusinessManual.setId(manualId);
        AdminInfo currentHr = AdminUtils.getCurrentHr();
        tBusinessManual.setCreateUserId(currentHr.getId());
        tBusinessManual.setCreateTime(new Date());
        tBusinessManual.setCreateUserName(currentHr.getNameZh());
        if (null == tBusinessManual.getManualState()) {
            tBusinessManual.setManualState((byte) 0);
        }
        tBusinessManual.setManualDownTimes(0);
        tBusinessManual.setIsDel((byte) 0);
        long rows = tBusinessManualMapper.insert(tBusinessManual);
        if (rows <= 0) {
            return new ResEntity(false, "保存失败，数据库异常！！", null);
        }
        List<TBusinessAnnex> annexList = tBusinessManual.getAnnexList();
        if (!annexList.isEmpty()) {
            for (TBusinessAnnex tBusinessAnnex : annexList) {
                tBusinessAnnex.setId(IDUtil.getID());
                tBusinessAnnex.setAnnexForeignId(manualId);
                tBusinessAnnex.setAnnexType((byte) 2);
                tBusinessAnnex.setIsDel((byte) 0);
            }
            tBusinessAnnexMapper.insertList(annexList);
        }
        return ResEntity.entity(true, Constant.SUCCESS_DX, tBusinessManual);
    }


    /**
     * 修改
     *
     * @param tBusinessManual
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity update(TBusinessManual tBusinessManual) {
        String manualId = tBusinessManual.getId();
        if (StringUtils.isBlank(manualId)) {
            return ResEntity.entity(false, "操作手册修改失败,操作手册id为空", tBusinessManual);
        }
        long rows = tBusinessManualMapper.updateByPrimaryKey(tBusinessManual);
        // 先作废掉所有附件数据
        TBusinessAnnex annex = new TBusinessAnnex();
        annex.setAnnexForeignId(manualId);
        annex.setIsDel((byte) 1);
        int row = tBusinessAnnexMapper.updateByAnnex(annex);
        List<TBusinessAnnex> annexList = tBusinessManual.getAnnexList();
        if (null != annexList && !annexList.isEmpty()) {
            for (TBusinessAnnex tBusinessAnnex : annexList) {
                if (StringUtils.isBlank(tBusinessAnnex.getId())) {
                    tBusinessAnnex.setId(IDUtil.getID());
                }
                if (StringUtils.isBlank(tBusinessAnnex.getAnnexForeignId())) {
                    tBusinessAnnex.setAnnexForeignId(manualId);
                }
                if (null == tBusinessAnnex.getIsDel()) {
                    // 可以传作废标志来
                    tBusinessAnnex.setIsDel((byte) 0);
                }
            }
            tBusinessAnnexMapper.insertUpdateAnnex(annexList);
        }
        return ResEntity.entity(true, Constant.SUCCESS_DX, null);
    }

    /**
     * 加载某条数据
     *
     * @param id
     * @return
     */
    public ResEntity findObj(String id) {

        if (StringUtils.isBlank(id)) {
            return new ResEntity(false, "参数不能为空哦", null);
        }
        TBusinessManual tBusinessManual = tBusinessManualMapper.getObjectById(id);
        if (null == tBusinessManual) {
            return new ResEntity(false, "传入的id在数据库找不到!", id);
        }
        TBusinessAnnex tBusinessAnnex = new TBusinessAnnex();
        tBusinessAnnex.setAnnexForeignId(id);
        List<TBusinessAnnex> annexList = tBusinessAnnexMapper.getPageListByObj(tBusinessAnnex);
        tBusinessManual.setAnnexList(annexList);
        return new ResEntity(true, Constant.SUCCESS_DX, tBusinessManual);
    }

    /**
     * 删除
     *
     * @param ids
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity deleteLis(String ids) {
        if (StringUtils.isBlank(ids)) {
            return new ResEntity(false, "参数错误(缺少参数)！", null);
        }
        List<String> split = Arrays.asList(ids.split(","));
        TBusinessManual tBusinessManual = new TBusinessManual();
        tBusinessManual.setDeleteTime(new Date());
        tBusinessManual.setDeleteUserId(AdminUtils.getCurrentHr().getId());
        tBusinessManual.setIsDel((byte) 1);
        long rowsR = tBusinessManualMapper.deleteByManualList(tBusinessManual, split);
        if (rowsR != split.size()) {
            // 似乎有点多余啊
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return new ResEntity(false, "删除失败(数据库异常)！", ids);
        }
        tBusinessAnnexMapper.deleteByForeignIds(split);
        return new ResEntity(true, Constant.SUCCESS_DX, rowsR);
    }

    /**
     * @param tBusinessManual :
     * @return : java.lang.Object
     * @Description : 帮助手册附件查询
     * <AUTHOR> xhq
     * @updateTime : 2020/2/28 10:17
     */
    public Object getManualAnnexS(TBusinessManual tBusinessManual) {
        List<TBusinessAnnex> list = tBusinessAnnexMapper.getManualAnnexS(tBusinessManual);
        return ResEntity.entity(true, Constant.SUCCESS_DX, list);
    }

    /**
     * @return :
     * @Description : 禁用/启用操作手册
     * <AUTHOR> xhq
     * @updateTime : 2020/2/28 11:05
     */
    public ResEntity disableOrEnable(String id, String manualType) {
        if (StringUtils.isBlank(id) || StringUtils.isBlank(manualType)) {
            return ResEntity.entity(false, "有参数为空", "id=" + id + " & manualType=" + manualType);
        }
        TBusinessManual tBusinessManual = new TBusinessManual();
        tBusinessManual.setId(id);
        tBusinessManual.setUpdateUserId(AdminUtils.getCurrentHr().getId());
        tBusinessManual.setUpdateTime(new Date());
        tBusinessManual.setManualState(Byte.parseByte(manualType));
        int i = tBusinessManualMapper.updateByPrimaryKey(tBusinessManual);
        return ResEntity.entity(true, Constant.SUCCESS_DX, i);
    }

    @Value("${file.address}")
    private String localtion;

    @Value("${root.preview}")
    private String preview;

    /**
     * @param fileUrl  :
     * @param id
     * @param response :
     * @return : java.lang.Object
     * @Description : 下载帮助手册,并修改下载次数
     * <AUTHOR> guowei
     * @updateTime : 2020/3/3 17:01
     */
    public Object getFileByUrl(String fileUrl, String id, HttpServletResponse response) {
        TBusinessAnnex tBusinessAnnex = new TBusinessAnnex();
        tBusinessAnnex.setAnnexPath(fileUrl);
        tBusinessAnnex = tBusinessAnnexMapper.getBusinessAnnexByUrl(tBusinessAnnex);

        if (tBusinessAnnex == null) {
            return ResEntity.entity(false, "无此记录!", fileUrl);
        }

        if (fileUrl.startsWith(preview)) {
            fileUrl = fileUrl.replace(preview, localtion);
        }

        File file = new File(fileUrl);
        if (file.exists()) {
            try {
                //为文件重新设置名字，采用数据库内存储的文件名称
                String fileName = tBusinessAnnex.getAnnexName();
                fileName = new String(fileName.getBytes("UTF-8"), "ISO_8859_1");

//                response.setContentType("multipart/form-data");
                if (StringUtils.isNotBlank(tBusinessAnnex.getAnnexSuffixName())) {
                    response.setContentType(tBusinessAnnex.getAnnexSuffixName());
                } else {
                    response.setContentType("application/octet-stream");
                }
                response.setHeader("Content-Length", file.length() + "");
                response.setHeader("Content-Disposition", "attachment;filename=" + fileName);

                BufferedInputStream br = new BufferedInputStream(new FileInputStream(file));
                OutputStream os = response.getOutputStream();
                byte[] b = new byte[1024];
                while (br.read(b) != -1) {
                    os.write(b);
                }
                br.close();
                os.flush();
                os.close();

                // 更新帮助手册的下载次数
                if (StringUtils.isNotBlank(id)) {
                    long uRow = tBusinessManualMapper.updateDownTimes(id);
                }
                return null;

            } catch (IOException e) {
                e.printStackTrace();
                return ResEntity.entity(false, "下载帮助手册失败!", fileUrl);
            }
        } else {
            return ResEntity.entity(false, "找不到该文件!", fileUrl);
        }
    }
}