package com.jiuzhekan.cbkj.common.interceptor.tianan;

import com.alibaba.fastjson.JSONObject;
import com.jiuzhekan.cbkj.common.interceptor.SecretStrategy;
import com.jiuzhekan.cbkj.common.utils.Base64OfSunMisc;
import com.jiuzhekan.cbkj.common.utils.HmacSHA1;
import com.jiuzhekan.cbkj.common.utils.SkipHttpsUtil;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @program: pre_api
 * @description: 第三方加解密
 * @author: wangtao
 * @create: 2021-04-07 14:26
 **/
@Component(value = "tianan")
public class TianAnUtils implements SecretStrategy {
    private static String ip;
    @Value(value="${tianan.encrypt.url:}")
    public void setIp(String saveIp){
        ip = saveIp;
    }

    public static String post(String url,String params){

        String string = "";
        try {
            CloseableHttpClient httpClient = (CloseableHttpClient) SkipHttpsUtil.wrapClient();
            RequestConfig requestConfig = RequestConfig.custom().setConnectionRequestTimeout(2000)
                    .setSocketTimeout(3000).setConnectTimeout(2000).build();
            HttpPost httpPost = new HttpPost(url);
            httpPost.setConfig(requestConfig);
            httpPost.addHeader("Content-Type", "application/json; charset=UTF-8");
            httpPost.addHeader("Accept", "application/json; charset=UTF-8");
            httpPost.addHeader("Accept-Encoding", "gzip, deflate, br");
            httpPost.addHeader("appId", "3b4a62fb-afcd-48e1-a948-6b7171c84481");
            httpPost.addHeader("ak", "AKCACBAEBJBFCACGWWWS");
            httpPost.addHeader("digest", "1");
            String hmacSHA1 = HmacSHA1.getHmacSign(params, "UTF-8", "UWC52A1O88Q0EWRX", "HmacSHA1");
            httpPost.addHeader("mac",hmacSHA1);
            StringEntity s = new StringEntity(params,"UTF-8");
            s.setContentEncoding("UTF-8");
            s.setContentType("application/json");
            httpPost.setEntity(s);
            CloseableHttpResponse response = httpClient.execute(httpPost);
            HttpEntity entity = response.getEntity();
            string = EntityUtils.toString(entity, "UTF-8");

        } catch (Exception e) {
            e.printStackTrace();
        }
        return string;
    }

    /**
     * @program: pre_api
     * @description: 加密接口
     * @author: wangtao
     * @create: 2021-04-07 14:26
     **/
    @Override
    public String encrypt(String input) {

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("keyArgType", 1);
        jsonObject.put("keyIndex", "c7264132-7490-4ada-8e65-2f06f1ba2d59");
        //  if (input.length()>16){jsonObject.put("transformation","ECB/NoPadding");}else {
        jsonObject.put("transformation", "ECB/PKCS7Padding");
        // }
        jsonObject.put("input", Base64OfSunMisc.toBase64(input));
        String URL = ip + "/cipher/encrypt";
        String postResult = post(URL, jsonObject.toJSONString());
        jsonObject = JSONObject.parseObject(postResult);
        if (jsonObject == null || "".equals(jsonObject.toString())) {
            return null;
        } else {
            if ("success".equals(jsonObject.get("message"))) {
                return jsonObject.get("data").toString();
            } else {
                return null;
            }
        }
    }
    /**
     * @program: pre_api
     * @description: 解密接口
     * @author: wangtao
     * @create: 2021-04-07 14:26
     **/
    @Override
    public  String decrypt(String input) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("keyArgType", 1);
        jsonObject.put("keyIndex", "c7264132-7490-4ada-8e65-2f06f1ba2d59");
        // if (input.length()>16){ jsonObject.put("transformation","ECB/NoPadding"); }else {
        jsonObject.put("transformation", "ECB/PKCS7Padding");
        //  }
        jsonObject.put("input", input);
        String URL = ip + "/cipher/decrypt";
        String postResult = post(URL, jsonObject.toJSONString());
        jsonObject = JSONObject.parseObject(postResult);
        String result;
        if (jsonObject == null || "".equals(jsonObject.toString())) {
            return null;
        } else {
            if ("success".equals(jsonObject.get("message"))) {
                JSONObject data = (JSONObject) jsonObject.get("data");
                if (data!=null){
                    result = data.get("plaintext").toString();
                }else {
                    return null;
                }
                if (result ==null || "".equals(data)){
                    return null;
                }else {
                    return Base64OfSunMisc.fromBase64(result);
                }
            } else {
                return null;
            }
        }
    }

}
