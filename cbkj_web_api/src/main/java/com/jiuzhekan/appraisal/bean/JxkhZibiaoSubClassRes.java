package com.jiuzhekan.appraisal.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
@ApiModel
public class JxkhZibiaoSubClassRes implements Serializable {

    @ApiModelProperty(value = "科室名称")
    private String keshidmmc;

    @ApiModelProperty(value = "科室代码")
    private String keShidm;
    @ApiModelProperty(value = "机构名称")
    private String jigoudmmc;

    @ApiModelProperty(value = "机构代码")
    private String jigoudm;

    @ApiModelProperty(value = "中间指标代码")
    private String zibiaodm;

    @ApiModelProperty(value = "中间指标名称")
    private String zibiaomc;

    @ApiModelProperty(value = "计量单位")
    private String jiliangdw;

    @ApiModelProperty(value = "指标说明")
    private String zibiaosm;

    @ApiModelProperty(value = "计算方法")
    private String jisuanff;

    @ApiModelProperty(value = "计算公式")
    private String jisuangs;

    @ApiModelProperty(value = "修改时间")
    private Date xiugaisj;

    @ApiModelProperty(value = "0累加指标，1一次性指标（取结束日期最近一条）",hidden = true)
    private Integer zibiaolb;


    @ApiModelProperty(value = "指标值")
    private String jisuangsValue;

}
