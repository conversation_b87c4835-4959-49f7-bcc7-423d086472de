<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.statistics.mapper.board.TStatisticsPrescriptionAuditMapper">

    <resultMap id="BaseResultMap" type="com.jiuzhekan.statistics.beans.board.TStatisticsPrescriptionAudit">
        <id column="id" jdbcType="INTEGER"  property="id" />
        <result column="app_id" jdbcType="VARCHAR" property="appId" />
        <result column="app_name" jdbcType="VARCHAR" property="appName" />
        <result column="ins_code" jdbcType="VARCHAR" property="insCode" />
        <result column="ins_name" jdbcType="VARCHAR" property="insName" />
        <result column="dept_id" jdbcType="VARCHAR" property="deptId" />
        <result column="create_date" jdbcType="DATE" property="createDate" />
        <result column="insert_date" jdbcType="TIMESTAMP" property="insertDate" />
        <result column="tcm_all_num" jdbcType="INTEGER" property="tcmAllNum" />
    </resultMap>


    <sql id="Base_Column_List">
    id,app_id,app_name,ins_code,ins_name,dept_id,create_date,insert_date,tcm_all_num
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.jiuzhekan.statistics.beans.board.TStatisticsPrescriptionAudit">
        delete from t_statistics_prescription_audit where id = #{ id }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from t_statistics_prescription_audit where id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </delete>

    <insert id="insert"  parameterType="com.jiuzhekan.statistics.beans.board.TStatisticsPrescriptionAudit">
        insert into t_statistics_prescription_audit (<include refid="Base_Column_List" />) values
        (#{id},#{appId},#{appName},#{insCode},#{insName},#{deptId},#{createDate},#{insertDate},#{tcmAllNum})
    </insert>

    <insert id="insertList" parameterType="List">
        insert into t_statistics_prescription_audit (<include refid="Base_Column_List" />) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.id},#{item.appId},#{item.appName},#{item.insCode},#{item.insName},#{item.deptId},#{item.createDate},#{item.insertDate},#{item.tcmAllNum})
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.jiuzhekan.statistics.beans.board.TStatisticsPrescriptionAudit">
        update t_statistics_prescription_audit
        <set>
             <if test="appId != null">
                app_id = #{ appId },
             </if>
             <if test="appName != null">
                app_name = #{ appName },
             </if>
             <if test="insCode != null">
                ins_code = #{ insCode },
             </if>
             <if test="insName != null">
                ins_name = #{ insName },
             </if>
             <if test="deptId != null">
                dept_id = #{ deptId },
             </if>
             <if test="createDate != null">
                create_date = #{ createDate },
             </if>
             <if test="insertDate != null">
                insert_date = #{ insertDate },
             </if>
             <if test="tcmAllNum != null">
                tcm_all_num = #{ tcmAllNum },
             </if>
        </set>
        where id = #{ id }
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select <include refid="Base_Column_List" />
        from t_statistics_prescription_audit where id = #{id}
    </select>

    <select id="getPageListByObj" parameterType="com.jiuzhekan.statistics.beans.board.TStatisticsPrescriptionAudit" resultMap="BaseResultMap">
        SELECT id,app_id,app_name,ins_code,ins_name,dept_id,create_date,insert_date,tcm_all_num
        from t_statistics_prescription_audit
        <where>
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>

    <select id="getStaticsResult" parameterType="Map" resultMap="BaseResultMap">
        select b.`id`,a.`APP_ID` app_id ,a.`INS_CODE` ins_code , count(a.`PRE_ID`) as tcm_all_num from `t_prescription` as a
        left join t_statistics_prescription_audit as b on a.`APP_ID`=b.`app_id` and a.`INS_CODE`=b.`ins_code` and b.`create_date`=#{curDate}
        where TIMESTAMPDIFF(DAY,a.`CHECK_TIME`, DATE_ADD(#{curDate},INTERVAL 1 DAY))=#{diff}
          and a.CHECK_TYPE in (0,1,2)
        group by a.`APP_ID` , a.`INS_CODE`
    </select>
    <select id="sumStatistics" resultType="java.lang.Integer">
        select
        SUM(tcm_all_num) as number
        from t_statistics_prescription_audit
        <where>
            <if test=" appId != null and appId!='' ">
                and app_id = #{appId}
            </if>
            <if test=" insCode != null and insCode!='' ">
                and ins_code = #{insCode}
            </if>
            <if test="queryStartDate != null and queryEndDate!= null">
                and create_date &lt;= #{queryEndDate} and create_date >= #{queryStartDate}
            </if>
        </where>
    </select>

</mapper>