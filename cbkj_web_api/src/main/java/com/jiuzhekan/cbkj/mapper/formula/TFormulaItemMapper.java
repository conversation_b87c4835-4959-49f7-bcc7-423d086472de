package com.jiuzhekan.cbkj.mapper.formula;

import com.jiuzhekan.cbkj.beans.formula.TFormulaItem;
import org.springframework.stereotype.Component;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;

import java.util.List;

@Component
public interface TFormulaItemMapper extends BaseMapper<TFormulaItem> {

    /**
     * 根据配方ID获取明细
     *
     * @param formulaId formulaId
     * @return java.util.List<com.jiuzhekan.cbkj.beans.formula.TFormulaItem>
     * <AUTHOR>
     * @date 2021/7/21
     */
    List<TFormulaItem> getListByFormulaId(String formulaId);

    /**
     * 根据配方ID删除明细
     *
     * @param formulaId formulaId
     * <AUTHOR>
     * @date 2021/7/21
     */
    void deleteByFormulaId(String formulaId);
}