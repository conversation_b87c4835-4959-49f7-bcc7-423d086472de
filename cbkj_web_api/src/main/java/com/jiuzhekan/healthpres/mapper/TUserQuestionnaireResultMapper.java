package com.jiuzhekan.healthpres.mapper;

import com.jiuzhekan.healthpres.beans.TUserQuestionnaireResult;
import com.jiuzhekan.healthpres.beans.heathPresControllerVo.*;
import org.springframework.stereotype.Component;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;

import java.util.List;

@Component
public interface TUserQuestionnaireResultMapper extends BaseMapper<TUserQuestionnaireResult>{


    List<GetChildIndexPageList> getChildIndexPageList(ChildIndexPageListVo childIndexPageListVo);

    List<GetDetailPageListResponse> getDetailPageList(GetDetailPageListRequest getDetailPageListRequest);

    List<GetDetailPageListResponse> getReportList(GetReportListRequestVo getReportListRequestVo);

    LookHealthPreMainResponse lookHealthPre(String resultId);

    void updateWordPdf(TUserQuestionnaireResult result);
}