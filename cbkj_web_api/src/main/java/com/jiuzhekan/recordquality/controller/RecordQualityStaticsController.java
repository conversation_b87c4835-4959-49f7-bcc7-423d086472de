package com.jiuzhekan.recordquality.controller;

import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.recordquality.service.RecordQualityStaticsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import com.jiuzhekan.recordquality.beans.*;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/6/26 13:57
 * @Version 1.0
 */
@Api(value = "质量统计管理", tags = "质量统计管理")
@Controller
@RequestMapping("/recordQualityStatics")
public class RecordQualityStaticsController {
    private final RecordQualityStaticsService recordQualityStaticsService;

    public RecordQualityStaticsController(RecordQualityStaticsService recordQualityStaticsService) {
        this.recordQualityStaticsService = recordQualityStaticsService;
    }

    /**
     * 获取质控目标的枚举数据
     */
    @GetMapping("/getQualityTarget")
    @ApiOperation(value = "获取质控目标的枚举数据", response = ResEntity.class)
    @ResponseBody
    public Object getQualityTarget() {
        return ResEntity.success(QualityGoal.getAllQualityGoal());
    }

    /**
     * 获取质控时间点枚举数据
     */
    @GetMapping("/getQualityTimePoint")
    @ApiOperation(value = "获取质控时间点枚举数据", response = ResEntity.class)
    @ResponseBody
    public Object getQualityTimePoint() {
        return ResEntity.success(QualityTimePoint.getAllQualityTimePoint());
    }

    /**
     * 获取质控关键点枚举数据
     */
    @GetMapping("/getQualityEventPoint")
    @ApiOperation(value = "获取质控关键点枚举数据", response = ResEntity.class)
    @ResponseBody
    public Object getQualityEventPoint() {
        return ResEntity.success(QualityEventPoint.getAllQualityEventPoint());
    }

    /**
     * 获取列表
     */
    @GetMapping("/getList")
    @ApiOperation(value = "获取列表", response = TRecordQualityStatistics.class)
    @ResponseBody
    public Object getList(RecordQualityStaticsListReq recordQualityStaticsListReq) {
        return (recordQualityStaticsService.getList(recordQualityStaticsListReq));
    }

    /**
     * 获取单个详情
     */
    @GetMapping("/getOneDetail")
    @ResponseBody
    @ApiOperation(value = "获取单个详情", response = TRecordQualityStatistics.class)
    public Object getOneDetail(Integer recordQualityStatisticsId) {
        return ResEntity.success(recordQualityStaticsService.getOneDetail(recordQualityStatisticsId));
    }

    /**
     * 保存质量评分
     */
    @PostMapping("/saveQualityScore")
    @ResponseBody
    @ApiOperation(value = "保存质量评分", response = TRecordQualityStatistics.class)
    public Object saveQualityScore(@RequestBody TRecordQualityStatistics tRecordQualityStatistics) {
        if (tRecordQualityStatistics.getRecordQualityStatisticsId() == null) {
            return ResEntity.error("请传主键id");
        }
        if (tRecordQualityStatistics.getQualityScore() == null) {
            return ResEntity.error("请填写质量评分");
        }
        recordQualityStaticsService.saveQualityScore(tRecordQualityStatistics);
        return ResEntity.success();
    }

    /**
     * 下载质控分析excel表格
     */
    @GetMapping(value = "/getQualityExcel")
    @ApiOperation(value = "下载质控分析excel表格")
    public void getQualityExcel(RecordQualityStaticsListReq recordQualityStaticsListReq, HttpServletResponse response) {
         recordQualityStaticsService.getQualityExcel(recordQualityStaticsListReq,response);
    }
}
