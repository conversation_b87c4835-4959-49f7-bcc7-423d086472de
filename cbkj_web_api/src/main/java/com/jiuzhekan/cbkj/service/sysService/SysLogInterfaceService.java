package com.jiuzhekan.cbkj.service.sysService;

import com.jiuzhekan.cbkj.beans.sysBeans.SysLogInterface;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.mapper.sysMapper.SysLogInterfaceMapper;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

@Service
public class SysLogInterfaceService {

    @Autowired
    private SysLogInterfaceMapper sysLogInterfaceMapper;

    /**
     * 加载分页数据
     *
     * @param sysLogInterface 接口日志
     * @param page 分页
     * @return Object
     * <AUTHOR>
     * @date 2021-06-21
     */
    public Object getPageDatas(SysLogInterface sysLogInterface, Page page) {

        PageHelper.startPage(page.getPage(), page.getLimit());
        if(StringUtils.isNotBlank(sysLogInterface.getBeginTime())){
            sysLogInterface.setBeginTime(sysLogInterface.getBeginTime()+" 00:00:00");
        }
        if(StringUtils.isNotBlank(sysLogInterface.getEndTime())){
            sysLogInterface.setEndTime(sysLogInterface.getEndTime()+" 23:59:59");
        }
        List<SysLogInterface> list = sysLogInterfaceMapper.getPageListByObj(sysLogInterface);
        return Page.getLayUiTablePageData(list);
    }

    /**
     * 插入新数据
     *
     * @param sysLogInterface 接口日志
     * @return ResEntity
     * <AUTHOR>
     * @date 2021-06-21
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insert(SysLogInterface sysLogInterface){

        sysLogInterface.setId(IDUtil.getID());
        long rows = sysLogInterfaceMapper.insert(sysLogInterface);
        if(rows > 0) {
            return ResEntity.entity(true, Constant.SUCCESS_DX, sysLogInterface);
        }
        return ResEntity.entity(false, "保存失败，数据库异常！！", null);
    }


    /**
     * 修改
     *
     * @param sysLogInterface 接口日志
     * @return ResEntity
     * <AUTHOR>
     * @date 2021-06-21
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity update(SysLogInterface sysLogInterface) {

        long rows = sysLogInterfaceMapper.updateByPrimaryKey(sysLogInterface);
        if(rows >0){
            return ResEntity.entity(true, Constant.SUCCESS_DX, sysLogInterface);
        }
        return ResEntity.entity(false, "修改失败，数据库异常", null);
    }

    /**
     * 加载某条数据
     *
     * @param id 接口日志
     * @return ResEntity
     * <AUTHOR>
     * @date 2021-06-21
     */
    public ResEntity findObj(String id) {

        if(StringUtils.isBlank(id)){
            return ResEntity.entity(false, "参数不能为空哦", null);
        }
        SysLogInterface sysLogInterface = sysLogInterfaceMapper.getObjectById(id);
        return ResEntity.entity(true, Constant.SUCCESS_DX, sysLogInterface);
    }

    /**
     * 删除
     *
     * @param ids ids
     * @return ResEntity
     * <AUTHOR>
     * @date 2021-06-21
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity deleteLis(String ids){
        if(StringUtils.isBlank(ids)){
            return ResEntity.entity(false, "参数错误(缺少参数)！", null);
        }
        long rowsR = sysLogInterfaceMapper.deleteBylist(ids.split(","));

        return ResEntity.entity(true, Constant.SUCCESS_DX, rowsR);
    }

}
