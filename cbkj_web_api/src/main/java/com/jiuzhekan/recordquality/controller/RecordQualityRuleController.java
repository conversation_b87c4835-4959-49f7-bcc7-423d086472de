package com.jiuzhekan.recordquality.controller;

import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.recordquality.beans.RecordQualityListReq;
import com.jiuzhekan.recordquality.beans.TRecordQualityRuleMain;
import com.jiuzhekan.recordquality.beans.TRecordQualityRuleMainSave;
import com.jiuzhekan.recordquality.service.RecordQualityRuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/6/26 13:57
 * @Version 1.0
 */
@Api(value = "质量规则管理", tags = "质量规则管理")
@RestController
@RequestMapping("/recordQualityStatics")
public class RecordQualityRuleController {

    private final RecordQualityRuleService recordQualityRuleService;

    public RecordQualityRuleController(RecordQualityRuleService recordQualityRuleService) {
        this.recordQualityRuleService = recordQualityRuleService;
    }

    /**
     * 获取列表
     *
     * @param recordQualityListReq
     * @return
     */
    @GetMapping("/list")
    @ApiOperation(value = "列表", response = TRecordQualityRuleMain.class)
    public Object list(RecordQualityListReq recordQualityListReq) {
        return recordQualityRuleService.list(recordQualityListReq);
    }

    /**
     * 删除
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除", response = TRecordQualityRuleMain.class)
    public Object delete(String recordQualityRuleId) {
        return recordQualityRuleService.deleteById(recordQualityRuleId);
    }

    /**
     * 获取单个信息
     */
    @GetMapping("/get")
    @ApiOperation(value = "获取单个信息", response = TRecordQualityRuleMain.class)
    public Object get(String recordQualityRuleId) {
        return recordQualityRuleService.getObjectById(recordQualityRuleId);
    }

    /**
     * 修改启用、禁用状态
     */
    @GetMapping("/updateStatus")
    @ApiOperation(value = "修改启用(0)、禁用状态(2)、删除（1）", response = TRecordQualityRuleMain.class)
    public Object updateStatus(String recordQualityRuleId, Integer recordQualityRuleStatus) {
        //判断recordQualityRuleStatus字段的值只能是 0 和 1
        if (recordQualityRuleStatus != 0 && recordQualityRuleStatus != 2) {
            return ResEntity.error("recordQualityRuleStatus 值 不正确");
        }
        return recordQualityRuleService.updateStatus(recordQualityRuleId, recordQualityRuleStatus);
    
    }

    /**
     * 创建规则
     */
    @PostMapping("/create")
    @ApiOperation(value = "创建规则", response = ResEntity.class)
    public Object create(@Valid @RequestBody TRecordQualityRuleMainSave tRecordQualityRuleMainSave) {
//        ResEntity jugmentExist = recordQualityRuleService.jugmentExist(tRecordQualityRuleMainSave);
//        if (!jugmentExist.getStatus()) {
//            return jugmentExist;
//        }
        return recordQualityRuleService.create(tRecordQualityRuleMainSave);
   }

    /**
     * 修改规则
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改规则", response = ResEntity.class)
    public Object update(@Valid @RequestBody TRecordQualityRuleMainSave tRecordQualityRuleMainSave) {
//        ResEntity jugmentExist = recordQualityRuleService.jugmentExist(tRecordQualityRuleMainSave);
//        if (!jugmentExist.getStatus()) {
//            return jugmentExist;
//        }
        return recordQualityRuleService.update(tRecordQualityRuleMainSave);
    }

    /**
     * 获取规则类型
     */
    @GetMapping("/getRuleType")
    @ApiOperation(value = "获取所有规则类型", response = ResEntity.class)
    public Object getRuleType() {
        return ResEntity.success(recordQualityRuleService.getRuleType());
    }

    /**
     * 获取症状列表数据
     */
    @GetMapping("/getSymptomList")
    @ApiOperation(value = "获取所有有症状列表数据", response = ResEntity.class)
    public Object getSymptomList(String name) {
        return ResEntity.success(recordQualityRuleService.getSymptomList(name));
    }
}
