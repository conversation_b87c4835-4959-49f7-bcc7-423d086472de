package com.jiuzhekan.cbkj.service.template;

import com.jiuzhekan.cbkj.beans.business.template.TRecordTemplateDetail;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.mapper.business.template.TRecordTemplateDetailMapper;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class TRecordTemplateDetailService {

    @Autowired
    private TRecordTemplateDetailMapper tRecordTemplateDetailMapper;

    /**
     * 加载分页数据
     * @param tRecordTemplateDetail
     * @param page
     * @return
     */
    public Object getPageDatas(TRecordTemplateDetail tRecordTemplateDetail, Page page) {

        PageHelper.startPage(page.getPage(), page.getLimit());
        List<TRecordTemplateDetail> list = tRecordTemplateDetailMapper.getPageListByObj(tRecordTemplateDetail);
        Object result = Page.getLayUiTablePageData(list);
        return result;
    }

    /**
     * 插入新数据
     * @param tRecordTemplateDetail
     * @return
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insert(TRecordTemplateDetail tRecordTemplateDetail){

        tRecordTemplateDetail.setDetailId(IDUtil.getID());
        long rows = tRecordTemplateDetailMapper.insert(tRecordTemplateDetail);
        if(rows > 0){
            return ResEntity.entity(true, Constant.SUCCESS_DX,tRecordTemplateDetail);
        }
        return new ResEntity(false,"保存失败，数据库异常！！",null);
    }


    /**
     * 修改
     * @param tRecordTemplateDetail
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity update(TRecordTemplateDetail tRecordTemplateDetail) {

        long rows = tRecordTemplateDetailMapper.updateByPrimaryKey(tRecordTemplateDetail);
        if(rows >0){
            return ResEntity.entity(true,Constant.SUCCESS_DX,null);
        }
        return new ResEntity(false,"修改失败，数据库异常",null);
    }

    /**
     * 加载某条数据
     * @param detailId
     * @return
     */
    public ResEntity findObj(String detailId) {

        if(StringUtils.isBlank(detailId)){
            return new ResEntity(false,"参数不能为空哦",null);
        }
        TRecordTemplateDetail tRecordTemplateDetail = tRecordTemplateDetailMapper.getObjectById(detailId);
        return new ResEntity(true,Constant.SUCCESS_DX,tRecordTemplateDetail);
    }

    /**
     * 删除
     * @param ids
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity deleteLis(String ids){
        if(StringUtils.isBlank(ids)){
            return new ResEntity(false,"参数错误(缺少参数)！",null);
        }
        long rowsR = tRecordTemplateDetailMapper.deleteBylist(ids.split(","));

        return new ResEntity(true,Constant.SUCCESS_DX,rowsR);
    }

}
