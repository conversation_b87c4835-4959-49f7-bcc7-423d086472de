package com.jiuzhekan.cbkj.service.common;

import com.jiuzhekan.cbkj.beans.bs.*;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.mapper.bs.BsAreaMapper;
import com.jiuzhekan.cbkj.mapper.bs.BsCityMapper;
import com.jiuzhekan.cbkj.mapper.bs.BsProvinceMapper;
import com.jiuzhekan.cbkj.mapper.bs.BsStreetMapper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class AddressService {

    @Autowired
    private BsProvinceMapper bsProvinceMapper;
    @Autowired
    private BsCityMapper bsCityMapper;
    @Autowired
    private BsAreaMapper bsAreaMapper;
    @Autowired
    private BsStreetMapper bsStreetMapper;


    public ResEntity getProvinceList(BsProvince bsProvince) {
        List<BsProvince> list = bsProvinceMapper.getPageListByObj(bsProvince);
        return ResEntity.entity(true, "", list);
    }

    public ResEntity getCityList(BsCity bsCity) {
        if (StringUtils.isBlank(bsCity.getProvinceCode())) {
            return ResEntity.entity(false, "请选择省！", null);
        }
        List<BsCity> list = bsCityMapper.getPageListByObj(bsCity);
        return ResEntity.entity(true, "", list);
    }

    public ResEntity getAreaList(BsArea bsArea) {
        if (StringUtils.isBlank(bsArea.getCityCode())) {
            return ResEntity.entity(false, "请选择市！", null);
        }
        List<BsArea> list = bsAreaMapper.getPageListByObj(bsArea);
        return ResEntity.entity(true, "", list);
    }

    public ResEntity getStreetList(BsStreet bsStreet) {
        if (StringUtils.isBlank(bsStreet.getAreaCode())) {
            return ResEntity.entity(false, "请选择区！", null);
        }

        List<BsStreet> list = bsStreetMapper.getPageListByObj(bsStreet);
        return ResEntity.entity(true, "", list);
    }


    public Object getBsVillageList(BsVillage bsVillage) {
        //不传返回全部
//        if (StringUtils.isBlank(bsVillage.getStreetCode())) {
//            return ResEntity.entity(false, "请选择街道！", null);
//        }

        List<BsVillage> list = bsStreetMapper.getBsVillageListByObj(bsVillage);
        return ResEntity.entity(true, "", list);
    }
}