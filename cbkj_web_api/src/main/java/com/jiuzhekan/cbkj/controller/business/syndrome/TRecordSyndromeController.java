package com.jiuzhekan.cbkj.controller.business.syndrome;

import com.jiuzhekan.cbkj.beans.business.syndrome.TRecordMaster;
import com.jiuzhekan.cbkj.beans.business.syndrome.TRecordSyndromeFile;
import com.jiuzhekan.cbkj.beans.business.syndrome.TRecordSyndromeGroup;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.common.annotaionUtil.StatisticsFunction;
import com.jiuzhekan.cbkj.service.business.syndrome.TRecordSyndromeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

@Api(value = "智能辩证记录", tags = "智能辩证记录")
@Controller
public class TRecordSyndromeController {

    @Autowired
    private TRecordSyndromeService tRecordSyndromeService;


    @RequestMapping(value = "tRecordSyndrome/save", method = RequestMethod.POST)
    @ApiOperation(value = "保存智能辩证记录", notes = "保存智能辩证记录")
    @LogAnnotation(value = "保存智能辩证记录", isWrite = true)
    @StatisticsFunction(source = "智能辨证", value = "AI辨证次数")
    @ResponseBody
    public Object saveRecordSyndrome(@RequestBody TRecordSyndromeGroup tRecordSyndromeGroup) {
        return tRecordSyndromeService.insert(tRecordSyndromeGroup);
    }

    @RequestMapping(value = "tRecordSyndrome/findObj", method = RequestMethod.GET)
    @ApiOperation(value = "获取智能辩证记录", notes = "根据挂号ID获取智能辩证记录", response = TRecordSyndromeGroup.class)
    @LogAnnotation(value = "根据挂号ID获取智能辩证记录")
    @ResponseBody
    @ApiImplicitParams({
            @ApiImplicitParam(name = "registerId", value = "挂号ID", paramType = "query", required = true),
            @ApiImplicitParam(name = "type", value = "类型1.一件事 2.辩证 4 咳嗽病 5.痛风病 ", paramType = "query",defaultValue = "1", required = false),
            @ApiImplicitParam(name = "groupId", value = "groupId", paramType = "query",defaultValue = "", required = false)
    }
    )
    public Object getRecordSyndrome(String registerId,String type,String groupId) {
        return tRecordSyndromeService.findObj(registerId,type,groupId);
    }

    @RequestMapping(value = "tRecordMaster/save", method = RequestMethod.POST)
    @ApiOperation(value = "保存国医大师辩证记录", notes = "保存国医大师辩证记录")
    @LogAnnotation(value = "保存国医大师辩证记录", isWrite = true)
    @StatisticsFunction(source = "智能辨证", value = "国医大师辨证次数")
    @ResponseBody
    public Object saveRecordMaster(@RequestBody TRecordMaster tRecordMaster) {
        return tRecordSyndromeService.insertMasterResult(tRecordMaster);
    }

    @RequestMapping(value = "/physicalCheck" , method = RequestMethod.GET)
    @LogAnnotation(value = "体质检查调用接口" , isWrite = true)
    @ApiOperation(value = "体质检查调用接口" , notes = "体质检查调用接口")
    @ResponseBody
    public Object physicalCheck(String registerId){
        return tRecordSyndromeService.physicalCheck(registerId);
    }

    @RequestMapping(value = "/deleteCheckResult" , method = RequestMethod.POST)
    @LogAnnotation(value = "体质检查结果路径删除接口" , isWrite = true)
    @ApiOperation(value = "体质检查结果路径删除接口" , notes = "体质检查结果路径删除接口")
    @ResponseBody
    public Object deleteCheckResult(@RequestBody List<TRecordSyndromeFile> tRecordSyndromeFiles){
        return tRecordSyndromeService.deleteCheckResult(tRecordSyndromeFiles);
    }

    @RequestMapping(value = "/getSyndromeFile" , method = RequestMethod.GET)
    @LogAnnotation(value = "体质检查结果查看")
    @ApiOperation(value = "体质检查结果查看" , notes = "体质检查结果查看")
    @ResponseBody
    public Object getSyndromeFile(String registerId, String fileType) throws IOException {
        return tRecordSyndromeService.getSyndromeFile(registerId , fileType);
    }

    @RequestMapping(value = "/downloadResult", method = RequestMethod.GET)
    @LogAnnotation(value = "下载圣美孚体质检查结果")
    @ApiOperation(value = "下载圣美孚体质检查结果", notes = "下载圣美孚体质检查结果")
    @ApiImplicitParams({
            @ApiImplicitParam(value = "挂号ID", name = "registerId", paramType = "query", dataType = "String", required = true)})
    @ResponseBody
    public void downloadSMFResult(HttpServletRequest req , HttpServletResponse reps , String registerId) {
        tRecordSyndromeService.createRecordWord(req , reps , registerId);
    }
}