package com.jiuzhekan.managementTCMSP.beans.user;//package com.jiuzhekan.cbkj.beans.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class SysAdminInfoAssessmentEvaluation implements Serializable{

    @ApiModelProperty(value = "考核测评表主键id")
    private Integer id;

    @ApiModelProperty(value = "")
    private String userName;

    @ApiModelProperty(value = "")
    private String userId;

    @ApiModelProperty(value = "")
    private String appId;

    @ApiModelProperty(value = "")
    private String appName;

    @ApiModelProperty(value = "")
    private String insName;

    @ApiModelProperty(value = "")
    private String insCode;

    @ApiModelProperty(value = "")
    private String deptId;

    @ApiModelProperty(value = "")
    private String deptName;

    @ApiModelProperty(value = "数据插入日期")
    private Date insertTime;

    @ApiModelProperty(value = "1职场/人才测评2.教育测评3.心理测评4.个人成长测评")
    private Integer assessmentEvaluationClass;

    @ApiModelProperty(value = "测评名称")
    private String assessmentEvaluationName;

    @ApiModelProperty(value = "测评分数")
    private Double assessmentEvaluationScore;

    @ApiModelProperty(value = "测试日期")
    private Date assessmentEvaluationTime;

    @ApiModelProperty(value = "")
    private String assessmentEvaluationUserName;

    @ApiModelProperty(value = "")
    private String assessmentEvaluationUserId;

    @ApiModelProperty(value = "测评结论")
    private String assessmentEvaluationComment;

    @ApiModelProperty(value = "测评附件")
    private String certificateAttachment;


}
