package com.jiuzhekan.healthpres.beans.heathPresControllerVo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/5/27 13:53
 * @Version 1.0
 */
@ApiModel
@Data
public class SaveHealthPreModelMainRequest {

    @ApiModelProperty(value = "报告id")
    private String resultId;

    @NotNull(message = "questionnaireMainType不能为空")
    @ApiModelProperty(value = "1.儿童保健2.慢病调理",required  = true)
    private Integer questionnaireMainType;

    @NotBlank(message = "questionnaireMainType不能为空")
    @ApiModelProperty(value = "子分类 1-1(6、12月龄儿童)1-2(18、24月龄儿童)1-3(30、36月龄儿童) 等名儿")
    private String questionnaireMainSecondTypeName;

    @NotBlank(message = "questionnaireMainSecondTypeCode不能为空")
    @ApiModelProperty(value = "子分类 1-1(6、12月龄儿童)1-2(18、24月龄儿童)1-3(30、36月龄儿童) 等代码",required = true)
    private String questionnaireMainSecondTypeCode;

    @NotBlank(message = "patientId不能为空")
    @ApiModelProperty(value = "患者id")
    private String patientId;

    @NotBlank(message = "questionnaireMainId不能为空")
    @ApiModelProperty(value = "模板内容id")
    private String questionnaireMainId;

    @NotBlank(message = "questionnaireMainTitle不能为空")
    @ApiModelProperty(value = "标题")
    private String questionnaireMainTitle;

//    @NotBlank(message = "questionnaireMainFirstLine不能为空")
    @ApiModelProperty(value = "内容首行文本")
    private String questionnaireMainFirstLine;

    //@NotBlank(message = "questionnaireMainTips不能为空")
    @ApiModelProperty(value = "内容第二行提示。")
    private String questionnaireMainTips;

    @NotEmpty(message = "saveQuestionnaireMainItemList不能为空")
    private List<SaveHealthPreModelMainItemRequest> saveQuestionnaireMainItemList;

}
