package com.jiuzhekan.cbkj.service.business.doctor;

import com.github.pagehelper.PageHelper;
import com.jiuzhekan.cbkj.beans.business.doctor.TDoctorBook;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.http.KnowRestTemplate;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.mapper.business.doctor.TDoctorBookMapper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class TDoctorBookService {

    @Autowired
    private TDoctorBookMapper tDoctorBookMapper;
    @Autowired
    private KnowRestTemplate knowRestTemplate;


    /**
     * 首页最近阅读
     *
     * @return java.lang.Object
     * <AUTHOR>
     * @date 2021/10/28
     */
    public Object recentReading() {
        TDoctorBook tDoctorBook = new TDoctorBook();
        tDoctorBook.setDoctorId(AdminUtils.getCurrentHr().getId());
        TDoctorBook recent = tDoctorBookMapper.recentReading(tDoctorBook);

        Map<String, Object> result = new HashMap<>();

        if (recent != null && recent.getId() != null) {
            getBookFromKnow(recent);

            result.put("recentReading", recent);

            if (recent.getBookDetail() != null && recent.getBookDetail() instanceof HashMap) {
                result.put("recommendBooks", ((Map<String, Object>) recent.getBookDetail()).get("recommendBooks"));
            }
        } else {

            ResEntity resEntity = knowRestTemplate.postKnow("classify/book", new HashMap<>());
            if (resEntity.getStatus()) {
                result.put("recommendBooks", resEntity.getData());
            }
        }
        return ResEntity.success(result);
    }


    /**
     * 阅读记录
     *
     * @param tDoctorBook tDoctorBook
     * @param page        page
     * @return java.lang.Object
     * <AUTHOR>
     * @date 2021/10/28
     */
    public Object readRecord(TDoctorBook tDoctorBook, Page page) {
        tDoctorBook.setDoctorId(AdminUtils.getCurrentHr().getId());
        PageHelper.startPage(page.getPage(), page.getLimit());
        List<TDoctorBook> list = tDoctorBookMapper.readRecord(tDoctorBook);
        return Page.getLayUiTablePageData(list);
    }

    /**
     * 我的书架
     *
     * @param tDoctorBook tDoctorBook
     * @param page        page
     * @return java.lang.Object
     * <AUTHOR>
     * @date 2021/10/28
     */
    public Object shelf(TDoctorBook tDoctorBook, Page page) {
        tDoctorBook.setDoctorId(AdminUtils.getCurrentHr().getId());
        PageHelper.startPage(page.getPage(), page.getLimit());
        List<TDoctorBook> list = tDoctorBookMapper.shelf(tDoctorBook);
        return Page.getLayUiTablePageData(list);
    }


    /**
     * 加载某书籍的阅读记录详情
     *
     * @param bookId 书籍阅读记录
     * @return ResEntity
     * <AUTHOR>
     * @date 2021-10-29
     */
    public ResEntity readDetail(String bookId) {

        if (StringUtils.isBlank(bookId)) {
            return ResEntity.entity(false, "书籍ID不能为空哦", null);
        }
        TDoctorBook tDoctorBook = tDoctorBookMapper.readDetail(new TDoctorBook(AdminUtils.getCurrentHr().getId(), bookId));
        return ResEntity.entity(true, Constant.SUCCESS_DX, tDoctorBook);
    }


    /**
     * 阅读书籍
     *
     * @param bookId bookId
     * @return com.jiuzhekan.cbkj.beans.sysBeans.ResEntity
     * <AUTHOR>
     * @date 2021/10/29
     */
    public ResEntity reading(String bookId, String chapterId, String progress) {

        TDoctorBook doctorBook;
        TDoctorBook old = tDoctorBookMapper.readDetail(new TDoctorBook(AdminUtils.getCurrentHr().getId(), bookId));
        if (old == null) {
            doctorBook = new TDoctorBook(AdminUtils.getCurrentHr().getId(), bookId);
            doctorBook.setDoctorName(AdminUtils.getCurrentHr().getNameZh());
            getBookFromKnow(doctorBook);
            doctorBook.setChapterId(chapterId);
            doctorBook.setProgress(progress);
            doctorBook.setRate(new BigDecimal("0.01"));
            doctorBook.setCreateDate(new Date());
            doctorBook.setUpdateDate(new Date());
            tDoctorBookMapper.insert(doctorBook);
        } else {
            doctorBook = new TDoctorBook();
            doctorBook.setId(old.getId());
            doctorBook.setChapterId(chapterId);
            doctorBook.setProgress(progress);
            doctorBook.setRate(new BigDecimal("0.01"));
            doctorBook.setUpdateDate(new Date());
            tDoctorBookMapper.updateByPrimaryKey(doctorBook);
        }

        return ResEntity.success(doctorBook);
    }

    /**
     * 加入书架
     *
     * @param bookId bookId
     * @return com.jiuzhekan.cbkj.beans.sysBeans.ResEntity
     * <AUTHOR>
     * @date 2021/11/1
     */
    public ResEntity addShelf(String bookId) {

        TDoctorBook doctorBook;
        TDoctorBook old = tDoctorBookMapper.readDetail(new TDoctorBook(AdminUtils.getCurrentHr().getId(), bookId));

        if (old == null) {

            doctorBook = new TDoctorBook(AdminUtils.getCurrentHr().getId(), bookId);
            doctorBook.setDoctorName(AdminUtils.getCurrentHr().getNameZh());
            getBookFromKnow(doctorBook);
            doctorBook.setShelf(1);
            doctorBook.setCreateDate(new Date());
            tDoctorBookMapper.insert(doctorBook);

        } else {

            if (old.getShelf() != null && old.getShelf() == 1) {
                return ResEntity.error("已加入书架");
            }

            doctorBook = new TDoctorBook();
            doctorBook.setId(old.getId());
            doctorBook.setShelf(1);
            doctorBook.setUpdateDate(new Date());
            tDoctorBookMapper.updateByPrimaryKey(doctorBook);
        }

        return ResEntity.success(old);
    }

    /**
     * 移出书架
     *
     * @param bookId bookId
     * @return com.jiuzhekan.cbkj.beans.sysBeans.ResEntity
     * <AUTHOR>
     * @date 2021/11/1
     */
    public ResEntity delShelf(String bookId) {

        TDoctorBook doctorBook;
        TDoctorBook old = tDoctorBookMapper.readDetail(new TDoctorBook(AdminUtils.getCurrentHr().getId(), bookId));

        if (old != null) {

            if (old.getShelf() != null && old.getShelf() == 0) {
                return ResEntity.error("已移出书架");
            }

            doctorBook = new TDoctorBook();
            doctorBook.setId(old.getId());
            doctorBook.setShelf(0);
            doctorBook.setUpdateDate(new Date());
            tDoctorBookMapper.updateByPrimaryKey(doctorBook);
        }

        return ResEntity.success(old);
    }


    private void getBookFromKnow(TDoctorBook book) {
        if (book == null || StringUtils.isBlank(book.getBookId())) {
            return;
        }

        Map<String, Object> map = new HashMap<>();
        map.put("bookId", book.getBookId());
        ResEntity resEntity = knowRestTemplate.postKnow("classify/book", map);
        if (resEntity.getStatus()) {
            Map<String, Object> bookDetail = (Map<String, Object>) resEntity.getData();
            String bookName = (String) bookDetail.getOrDefault("bookName", "");
            String author = (String) bookDetail.getOrDefault("author", "");

            book.setBookName(bookName);
            book.setAuthor(author);
            book.setBookDetail(bookDetail);
        }
    }
}
