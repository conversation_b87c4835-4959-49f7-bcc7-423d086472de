package com.jiuzhekan.cbkj.service.template;

import com.github.pagehelper.PageHelper;
import com.jiuzhekan.cbkj.beans.business.doctor.TDoctorNote;
import com.jiuzhekan.cbkj.beans.business.template.TRecordTemplate;
import com.jiuzhekan.cbkj.beans.business.template.TRecordTemplateDetail;
import com.jiuzhekan.cbkj.beans.sysBeans.AdminInfo;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.beans.sysExt.SysAdminInfoex;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.common.utils.IDUtil;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.mapper.business.template.TRecordTemplateDetailMapper;
import com.jiuzhekan.cbkj.mapper.business.template.TRecordTemplateMapper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class TRecordTemplateService {

    @Autowired
    private TRecordTemplateMapper tRecordTemplateMapper;

    @Autowired
    private TRecordTemplateDetailMapper tRecordTemplateDetailMapper;


    public Object getShareType(){
        List<SysAdminInfoex> list = new ArrayList<>();
//        for (SysAdminInfoex infoex : AdminUtils.getCurrentHr().getSysAdminInfoexs()) {
//            //4病历模板分享权限
//            if (infoex.getUserExType() != null && infoex.getUserExType() == 4) {
//                list.add(infoex);
//            }
//        }

        String share = AdminUtils.getCurrentHr().getAdminInfoEx().getDescriptionShare();

        if (StringUtils.isNotBlank(share)) {
            for (String s : share.split(Constant.ENGLISH_COMMA)) {
                list.add(new SysAdminInfoex(s, Constant.TEMPLATE_SHARE_TEXT[Integer.parseInt(s)]));
            }
        }

        if (list.size() == 0) {
            SysAdminInfoex infoex = new SysAdminInfoex();
            infoex.setUserExType(4);
            infoex.setUserExContent(Constant.BASIC_STRING_ZERO);
            infoex.setUserExText(Constant.TEMPLATE_SHARE_TEXT[0]);
            list.add(infoex);
        }
        return ResEntity.entity(true, Constant.SUCCESS_DX, list);
    }

    /**
     * 加载分页数据
     *
     * @param tRecordTemplate
     * @param page
     * @return
     */
    public Object getPageDatas(TRecordTemplate tRecordTemplate, Page page) {

        String appId = AdminUtils.getCurrentAppId();
        String insCode = AdminUtils.getCurrentInsCode();
        AdminInfo admin = AdminUtils.getCurrentHr();
        if(!Constant.BASIC_APP_ID.equals(appId)){
            tRecordTemplate.setAppId(appId);
        }
        if(!Constant.BASIC_INS_CODE.equals(insCode)){
            tRecordTemplate.setInsCode(insCode);
        }
        String deptId = AdminUtils.getCurrentDeptId();
        if(!Constant.BASIC_DEPT_ID.equals(deptId)){
            tRecordTemplate.setDeptId(deptId);
        }

        tRecordTemplate.setCreateUser(admin.getId());

        PageHelper.startPage(page.getPage(), page.getLimit());
        List<TRecordTemplate> list = tRecordTemplateMapper.getPageListByObj(tRecordTemplate);
        List<TRecordTemplate> collect = list.stream().map(i -> {
            i.setSelfTemp(admin.getId().equals(i.getCreateUser()) ? true : false);
            return i;
        }).collect(Collectors.toList());
        return Page.getLayUiTablePageData(collect);
    }

    /**
     * 插入新数据
     *
     * @param tRecordTemplate
     * @return
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity insert(TRecordTemplate tRecordTemplate) {

        if (StringUtils.isBlank(tRecordTemplate.getTemplName())) {
            return ResEntity.entity(false, "模板名称不能为空", null);
        }
        if (StringUtils.isBlank(tRecordTemplate.getTemplType())) {
            return ResEntity.entity(false, "模板类型不能为空", null);
        }
        if (StringUtils.isBlank(tRecordTemplate.getIsUsing())) {
            return ResEntity.entity(false, "模板是否启用不能为空", null);
        }
        if (StringUtils.isBlank(tRecordTemplate.getIsShare())) {
            return ResEntity.entity(false, "模板共享权限不能为空", null);
        }

        Date date = new Date();
        AdminInfo admin = AdminUtils.getCurrentHr();

        tRecordTemplate.setTemplId(IDUtil.getID());
        tRecordTemplate.setAppId(AdminUtils.getCurrentAppId());
        tRecordTemplate.setInsCode(AdminUtils.getCurrentInsCode());
        tRecordTemplate.setDeptId(AdminUtils.getCurrentDeptId());
        tRecordTemplate.setIsDel(Constant.BASIC_STRING_ZERO);
        tRecordTemplate.setCreateDate(date);
        tRecordTemplate.setCreateUser(admin.getId());
        tRecordTemplate.setCreateUsername(admin.getNameZh());

        if (Constant.ADMIN.equals(admin.getUsername())) {
            tRecordTemplate.setTemplClassify(Constant.BASIC_STRING_ONE);
            tRecordTemplate.setIsShare(Constant.BASIC_STRING_THREE);
        } else {
            tRecordTemplate.setTemplClassify(Constant.BASIC_STRING_TWO);
        }

        if (Constant.BASIC_STRING_ONE.equals(tRecordTemplate.getIsDefault())) {
            Integer num = tRecordTemplateMapper.noDefaultByDisName(tRecordTemplate);
        } else {
            Integer num = tRecordTemplateMapper.getDefaultCountByDisName(tRecordTemplate);
            if (num == null || num == 0) {
                tRecordTemplate.setIsDefault(Constant.BASIC_STRING_ONE);
            }
        }

        tRecordTemplateMapper.insert(tRecordTemplate);

        List<TRecordTemplateDetail> list = getDetailListFromTree(tRecordTemplate.getDetailList());
        for (TRecordTemplateDetail detail : list) {
            if (detail.getDetailName() == null) {
                detail.setDetailName("");
            }
            if (detail.getDetailType() == null) {
                return ResEntity.entity(false, "明细类型不能为空", null);
            }
            if (detail.getDetailDisplay() == null) {
                return ResEntity.entity(false, "明细显示形式不能为空", null);
            }

            detail.setTemplId(tRecordTemplate.getTemplId());
            detail.setIsDel(Constant.BASIC_STRING_ZERO);
            detail.setCreateDate(date);
            detail.setCreateUser(admin.getId());
            detail.setCreateUsername(admin.getNameZh());
        }

        tRecordTemplateDetailMapper.insertList(list);
        list.clear();
        list = null;
        return ResEntity.entity(true, Constant.SUCCESS_DX, tRecordTemplate);
    }


    /**
     * 修改
     *
     * @param tRecordTemplate
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity update(TRecordTemplate tRecordTemplate) {

        if (StringUtils.isBlank(tRecordTemplate.getTemplId())) {
            return ResEntity.entity(false, "模板ID不能为空", null);
        }
        if (StringUtils.isBlank(tRecordTemplate.getTemplName())) {
            return ResEntity.entity(false, "模板名称不能为空", null);
        }
        if (StringUtils.isBlank(tRecordTemplate.getTemplType())) {
            return ResEntity.entity(false, "模板类型不能为空", null);
        }
        if (StringUtils.isBlank(tRecordTemplate.getIsUsing())) {
            return ResEntity.entity(false, "模板是否启用不能为空", null);
        }
        if (StringUtils.isBlank(tRecordTemplate.getIsShare())) {
            return ResEntity.entity(false, "模板共享权限不能为空", null);
        }

        Date date = new Date();
        AdminInfo admin = AdminUtils.getCurrentHr();
        String appId = AdminUtils.getCurrentAppId();
        String insCode = AdminUtils.getCurrentInsCode();

        tRecordTemplate.setTemplClassify(null);
        tRecordTemplate.setAppId(appId);
        tRecordTemplate.setIsDel(Constant.BASIC_STRING_ZERO);
        tRecordTemplate.setUpdateDate(date);
        tRecordTemplate.setUpdateUser(admin.getId());
        tRecordTemplate.setUpdateUsername(admin.getNameZh());

        if (Constant.ADMIN.equals(admin.getUsername())) {
            tRecordTemplate.setTemplClassify(Constant.BASIC_STRING_ONE);
            tRecordTemplate.setIsShare(Constant.BASIC_STRING_THREE);
        } else {
            tRecordTemplate.setTemplClassify(Constant.BASIC_STRING_TWO);
        }

        if (Constant.BASIC_STRING_ONE.equals(tRecordTemplate.getIsDefault())) {
            Integer num = tRecordTemplateMapper.noDefaultByDisName(tRecordTemplate);
        } else {
            Integer existNum = tRecordTemplateMapper.getDefaultCountByDisName(tRecordTemplate);
            if (existNum == null || existNum == 0) {
                tRecordTemplate.setIsDefault(Constant.BASIC_STRING_ONE);
            }
        }

        tRecordTemplateMapper.updateByPrimaryKey(tRecordTemplate);

        List<TRecordTemplateDetail> list = getDetailListFromTree(tRecordTemplate.getDetailList());
        for (TRecordTemplateDetail detail : list) {
            if (detail.getDetailName() == null) {
                detail.setDetailName("");
            }
            if (detail.getDetailType() == null) {
                return ResEntity.entity(false, "明细类型不能为空", null);
            }
            if (detail.getDetailDisplay() == null) {
                return ResEntity.entity(false, "明细显示形式不能为空", null);
            }

            detail.setTemplId(tRecordTemplate.getTemplId());
            detail.setIsDel(Constant.BASIC_STRING_ZERO);
            detail.setCreateDate(date);
            detail.setCreateUser(admin.getId());
            detail.setCreateUsername(admin.getNameZh());
        }

        tRecordTemplateDetailMapper.deleteByTempId(tRecordTemplate.getTemplId());
        tRecordTemplateDetailMapper.insertList(list);
        list.clear();
        list = null;
        return ResEntity.entity(true, Constant.SUCCESS_DX, null);
    }

    /**
     * 加载某条数据
     *
     * @param templId
     * @return
     */
    public ResEntity findObj(String templId, String registerId) {

        if (StringUtils.isBlank(templId)) {
            return new ResEntity(false, "参数不能为空哦", null);
        }
        TRecordTemplate tRecordTemplate = tRecordTemplateMapper.getObjectById(templId);

        tRecordTemplate.setDetailList(getTeplateDetailList(templId));

        // 电子病历模板配置页面不需要智能填写
//        electronicRecordService.setTempFromRecordSyndrome(tRecordTemplate, registerId, null);

        return new ResEntity(true, Constant.SUCCESS_DX, tRecordTemplate);
    }

    /**
     * 删除
     *
     * @param ids
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResEntity deleteLis(String ids) {
        if (StringUtils.isBlank(ids)) {
            return new ResEntity(false, "参数错误(缺少参数)！", null);
        }
        long rowsR = tRecordTemplateMapper.deleteBylist(ids.split(","));

        return new ResEntity(true, Constant.SUCCESS_DX, rowsR);
    }

    /**
     * 根据模板ID获取模板明细，并组装成树
     *
     * @param templId
     * @return java.util.List<com.jiuzhekan.cbkj.beans.business.template.TRecordTemplateDetail>
     * <AUTHOR>
     * @date 2020/2/12
     */
    public List<TRecordTemplateDetail> getTeplateDetailList(String templId) {

        TRecordTemplateDetail detailModel = new TRecordTemplateDetail();
        detailModel.setTemplId(templId);
        List<TRecordTemplateDetail> allList = tRecordTemplateDetailMapper.getPageListByObj(detailModel);

        return getListToTree(allList);
    }


    /**
     * list组装成树
     *
     * @param allList
     * @return java.util.List<com.jiuzhekan.cbkj.beans.business.template.TRecordTemplateDetail>
     * <AUTHOR>
     * @date 2020/2/17
     */
    private List<TRecordTemplateDetail> getListToTree(List<TRecordTemplateDetail> allList) {
        Map<String, TRecordTemplateDetail> map = new HashMap<>();
        List<TRecordTemplateDetail> rootList = new ArrayList<>();

        for (TRecordTemplateDetail detail : allList) {
            map.put(detail.getDetailId(), detail);
        }

        for (TRecordTemplateDetail detail : allList) {

            TRecordTemplateDetail parent = map.get(detail.getDetailPid());

            if (parent != null) {

                if (parent.getChildren() == null) {
                    parent.setChildren(new ArrayList<>());
                }

                if (parent.getOptions() == null) {
                    parent.setOptions(new ArrayList<>());
                }

                if (detail.getDetailDisplay() == 10) {
                    parent.getOptions().add(detail);
                } else {
                    parent.getChildren().add(detail);
                }
            } else {
                rootList.add(detail);
            }
        }


        return rootList;
    }


    /**
     * 把树转化成List
     *
     * @param rootList 根节点集合
     * @return java.util.List<com.jiuzhekan.cbkj.beans.business.template.TRecordTemplateDetail>
     * <AUTHOR>
     * @date 2020/2/12
     */
    private List<TRecordTemplateDetail> getDetailListFromTree(List<TRecordTemplateDetail> rootList) {

        List<TRecordTemplateDetail> list = new ArrayList<>();

        if (rootList != null && rootList.size() > 0) {
            for (int i = 0; i < rootList.size(); i++) {
                TRecordTemplateDetail detail = rootList.get(i);
                detail.setDetailId(IDUtil.getID());
                detail.setDetailPid(Constant.BASIC_STRING_MINUS_ONE);
                detail.setDetailLevel(1);
                detail.setDetailNum(i);
                iterTreeToList(detail, list, 2);
            }
        }

        return list;
    }

    /**
     * 把树转化成List
     *
     * @param parent
     * @param list
     * @return void
     * <AUTHOR>
     * @date 2020/2/12
     */
    private void iterTreeToList(TRecordTemplateDetail parent, List<TRecordTemplateDetail> list, int level) {
        list.add(parent);

        if (parent.getChildren() != null && parent.getChildren().size() > 0) {
            for (int i = 0; i < parent.getChildren().size(); i++) {
                TRecordTemplateDetail detail = parent.getChildren().get(i);
                detail.setDetailId(IDUtil.getID());
                detail.setDetailPid(parent.getDetailId());
                detail.setDetailLevel(level);
                detail.setDetailNum(i);
                iterTreeToList(detail, list, level + 1);
            }
        }

        if (parent.getOptions() != null && parent.getOptions().size() > 0) {
            for (int i = 0; i < parent.getOptions().size(); i++) {
                TRecordTemplateDetail detail = parent.getOptions().get(i);
                detail.setDetailId(IDUtil.getID());
                detail.setDetailPid(parent.getDetailId());
                detail.setDetailLevel(level);
                detail.setDetailNum(i);
                iterTreeToList(detail, list, level + 1);
            }
        }
    }
}