package com.jiuzhekan.managementTCMSP.utils;

import java.util.*;

/**
 * 树结构工具类
 * 用于将平铺的数据转换为树状结构
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class TreeUtils {
    
    /**
     * 构建机构树状结构
     * 
     * @param flatList 平铺的机构列表，每个HashMap包含insCode、insName、insParentCode
     * @return 树状结构的机构列表
     */
    public static List<HashMap<String, Object>> buildInsTree(List<HashMap<String, String>> flatList) {
        if (flatList == null || flatList.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 创建所有节点的映射，key为insCode，value为节点对象
        Map<String, HashMap<String, Object>> nodeMap = new HashMap<>();
        List<HashMap<String, Object>> rootNodes = new ArrayList<>();
        
        // 第一遍遍历：创建所有节点对象
        for (HashMap<String, String> item : flatList) {
            HashMap<String, Object> node = createTreeNode(item);
            nodeMap.put(item.get("insCode"), node);
        }
        
        // 第二遍遍历：建立父子关系
        for (HashMap<String, String> item : flatList) {
            String insCode = item.get("insCode");
            String parentCode = item.get("insParentCode");
            
            HashMap<String, Object> currentNode = nodeMap.get(insCode);
            
            if (isRootNode(parentCode)) {
                // 根节点：父代码为空、空字符串或"0"
                rootNodes.add(currentNode);
            } else {
                // 子节点：查找父节点并添加到其children中
                HashMap<String, Object> parentNode = nodeMap.get(parentCode);
                if (parentNode != null) {
                    addChildToParent(parentNode, currentNode);
                } else {
                    // 如果找不到父节点，将其作为根节点处理（数据异常情况）
                    rootNodes.add(currentNode);
                }
            }
        }
        
        return rootNodes;
    }
    
    /**
     * 创建树节点对象
     * 
     * @param item 原始数据项
     * @return 树节点对象
     */
    private static HashMap<String, Object> createTreeNode(HashMap<String, String> item) {
        HashMap<String, Object> node = new HashMap<>();
        node.put("insCode", item.get("insCode"));
        node.put("insName", item.get("insName"));
        node.put("insParentCode", item.get("insParentCode"));
        node.put("children", new ArrayList<HashMap<String, Object>>());
        return node;
    }
    
    /**
     * 判断是否为根节点
     * 
     * @param parentCode 父代码
     * @return true表示是根节点
     */
    private static boolean isRootNode(String parentCode) {
        return parentCode == null || 
               parentCode.trim().isEmpty() || 
               "0".equals(parentCode) ||
               "null".equalsIgnoreCase(parentCode);
    }
    
    /**
     * 将子节点添加到父节点的children中
     * 
     * @param parentNode 父节点
     * @param childNode 子节点
     */
    @SuppressWarnings("unchecked")
    private static void addChildToParent(HashMap<String, Object> parentNode, HashMap<String, Object> childNode) {
        List<HashMap<String, Object>> children = 
            (List<HashMap<String, Object>>) parentNode.get("children");
        children.add(childNode);
    }
    
    /**
     * 通用的树构建方法
     * 
     * @param flatList 平铺的数据列表
     * @param idField ID字段名
     * @param parentIdField 父ID字段名
     * @param childrenField 子节点字段名
     * @return 树状结构列表
     */
    public static List<HashMap<String, Object>> buildTree(
            List<HashMap<String, String>> flatList,
            String idField,
            String parentIdField,
            String childrenField) {
        
        if (flatList == null || flatList.isEmpty()) {
            return new ArrayList<>();
        }
        
        Map<String, HashMap<String, Object>> nodeMap = new HashMap<>();
        List<HashMap<String, Object>> rootNodes = new ArrayList<>();
        
        // 创建所有节点
        for (HashMap<String, String> item : flatList) {
            HashMap<String, Object> node = new HashMap<>(item);
            node.put(childrenField, new ArrayList<HashMap<String, Object>>());
            nodeMap.put(item.get(idField), node);
        }
        
        // 建立父子关系
        for (HashMap<String, String> item : flatList) {
            String id = item.get(idField);
            String parentId = item.get(parentIdField);
            
            HashMap<String, Object> currentNode = nodeMap.get(id);
            
            if (isRootNode(parentId)) {
                rootNodes.add(currentNode);
            } else {
                HashMap<String, Object> parentNode = nodeMap.get(parentId);
                if (parentNode != null) {
                    @SuppressWarnings("unchecked")
                    List<HashMap<String, Object>> children = 
                        (List<HashMap<String, Object>>) parentNode.get(childrenField);
                    children.add(currentNode);
                } else {
                    rootNodes.add(currentNode);
                }
            }
        }
        
        return rootNodes;
    }
    
    /**
     * 打印树结构（用于调试）
     * 
     * @param treeList 树结构列表
     * @param nameField 名称字段
     */
    public static void printTree(List<HashMap<String, Object>> treeList, String nameField) {
        for (HashMap<String, Object> node : treeList) {
            printNode(node, nameField, 0);
        }
    }
    
    /**
     * 递归打印节点
     * 
     * @param node 节点
     * @param nameField 名称字段
     * @param level 层级
     */
    @SuppressWarnings("unchecked")
    private static void printNode(HashMap<String, Object> node, String nameField, int level) {
        StringBuilder indent = new StringBuilder();
        for (int i = 0; i < level; i++) {
            indent.append("  ");
        }
        
        System.out.println(indent + "- " + node.get(nameField));
        
        List<HashMap<String, Object>> children = 
            (List<HashMap<String, Object>>) node.get("children");
        
        if (children != null) {
            for (HashMap<String, Object> child : children) {
                printNode(child, nameField, level + 1);
            }
        }
    }
    
    /**
     * 获取树的深度
     * 
     * @param treeList 树结构列表
     * @return 树的最大深度
     */
    public static int getTreeDepth(List<HashMap<String, Object>> treeList) {
        int maxDepth = 0;
        for (HashMap<String, Object> node : treeList) {
            int depth = getNodeDepth(node, 1);
            maxDepth = Math.max(maxDepth, depth);
        }
        return maxDepth;
    }
    
    /**
     * 获取节点深度
     * 
     * @param node 节点
     * @param currentDepth 当前深度
     * @return 节点的最大深度
     */
    @SuppressWarnings("unchecked")
    private static int getNodeDepth(HashMap<String, Object> node, int currentDepth) {
        List<HashMap<String, Object>> children = 
            (List<HashMap<String, Object>>) node.get("children");
        
        if (children == null || children.isEmpty()) {
            return currentDepth;
        }
        
        int maxChildDepth = currentDepth;
        for (HashMap<String, Object> child : children) {
            int childDepth = getNodeDepth(child, currentDepth + 1);
            maxChildDepth = Math.max(maxChildDepth, childDepth);
        }
        
        return maxChildDepth;
    }
    
    /**
     * 统计树中节点总数
     * 
     * @param treeList 树结构列表
     * @return 节点总数
     */
    public static int countNodes(List<HashMap<String, Object>> treeList) {
        int count = 0;
        for (HashMap<String, Object> node : treeList) {
            count += countNodeRecursive(node);
        }
        return count;
    }
    
    /**
     * 递归统计节点数量
     * 
     * @param node 节点
     * @return 该节点及其子节点的总数
     */
    @SuppressWarnings("unchecked")
    private static int countNodeRecursive(HashMap<String, Object> node) {
        int count = 1; // 当前节点
        
        List<HashMap<String, Object>> children = 
            (List<HashMap<String, Object>>) node.get("children");
        
        if (children != null) {
            for (HashMap<String, Object> child : children) {
                count += countNodeRecursive(child);
            }
        }
        
        return count;
    }
}
