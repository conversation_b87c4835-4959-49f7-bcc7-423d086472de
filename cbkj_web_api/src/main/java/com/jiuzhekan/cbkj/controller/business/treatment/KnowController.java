package com.jiuzhekan.cbkj.controller.business.treatment;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jiuzhekan.cbkj.beans.ai.CueWordURLRequest;
import com.jiuzhekan.cbkj.beans.business.TSysParam;
import com.jiuzhekan.cbkj.beans.business.doctor.TDoctorDisease;
import com.jiuzhekan.cbkj.beans.business.zkxcs.ZkxcMapping;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.common.annotaionUtil.StatisticsFunction;
import com.jiuzhekan.cbkj.common.http.GptAPIRes;
import com.jiuzhekan.cbkj.common.http.GptAPIResItem;
import com.jiuzhekan.cbkj.common.http.KnowRestTemplate;
import com.jiuzhekan.cbkj.common.utils.*;
import com.jiuzhekan.cbkj.controller.business.treatment.vo.KnowCheckPres;
import com.jiuzhekan.cbkj.mapper.business.zkxcs.ZkxcMappingMapper;
import com.jiuzhekan.cbkj.service.ai.SySCueWordService;
import com.jiuzhekan.cbkj.service.business.TSysParamService;
import com.jiuzhekan.cbkj.service.business.analysis.TUserAnalysisResultService;
import com.jiuzhekan.cbkj.service.business.dic.DicBaseService;
import com.jiuzhekan.cbkj.service.business.doctor.TDoctorCollectService;
import com.jiuzhekan.cbkj.service.business.doctor.TDoctorDiseaseService;
import com.jiuzhekan.cbkj.service.http.CbkjZbService;
import com.jiuzhekan.cbkj.service.myData.TPersonalPrescriptionService;
import com.jiuzhekan.healthpres.beans.TUserQuestionnaireCommonDis;
import com.jiuzhekan.healthpres.mapper.TUserQuestionnaireCommonDisMapper;
import com.jiuzhekan.statistics.aop.annotation.StatisticsFunction2;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.*;
import java.util.stream.Collectors;

@Controller
@RequestMapping("know")
@Api(value = "知识库接口", tags = "知识库接口")
@Slf4j
public class KnowController {

    @Autowired
    private KnowRestTemplate knowRestTemplate;
    @Autowired
    private CbkjZbService cbkjZbService;
    @Autowired
    private TPersonalPrescriptionService tPersonalPrescriptionService;
    @Autowired
    private TDoctorDiseaseService tDoctorDiseaseService;
    @Autowired
    private TDoctorCollectService tDoctorCollectService;
    @Autowired
    private TSysParamService tSysParamService;
    private final SySCueWordService sySCueWordService;
    private final TUserQuestionnaireCommonDisMapper tUserQuestionnaireCommonDisMapper;
    private final ZkxcMappingMapper zkxcMappingMapper;
    private final DicBaseService dicBaseService;

    public KnowController(SySCueWordService sySCueWordService, TUserQuestionnaireCommonDisMapper tUserQuestionnaireCommonDisMapper, ZkxcMappingMapper zkxcMappingMapper, DicBaseService dicBaseService) {
        this.sySCueWordService = sySCueWordService;
        this.tUserQuestionnaireCommonDisMapper = tUserQuestionnaireCommonDisMapper;
        this.zkxcMappingMapper = zkxcMappingMapper;
        this.dicBaseService = dicBaseService;
    }

    /*************************************智能辩证*******************************************/

    @RequestMapping(value = "syndrome/classify", method = RequestMethod.GET)
    @ApiOperation(value = "智能辩证-获取分类")
    @LogAnnotation("智能辩证-获取分类")
    @ResponseBody
    public Object syndromeClassify() {

        Map<String, Object> map = new HashMap<>();
        return knowRestTemplate.postKnow("syndrome/classify", map);
    }


    @RequestMapping(value = "syndrome/group", method = RequestMethod.GET)
    @ApiOperation(value = "智能辩证-获取分组")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "keyWord", value = "分组(中英)", paramType = "query"),
            @ApiImplicitParam(name = "classifyId", value = "分类ID（包括一级和二级分类）", paramType = "query"),
            @ApiImplicitParam(name = "usual", value = "是否常见病，传“Y”表示查询常见病", paramType = "query"),
            @ApiImplicitParam(name = "isUse", value = "是否使用，0为使用，1为不使用", paramType = "query")})
    @LogAnnotation("智能辩证-获取分组")
    @ResponseBody
    public Object syndromeGroup(String keyWord, String classifyId, String usual, String isUse) {
        List<TDoctorDisease> disList = tDoctorDiseaseService.getCurrentDoctorDiseaseListInThirtyDay();
        List<LinkedHashMap<String, Object>> groupChangYong = new ArrayList<>();
        HashMap<String, Object> map1 = new HashMap<>();
        try {
            String searchtype = AdminUtils.getCurrentShuruma();
            if (StringJudges.isContainChinese(keyWord)) {
                searchtype = "";
            }
            Map<String, Object> map = new HashMap<>();
            map.put("keyword", keyWord);
            map.put("searchtype", searchtype);
            map.put("classifyid", classifyId);
            map.put("usual", usual);
            map.put("isuse", isUse);

            ResEntity entity = knowRestTemplate.postKnow("syndrome/group", map);
            if (entity == null || !entity.getStatus()) {
                return ResEntity.error("获取分组数据失败");
            }

            LinkedHashMap data = (LinkedHashMap) entity.getData();
            List<LinkedHashMap> groups = (List<LinkedHashMap>) data.get("groups");

            Map<String, JSONObject> zbMap = new HashMap<>();
            Map<String, JSONObject> ccMap = new HashMap<>();

            ResEntity zbJson = cbkjZbService.getListByApiToken();
            boolean zbStatus = zbJson != null && zbJson.getStatus();
            if (zbStatus) {
                Object zbData = zbJson.getData();
                if (zbData instanceof JSONArray) {
                    JSONArray zbArr = (JSONArray) zbData;
                    for (Object o : zbArr) {
                        if (!(o instanceof JSONObject)) continue;
                        JSONObject zb = (JSONObject) o;
                        String type = zb.getString("type");
                        String disId = zb.getString("diseaseId");
                        String disCode = zb.getString("diseaseCode");

                        if ("master".equals(type)) {
                            if (disCode != null) {
                                zbMap.put(disCode, zb);
                            }
                        } else if ("chuancheng".equals(type)) {
                            if (disId != null) {
                                ccMap.put(disId, zb);
                            }
                        }
                    }
                }
            }
            JSONArray manchildren = new JSONArray();
            for (LinkedHashMap groupMap : groups) {
                Object o = groupMap.get("group");
                if (!(o instanceof List)) {
                    continue;
                }
                List<LinkedHashMap<String, Object>> group = (List<LinkedHashMap<String, Object>>) o;
                TreeMap<String, List<LinkedHashMap<String, Object>>> pyMap = new TreeMap<>();

                for (LinkedHashMap<String, Object> itemMap : group) {
                    String groupName = (String) itemMap.get("groupname");

                    Object py = itemMap.get("grouppy");
                    if (py == null) {
                        py = Dist.getFirstUpperPinYin(groupName);
                    } else {
                        if (StringUtils.isNotBlank(py.toString()) && py.toString().length() > 0) {
                            py = py.toString().substring(0, 1).toUpperCase();
                        } else {
                            py = Dist.getFirstUpperPinYin(groupName);
                        }
                    }
                    String disid = (String) itemMap.get("disid");
                    if (StringUtils.isNotBlank(disid)) {
                        Object chronicDis = itemMap.get("chronicDis");
                        if (null != chronicDis && StringUtils.isNumeric(chronicDis.toString()) && chronicDis.toString().equals("1")) {
                            JSONObject objman = new JSONObject();
                            objman.put("disid", itemMap.get("disid"));
                            objman.put("disname", itemMap.get("disname"));
                            objman.put("gender", itemMap.get("gender"));
                            objman.put("age", itemMap.get("age"));
                            objman.put("chronicDis", itemMap.get("chronicDis"));
                            objman.put("grouppy", itemMap.get("grouppy"));
                            objman.put("groupid", itemMap.get("groupid"));
                            objman.put("groupname", itemMap.get("groupname"));
                            manchildren.add(objman);
                        }
                        if (disList != null && !disList.isEmpty()) {

                            for (TDoctorDisease disease : disList) {
                                if (disease.getGroupId().equals(itemMap.get("groupid")) && !map1.containsKey(disid)) {
                                    LinkedHashMap<String, Object> itemChangYong = new LinkedHashMap<>();
                                    map1.put(disid, "1");
                                    itemChangYong.put("disid", disease.getDisId());
                                    itemChangYong.put("disname", disease.getDisName());
                                    itemChangYong.put("groupid", disease.getGroupId());
                                    itemChangYong.put("groupname", disease.getGroupName());
                                    itemChangYong.put("disnum", disease.getDisNum());
                                    itemChangYong.put("gender", itemMap.get("gender"));
                                    itemChangYong.put("age", itemMap.get("age"));
                                    itemChangYong.put("chronicDis", itemMap.get("chronicDis"));
                                    itemChangYong.put("grouppy", itemMap.get("grouppy"));
                                    groupChangYong.add(itemChangYong);
                                }

                            }

                        }


                        if (zbMap.containsKey(disid)) {
                            itemMap.put("diseaseCode", disid);
                            itemMap.put("master", (String) zbMap.get(disid).get("url"));
                        }
                        if (ccMap.containsKey(disid)) {
                            itemMap.put("chuancheng", (String) ccMap.get(disid).get("url"));
                        }
                    }

                    List<LinkedHashMap<String, Object>> list = pyMap.computeIfAbsent(py.toString(), k -> new ArrayList<>());
                    list.add(itemMap);
                }
                groupMap.put("group", pyMap);
            }
            LinkedHashMap<String, JSONArray> objmanALL = new LinkedHashMap();
            objmanALL.put("ALL", manchildren);
            LinkedHashMap<String, Object> objman = new LinkedHashMap<>();
            objman.put("classname", "常见慢病");
            objman.put("group", objmanALL);

            //data.put("groups", objman);


            if (disList != null && !disList.isEmpty()) {
//                List<LinkedHashMap<String, Object>> group = new ArrayList<>();
//
//                for (TDoctorDisease disease : disList) {
//                    LinkedHashMap<String, Object> item = new LinkedHashMap<>();
//                    item.put("disid", disease.getDisId());
//                    item.put("disname", disease.getDisName());
//                    item.put("groupid", disease.getGroupId());
//                    item.put("groupname", disease.getGroupName());
//                    item.put("disnum", disease.getDisNum());
//                    item.put("gender", disease.getGender());
//                    group.add(item);
//                }

                LinkedHashMap<String, Object> group00 = new LinkedHashMap<>();
                group00.put("ALL", groupChangYong);

                LinkedHashMap<String, Object> group0 = new LinkedHashMap<>();
                group0.put("classname", "我最常用");
                group0.put("group", group00);

                List<LinkedHashMap> groups2 = new ArrayList<>();
                groups2.add(group0);
                groups2.addAll(groups);
                groups2.add(2, objman);

//                List<TUserQuestionnaireCommonDis> list =  tUserQuestionnaireCommonDisMapper.getAllList();

                data.put("groups", groups2);
            } else {
                List<LinkedHashMap> groups2 = new ArrayList<>();

                groups2.addAll(groups);
                groups2.add(1, objman);
                data.put("groups", groups2);
            }
            return entity;
        } catch (Exception e) {
            // 记录异常日志
            log.error("智能辩证-获取分组异常", e);
            return ResEntity.error("服务器内部错误");
        }
    }
// ... existing code ...


    @RequestMapping(value = "syndrome/ask", method = RequestMethod.GET)
    @ApiOperation(value = "智能辩证-分组问卷")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "groupId", value = "分组ID", paramType = "query", required = true),
            @ApiImplicitParam(name = "askType", value = "问卷类型，1为专业版问卷，2为简易版问卷", paramType = "query", required = true)})
    @LogAnnotation("智能辩证-分组问卷")
    @ResponseBody
    public Object syndromeAsk(String groupId, String askType, String registerId) {

        Map<String, Object> map = new HashMap<>();
        map.put("groupid", groupId);
        map.put("asktype", askType);
        ResEntity resEntity = knowRestTemplate.postKnow("syndrome/ask", map);
        if (resEntity.getStatus()) {
            LinkedHashMap<Object, Object> data = (LinkedHashMap) resEntity.getData();
            ArrayList asks = (ArrayList) data.get("asks");
            //移除asks最后的个对象
            asks.remove(asks.size() - 1);
            // 从一件事获取舌脉数据
            HashMap<String, ArrayList<HashMap<String, Object>>> bianZhengDic = dicBaseService.getBianZhengDic(registerId);
            data.put("tongueAndPulse", bianZhengDic);
        }

        return resEntity;
    }

    @RequestMapping(value = "syndrome/save", method = RequestMethod.GET)
    @ApiOperation(value = "智能辩证-辨证结果")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "groupId", value = "分组ID", paramType = "query", required = true),
            @ApiImplicitParam(name = "itemIds", value = "所有勾选的症状ID拼接，用“,”隔开", paramType = "query", required = true)})
    @LogAnnotation("智能辩证-辨证结果")
    @ResponseBody
    @StatisticsFunction2(classify = "中医诊疗", function = "智能辩证")
    public Object syndromeSave(String groupId, String itemIds,String tongueIdAndpulseId ) {
        //需要对itemIds进行从一件事的code编码 转成 辩证的 itemId（b_syndrome_answer的S_ID 字段，也就是b_syndrome的主键id）
//        String[] split = itemIds.split(",");
        if (StringUtils.isNotBlank(tongueIdAndpulseId)){
            String[] tongueIdAndpulseIdSplit = tongueIdAndpulseId.split(",");
            List<String> list = Arrays.asList(tongueIdAndpulseIdSplit);
            List<ZkxcMapping> zkxcMappingList = zkxcMappingMapper.getKnowMapOneThingByIdsTwo(list);
//            StringBuilder stringBuilder = new StringBuilder();
            String collect = zkxcMappingList.stream().map(ZkxcMapping::getMapCode).collect(Collectors.joining(","));
//            zkxcMappingList.forEach(zkxcMapping -> {
//                stringBuilder.append(zkxcMapping.getMapCode()).append(",");
//            });
            if (StringUtils.isNotBlank(collect) ) {
//                String a = "";
                //stringBuilder如果最后字符是,则去掉
//                if (stringBuilder.charAt(stringBuilder.length() - 1) == ',') {
//                    a = stringBuilder.substring(0, stringBuilder.length() - 1);
//                }else {
//                    a = stringBuilder.toString();
//                }
                itemIds = itemIds+","+collect;
            }
        }






        Map<String, Object> map = new HashMap<>();

        map.put("groupid", groupId);
        map.put("itemids", itemIds);
        ResEntity resEntity = knowRestTemplate.postKnow("syndrome/save", map);
        Object obj = resEntity.getData();


        TSysParam jumpUrl = tSysParamService.getSysParam(Constant.INTELLIGENT_JUMP);

        if (obj instanceof ArrayList) {
            ArrayList<Object> a = (ArrayList<Object>) obj;
            if (jumpUrl != null && StringUtils.isNotBlank(jumpUrl.getParValues())) {
                HashMap<String, Object> objectObjectHashMap = new HashMap<>();
                objectObjectHashMap.put("url", jumpUrl.getParValues());
                a.add(objectObjectHashMap);
            }
            return ResEntity.entity(true, Constant.SUCCESS_DX, a);
        } else if (obj instanceof LinkedHashMap) {
            if (jumpUrl != null && StringUtils.isNotBlank(jumpUrl.getParValues())) {
                LinkedHashMap<String, Object> a = (LinkedHashMap<String, Object>) obj;
                a.put("url", jumpUrl.getParValues());
                return ResEntity.entity(true, Constant.SUCCESS_DX, a);
            }
        }

        return resEntity;
    }

    /*************************************疾病证型*******************************************/

    @RequestMapping(value = "disease/getCommonDisS", method = RequestMethod.GET)
    @ApiOperation(value = "疾病证型-常见疾病")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "limit", value = "条数", paramType = "query")})
    @LogAnnotation("疾病证型-常见疾病")
    @StatisticsFunction(source = "名医验案", value = "浏览次数")
    @ResponseBody
    public ResEntity commonDiseaseList() {
        Map<String, Object> map = new HashMap<>();

        return knowRestTemplate.postKnow("disease/getCommonDisS", map);
    }

    @RequestMapping(value = "disease/list", method = RequestMethod.GET)
    @ApiOperation(value = "疾病证型-疾病查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "disName", value = "中医病名(中英)", paramType = "query"),
            @ApiImplicitParam(name = "deptId", value = "科室ID", paramType = "query"),
            @ApiImplicitParam(name = "westCode", value = "西医疾病代码", paramType = "query")
    })
    @LogAnnotation("疾病证型-疾病查询")
    @ResponseBody
//    @Cacheable(value = "know::disease", keyGenerator = "cacheKeyGenerator")
    public ResEntity diseaseList(String disName, String deptId, String westCode, Page page) {
//        1拼音 2五笔
        String searchtype = AdminUtils.getCurrentShuruma();
        if (StringJudges.isContainChinese(disName)) {
            searchtype = "";
        }

        Map<String, Object> map = new HashMap<>();

        map.put("keyword", disName);
        map.put("searchtype", searchtype);
        map.put("deptid", deptId);
        map.put("westCode", westCode);
        map.put("pagesize", page.getLimit().toString());
        map.put("pageid", page.getPage().toString());

        TSysParam param = tSysParamService.getSysParam(Constant.DIS_FILTER_CATEGORY);
        TSysParam param2 = tSysParamService.getSysParam(Constant.DIAGNOSIS_RANGE_VERIFY);
        if (param != null && StringUtils.isNotBlank(param.getParValues())) {
            map.put("filterCategory", param.getParValues());
        }
        //中医病名搜索列表过滤“扩充”的疾病：1限制 0不限制
        if (param2 != null && StringUtils.isNotBlank(param2.getParValues())) {
            map.put("filterDisLimit", param2.getParValues());
        }
        ResEntity resEntity = knowRestTemplate.postKnow("disease/list", map);
        return tDoctorCollectService.knowDataIsCollect(resEntity, "diseases", "disid");
    }

    @RequestMapping(value = "disease/detail", method = RequestMethod.GET)
    @ApiOperation(value = "疾病证型-疾病详情")
    @ApiImplicitParam(name = "disId", value = "疾病id", paramType = "query", required = true)
    @LogAnnotation("疾病证型-疾病详情")
    @ResponseBody
    @StatisticsFunction2(classify = "中医知识库", function = "疾病查询")
    public ResEntity diseaseDetail(String disId) {

        Map<String, Object> map = new HashMap<>();
        map.put("disid", disId);
        return tDoctorCollectService.knowDataIsCollect(knowRestTemplate.postKnow("disease/detail", map), disId);

    }

    @RequestMapping(value = "disease/verify", method = RequestMethod.GET)
    @ApiOperation(value = "疾病证型-疾病限制判断")
    @ApiImplicitParam(name = "disId", value = "疾病id", paramType = "query", required = true)
    @LogAnnotation("疾病证型-疾病限制判断")
    @ResponseBody
    public ResEntity diseaseVerify(String disId) {
        Map<String, Object> map = new HashMap<>(8);
        map.put("disId", disId);
        TSysParam param = tSysParamService.getSysParam(Constant.DIS_FILTER_CATEGORY);
        TSysParam param2 = tSysParamService.getSysParam(Constant.DIAGNOSIS_RANGE_VERIFY);
        if (null != param) {
            map.put("filterCategory", param.getParValues());
        }
        if (null != param2) {
            map.put("filterDisSource", param2.getParValues());
        }
        return knowRestTemplate.postKnow("disease/verify", map);
    }

    @RequestMapping(value = "symptom/verify", method = RequestMethod.GET)
    @ApiOperation(value = "疾病证型-证型限制判断")
    @LogAnnotation("疾病证型-证型限制判断")
    @ResponseBody
    public ResEntity symptomVerify(String symId) {
        Map<String, Object> map = new HashMap<>(8);
        map.put("symId", symId);
        TSysParam param = tSysParamService.getSysParam(Constant.TCM_SYNDROME_TYPE);
        TSysParam param2 = tSysParamService.getSysParam(Constant.DIAGNOSIS_RANGE_VERIFY);
        if (null != param) {
            map.put("filterCategory", param.getParValues());
        }
        if (null != param2) {
            map.put("filterDisSource", param2.getParValues());
        }
        return knowRestTemplate.postKnow("symptom/verify", map);
    }

    @RequestMapping(value = "therapy/verify", method = RequestMethod.GET)
    @ApiOperation(value = "疾病证型-治法限制判断")
    @LogAnnotation("疾病证型-治法限制判断")
    @ResponseBody
    public ResEntity therapyVerify(String therapyCode, String therapyName) {
        Map<String, Object> map = new HashMap<>(8);
        map.put("therapyCode", therapyCode);
        map.put("therapyName", therapyName);
        TSysParam param = tSysParamService.getSysParam(Constant.TCM_TREATMENT);
        TSysParam param2 = tSysParamService.getSysParam(Constant.DIAGNOSIS_RANGE_VERIFY);
        if (null != param) {
            map.put("filterCategory", param.getParValues());
        }
        if (null != param2) {
            map.put("filterDisSource", param2.getParValues());
        }
        return knowRestTemplate.postKnow("therapy/verify", map);
    }


    @RequestMapping(value = "symptom/list", method = RequestMethod.GET)
    @ApiOperation(value = "疾病证型-证型查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "symName", value = "证型(中英)", paramType = "query", required = true),
            @ApiImplicitParam(name = "disId", value = "疾病ID", paramType = "query")})
    @LogAnnotation("疾病证型-证型查询")
    @ResponseBody
//    @Cacheable(value = "know::disease", keyGenerator = "cacheKeyGenerator")
    public ResEntity symptomList(String symName, String disId, String westCode, Page page) {
        String searchtype = AdminUtils.getCurrentShuruma();
        if (StringJudges.isContainChinese(symName)) {
            searchtype = "";
        }

        Map<String, Object> map = new HashMap<>();
        TSysParam param = tSysParamService.getSysParam(Constant.TCM_SYNDROME_TYPE);
        if (null != param) {
            map.put("filterCategory", param.getParValues());
        }
        map.put("keyword", symName);
        map.put("searchtype", searchtype);
        map.put("westCode", westCode);
        map.put("disid", disId);
        map.put("pagesize", page.getLimit().toString());
        map.put("pageid", page.getPage().toString());
        return knowRestTemplate.postKnow("symptom/list", map);
    }

    @RequestMapping(value = "symptom/detail", method = RequestMethod.GET)
    @ApiOperation(value = "疾病证型-证型详情")
    @ApiImplicitParam(name = "symId", value = "证型id", paramType = "query", required = true)
    @LogAnnotation("疾病证型-证型详情")
    @ResponseBody
    public ResEntity symptomDetail(String symId) {
        Map<String, Object> map = new HashMap<>();
        map.put("symId", symId);
        return knowRestTemplate.postKnow("symptom/detail", map);
    }

    @RequestMapping(value = "therapy/list", method = RequestMethod.GET)
    @ApiOperation(value = "疾病证型-治法查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "therapy", value = "治法(中英)", paramType = "query", required = true)})
    @LogAnnotation("疾病证型-治法查询")
    @ResponseBody
//    @Cacheable(value = "know::disease", keyGenerator = "cacheKeyGenerator")
    public ResEntity therapyList(String disId, String symId, String therapy, Page page) {
        String searchtype = AdminUtils.getCurrentShuruma();
        if (StringJudges.isContainChinese(therapy)) {
            searchtype = "";
        }

        Map<String, Object> map = new HashMap<>();
        TSysParam param = tSysParamService.getSysParam(Constant.TCM_TREATMENT);
        if (null != param) {
            map.put("filterCategory", param.getParValues());
        }
        map.put("disId", disId);
        map.put("symId", symId);
        map.put("keyword", therapy);
        map.put("searchtype", searchtype);
        map.put("pagesize", page.getLimit().toString());
        map.put("pageid", page.getPage().toString());
        return knowRestTemplate.postKnow("therapy/list", map);
    }

    @RequestMapping(value = "dis/tree", method = RequestMethod.GET)
    @ApiOperation(value = "疾病证型治法获取树状结构数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type", value = "1.疾病2.证型3治法", paramType = "query", required = true)})
    @LogAnnotation("疾病证型-治法查询")
    @ResponseBody
    public ResEntity disTree(Integer type) {
        Map<String, Object> map = new HashMap<>();
        map.put("type", type);
        return knowRestTemplate.postKnow("disease/tree", map);
    }

    /*************************************治疗方案*******************************************/

    @RequestMapping(value = "scheme/list", method = RequestMethod.GET)
    @ApiOperation(value = "治疗方案-治疗方案推荐")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "disId", value = "中医病名ID", paramType = "query", required = true),
            @ApiImplicitParam(name = "symId", value = "证型ID", paramType = "query", required = true),
            @ApiImplicitParam(name = "therapy", value = "治法名称", paramType = "query", required = true),
            @ApiImplicitParam(name = "businessType", value = "方案类型：1为内服，2为中成药，3为适宜技术，4为外用，5为保健食疗，6为验案，如果不传获取所有方案，如果多个请用“,”隔开，例：1,2,3", paramType = "query"),
            @ApiImplicitParam(name = "gravidity", value = "是否怀孕：Y为怀孕，N为没有怀孕", paramType = "query"),
            @ApiImplicitParam(name = "age", value = "年龄，以岁为单位", paramType = "query")})
    @LogAnnotation("治疗方案-治疗方案推荐（合并院内方）")
    @StatisticsFunction(source = "智能开方", value = "病证推导次数")
    @StatisticsFunction2(classify = "中医诊疗", function = "智能推导")
    @ResponseBody
    @Cacheable(value = "know::scheme", keyGenerator = "cacheKeyGenerator")
    public ResEntity schemeList(String disId, String symId, String therapy, String businessType) {
        Map<String, Object> map = new HashMap<>();
        map.put("disid", disId);
        map.put("symid", symId);
        map.put("therapy", therapy);
        map.put("businesstype", businessType);


        ResEntity res = knowRestTemplate.postKnow("scheme/list", map);
        if (res.getStatus()) {
            LinkedHashMap data = (LinkedHashMap) res.getData();
            List<LinkedHashMap> schemes = (List<LinkedHashMap>) data.get("schemes");
            for (LinkedHashMap scheme : schemes) {
                String businesstype = (String) scheme.get("businesstype");
                if (Constant.BASIC_STRING_ONE.equals(businesstype) || Constant.BASIC_STRING_FOUR.equals(businesstype)) {
                    String preType = Constant.BASIC_STRING_FOUR.equals(businesstype)
                            ? Constant.BASIC_STRING_TWO : Constant.BASIC_STRING_ONE;
                    List<Map> list = new ArrayList<>();
                    List<Map> appPres = tPersonalPrescriptionService.getAppPersonalPres(disId, symId, preType);
                    if (appPres != null && appPres.size() > 0) {
                        list.addAll(appPres);
                    }
                    List<LinkedHashMap> pres = (List<LinkedHashMap>) scheme.get("pres");
                    if (pres != null && pres.size() > 0) {
                        list.addAll(pres);
                    }
                    scheme.put("pres", list);
                }
            }
        }
        return res;
    }


    @RequestMapping(value = "pres/detail", method = RequestMethod.GET)
    @ApiOperation(value = "治疗方案-方剂处方明细")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "disId", value = "中医病名ID", paramType = "query", required = true),
            @ApiImplicitParam(name = "symId", value = "证型ID", paramType = "query", required = true),
            @ApiImplicitParam(name = "preId", value = "方剂处方id", paramType = "query", required = true),
            @ApiImplicitParam(name = "gravidity", value = "是否怀孕：Y为怀孕，N为没有怀孕", paramType = "query"),
            @ApiImplicitParam(name = "age", value = "年龄，以岁为单位", paramType = "query")})
    @LogAnnotation("治疗方案-方剂处方明细")
    @ResponseBody
    @StatisticsFunction2(classify = "中医知识库", function = "方剂查询")
    public ResEntity presDetail(String disId, String symId, String preId) {
        Map<String, Object> map = new HashMap<>();
        map.put("preid", preId);
        map.put("disid", disId);
        map.put("symid", symId);

        return tDoctorCollectService.knowDataIsCollect(knowRestTemplate.postKnow("pres/detail", map), preId);
    }

    @RequestMapping(value = "scheme/add", method = RequestMethod.GET)
    @ApiOperation(value = "治疗方案-兼证加减明细")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "disId", value = "中医病名ID", paramType = "query", required = true),
            @ApiImplicitParam(name = "symId", value = "证型ID", paramType = "query", required = true),
            @ApiImplicitParam(name = "schemeAddId", value = "兼证加减id", paramType = "query", required = true),
            @ApiImplicitParam(name = "gravidity", value = "是否怀孕：Y为怀孕，N为没有怀孕", paramType = "query"),
            @ApiImplicitParam(name = "age", value = "年龄，以岁为单位", paramType = "query")})
    @LogAnnotation("治疗方案-兼证加减明细")
    @ResponseBody
    public ResEntity schemeAdd(String disId, String symId, String schemeAddId) {
        Map<String, Object> map = new HashMap<>();
        map.put("schemeaddid", schemeAddId);
        map.put("disid", disId);
        map.put("symid", symId);

        return knowRestTemplate.postKnow("scheme/add", map);
    }

    @RequestMapping(value = "smoked/detail", method = RequestMethod.GET)
    @ApiOperation(value = "治疗方案-熏蒸处方明细")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "disId", value = "中医病名ID", paramType = "query", required = true),
            @ApiImplicitParam(name = "symId", value = "证型ID", paramType = "query", required = true),
            @ApiImplicitParam(name = "smoPreId", value = "熏蒸处方id", paramType = "query", required = true),
            @ApiImplicitParam(name = "gravidity", value = "是否怀孕：Y为怀孕，N为没有怀孕", paramType = "query"),
            @ApiImplicitParam(name = "age", value = "年龄，以岁为单位", paramType = "query")})
    @LogAnnotation("治疗方案-熏蒸处方明细")
    @ResponseBody
    public ResEntity smokedDetail(String disId, String symId, String smoPreId) {
        Map<String, Object> map = new HashMap<>();
        map.put("smoPreId", smoPreId);
        map.put("disid", disId);
        map.put("symid", symId);

        return tDoctorCollectService.knowDataIsCollect(knowRestTemplate.postKnow("smoked/detail", map), smoPreId);
    }

    @RequestMapping(value = "acupres/detail", method = RequestMethod.GET)
    @ApiOperation(value = "治疗方案-针灸处方明细")
    @ApiImplicitParam(name = "acuPreId", value = "针灸处方id", paramType = "query", required = true)
    @LogAnnotation("治疗方案-针灸处方明细")
    @ResponseBody
    public ResEntity acupresDetail(String acuPreId) {
        Map<String, Object> map = new HashMap<>();
        map.put("acupreid", acuPreId);

        return tDoctorCollectService.knowDataIsCollect(knowRestTemplate.postKnow("acupres/detail", map), acuPreId);
    }

    @RequestMapping(value = "knowcount/num", method = RequestMethod.GET)
    @ApiOperation(value = "获取知识库各个模块数据总数")
    @ApiImplicitParam(name = "moduleName", value = "moduleName", paramType = "query", required = true)
    @LogAnnotation("获取知识库各个模块数据总数")
    @ResponseBody
    public ResEntity knowCount(String moduleName) {
        Map<String, Object> map = new HashMap<>();
        map.put("name", moduleName);
        return knowRestTemplate.postKnow("knowcount/num", map);
    }

    @RequestMapping(value = "dite/detail", method = RequestMethod.GET)
    @ApiOperation(value = "治疗方案-保健食疗方明细")
    @ApiImplicitParam(name = "diteId", value = "保健食疗id", paramType = "query", required = true)
    @LogAnnotation("治疗方案-保健食疗方明细")
    @ResponseBody
    public ResEntity diteDetail(String diteId) {
        Map<String, Object> map = new HashMap<>();
        map.put("diteid", diteId);
        return knowRestTemplate.postKnow("dite/detail", map);
    }

    @RequestMapping(value = "dite/disease", method = RequestMethod.GET)
    @ApiOperation(value = "治疗方案-疾病饮食注意")
    @ApiImplicitParam(name = "disId", value = "疾病id", paramType = "query", required = true)
    @LogAnnotation("治疗方案-疾病饮食注意")
    @ResponseBody
    public ResEntity diteDisease(String disId) {
        Map<String, Object> map = new HashMap<>();
        map.put("disid", disId);
        return knowRestTemplate.postKnow("dite/disease", map);
    }

    /*************************************方剂检索*******************************************/

    @RequestMapping(value = "pres/list", method = RequestMethod.GET)
    @ApiOperation(value = "方剂检索-处方查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "preName", value = "处方名称(中英)", paramType = "query", required = true),
            @ApiImplicitParam(name = "preType", value = "处方分类，1为方剂，2为经方，不传查所有", paramType = "query"),
            @ApiImplicitParam(name = "sourceType", value = "1.证型方剂2.证型推导3.经方", paramType = "query"),
            @ApiImplicitParam(name = "categoryId", value = "方剂类别ID，不传查所有", paramType = "query"),
            @ApiImplicitParam(name = "effectId", value = "标准功效id", paramType = "query"),
            @ApiImplicitParam(name = "effect", value = "功效", paramType = "query"),
            @ApiImplicitParam(name = "dosId", value = "剂型id", paramType = "query"),
            @ApiImplicitParam(name = "disId", value = "疾病id", paramType = "query"),
            @ApiImplicitParam(name = "disName", value = "疾病名称", paramType = "query")})
    @LogAnnotation("方剂检索-处方查询")
    @ResponseBody
    public ResEntity presList(String preName, String preType, String sourceType, String categoryId, String effectId, String effect, String dosId,
                              String disId, String disName, Page page) {
        String searchtype = AdminUtils.getCurrentShuruma();
        if (StringJudges.isContainChinese(preName)) {
            searchtype = "";
        }
        Map<String, Object> map = new HashMap<>();
        map.put("keyword", preName);
        map.put("searchtype", searchtype);
        map.put("pretype", preType);
        map.put("sourceType", sourceType);
        map.put("categoryid", categoryId);
        map.put("effect", effect);
        map.put("effectid", effectId);
        map.put("dosid", dosId);
        map.put("disid", disId);
        map.put("disname", disName);
        map.put("pagesize", page.getLimit().toString());
        map.put("pageid", page.getPage().toString());
        return tDoctorCollectService.knowDataIsCollect(knowRestTemplate.postKnow("pres/list", map), "pres", "preid");
    }

    @RequestMapping(value = "smoked/list", method = RequestMethod.GET)
    @ApiOperation(value = "方剂检索-熏蒸处方查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "preName", value = "处方名称(中)", paramType = "query", required = true),
            @ApiImplicitParam(name = "disId", value = "疾病id", paramType = "query"),
            @ApiImplicitParam(name = "disName", value = "疾病名称", paramType = "query"),
            @ApiImplicitParam(name = "effect", value = "功效", paramType = "query"),
            @ApiImplicitParam(name = "preUseType", value = "类型id", paramType = "query")
    }
    )
    @LogAnnotation("方剂检索-熏蒸处方查询")
    @ResponseBody
    public ResEntity smokedPresList(String preName, String disId, String disName, String effect, String preUseType, Page page) {
        Map<String, Object> map = new HashMap<>();
        map.put("keyword", preName);
        map.put("disid", disId);
        map.put("disName", disName);
        map.put("effect", effect);
        map.put("preUseType", preUseType);
        map.put("pagesize", page.getLimit().toString());
        map.put("pageid", page.getPage().toString());
        return tDoctorCollectService.knowDataIsCollect(knowRestTemplate.postKnow("smoked/list", map), "pres", "preid");
    }

    @RequestMapping(value = "acupres/list", method = RequestMethod.GET)
    @ApiOperation(value = "方剂检索-查询适宜技术")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "acuPreType", value = "类型", paramType = "query"),
            @ApiImplicitParam(name = "disName", value = "疾病名称", paramType = "query")})
    @LogAnnotation("方剂检索-查询适宜技术")
    @ResponseBody
    public ResEntity smokedPresList(String acuPreType, String disName, Page page) {
        Map<String, Object> map = new HashMap<>();
        map.put("acuPreType", acuPreType);
        map.put("disName", disName);
        map.put("pagesize", page.getLimit().toString());
        map.put("pageid", page.getPage().toString());
        return tDoctorCollectService.knowDataIsCollect(knowRestTemplate.postKnow("acupres/list", map), "pres", "acuPreId");
    }

    /**************************************基础字典*******************************************/

    @RequestMapping(value = "dic/dept", method = RequestMethod.GET)
    @ApiOperation(value = "基础字典-基础科室")
    @LogAnnotation("基础字典-基础科室")
    @ResponseBody
    public Object dept() {
        Map<String, Object> map = new HashMap<>();
        return knowRestTemplate.postKnow("dic/dept", map);
    }

    @RequestMapping(value = "dic/matUseage", method = RequestMethod.GET)
    @ApiOperation(value = "基础字典-中药用法")
    @LogAnnotation("基础字典-中药用法")
    @ResponseBody
    public Object matUseage() {
        Map<String, Object> map = new HashMap<>();
        return knowRestTemplate.postKnow("dic/matuseage", map);
    }

    @RequestMapping(value = "dic/matUnit", method = RequestMethod.GET)
    @ApiOperation(value = "基础字典-中药单位")
    @ApiImplicitParam(name = "unitType", value = "中药单位类型：1为中药，2为中成药", paramType = "query", required = true)
    @LogAnnotation("基础字典-中药单位")
    @ResponseBody
    public Object matUnit(String unitType) {
        Map<String, Object> map = new HashMap<>();
        map.put("unittype", unitType);
        return knowRestTemplate.postKnow("dic/matunit", map);
    }

    @RequestMapping(value = "dic/effect", method = RequestMethod.GET)
    @ApiOperation(value = "基础字典-标准功效")
    @ApiImplicitParam(name = "effecttype", value = "功效类型：1为处方及中成药功效，2为中药功效", paramType = "query", required = true)
    @LogAnnotation("基础字典-标准功效")
    @ResponseBody
    public Object effect(String effecttype) {
        Map<String, Object> map = new HashMap<>();
        map.put("effecttype", effecttype);
        return knowRestTemplate.postKnow("dic/effect", map);
    }

    @RequestMapping(value = "dic/dosagetype", method = RequestMethod.GET)
    @ApiOperation(value = "基础字典-剂型查询")
    @LogAnnotation("基础字典-剂型查询")
    @ResponseBody
    public Object dosagetype() {
        Map<String, Object> map = new HashMap<>();
        return knowRestTemplate.postKnow("dic/dosagetype", map);
    }

    @RequestMapping(value = "dic/category", method = RequestMethod.GET)
    @ApiOperation(value = "基础字典-处方剂型")
    @LogAnnotation("基础字典-处方剂型")
    @ResponseBody
    public Object category() {
        Map<String, Object> map = new HashMap<>();
        return knowRestTemplate.postKnow("dic/category", map);
    }

    @RequestMapping(value = "dic/useType", method = RequestMethod.GET)
    @ApiOperation(value = "基础字典-外用方式")
    @LogAnnotation("基础字典-外用方式")
    @ResponseBody
    public Object useType() {
        Map<String, Object> map = new HashMap<>();
        return knowRestTemplate.postKnow("dic/useType", map);
    }

    /**************************************合理用药*******************************************/

    @RequestMapping(value = "check/mat", method = RequestMethod.GET)
    @ApiOperation(value = "合理用药-中药检测", notes = "对单味中药做合理用药检测")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "disId", value = "中医病名ID", paramType = "query"),
            @ApiImplicitParam(name = "symId", value = "证型ID", paramType = "query"),
            @ApiImplicitParam(name = "gravidity", value = "是否怀孕：Y为怀孕，N为没有怀孕", paramType = "query"),
            @ApiImplicitParam(name = "matId", value = "药品id", paramType = "query", required = true)})
    @LogAnnotation("合理用药-中药检测")
    @ResponseBody
//    @Cacheable(value = "know::check", keyGenerator = "cacheKeyGenerator")
    public ResEntity checkMat(String disId, String symId, String matId) {
        Map<String, Object> map = new HashMap<>();
        map.put("matid", matId);
        map.put("disid", disId);
        map.put("symid", symId);

        return knowRestTemplate.postKnow("check/mat", map);
    }

    @RequestMapping(value = "check/pres", method = RequestMethod.POST)
    @ApiOperation(value = "合理用药-中药处方检测", notes = "对整个中药处方做合理用药检测")
    @LogAnnotation("合理用药-中药处方检测")
    @ResponseBody
//    @Cacheable(value = "know::check", keyGenerator = "cacheKeyGenerator")
    public ResEntity checkPres(@RequestBody KnowCheckPres knowCheckPres) {
        Map<String, Object> map = new HashMap<>();
        map.put("pres", knowCheckPres.getPres());
        map.put("disid", knowCheckPres.getDisId());
        map.put("symid", knowCheckPres.getSymId());
        //knowRestTemplate.postKnow("check/pres", map);

        return knowRestTemplate.postKnow("check/pres", map);
    }

    @RequestMapping(value = "check/result", method = RequestMethod.POST)
    @ApiOperation(value = "合理用药-中药处方检测报告", notes = "处方保存时对整个中药处方做合理用药检测报告")
    @LogAnnotation("合理用药-中药处方检测报告")
    @ResponseBody
//    @Cacheable(value = "know::check", keyGenerator = "cacheKeyGenerator")
    public ResEntity checkResult(@RequestBody KnowCheckPres knowCheckPres) {
        Map<String, Object> map = new HashMap<>();
        map.put("pres", knowCheckPres.getPres());
        map.put("disid", knowCheckPres.getDisId());
        map.put("symid", knowCheckPres.getSymId());

        return knowRestTemplate.postKnow("check/result", map);
    }


    /**************************************中药*******************************************/

    @RequestMapping(value = "mat/list", method = RequestMethod.GET)
    @ApiOperation(value = "中药-中药查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "matName", value = "中药名称(中英)", paramType = "query"),
            @ApiImplicitParam(name = "effectId", value = "标准功效id", paramType = "query"),
            @ApiImplicitParam(name = "efficacy", value = "功效", paramType = "query"),
            @ApiImplicitParam(name = "flavourId", value = "性味id", paramType = "query"),
            @ApiImplicitParam(name = "meridianId", value = "归经id", paramType = "query"),
            @ApiImplicitParam(name = "disId", value = "疾病id", paramType = "query"),
            @ApiImplicitParam(name = "disName", value = "疾病名称", paramType = "query")})
    @LogAnnotation("中药-中药查询")
    @StatisticsFunction(source = "", value = "")
    @ResponseBody
    public ResEntity matList(String matName, String flavourId, String meridianId, String effectId, String efficacy, String disId,
                             String disName, Page page) {
        String searchtype = AdminUtils.getCurrentShuruma();
        if (StringJudges.isContainChinese(matName)) {
            searchtype = "";
        }
        Map<String, Object> map = new HashMap<>();
        map.put("keyword", matName);
        map.put("searchtype", searchtype);
        map.put("effectid", effectId);
        map.put("efficacy", efficacy);
        map.put("flavourid", flavourId);
        map.put("meridianid", meridianId);
        map.put("disid", disId);
        map.put("disname", disName);
        map.put("pagesize", page.getLimit().toString());
        map.put("pageid", page.getPage().toString());
        return tDoctorCollectService.knowDataIsCollect(knowRestTemplate.postKnow("mat/list", map), "mats", "matid");
    }

    @RequestMapping(value = "mat/detail", method = RequestMethod.GET)
    @ApiOperation(value = "中药-中药详情")
    @ApiImplicitParam(name = "matId", value = "药品id", paramType = "query", required = true)
    @LogAnnotation("中药-中药详情")
    @ResponseBody
    @StatisticsFunction2(classify = "中医知识库", function = "中药查询")
    public ResEntity matDetail(String matId) {

        Map<String, Object> map = new HashMap<>();
        map.put("matid", matId);
        return tDoctorCollectService.knowDataIsCollect(knowRestTemplate.postKnow("mat/detail", map), matId);
    }

    /**************************************中成药*******************************************/

    @RequestMapping(value = "patentDrug/list", method = RequestMethod.GET)
    @ApiOperation(value = "中成药-中成药查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "drugName", value = "中成药名称(中英)", paramType = "query"),
            @ApiImplicitParam(name = "effectId", value = "标准功效id", paramType = "query"),
            @ApiImplicitParam(name = "efficacy", value = "功效", paramType = "query"),
            @ApiImplicitParam(name = "isPre", value = "是否处方药：1为处方药，2为OTC", paramType = "query"),
            @ApiImplicitParam(name = "dosId", value = "剂型id", paramType = "query"),
            @ApiImplicitParam(name = "deptId", value = "剂型id", paramType = "query"),
            @ApiImplicitParam(name = "disId", value = "疾病id", paramType = "query"),
            @ApiImplicitParam(name = "disName", value = "疾病名称", paramType = "query")})
    @LogAnnotation("中成药-中成药查询")
    @ResponseBody
    public ResEntity patentDrugList(String drugName, String isPre, String efficacy, String dosId, String deptId, String effectId, String disId, String disName, Page page) {
        String searchtype = AdminUtils.getCurrentShuruma();
        if (StringJudges.isContainChinese(drugName)) {
            searchtype = "";
        }
        Map<String, Object> map = new HashMap<>();
        map.put("keyword", drugName);
        map.put("searchtype", searchtype);
        map.put("effectid", effectId);
        map.put("efficacy", efficacy);
        map.put("ispre", isPre);
        map.put("dosid", dosId);
        map.put("deptid", deptId);
        map.put("disid", disId);
        map.put("disname", disName);
        map.put("pagesize", page.getLimit().toString());
        map.put("pageid", page.getPage().toString());
        return tDoctorCollectService.knowDataIsCollect(knowRestTemplate.postKnow("patentdrug/list", map), "drugs", "drugid");
    }

    @RequestMapping(value = "patentDrug/detail", method = RequestMethod.GET)
    @ApiOperation(value = "中成药-中成药详情")
    @ApiImplicitParam(name = "drugId", value = "中成药id", paramType = "query", required = true)
    @LogAnnotation("中成药-中成药详情")
    @ResponseBody
    @StatisticsFunction2(classify = "中医知识库", function = "中成药")
    public ResEntity patentDrugDetail(String drugId) {

        Map<String, Object> map = new HashMap<>();
        map.put("drugid", drugId);
        return tDoctorCollectService.knowDataIsCollect(knowRestTemplate.postKnow("patentdrug/detail", map), drugId);
    }

    /**************************************经络*******************************************/

    @RequestMapping(value = "meridian/list", method = RequestMethod.GET)
    @ApiOperation(value = "2.9.3 经络查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "keyword", value = "搜索关键词（经络名称），如果不传默认查询所有", paramType = "query")
            , @ApiImplicitParam(name = "mertype", value = "类型：1为十二经脉，2为奇经八脉，3为经外奇穴", paramType = "query")
            , @ApiImplicitParam(name = "pagesize", value = "获取条数，不传默认20条", paramType = "query", dataType = "String")
            , @ApiImplicitParam(name = "pageid", value = "分页页码，整数类型", paramType = "query", dataType = "String")
    })
    @LogAnnotation("2.9.3 经络查询")
    @ResponseBody
    public ResEntity meridianList(String keyword, String mertype, String pagesize, String pageid) {

        String searchtype = AdminUtils.getCurrentShuruma();
        if (StringJudges.isContainChinese(keyword)) {
            searchtype = "";
        }

        Map<String, Object> map = new HashMap<>();
        map.put("keyword", keyword);
        map.put("searchtype", searchtype);
        map.put("mertype", mertype);
        map.put("pagesize", pagesize);
        map.put("pageid", pageid);
        return knowRestTemplate.postKnow("meridian/list", map);
    }

    @RequestMapping(value = "meridian/detail", method = RequestMethod.GET)
    @ApiOperation(value = "2.9.4 经络详情")
    @ApiImplicitParam(name = "merid", value = "经络id", paramType = "query", dataType = "String", required = true)
    @LogAnnotation("2.9.4 经络详情")
    @ResponseBody
    @StatisticsFunction2(classify = "中医知识库", function = "经络穴位查询")
    public ResEntity meridianDetail(String merid) {

        Map<String, Object> map = new HashMap<>();
        map.put("merid", merid);
        return knowRestTemplate.postKnow("meridian/detail", map);
    }

    /**************************************穴位*******************************************/

    @RequestMapping(value = "acupoint/list", method = RequestMethod.GET)
    @ApiOperation(value = "2.9.1 穴位查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "keyword", value = "搜索关键词（穴位名称），如果不传默认查询所有", paramType = "query")
            , @ApiImplicitParam(name = "merid", value = "经络ID", paramType = "query")
            , @ApiImplicitParam(name = "pagesize", value = "获取条数，不传默认20条", paramType = "query", dataType = "String")
            , @ApiImplicitParam(name = "pageid", value = "分页页码，整数类型", paramType = "query", dataType = "String")
    })
    @LogAnnotation("2.9.1 穴位查询")
    @ResponseBody
    public ResEntity acuList(String keyword, String merid, String pagesize, String pageid) {

        String searchtype = AdminUtils.getCurrentShuruma();
        if (StringJudges.isContainChinese(keyword)) {
            searchtype = "";
        }

        Map<String, Object> map = new HashMap<>();
        map.put("keyword", keyword);
        map.put("searchtype", searchtype);
        map.put("merid", merid);
        map.put("pagesize", pagesize);
        map.put("pageid", pageid);
        return tDoctorCollectService.knowDataIsCollect(knowRestTemplate.postKnow("acupoint/list", map), "acupoints", "acuid");
    }

    @RequestMapping(value = "acupoint/detail", method = RequestMethod.GET)
    @ApiOperation(value = "2.9.2 穴位详情")
    @ApiImplicitParam(name = "acuid", value = "穴位id", paramType = "query", dataType = "String", required = true)
    @LogAnnotation("2.9.2 穴位详情")
    @ResponseBody
    @StatisticsFunction2(classify = "中医知识库", function = "经络穴位查询")
    public ResEntity acuDetail(String acuid) {

        Map<String, Object> map = new HashMap<>();
        map.put("acuid", acuid);
        return tDoctorCollectService.knowDataIsCollect(knowRestTemplate.postKnow("acupoint/detail", map), acuid);
    }

    /**************************************验案*******************************************/

    @RequestMapping(value = "verify/list", method = RequestMethod.GET)
    @ApiOperation(value = "2.5.1验案查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "keyword", value = "疾病名称搜索，如果不传默认查询所有(后台处理中英文五笔)", paramType = "query")
            //   ,@ApiImplicitParam(name = "searchtype", value = "搜索类型， 1为拼音码，2为五笔码 ，搜索中文时可传空", paramType = "query")
            , @ApiImplicitParam(name = "disid", value = "疾病ID", paramType = "query")
            , @ApiImplicitParam(name = "symid", value = "证型ID", paramType = "query", dataType = "String")
            , @ApiImplicitParam(name = "attendingexpert", value = "主治专家，中文搜索", paramType = "query", dataType = "String")
            , @ApiImplicitParam(name = "bookid", value = "书籍ID", paramType = "query", dataType = "String")
            , @ApiImplicitParam(name = "book", value = "书籍", paramType = "query", dataType = "String")
            , @ApiImplicitParam(name = "pagesize", value = "每页条数，整数类型，建议每次分页的条数最好统一，不然数据会有遗漏或者重复", paramType = "query", dataType = "String")
            , @ApiImplicitParam(name = "pageid", value = "分页页码，整数类型", paramType = "query", dataType = "String")
    })
    @LogAnnotation("2.5.1验案查询")
    @ResponseBody
    public ResEntity verifyList(String keyword, String disid, String symid, String attendingexpert, String bookid, String book, String pagesize, String pageid) {
        String searchtype = "";
        String currentExtContent = AdminUtils.getCurrentShuruma();
        if (!StringJudges.isContainChinese(keyword) && "1".equals(currentExtContent)) {
            searchtype = "1";
        } else if (!StringJudges.isContainChinese(keyword) && "2".equals(currentExtContent)) {
            searchtype = "2";
        }
        Map<String, Object> map = new HashMap<>();
        map.put("keyword", keyword);
        map.put("searchtype", searchtype);
        map.put("disid", disid);
        map.put("symid", symid);
        map.put("attendingexpert", attendingexpert);
        map.put("bookid", bookid);
        map.put("book", book);
        map.put("pagesize", pagesize);
        map.put("pageid", pageid);
        return tDoctorCollectService.knowDataIsCollect(knowRestTemplate.postKnow("verify/list", map), "verifys", "verifyid");
    }

    @RequestMapping(value = "verify/listByAi", method = RequestMethod.POST)
    @ApiOperation(value = "2.5.1验案查询通过ai获取验案")
    @LogAnnotation("2.5.1验案查询通过ai获取验案")
    @ResponseBody
    public ResEntity verifyListByAi(@RequestBody CueWordURLRequest cueWordURLRequest) {
//        if (StringUtils.isBlank(cueWordURLRequest.getContext())) {
//            return ResEntity.error("请输入病历内容");
//        }
        GptAPIRes yiAnList = sySCueWordService.getYiAnList(cueWordURLRequest);
        if (yiAnList != null && yiAnList.getData() != null) {
            List<GptAPIResItem> data = yiAnList.getData();
            //把GptAPIResItem 中的content 取出来 拼接字符串逗号分隔
            StringBuilder stringBuilder = new StringBuilder();
            for (GptAPIResItem gptAPIResItem : data) {
                stringBuilder.append(gptAPIResItem.getContent()).append(",");
            }
            Map<String, Object> map = new HashMap<>();
            map.put("ids", stringBuilder.toString());
            ResEntity resEntity = knowRestTemplate.postKnow("verify/listbyids", map);
            verifyListByAiAddMatchingDegree(resEntity, yiAnList.getData());
            return tDoctorCollectService.knowDataIsCollect(resEntity, "verifys", "verifyid");
        }
        return ResEntity.error("大模型未返回数据");
    }

    public void verifyListByAiAddMatchingDegree(ResEntity resEntity, List<GptAPIResItem> data) {
        LinkedHashMap<String, Object> data1 = (LinkedHashMap<String, Object>) resEntity.getData();
        ArrayList<LinkedHashMap<String, Object>> dataList = (ArrayList<LinkedHashMap<String, Object>>) data1.get("verifys");
        for (LinkedHashMap<String, Object> dataLstOne : dataList) {
            for (GptAPIResItem datum : data) {
                if (dataLstOne.get("verifyid").equals(datum.getContent())) {
                    dataLstOne.put("matchingDegree", datum.getScore());
                }
            }
        }
        //对dataList中的matchingDegree字段值大小进行降序排序
        dataList.sort((o1, o2) -> {
            Double score1 = Double.valueOf(o1.get("matchingDegree").toString());
            Double score2 = Double.valueOf(o2.get("matchingDegree").toString());
            return score2.compareTo(score1);
        });

    }

    @RequestMapping(value = "verify/detail", method = RequestMethod.GET)
    @ApiOperation(value = "2.5.2验案详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "verifyid", value = "验案id", paramType = "query")
    })
    @LogAnnotation("2.5.2验案详情")
    @ResponseBody
    @StatisticsFunction2(classify = "中医知识库", function = "参考医案")
    public ResEntity verifyDetail(String verifyid) {
        Map<String, Object> map = new HashMap<>();
        map.put("verifyid", verifyid);

        return tDoctorCollectService.knowDataIsCollect(knowRestTemplate.postKnow("verify/detail", map), verifyid);
    }

    @RequestMapping(value = "verify/pres", method = RequestMethod.GET)
    @ApiOperation(value = "2.5.3验案处方明细")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "verifyid", value = "验案处方id", paramType = "query")
    })
    @LogAnnotation("2.5.3验案处方明细")
    @ResponseBody
    public ResEntity verifyPres(String verifyid) {
        Map<String, Object> map = new HashMap<>();
        map.put("verfjid", verifyid);
        return knowRestTemplate.postKnow("verify/pres", map);
    }

    /**************************************中西医对照*******************************************/


    @RequestMapping(value = "icd/list", method = RequestMethod.GET)
    @ApiOperation(value = "2.13.1 西医疾病获取")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "keyword", value = "搜索关键词（疾病名称），如果不传默认查询所有", paramType = "query", required = true),
            @ApiImplicitParam(name = "westcode", value = "编码", paramType = "query")
    })
    @LogAnnotation("2.13.1 西医疾病获取")
    @ResponseBody
    public ResEntity westDiseaseList(String keyword, String westcode, Page page) {
        String searchtype = AdminUtils.getCurrentShuruma();
        if (StringJudges.isContainChinese(keyword)) {
            searchtype = "";
        }

        Map<String, Object> map = new HashMap<>();
        map.put("keyword", keyword);
        map.put("searchtype", searchtype);
        map.put("westcode", westcode);
        map.put("pagesize", page.getLimit().toString());
        map.put("pageid", page.getPage().toString());
        TSysParam param = tSysParamService.getSysParam(Constant.WEST_DIS_VISION);
        if (param != null && StringUtils.isNotBlank(param.getParValues())) {
            map.put("westVision", param.getParValues());
        }
        return knowRestTemplate.postKnow("icd/list", map);
    }

    @RequestMapping(value = "icd/detail", method = RequestMethod.GET)
    @ApiOperation(value = "2.13.1 西医疾病获取")
    @ApiImplicitParam(name = "westcode", value = "编码", paramType = "query")
    @LogAnnotation("2.13.1 西医疾病获取")
    @ResponseBody
    public ResEntity westDiseaseDetail(String westcode) {

        Map<String, Object> map = new HashMap<>();
        map.put("westcode", westcode);
        TSysParam param = tSysParamService.getSysParam(Constant.WEST_DIS_VISION);
        if (param != null && StringUtils.isNotBlank(param.getParValues())) {
            map.put("westVision", param.getParValues());
        }
        return knowRestTemplate.postKnow("icd/detail", map);
    }

    @RequestMapping(value = "disease/cw", method = RequestMethod.GET)
    @ApiOperation(value = "2.13.2中西医病名对照", notes = "可以通过西医病名查询对照的中医病名，也可以通过中医病名查询对照的西医病名")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "keyword", value = "疾病名称", paramType = "query"),
            @ApiImplicitParam(name = "westCode", value = "西医疾病ID", paramType = "query"),
            @ApiImplicitParam(name = "disId", value = "中医疾病ID", paramType = "query")
    })
    @LogAnnotation("2.13.2中西医病名对照")
    @ResponseBody
    public ResEntity diseaseCw(String keyword, String westCode, String disId) {
        String searchtype = AdminUtils.getCurrentShuruma();
        if (StringJudges.isContainChinese(keyword)) {
            searchtype = "";
        }

        Map<String, Object> map = new HashMap<>();
        map.put("keyword", keyword);
        map.put("searchtype", searchtype);
        map.put("westCode", westCode);
        map.put("disId", disId);
        return knowRestTemplate.postKnow("disease/cw", map);
    }

    /**************************************体质辨识*******************************************/

    @RequestMapping(value = "analysis/group", method = RequestMethod.GET)
    @ApiOperation(value = "2.12.1体质辨识问卷", notes = "2.12.1体质辨识问卷")
    @ApiImplicitParam(name = "analyType", value = "1标准2公卫", paramType = "query")
    @LogAnnotation("2.12.1体质辨识问卷")
    @StatisticsFunction(source = "中医体质辨识", value = "使用次数")
    @ResponseBody
    public ResEntity analysisGroup(String analyType) {
        Map<String, Object> map = new HashMap<>();
        map.put("analyType", analyType);
        return knowRestTemplate.postKnow("analysis/group", map);
    }

    @Autowired
    private TUserAnalysisResultService tUserAnalysisResultService;

    @RequestMapping(value = "analysis/result", method = RequestMethod.POST)
    @ApiOperation(value = "2.12.2体质辨识结果", notes = "2.12.2体质辨识结果")
    @LogAnnotation("2.12.2体质辨识结果")
    @ResponseBody
    public ResEntity analysisResult(@RequestBody Map<String, Object> answers) {

        if (null == answers.get("answers") && StringUtils.isBlank(answers.get("answers").toString())) {
            return ResEntity.error("问卷内容不能为空");
        }
        Object type = answers.get("analyType");
        Integer analyType = type instanceof Integer ? (Integer) type : type instanceof String ? Integer.parseInt((String) type) : 1;
        List<Map<String, Object>> params = (List<Map<String, Object>>) answers.get("answers");

        Map<String, Object> rmap = new HashMap<>();
        rmap.put("answers", JSON.toJSONString(params));
        rmap.put("analyType", analyType.toString());
        ResEntity entity = knowRestTemplate.postKnow("analysis/result", rmap);
        if (entity.getStatus()) {
            Map<String, Object> analyResult = (Map<String, Object>) entity.getData();
            String patientId = (String) answers.get("patientId");
            return tUserAnalysisResultService.insert(params, analyResult, patientId, analyType);
        }
        return entity;
    }

    @RequestMapping(value = "analysis/resultInsert", method = RequestMethod.POST)
    @ApiOperation(value = "体质辨识结果插入", notes = "体质辨识结果插入")
    @LogAnnotation("体质辨识结果插入")
    @ResponseBody
    public ResEntity analysisResultInsert(@RequestBody Map<String, Object> map) {
        return tUserAnalysisResultService.insertNeza(map);
    }

    @RequestMapping(value = "analysis/resultByName", method = RequestMethod.GET)
    @ApiOperation(value = "2.12.3通过名字获取体质辨识结果", notes = "2.12.3通过名字获取体质辨识结果")
    @LogAnnotation("2.12.3通过名字获取体质辨识结果")
    @ResponseBody
    public ResEntity GetAnalResByName(String analysisName, String analysisId) {

        if (StringUtils.isBlank(analysisName) && StringUtils.isBlank(analysisId)) {
            return ResEntity.error("辨识名和ID不能为空");
        }
        Map<String, Object> rmap = new HashMap<>();
        rmap.put("groupName", analysisName);
        ResEntity entity = knowRestTemplate.postKnow("analysis/group/scheme", rmap);
        Map<String, Object> analyResult = new HashMap<>();
        if (entity.getStatus()) {
            analyResult = (Map<String, Object>) entity.getData();
        }
        return tUserAnalysisResultService.getAnalResByName(analyResult, analysisId);
    }

    /**************************************舌诊脉象*******************************************/

    @RequestMapping(value = "pulsecondition/detail", method = RequestMethod.GET)
    @ApiOperation(value = "舌诊脉象-查询脉象详情", notes = "舌诊脉象-查询脉象详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pulseId", value = "脉象ID", paramType = "query", required = true)
    })
    @LogAnnotation("舌诊脉象-查询脉象详情")
    @ResponseBody
    @StatisticsFunction2(classify = "中医知识库", function = "脉诊")
    public ResEntity getPulseDetailById(String pulseId) {
        Map<String, Object> map = new HashMap<>();
        map.put("pulseId", pulseId);
        return knowRestTemplate.postKnow("pulsecondition/detail", map);
    }

    @RequestMapping(value = "pulsecondition/list", method = RequestMethod.GET)
    @ApiOperation(value = "舌诊脉象-查询脉位下的所有脉象", notes = "舌诊脉象-查询脉位下的所有脉象")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pulseType", value = "脉类", paramType = "query", required = true)
    })
    @LogAnnotation("舌诊脉象-查询脉位下的所有脉象")
    @ResponseBody
    public ResEntity getPulseNameListByType(String pulseType) {
        Map<String, Object> map = new HashMap<>();
        map.put("pulseType", pulseType);
        return knowRestTemplate.postKnow("pulsecondition/list", map);
    }

    @RequestMapping(value = "tonguediagnosis/searchtongue", method = RequestMethod.GET)
    @ApiOperation(value = "舌诊脉象-查询所有舌象")
    @ApiImplicitParams(
            @ApiImplicitParam(name = "tongueName", value = "舌象(中英)", paramType = "query", required = true)
    )
    @LogAnnotation("舌诊脉象-查询所有舌象")
    @ResponseBody
    public ResEntity searchtongue(String tongueName, String pageid, String pagesize) {
        String searchtype = AdminUtils.getCurrentShuruma();
        if (StringJudges.isContainChinese(tongueName)) {
            searchtype = "";
        }
        Map<String, Object> map = new HashMap<>();
        map.put("keyword", tongueName);
        map.put("searchtype", searchtype);
        map.put("pagesize", pagesize);
        map.put("pageid", pageid);
        return knowRestTemplate.postKnow("tonguediagnosis/searchtongue", map);
    }


    @RequestMapping(value = "tonguediagnosis/alltongue", method = RequestMethod.GET)
    @ResponseBody
    @LogAnnotation("舌诊脉象-根据一二级分类查询舌诊")
    @ApiOperation(value = "舌诊脉象-根据一二级分类查询舌诊", notes = "舌诊脉象-根据一二级分类查询舌诊")
    @ApiImplicitParams({@ApiImplicitParam(name = "pClassification", value = "一级分类", paramType = "query", required = true),
            @ApiImplicitParam(name = "sClassification", value = "二级分类", paramType = "query", required = true)})
    public ResEntity getTongueNameListByClass(String pClassification, String sClassification) {
        Map<String, Object> map = new HashMap<>();
        map.put("pClassification", pClassification);
        map.put("sClassification", sClassification);
        return knowRestTemplate.postKnow("tonguediagnosis/alltongue", map);
    }

    @ResponseBody
    @RequestMapping(value = "tonguediagnosis/detail", method = RequestMethod.GET)
    @ApiOperation(value = "舌诊脉象-查询舌诊详情")
    @ApiImplicitParam(name = "tongueId", value = "舌象id", paramType = "query", required = true)
    @StatisticsFunction2(classify = "中医知识库", function = "舌诊")
    public ResEntity getTongueDetailById(String tongueId) {
        Map<String, Object> map = new HashMap<>();
        map.put("tongueId", tongueId);
        ResEntity resEntity = knowRestTemplate.postKnow("tonguediagnosis/detail", map);
        return tDoctorCollectService.knowDataIsCollect(resEntity, "tongueNameList", "tongueId");
    }


    /**************************************临床诊疗指南*******************************************/


    @LogAnnotation("临床诊疗指南-获取临床诊疗指南已有的部门列表")
    @RequestMapping(value = "guide/getDeptList", method = RequestMethod.GET)
    @ApiOperation(value = "临床诊疗指南-获取临床诊疗指南已有的部门列表", notes = "临床诊疗指南-获取临床诊疗指南已有的部门列表")
    @ResponseBody
    public ResEntity getDeptList() {
        Map<String, Object> map = new HashMap<>();
        return knowRestTemplate.postKnow("guide/getDeptList", map);
    }

    @LogAnnotation("临床诊疗指南-获取临床诊疗指南部门下的所有疾病")
    @RequestMapping(value = "guide/getGuideDeptList", method = RequestMethod.GET)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "deptId", value = "deptId", paramType = "query", required = true),
            @ApiImplicitParam(name = "disName", value = "disName", paramType = "query")
    })
    @ApiOperation(value = "临床诊疗指南-获取临床诊疗指南部门下的所有疾病", notes = "临床诊疗指南-获取临床诊疗指南部门下的所有疾病")
    @ResponseBody
    public ResEntity getGuideDeptList(String deptId, String disName, Page page) {
        Map<String, Object> map = new HashMap<>();
        map.put("deptId", deptId);
        map.put("dn", disName);
        map.put("pagesize", page.getLimit().toString());
        map.put("pageid", page.getPage().toString());
        return tDoctorCollectService.knowDataIsCollect(knowRestTemplate.postKnow("guide/getGuideDeptList", map), "diseases", "ID,disId");

    }

    @LogAnnotation("临床诊疗指南-获取临床诊疗指南列表")
    @RequestMapping(value = {"guide/getList"}, method = {RequestMethod.GET})
    @ApiImplicitParams({@ApiImplicitParam(name = "deptId", value = "deptId", paramType = "query", required = true),
            @ApiImplicitParam(name = "disName", value = "disName", paramType = "query")})
    @ApiOperation(value = "临床诊疗指南-获取临床诊疗指南列表", notes = "临床诊疗指南-获取临床诊疗指南列表")
    @ResponseBody
    public ResEntity getList(String deptId, String disName, Page page) {
        Map<String, Object> map = new HashMap<>();
        map.put("deptId", deptId);
        map.put("dn", disName);
        map.put("pagesize", page.getLimit().toString());
        map.put("pageid", page.getPage().toString());
        return this.tDoctorCollectService.knowDataIsCollect(this.knowRestTemplate.postKnow("guide/getList", map), "guides", "id");
    }

    @RequestMapping(value = "disease/more", method = RequestMethod.GET)
    @ApiOperation(value = "临床诊疗指南-临床诊疗多版本查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "disId", value = "疾病ID", paramType = "query", required = true),
            @ApiImplicitParam(name = "deptId", value = "科室ID", paramType = "query")})
    @LogAnnotation("临床诊疗指南-临床诊疗多版本查询")
    @ResponseBody
    public ResEntity diseaseMore(String disId, String deptId) {
        Map<String, Object> map = new HashMap<>();
        map.put("disId", disId);
        map.put("deptid", deptId);
        return knowRestTemplate.postKnow("disease/more", map);
    }

    @RequestMapping(value = "disease/moredetail", method = RequestMethod.GET)
    @ApiOperation(value = "临床诊疗指南-临床诊疗方案详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "guideId", value = "临床诊疗ID", paramType = "query", required = true),
            @ApiImplicitParam(name = "deptId", value = "科室ID", paramType = "query"),
            @ApiImplicitParam(name = "disId", value = "疾病ID", paramType = "query")})
    @LogAnnotation("临床诊疗指南-临床诊疗方案详情")
    @ResponseBody
    public ResEntity diseaseMoreDetail(String guideId, String deptId, String disId) {
        Map<String, Object> map = new HashMap<>();
        map.put("guideId", guideId);
        map.put("deptid", deptId);
        map.put("disId", disId);
        return knowRestTemplate.postKnow("disease/moredetail", map);
    }

    @RequestMapping(value = "book/classify", method = RequestMethod.GET)
    @ApiOperation(value = "古籍管理-查询古籍分类列表")
    @LogAnnotation("古籍管理-查询古籍分类列表")
    @ResponseBody
    public Object getClassifyList() {
        Map<String, Object> map = new HashMap<>();
        return knowRestTemplate.postKnow("book/classify", map);
    }

    @RequestMapping(value = "book/special", method = RequestMethod.GET)
    @ApiOperation(value = "古籍管理-中医入门")
    @LogAnnotation("古籍管理-中医入门")
    @ResponseBody
    public Object getSpecialBooks() {
        Map<String, Object> map = new HashMap<>();
        return knowRestTemplate.postKnow("book/special", map);
    }

    @RequestMapping(value = "catalogue/content", method = RequestMethod.GET)
    @ApiOperation(value = "古籍管理-查询古籍章节正文")
    @LogAnnotation("古籍管理-查询古籍章节正文")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "bookId", value = "古籍ID", paramType = "query", required = true),
            @ApiImplicitParam(name = "catalogueId", value = "目录ID", paramType = "query", required = true)})
    @ResponseBody
    public Object getCatalogueContent(String bookId, int catalogueId) {
        Map<String, Object> map = new HashMap<>();
        map.put("bookId", bookId);
        map.put("catalogueId", catalogueId);
        return knowRestTemplate.postKnow("catalogue/content", map);
    }


    @RequestMapping(value = "classify/bookList", method = RequestMethod.GET)
    @ApiOperation(value = "古籍管理-按分类返回书籍信息")
    @LogAnnotation("古籍管理-按分类返回书籍信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "classifyId", value = "分类ID", paramType = "query"),
            @ApiImplicitParam(name = "bookName", value = "书名", paramType = "query"),
            @ApiImplicitParam(name = "author", value = "作者", paramType = "query"),
            @ApiImplicitParam(name = "page", value = "页码", paramType = "query"),
            @ApiImplicitParam(name = "limit", value = "行数", paramType = "query")})
    @ResponseBody
    public Object getBookList(String classifyId, String bookName, String author, String page, String limit) {
        Map<String, Object> map = new HashMap<>();
        map.put("classifyId", classifyId);
        map.put("bookName", bookName);
        map.put("author", author);
        map.put("page", page);
        map.put("limit", limit);
        return knowRestTemplate.postKnow("classify/bookList", map);
    }


    @RequestMapping(value = "classify/book", method = RequestMethod.GET)
    @ApiOperation(value = "古籍管理-查询古籍明细")
    @LogAnnotation("古籍管理-查询古籍明细")
    @ApiImplicitParam(name = "bookId", value = "古籍ID", paramType = "query")
    @ResponseBody
    @StatisticsFunction2(classify = "中医知识库", function = "古书籍")
    public Object getBook(String bookId) {
        Map<String, Object> map = new HashMap<>();
        map.put("bookId", bookId);
        return knowRestTemplate.postKnow("classify/book", map);
    }

    @RequestMapping(value = "book/catalogue", method = RequestMethod.GET)
    @ApiOperation(value = "古籍管理-查询古籍目录列表")
    @LogAnnotation("古籍管理-查询古籍目录列表")
    @ApiImplicitParam(name = "bookId", value = "古籍ID", paramType = "query", required = true)
    @ResponseBody
    public Object getBookCatalogue(String bookId) {
        Map<String, Object> map = new HashMap<>();
        map.put("bookId", bookId);
        return knowRestTemplate.postKnow("book/catalogue", map);
    }


    @RequestMapping(value = "bPrescriptionChange/changelist", method = RequestMethod.GET)
    @ApiOperation(value = "查询兼证-查询兼证加减")
    @LogAnnotation("查询兼证加减")
    @ResponseBody
    public Object getPrescriptionChangeListByGroup() {
        Map<String, Object> map = new HashMap<>();
        return knowRestTemplate.postKnow("bPrescriptionChange/changelist", map);
    }

    @RequestMapping(value = "bPrescriptionChange/changeItems", method = RequestMethod.GET)
    @ApiOperation(value = "根据id查询兼证加减药")
    @LogAnnotation("查询兼证-根据id查询兼证加减药")
    @ResponseBody
    public Object getPrescriptionChangeItemByPreChaId(String preChaId) {
        Map<String, Object> map = new HashMap<>();
        map.put("preChaId", preChaId);
        return knowRestTemplate.postKnow("bPrescriptionChange/changeItems", map);
    }
}
