package com.jiuzhekan.cbkj.controller.business.setting;

import com.jiuzhekan.cbkj.beans.business.record.TBusinessProposal;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.common.annotaionUtil.StatisticsFunction;
import com.jiuzhekan.cbkj.common.utils.Page;
import com.jiuzhekan.cbkj.service.business.setting.TBusinessProposalService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

@Api(value = "建议接口", tags = "建议接口")
@Controller
@RequestMapping("tBusinessProposal/")
public class TBusinessProposalController {
    @Autowired
    private TBusinessProposalService tBusinessProposalService;

    @RequestMapping(value = "getPages", method = RequestMethod.GET)
    @ApiOperation(value = "分页查询-建议收集列表", notes = "分页查询-建议收集列表", response = TBusinessProposal.class)
    @ResponseBody
    @LogAnnotation("分页查询建议表")
    public Object getApps(TBusinessProposal tBusinessProposal, Page page) {
        Object obj = tBusinessProposalService.getPageDatas(tBusinessProposal, page);
        return obj;
    }

    @RequestMapping(value = "getMyProposalS", method = RequestMethod.GET)
    @ApiOperation(value = "分页查询-我的建议列表", notes = "分页查询-我的建议列表", response = TBusinessProposal.class)
    @ResponseBody
    @LogAnnotation("分页查询建议表")
    public Object getMyProposalS(TBusinessProposal tBusinessProposal, Page page) {
        Object obj = tBusinessProposalService.getMyProposalS(tBusinessProposal, page);
        return obj;
    }

    @RequestMapping(value = "update/findObj", method = RequestMethod.GET)
    @LogAnnotation("根据id加载建议表详情")
    @ApiOperation(value = "根据id加载建议表详情", notes = "根据id加载建议表详情", response = TBusinessProposal.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "根据主键id查询单条数据返回", dataType = "String", paramType = "query", required = true)
    })
    @ResponseBody
    public Object getObj(String id) {
        ResEntity result = tBusinessProposalService.findObj(id);
        return result;
    }

    @RequestMapping(value = "insert", method = RequestMethod.POST)
    @LogAnnotation(value = "新增建议", isWrite = true)
    @StatisticsFunction(source = "设置", value = "建议数")
    @ApiOperation(value = "新增建议", notes = "新增建议")
    @ResponseBody
    public Object insert(@RequestBody TBusinessProposal tBusinessProposal) {
        ResEntity result = tBusinessProposalService.insert(tBusinessProposal);
        return result;
    }

    @RequestMapping(value = "update", method = RequestMethod.POST)
    @ApiOperation(value = "修改建议表", notes = "修改建议表")
    @LogAnnotation(isWrite = true, value = "修改建议表")
    @ResponseBody
    public Object update(@RequestBody TBusinessProposal tBusinessProposal) {
        ResEntity result = tBusinessProposalService.update(tBusinessProposal);
        return result;
    }

    @RequestMapping(value = "deleteLis", method = RequestMethod.GET)
    @LogAnnotation(value = "根据ids删除建议表")
    @ApiOperation(value = "根据ids删除建议表", notes = "根据ids (如果是删除多条就用 英文逗号将id拼接起来)删除建议表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ids", value = "ids(如果是删除多条就用 英文逗号将id拼接起来)", dataType = "String", paramType = "query", required = true)
    })
    @ResponseBody
    public Object deleteLis(String ids) throws Exception {
        ResEntity result = tBusinessProposalService.deleteLis(ids);
        return result;
    }

    @RequestMapping(value = "accept", method = RequestMethod.POST)
    @ApiOperation(value = "受理保存接口", notes = "受理保存接口 (只需传递 主键id 和 受理意见 参数)")
    @LogAnnotation(isWrite = true, value = "受理保存接口")
    @ResponseBody
    public Object accept(@RequestBody TBusinessProposal tBusinessProposal) {
        if (null == tBusinessProposal.getProposalReceiveState()) {
            // 如果没传受理状态就默认已受理
            tBusinessProposal.setProposalReceiveState((byte) 0);
        }
        ResEntity result = tBusinessProposalService.update(tBusinessProposal);
        return result;
    }
}