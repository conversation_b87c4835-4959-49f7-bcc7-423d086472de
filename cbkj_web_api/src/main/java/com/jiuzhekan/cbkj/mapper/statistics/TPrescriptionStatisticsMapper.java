package com.jiuzhekan.cbkj.mapper.statistics;

import com.jiuzhekan.cbkj.beans.statistics.InstitutionStatsResponse;
import com.jiuzhekan.cbkj.beans.statistics.InstitutionStatsVO;
import com.jiuzhekan.cbkj.beans.statistics.TPrescriptionVO;
import com.jiuzhekan.cbkj.beans.statistics.TStatisticsPrescriptionItem;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 描述
 *
 * <AUTHOR>
 * @Date 2020/2/18
 */
@Component
public interface TPrescriptionStatisticsMapper {

    /**
     * 查询单次就诊金额在某范围内的次数
     * appId
     * insCode
     * preTimeBegin 开方开始日期
     * preTimeEnd  开方截止日期
     * preTolMoneyBegin 起始金额
     * preTolMoneyEnd   截止金额
     * @return
     */
    HashMap<String, Object> getCountGroupByPreTolMoney(TPrescriptionVO prescriptionVO);

    /**
     * 根据不同的处方类型，查看处方金额在某个范围内的次数
     * appId
     * insCode
     * preTimeBegin 开方起始日期
     * preTimeEnd  开方截止日期
     * preTolMoneyBegin 起始金额
     * preTolMoneyEnd   截止金额
     * preType 药方类型
     * @return count总数，preType处方类型
     */
    List<HashMap<String,Object>> getCountByPreTolMoneyGroupByType(TPrescriptionVO prescriptionVO);

    /**
     * 查询每个月某个处方类型的开方数量
     * appId
     * insCode
     * preTimeBegin 开方起始日期
     * preTimeEnd  开方截止日期
     * preType 药方类型
     * @return  preTime 月份 count数量
     */
    List<InstitutionStatsResponse> getCountByMonthAndType(Map<String, Object> paramMap);


    /**
     * 根据开方时间，获取某一时间段内开方总量
     * "中药处方分析"模块使用
     * @param prescriptionVO
     * @return
     */
    Integer getCountByPreTime(TPrescriptionVO prescriptionVO);

    /**
     * 根据开方时间，获取某一时间段内使用率最高的50种药材
     * "中药处方分析"模块使用
     * @param prescriptionVO
     * @return matName 药品名称，count 使用次数
     */
    List<HashMap> getCountByPreTimeGroupMatName(TPrescriptionVO prescriptionVO);


    /**
     * 根据开方时间，获取某一时间段内所有药材
     * "中药处方分析"模块使用
     * @param startTime
     * @param endTime
     * @return
     */
    List<TStatisticsPrescriptionItem> getCountByPreTimeGroupMatNameList(@Param(value = "startTime") String startTime, @Param(value = "endTime")  String endTime);

    /**
     * 根据开方时间，获取所有开方途径的数量
     * @param prescriptionVO
     * @return count 数量，preOrigin开方途径
     */
    List<HashMap> getCountByPreTimeGroupPreOrigin(TPrescriptionVO prescriptionVO);

    /**
     * 统计医生工作量
     * @param map
     * appId医联体id,
     * insCode医疗机构ID，
     * year年份，
     * month月份
     * @return
     * id 医生ID
     * name 医生姓名
     * regCount, 月就诊人次
     * nfCount,月内服药方数量
     * wyCount,月外用药方数量
     * zcCount,月中成药方数量
     * syCount,月适宜技术药方量
     * pCount,月总开方量
     *
     * qregCount,全年就诊人次
     * qnfCount,全年内服药方数量
     * qwyCount,全年外用药方量
     * qzcCount,全年中成药方量
     * qsyCount,全年适宜技术药方量
     * qpCount，全年开方量
     */
    List<HashMap<String,Object>> getDocutorWorkload(HashMap<String,Object> map);

    /**
     * 统计机构工作量
     * @param map
     * appId医联体id,
     * insCode医疗机构ID，
     * year年份，
     * month月份
     * @return
     * insCode 机构编码
     * insName 机构名称
     * regCount, 月就诊人次
     * nfCount,月内服药方数量
     * wyCount,月外用药方数量
     * zcCount,月中成药方数量
     * syCount,月适宜技术药方量
     * pCount,月总开方量
     *
     * qregCount,全年就诊人次
     * qnfCount,全年内服药方数量
     * qwyCount,全年外用药方量
     * qzcCount,全年中成药方量
     * qsyCount,全年适宜技术药方量
     * qpCount，全年开方量
     */
    void   savePreTimeGroupMatName(TPrescriptionVO prescriptionVO);


    /**
     * 获取临时表中的所有数据
     */
    List<InstitutionStatsVO> getInstitutionWorkload(HashMap<String,Object> map);

    /**
     * 删除临时表
     * @return
     */
    int removeTempDrugReportStatistics();

    /**
     * 医院-合计
     * @param map
     * @return
     */
    List<HashMap<String,Object>> getInstitutionWorkloadTotal(HashMap<String,Object> map);
}
