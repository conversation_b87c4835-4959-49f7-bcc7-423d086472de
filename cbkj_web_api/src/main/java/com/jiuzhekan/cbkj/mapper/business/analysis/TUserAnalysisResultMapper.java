package com.jiuzhekan.cbkj.mapper.business.analysis;

import com.jiuzhekan.cbkj.beans.business.analysis.TUSerAnalysusResultItem;
import com.jiuzhekan.cbkj.beans.business.analysis.TUserAnalysisGroupResult;
import com.jiuzhekan.cbkj.beans.business.analysis.TUserAnalysisResult;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public interface TUserAnalysisResultMapper extends BaseMapper<TUserAnalysisResult> {
    List<TUserAnalysisResult> getUserResultByRes(TUserAnalysisResult tUserAnalysisResult);

    Map<String, Object> getMapById(String analyId);

    /**
     * 获取9种体质得分
     *
     * @param analyId analyId
     * @return java.util.Map<java.lang.String, java.lang.Object>
     * <AUTHOR>
     * @date 2021/4/28
     */
    List<TUserAnalysisGroupResult> getGroupResults(String analyId);

    /**
     * 保存9种体质得分
     *
     * @param groupResultS groupResultS
     * @return long
     * <AUTHOR>
     * @date 2021/4/28
     */
    long insertGroupResult(List<TUserAnalysisGroupResult> groupResultS);


    /**
     * 获取问题勾选记录
     *
     * @param analyId analyId
     * @return java.util.List<com.jiuzhekan.cbkj.beans.business.analysis.TUSerAnalysusResultItem>
     * <AUTHOR>
     * @date 2021/4/28
     */
    List<TUSerAnalysusResultItem> getResultItemList(String analyId);

    /**
     * 保存问题勾选记录
     *
     * @param groupResultS groupResultS
     * @return long
     * <AUTHOR>
     * @date 2021/4/28
     */
    long insertResultItemList(List<TUSerAnalysusResultItem> groupResultS);


    TUserAnalysisResult getAnalysisResultById(String id);

    /**
     * 更新得分图片
     *
     * @param tUserAnalysisResult tUserAnalysisResult
     * @return int
     * <AUTHOR>
     * @date 2021/6/25
     */
    int updateSorceImg(TUserAnalysisResult tUserAnalysisResult);

    /**
     * 更新结果文件
     *
     * @param tUserAnalysisResult tUserAnalysisResult
     * @return int
     * <AUTHOR>
     * @date 2021/6/25
     */
    int updateWordPdf(TUserAnalysisResult tUserAnalysisResult);


    TUserAnalysisResult getAnalysisWordPdf(String id);
}