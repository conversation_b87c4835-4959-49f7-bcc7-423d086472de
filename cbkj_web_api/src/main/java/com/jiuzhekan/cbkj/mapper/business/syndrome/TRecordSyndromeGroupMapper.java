package com.jiuzhekan.cbkj.mapper.business.syndrome;


import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;
import org.springframework.stereotype.Component;
import com.jiuzhekan.cbkj.beans.business.syndrome.TRecordSyndromeGroup;

import java.util.List;

@Component
public interface TRecordSyndromeGroupMapper extends BaseMapper<TRecordSyndromeGroup> {

    /**
     * 根据挂号ID删除
     * 三个一起调用
     * @param registerId
     * @return int
     * <AUTHOR>
     * @date 2020/8/26
     */
    void deleteByRegisterId(String registerId);
    void deleteByRegisterIdV2(String registerId);
    void deleteByRegisterIdV3(String registerId);

    /**
     * 根据挂号ID更新为已保存处方
     *
     * @param registerId
     * @return int
     * <AUTHOR>
     * @date 2020/8/26
     */
    int hasSaveRecordByRegisterId(String registerId);

    /**
     * 获取分组、问题、答案记录
     *
     * @param tRecordSyndromeGroup registerId hasSave
     * @return com.jiuzhekan.cbkj.beans.business.syndrome.TRecordSyndromeGroup
     * <AUTHOR>
     * @date 2020/8/26
     */
    TRecordSyndromeGroup getSyndromeRecord(TRecordSyndromeGroup tRecordSyndromeGroup);

}