package com.jiuzhekan.healthpres.beans;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel
public class TUserQuestionnaireResultAcu implements Serializable{

    @ApiModelProperty(value = "")
    private String resultAcuId;

    @ApiModelProperty(value = "穴位ID")
    private String acuId;

    @ApiModelProperty(value = "结果id")
    private String resultId;

    @ApiModelProperty(value = "结果明细id")
    private String resultItemId;

    @ApiModelProperty(value = "穴位代码")
    private String acuCode;

    @ApiModelProperty(value = "穴位名称")
    private String acuName;

    @ApiModelProperty(value = "穴位图片")
    private String acuImg;

    @ApiModelProperty(value = "穴位定位")
    private String acuPosition;


}
