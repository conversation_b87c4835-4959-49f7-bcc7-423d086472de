package com.jiuzhekan.recordquality.mapper;

import com.jiuzhekan.recordquality.beans.RecordQualityListReq;
import com.jiuzhekan.recordquality.beans.TRecordQualityRuleItems;
import com.jiuzhekan.recordquality.beans.TRecordQualityRuleMain;
import org.springframework.stereotype.Component;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;

import java.util.HashMap;
import java.util.List;

@Component
public interface TRecordQualityRuleMainMapper extends BaseMapper<TRecordQualityRuleMain>{


    List<TRecordQualityRuleMain> list(RecordQualityListReq recordQualityListReq);

    TRecordQualityRuleMain getObjectMapping(String recordQualityRuleId);

    List<HashMap<String, String>> getQualitySymptom(String name);
}