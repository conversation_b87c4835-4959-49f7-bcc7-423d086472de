//package com.jiuzhekan.cbkj.service.statistics;
//
//import com.jiuzhekan.cbkj.beans.statistics.TAnalysisNationalIndexVo;
//import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
//import com.jiuzhekan.cbkj.common.utils.AdminUtils;
//import com.jiuzhekan.cbkj.common.utils.Constant;
//import com.jiuzhekan.cbkj.common.utils.DateUtil;
//import com.jiuzhekan.cbkj.mapper.statistics.TAnalysisNationalIndexMapper;
//import org.apache.commons.lang.StringUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//
//import java.util.*;
//
///**
// * @program: pre_api
// * @description: 医共体业务分析和国考统计分析
// * @author: buhongbo
// * @create: 2021-03-16 13:44
// **/
//@Service
//public class TAnalysisNationalIndexService {
//
//    @Autowired
//    private TAnalysisNationalIndexMapper tAnalysisNationalIndexMapper;
//
//    /**
//     * 获取国考按数量金额统计
//     * @param tAnalysisNationalIndexVo
//     * @return
//     */
//    public Object getTAnalysisNationalIndexVo(TAnalysisNationalIndexVo tAnalysisNationalIndexVo) {
//
//        ResEntity resEntity = this.gettAnalysisNationalIndexVos(tAnalysisNationalIndexVo);
//
//        if (!resEntity.getStatus()) {
//            return resEntity;
//        }
//
//        List<TAnalysisNationalIndexVo> list = (List<TAnalysisNationalIndexVo>) resEntity.getData();
//
//        Object result = this.getDataPage(list, tAnalysisNationalIndexVo);
//        return result;
//    }
//
//
//    /**
//     * 获取按照数量金额统计数据
//     * @param tAnalysisNationalIndexVo
//     * @return
//     */
//    public ResEntity gettAnalysisNationalIndexVos(TAnalysisNationalIndexVo
//                                                          tAnalysisNationalIndexVo) {
//
//        tAnalysisNationalIndexVo = this.dataValidation(tAnalysisNationalIndexVo);
//
//        List<TAnalysisNationalIndexVo> list =
//                tAnalysisNationalIndexMapper.getTAnalysisNationalIndexVo(tAnalysisNationalIndexVo);
//
//        if (null == list || list.size() <= 0) {
//            return new ResEntity(false , "该医疗机构尚未开具处方" , "");
//        }
//
//        TAnalysisNationalIndexVo analysisNationalIndexVo1 = new TAnalysisNationalIndexVo();
//        analysisNationalIndexVo1.setDeptName("全院");
//        Integer prescriptionNum = new Integer(0);
//        Integer preSZOrXBZNum = new Integer(0);
//        Integer peopleUseNum = new Integer(0);
//        Integer peopleNonUseNum = new Integer(0);
//        Double CMedicineIncome = new Double(0.0000);
//        Double CMedicinePiecesIncome = new Double(0.0000);
//        for (TAnalysisNationalIndexVo analysisNationalIndexVo : list) {
//            prescriptionNum +=
//                    Integer.valueOf(analysisNationalIndexVo.getPrescriptionNum());
//            preSZOrXBZNum +=
//                    Integer.valueOf(analysisNationalIndexVo.getPreSZOrXBZNum());
//            peopleUseNum +=
//                    Integer.valueOf(analysisNationalIndexVo.getPeopleUseNum());
//            peopleNonUseNum +=
//                    Integer.valueOf(analysisNationalIndexVo.getPeopleNonUseNum());
//            CMedicineIncome +=
//                    Double.parseDouble(analysisNationalIndexVo.getCMedicineIncome());
//            CMedicinePiecesIncome +=
//                    Double.parseDouble(analysisNationalIndexVo.getCMedicinePiecesIncome());
//        }
//
//        analysisNationalIndexVo1.setPrescriptionNum(String.valueOf(prescriptionNum));
//        analysisNationalIndexVo1.setPreSZOrXBZNum(String.valueOf(preSZOrXBZNum));
//        analysisNationalIndexVo1.setPeopleUseNum(String.valueOf(peopleUseNum));
//        analysisNationalIndexVo1.setPeopleNonUseNum(String.valueOf(peopleNonUseNum));
//        analysisNationalIndexVo1.setCMedicineIncome(String.valueOf(CMedicineIncome));
//        analysisNationalIndexVo1.setCMedicinePiecesIncome(String.valueOf(CMedicinePiecesIncome));
//        list.add(analysisNationalIndexVo1);
//        return new ResEntity(true , "成功" , list);
//    }
//
//    /**
//     * 数据验证
//     * @param tAnalysisNationalIndexVo
//     * @return
//     */
//    public TAnalysisNationalIndexVo dataValidation(TAnalysisNationalIndexVo tAnalysisNationalIndexVo) {
//        /**
//         * 设置医联体信息
//         */
//        if (StringUtils.isBlank(tAnalysisNationalIndexVo.getAppId())) {
//            tAnalysisNationalIndexVo.setAppId(AdminUtils.getCurrentAppId());
//            if ("000000".equals(tAnalysisNationalIndexVo.getAppId())) {
//                tAnalysisNationalIndexVo.setAppId("");
//            }
//        }else if ("000000".equals(tAnalysisNationalIndexVo.getAppId())) {
//            tAnalysisNationalIndexVo.setAppId("");
//        }
//
//        /**
//         * 配置医疗机构信息，如果医疗机构为000000，则查询所有，默认医疗机构为空
//         */
//        if (StringUtils.isBlank(tAnalysisNationalIndexVo.getInsCode())) {
//            tAnalysisNationalIndexVo.setInsCode(AdminUtils.getCurrentInsCode());
//            if ("000000".equals(tAnalysisNationalIndexVo.getInsCode())) {
//                tAnalysisNationalIndexVo.setInsCode("");
//            }
//        } else if ("000000".equals(tAnalysisNationalIndexVo.getInsCode())) {
//            tAnalysisNationalIndexVo.setInsCode("");
//        }
//
//        /**
//         * 获取当前年份和月份，用于配置查询当前月份信息，配置初始时间为当月月初
//         */
//        Calendar cal = Calendar.getInstance();
//        int year = cal.get(Calendar.YEAR);
//        int month = cal.get(Calendar.MONTH);
//
//        /**
//         * 配置查询起始时间
//         */
//        if (StringUtils.isBlank(tAnalysisNationalIndexVo.getStartTimeStr())) {
//            if (month < 9) {
//                tAnalysisNationalIndexVo.setStartTimeStr((year + "") +"-0"+ (month + 1 + "") + "-01 00:00:00");
//            }else {
//                tAnalysisNationalIndexVo.setStartTimeStr((year + "") +"-"+ (month + 1 + "") + "-01 00:00:00") ;
//            }
//        }else {
//            String startTimeStr = tAnalysisNationalIndexVo.getStartTimeStr();
//            tAnalysisNationalIndexVo.setStartTimeStr(startTimeStr + " 00:00:00");
//        }
//
//
//        /**
//         * 配置查询结束时间
//         */
//        if (StringUtils.isBlank(tAnalysisNationalIndexVo.getEndTimeStr())) {
//            tAnalysisNationalIndexVo.setEndTimeStr(DateUtil.getDateFormats(DateUtil.date1 , new Date()));
//        } else {
//            String endTimeStr = tAnalysisNationalIndexVo.getEndTimeStr();
//            tAnalysisNationalIndexVo.setEndTimeStr(endTimeStr + " 23:59:59");
//        }
//
//        return tAnalysisNationalIndexVo;
//    }
//
//
//    /**
//     * 设置国考指标分析科室目标
//     * @param tAnalysisNationalIndexVos
//     * @return
//     */
//    @Transactional(rollbackFor = Exception.class)
//    public ResEntity insertNationalGoal(List<TAnalysisNationalIndexVo> tAnalysisNationalIndexVos){
//
//        if (null == tAnalysisNationalIndexVos || tAnalysisNationalIndexVos.size() <= 0) {
//            return new ResEntity(false , "目标数据不能为空" , "");
//        }
//
//        String s = new String();
//        for (TAnalysisNationalIndexVo tAnalysisNationalIndexVo : tAnalysisNationalIndexVos) {
//
//            if (StringUtils.isBlank(tAnalysisNationalIndexVo.getAppId())) {
//                tAnalysisNationalIndexVo.setAppId(AdminUtils.getCurrentAppId());
//            }
//            if (StringUtils.isBlank(tAnalysisNationalIndexVo.getInsCode())) {
//                tAnalysisNationalIndexVo.setInsCode(AdminUtils.getCurrentInsCode());
//            }
//            if (StringUtils.isBlank(tAnalysisNationalIndexVo.getDeptName())) {
//                s = "科室不能为空！！！";
//            }
//            tAnalysisNationalIndexVo.setCreateDate(new Date());
//            tAnalysisNationalIndexVo.setCreateUser(AdminUtils.getCurrentHr().getId());
//
//            this.deleteNationalGoal(tAnalysisNationalIndexVo.getAppId() ,
//                    tAnalysisNationalIndexVo.getInsCode() , tAnalysisNationalIndexVo.getDeptId());
//        }
//        if (StringUtils.isNotBlank(s)) {
//            return new ResEntity(false , s , "");
//        }
//        Integer row = tAnalysisNationalIndexMapper.insertNationalGoal(tAnalysisNationalIndexVos);
//        if (row <= 0) {
//            return new ResEntity(false , "数据库异常，请稍后重试" , "");
//        }
//        return new ResEntity(true , "成功" , "");
//    }
//
//
//    public ResEntity deleteNationalGoal(String appId , String insCode , String depId) {
//
//        if (StringUtils.isBlank(appId)) {
//            return new ResEntity(false , "医共体编号不能为空" , "");
//        }
//        if (StringUtils.isBlank(insCode)) {
//            return new ResEntity(false , "医疗机构编号不能为空" , "");
//        }
//
//        tAnalysisNationalIndexMapper.deleteNationalGoal(appId , insCode , depId);
//        return new ResEntity(true , "成功", "");
//    }
//
//    /**
//     * 获取科室目标
//     * @param tAnalysisNationalIndexVo
//     * @return
//     */
//    public ResEntity getDeptGoal(TAnalysisNationalIndexVo tAnalysisNationalIndexVo) {
//
//        /**
//         * 设置医联体信息
//         */
//        if (StringUtils.isBlank(tAnalysisNationalIndexVo.getAppId())) {
//            tAnalysisNationalIndexVo.setAppId(AdminUtils.getCurrentAppId());
//            if ("000000".equals(tAnalysisNationalIndexVo.getAppId())) {
//                tAnalysisNationalIndexVo.setAppId("");
//            }
//        }else if ("000000".equals(tAnalysisNationalIndexVo.getAppId())) {
//            tAnalysisNationalIndexVo.setAppId("");
//        }
//
//        /**
//         * 配置医疗机构信息，如果医疗机构为000000，则查询所有，默认医疗机构为空
//         */
//        if (StringUtils.isBlank(tAnalysisNationalIndexVo.getInsCode())) {
//            tAnalysisNationalIndexVo.setInsCode(AdminUtils.getCurrentInsCode());
//            if ("000000".equals(tAnalysisNationalIndexVo.getInsCode())) {
//                tAnalysisNationalIndexVo.setInsCode("");
//            }
//        } else if ("000000".equals(tAnalysisNationalIndexVo.getInsCode())) {
//            tAnalysisNationalIndexVo.setInsCode("");
//        }
//
//        List<TAnalysisNationalIndexVo> deps = tAnalysisNationalIndexMapper.getDeps(tAnalysisNationalIndexVo);
//        if (deps != null && deps.size() > 0) {
//            for (int i = 0; i < deps.size(); i++) {
//                TAnalysisNationalIndexVo dep = deps.get(i);
//                TAnalysisNationalIndexVo deptGoal = tAnalysisNationalIndexMapper.getDeptGoal(dep);
//                if (deptGoal != null) {
//                    deps.get(i).setPrescriptionProportion(deptGoal.getPrescriptionProportion());
//                    deps.get(i).setPreSZOrXBZProportion(deptGoal.getPreSZOrXBZProportion());
//                    deps.get(i).setPeopleUseProportion(deptGoal.getPeopleUseProportion());
//                    deps.get(i).setPeopleNonUseProportion(deptGoal.getPeopleNonUseProportion());
//                    deps.get(i).setCMedicineIncomeProportion(deptGoal.getCMedicineIncomeProportion());
//                    deps.get(i).setCMedicinePiecesIncomeProportion(deptGoal.getCMedicinePiecesIncomeProportion());
//                }else {
//                    deps.get(i).setPrescriptionProportion(0.00);
//                    deps.get(i).setPreSZOrXBZProportion(0.00);
//                    deps.get(i).setPeopleUseProportion(0.00);
//                    deps.get(i).setPeopleNonUseProportion(0.00);
//                    deps.get(i).setCMedicineIncomeProportion(0.00);
//                    deps.get(i).setCMedicinePiecesIncomeProportion(0.00);
//                }
//            }
//            TAnalysisNationalIndexVo tAnalysisNationalIndexVo1 = new TAnalysisNationalIndexVo();
//            tAnalysisNationalIndexVo1.setAppId(tAnalysisNationalIndexVo.getAppId());
//            tAnalysisNationalIndexVo1.setInsCode(tAnalysisNationalIndexVo.getInsCode());
//            tAnalysisNationalIndexVo1.setDeptName("全院");
//            TAnalysisNationalIndexVo deptGoal = tAnalysisNationalIndexMapper.getDeptGoal(tAnalysisNationalIndexVo1);
//            if (null != deptGoal) {
//                tAnalysisNationalIndexVo1.setPrescriptionProportion(deptGoal.getPrescriptionProportion());
//                tAnalysisNationalIndexVo1.setPreSZOrXBZProportion(deptGoal.getPreSZOrXBZProportion());
//                tAnalysisNationalIndexVo1.setPeopleUseProportion(deptGoal.getPeopleUseProportion());
//                tAnalysisNationalIndexVo1.setPeopleNonUseProportion(deptGoal.getPeopleNonUseProportion());
//                tAnalysisNationalIndexVo1.setCMedicineIncomeProportion(deptGoal.getCMedicineIncomeProportion());
//                tAnalysisNationalIndexVo1.setCMedicinePiecesIncomeProportion(deptGoal.getCMedicinePiecesIncomeProportion());
//            }else {
//                tAnalysisNationalIndexVo1.setPrescriptionProportion(0.00);
//                tAnalysisNationalIndexVo1.setPreSZOrXBZProportion(0.00);
//                tAnalysisNationalIndexVo1.setPeopleUseProportion(0.00);
//                tAnalysisNationalIndexVo1.setPeopleNonUseProportion(0.00);
//                tAnalysisNationalIndexVo1.setCMedicineIncomeProportion(0.00);
//                tAnalysisNationalIndexVo1.setCMedicinePiecesIncomeProportion(0.00);
//            }
//            deps.add(tAnalysisNationalIndexVo1);
//            return new ResEntity(true , "成功" , deps);
//        }
//        return new ResEntity(false , "数据库异常，请稍后重试" , "");
//    }
//
//    /**
//     * 获取科室数量占比所有数据
//     * @param tAnalysisNationalIndexVo
//     * @return
//     */
//    public ResEntity getDeptProData(TAnalysisNationalIndexVo tAnalysisNationalIndexVo) {
//
//        tAnalysisNationalIndexVo = this.dataValidation(tAnalysisNationalIndexVo);
//
//        ResEntity resEntity = this.gettAnalysisNationalIndexVos(tAnalysisNationalIndexVo);
//
//        if (!resEntity.getStatus()) {
//            return resEntity;
//        }
//        List<TAnalysisNationalIndexVo> deps =
//                (List<TAnalysisNationalIndexVo>) resEntity.getData();
//
//        if (null != deps && deps.size() > 0) {
//            for (TAnalysisNationalIndexVo dep : deps) {
//                dep.setPrescriptionPro(50.00);
//                dep.setPreSZOrXBZPro(50.00);
//                dep.setPeopleUsePro(50.00);
//                dep.setPeopleNonUsePro(50.00);
//                dep.setCMedicineIncomePro(50.00);
//                dep.setCMedicinePiecesIncomePro(50.00);
//            }
//        }
//
//        return new ResEntity(true , "成功" , deps);
//    }
//
//    /**
//     * 科室数量占比
//     * @param tAnalysisNationalIndexVo
//     * @return
//     */
//    public Object getDeptPro(TAnalysisNationalIndexVo tAnalysisNationalIndexVo) {
//
//        ResEntity resEntity = this.getDeptProData(tAnalysisNationalIndexVo);
//
//        if (!resEntity.getStatus()) {
//            return resEntity;
//        }
//
//        List<TAnalysisNationalIndexVo> deps = (List<TAnalysisNationalIndexVo>) resEntity.getData();
//
//        Object dataPage = this.getDataPage(deps, tAnalysisNationalIndexVo);
//
//        return dataPage;
//    }
//
//    /**
//     * 获取科室目标数据
//     * @param tAnalysisNationalIndexVo
//     * @return
//     */
//    public ResEntity getDepGoalData(TAnalysisNationalIndexVo tAnalysisNationalIndexVo) {
//
//        tAnalysisNationalIndexVo = this.dataValidation(tAnalysisNationalIndexVo);
//
//        ResEntity resEntity = this.gettAnalysisNationalIndexVos(tAnalysisNationalIndexVo);
//
//        if (!resEntity.getStatus()) {
//            return resEntity;
//        }
//        List<TAnalysisNationalIndexVo> list =
//                (List<TAnalysisNationalIndexVo>) resEntity.getData();
//
//        List<String> depts = new ArrayList<>();
//        Integer num = 0;
//
//        if (null != list && list.size() > 0) {
//            for (TAnalysisNationalIndexVo analysisNationalIndexVo : list) {
//                analysisNationalIndexVo.setPrescriptionPro(50.00);
//                analysisNationalIndexVo.setPreSZOrXBZPro(50.00);
//                analysisNationalIndexVo.setPeopleUsePro(50.00);
//                analysisNationalIndexVo.setPeopleNonUsePro(50.00);
//                analysisNationalIndexVo.setCMedicineIncomePro(50.00);
//                analysisNationalIndexVo.setCMedicinePiecesIncomePro(50.00);
//                if ("全院".equals(analysisNationalIndexVo.getDeptName())) {
//                    num = list.indexOf(analysisNationalIndexVo);
//                }else {
//                    depts.add(analysisNationalIndexVo.getDeptId());
//                }
//            }
//        }
//
//        ResEntity resEntity1 = this.getDeptGoal(tAnalysisNationalIndexVo);
//
//        if (resEntity1.getStatus()) {
//            List<TAnalysisNationalIndexVo> list1 = (List<TAnalysisNationalIndexVo>) resEntity1.getData();
//            if (null != list1 && list1.size() > 0) {
//                for (TAnalysisNationalIndexVo analysisNationalIndexVo : list1) {
//                    if (StringUtils.isNotBlank(analysisNationalIndexVo.getDeptId())) {
//                        if (depts.contains(analysisNationalIndexVo.getDeptId())) {
//                            int index = depts.indexOf(analysisNationalIndexVo.getDeptId());
//                            list.get(index).setPrescriptionProportion(
//                                    analysisNationalIndexVo.getPrescriptionProportion());
//                            list.get(index).setPreSZOrXBZProportion(
//                                    analysisNationalIndexVo.getPreSZOrXBZProportion());
//                            list.get(index).setPeopleUseProportion(
//                                    analysisNationalIndexVo.getPeopleUseProportion());
//                            list.get(index).setPeopleNonUseProportion(
//                                    analysisNationalIndexVo.getPeopleNonUseProportion());
//                            list.get(index).setCMedicineIncomeProportion(
//                                    analysisNationalIndexVo.getCMedicineIncomeProportion());
//                            list.get(index).setCMedicinePiecesIncomeProportion(
//                                    analysisNationalIndexVo.getCMedicinePiecesIncomeProportion());
//                        }
//                    }else {
//                        list.get(num).setPrescriptionProportion(
//                                analysisNationalIndexVo.getPrescriptionProportion());
//                        list.get(num).setPreSZOrXBZProportion(
//                                analysisNationalIndexVo.getPreSZOrXBZProportion());
//                        list.get(num).setPeopleUseProportion(
//                                analysisNationalIndexVo.getPeopleUseProportion());
//                        list.get(num).setPeopleNonUseProportion(
//                                analysisNationalIndexVo.getPeopleNonUseProportion());
//                        list.get(num).setCMedicineIncomeProportion(
//                                analysisNationalIndexVo.getCMedicineIncomeProportion());
//                        list.get(num).setCMedicinePiecesIncomeProportion(
//                                analysisNationalIndexVo.getCMedicinePiecesIncomeProportion());
//                    }
//                }
//            }
//        }
//        return new ResEntity(true , "成功" , list);
//    }
//
//    /**
//     * 按照科室目标统计
//     * @param tAnalysisNationalIndexVo
//     * @return
//     */
//    public Object getDepGoalPro(TAnalysisNationalIndexVo tAnalysisNationalIndexVo) {
//
//        ResEntity resEntity = this.getDepGoalData(tAnalysisNationalIndexVo);
//
//        if (!resEntity.getStatus()) {
//            return resEntity;
//        }
//
//        List<TAnalysisNationalIndexVo> list = (List<TAnalysisNationalIndexVo>) resEntity.getData();
//
//        return this.getDataPage(list , tAnalysisNationalIndexVo);
//    }
//
//    /**
//     * 按医生统计数据
//     * @param tAnalysisNationalIndexVo
//     * @return
//     */
//    public ResEntity getDocProData(TAnalysisNationalIndexVo tAnalysisNationalIndexVo) {
//
//        tAnalysisNationalIndexVo = this.dataValidation(tAnalysisNationalIndexVo);
//
//        List<TAnalysisNationalIndexVo> doctors =
//                tAnalysisNationalIndexMapper.getDoctor(tAnalysisNationalIndexVo);
//        if (null != doctors && doctors.size() > 0) {
//            TAnalysisNationalIndexVo tAnalysisNationalIndexVo1 = new TAnalysisNationalIndexVo();
//            tAnalysisNationalIndexVo1.setDeptName("全院");
//            doctors.add(tAnalysisNationalIndexVo1);
//            for (TAnalysisNationalIndexVo analysisNationalIndexVo : doctors) {
//                analysisNationalIndexVo.setPrescriptionPro(50.00);
//                analysisNationalIndexVo.setPreSZOrXBZPro(50.00);
//                analysisNationalIndexVo.setPeopleUsePro(50.00);
//                analysisNationalIndexVo.setPeopleNonUsePro(50.00);
//                analysisNationalIndexVo.setCMedicineIncomePro(50.00);
//                analysisNationalIndexVo.setCMedicinePiecesIncomePro(50.00);
//            }
//        }
//        return new ResEntity(true , "成功" , doctors);
//    }
//
//    /**
//     * 处理按医生统计数据提供与表格导出使用
//     * @param tAnalysisNationalIndexVo
//     * @return
//     */
//    public List<TAnalysisNationalIndexVo> docProData(TAnalysisNationalIndexVo tAnalysisNationalIndexVo) {
//
//        ResEntity resEntity = this.getDocProData(tAnalysisNationalIndexVo);
//
//        if (resEntity.getStatus()) {
//            Integer index = 0;
//            List<TAnalysisNationalIndexVo> list = (List<TAnalysisNationalIndexVo>) resEntity.getData();
//            if (null != list && list.size() > 0) {
//                List<String> deps = new ArrayList<>();
//                List<TAnalysisNationalIndexVo> docs = new ArrayList<>();
//                for (TAnalysisNationalIndexVo analysisNationalIndexVo1 : list) {
//                    TAnalysisNationalIndexVo analysisNationalIndexVo = new TAnalysisNationalIndexVo();
//                    if (StringUtils.isNotBlank(analysisNationalIndexVo1.getDeptId())) {
//                        if (deps.contains(analysisNationalIndexVo1.getDeptId())) {
//                            index = deps.indexOf(analysisNationalIndexVo1.getDeptId());
//                            docs.get(index).getList().add(analysisNationalIndexVo1);
//                        }else {
//                            List<TAnalysisNationalIndexVo> list1 = new ArrayList<>();
//                            analysisNationalIndexVo.setDeptId(analysisNationalIndexVo1.getDeptId());
//                            analysisNationalIndexVo.setDeptName(analysisNationalIndexVo1.getDeptName());
//                            list1.add(analysisNationalIndexVo1);
//                            analysisNationalIndexVo.setList(list1);
//                            deps.add(analysisNationalIndexVo1.getDeptId());
//                            docs.add(analysisNationalIndexVo);
//                        }
//                    }else {
//                        List<TAnalysisNationalIndexVo> list1 = new ArrayList<>();
//                        list1.add(analysisNationalIndexVo1);
//                        analysisNationalIndexVo.setDeptName(analysisNationalIndexVo1.getDeptName());
//                        analysisNationalIndexVo.setList(list1);
//                        docs.add(analysisNationalIndexVo);
//                    }
//                }
////                System.out.println(docs);
//                return docs;
//            }
//        }
//        return null;
//    }
//
//    /**
//     * 按医生统计
//     * @param tAnalysisNationalIndexVo
//     * @return
//     */
//    public Object getDocPro(TAnalysisNationalIndexVo tAnalysisNationalIndexVo) {
//
//        ResEntity resEntity = this.getDocProData(tAnalysisNationalIndexVo);
//
//        if (!resEntity.getStatus()) {
//            return resEntity;
//        }
//        List<TAnalysisNationalIndexVo> doctors = (List<TAnalysisNationalIndexVo>) resEntity.getData();
//        return this.getDataPage(doctors , tAnalysisNationalIndexVo);
//    }
//
//    /**
//     * 分页方法
//     * @param list
//     * @param tAnalysisNationalIndexVo
//     * @return
//     */
//    public Object getDataPage(List<TAnalysisNationalIndexVo> list ,
//                              TAnalysisNationalIndexVo tAnalysisNationalIndexVo) {
//
//
//        Integer total = list.size();
//        Integer pagePage = tAnalysisNationalIndexVo.getPage();
//        Integer limit = tAnalysisNationalIndexVo.getLimit();
//        Integer pageSize = total / limit + 1;
//
//        Map<String, Object> result = new HashMap<String, Object>();
//        result.put("status", true);
//        result.put("message", Constant.SUCCESS);
//        result.put("count", total);
//
//        if (pageSize == 1) {
//            result.put("data", list);
//            return result;
//        }else if (pageSize > 1) {
//            if (pagePage < pageSize) {
//                List<TAnalysisNationalIndexVo> tAnalysisNationalIndexVos = new ArrayList<>();
//                for (int i = ((pagePage - 1)*limit); i < (pagePage*limit); i++) {
//                    tAnalysisNationalIndexVos.add(list.get(i));
//                }
//                result.put("data", tAnalysisNationalIndexVos);
//                return result;
//            }else {
//                List<TAnalysisNationalIndexVo> tAnalysisNationalIndexVos = new ArrayList<>();
//                for (int i = ((pagePage - 1)*limit); i < total; i++) {
//                    tAnalysisNationalIndexVos.add(list.get(i));
//                }
//                result.put("data" , tAnalysisNationalIndexVos);
//                return result;
//            }
//        }else {
//            result.put("data" , "");
//            return result;
//        }
//    }
//
//}
