package com.jiuzhekan.cbkj.controller.business.setting;

import com.jiuzhekan.cbkj.beans.business.record.TBusinessEdition;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.annotaionUtil.LogAnnotation;
import com.jiuzhekan.cbkj.service.business.setting.TBusinessEditionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping(value = "tBusinessEdition/")
@Api(value = "版本维护接口", tags = "版本维护接口")
public class TBusinessEditionController {

    @Autowired
    private TBusinessEditionService tBusinessEditionService;

    @RequestMapping(value = "getPages", method = RequestMethod.GET)
    @ApiOperation(value = "查询版本维护列表(不分页)", notes = "查询版本维护列表(不分页,返回格式{status:true,message:'success',data:null})"
            , response = TBusinessEdition.class)
    @ResponseBody
    @LogAnnotation("查询版本维护列表(不分页)")
    public Object getApps(TBusinessEdition tBusinessEdition) {
        return tBusinessEditionService.getPageDatas(tBusinessEdition);
    }

    @RequestMapping(value = "update/findObj", method = RequestMethod.GET)
    @LogAnnotation("加载版本维护详情")
    @ApiOperation(value = "加载版本维护详情", notes = "加载版本维护详情", response = TBusinessEdition.class)
    @ResponseBody
    public Object getObj(String id) {
        return tBusinessEditionService.findObj(id);
    }

    @RequestMapping(value = "insert", method = RequestMethod.POST)
    @ApiOperation(value = "新增版本维护", notes = "新增版本维护")
    @LogAnnotation(isWrite = true, value = "新增版本维护")
    @ResponseBody
    public Object insert(@RequestBody TBusinessEdition tBusinessEdition) {
        return tBusinessEditionService.insert(tBusinessEdition);
    }

    @RequestMapping(value = "update", method = RequestMethod.POST)
    @ApiOperation(value = "修改版本维护", notes = "修改版本维护")
    @LogAnnotation(isWrite = true, value = "修改版本维护")
    @ResponseBody
    public Object update(@RequestBody TBusinessEdition tBusinessEdition) {
        return tBusinessEditionService.update(tBusinessEdition);
    }

    @RequestMapping(value = "deleteLis", method = RequestMethod.GET)
    @ApiOperation(value = "删除版本维护", notes = "删除版本维护")
    @LogAnnotation(value = "删除版本维护", isWrite = true)
    @ResponseBody
    public Object deleteLis(String ids) {
        return tBusinessEditionService.deleteLis(ids);
    }


    @RequestMapping(value = "unread", method = RequestMethod.GET)
    @ApiOperation(value = "未读版本", notes = "未读版本")
    @LogAnnotation(value = "未读版本")
    @ResponseBody
    public Object unread() {
        return tBusinessEditionService.unread();
    }

}