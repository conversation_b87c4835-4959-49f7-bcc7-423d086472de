package com.jiuzhekan.cbkj.service.business.treatment;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jiuzhekan.cbkj.beans.business.record.*;
import com.jiuzhekan.cbkj.beans.drug.MatVo;
import com.jiuzhekan.cbkj.beans.drug.TCenterHisYpmlmx;
import com.jiuzhekan.cbkj.beans.drug.TCenterHisYpmlmxConvertUtil;
import com.jiuzhekan.cbkj.beans.sysBeans.ResEntity;
import com.jiuzhekan.cbkj.common.http.DrugsRestTemplate;
import com.jiuzhekan.cbkj.common.http.InterfaceRestTemplate;
import com.jiuzhekan.cbkj.common.http.KnowRestTemplate;
import com.jiuzhekan.cbkj.common.utils.AdminUtils;
import com.jiuzhekan.cbkj.common.utils.Constant;
import com.jiuzhekan.cbkj.controller.business.treatment.vo.CheckMatStock;
import com.jiuzhekan.cbkj.mapper.business.record.TPrescriptionMapper;
import com.jiuzhekan.cbkj.mapper.business.record.TPrescriptionPreparationItemMapper;
import com.jiuzhekan.cbkj.service.business.TSysParamService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 校验/保存处方
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2019年08月21日 15:53:00
 */
@Slf4j
@Service
public class ValidRecordService {

    @Autowired
    private TPrescriptionMapper tPrescriptionMapper;
    @Autowired
    private TPrescriptionPreparationItemMapper tPrescriptionPreparationItemMapper;
    @Autowired
    private TSysParamService tSysParamService;
    @Autowired
    private KnowRestTemplate knowRestTemplate;
    @Autowired
    private DrugsRestTemplate drugsRestTemplate;
    @Autowired
    private InterfaceRestTemplate interfaceRestTemplate;
    @Autowired
    private WithholdStockService withholdStockService;
//    @Autowired
//    private TStatisticsPrescriptionMedicationService tStatisticsPrescriptionMedicationService;
//    @Autowired
//    private TStatisticsHealthCareService tStatisticsHealthCareService;

    @Autowired
    private ValidRecordCommon validRecordCommon;

    /**
     * 校验
     *
     * @param record 病历   todo 看看合理安全用药特殊签字调整（参数C009）代码
     * @return com.jiuzhekan.cbkj.beans.sysBeans.ResEntity
     * <AUTHOR>
     * @date 2021/2/24
     */
    public ResEntity validRecord(TRecord record) {

        for (TPrescription pre : record.getPrescriptionList()) {

            ResEntity validRes = validRecordCommon.validPre(record, pre);
            if (!validRes.getStatus()) {
                return validRes;
            }

            //先只校验内服、外用
            if (Constant.BASIC_STRING_ONE.equals(pre.getPreType()) || Constant.BASIC_STRING_TWO.equals(pre.getPreType())) {

                pre.setPreSafetyEvaluation(new TPrescriptionEvaluation());
                Map<String, TCenterHisYpmlmx> mxMap = new HashMap<>();
                List<TCenterHisYpmlmx> mxList = new ArrayList<>();
                //2022-04-02 此处修改为接口调用
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("phaId", pre.getItemList().get(0).getCenterStoreId());
                JSONArray jsonArray = new JSONArray();

                pre.getItemList().forEach(mx -> {
                    JSONObject jsonArray2 = new JSONObject();
                    jsonArray2.put("matPriceIdHis", mx.getYpdmHis());
                    jsonArray2.put("matPriceId", mx.getYpdmCenter());
                    jsonArray.add(jsonArray2);
                });
                jsonObject.put("matPriceIdList", jsonArray);
                ResEntity post = drugsRestTemplate.post("mat/searchMatInfoById", jsonObject);
                if (200 == post.getCode() && post.getStatus()) {
                    List<LinkedHashMap> data = (List<LinkedHashMap>) post.getData();
                    List<MatVo> matVos = JSON.parseArray(JSON.toJSONString(data), MatVo.class);
                    for (MatVo matVo : matVos) {
                        TCenterHisYpmlmx vo1 = new TCenterHisYpmlmx();
                        TCenterHisYpmlmx centerHisMappingVO = TCenterHisYpmlmxConvertUtil.transferMatBean(matVo, vo1);
                        mxList.add(centerHisMappingVO);
                    }
                }


                mxList.forEach(mx -> mxMap.put(mx.getYpmlId() + "-" + mx.getYaopindm(), mx));

                StringBuffer sb = new StringBuffer();
                for (int i = 0; i < pre.getItemList().size(); i++) {

                    TPrescriptionItem item = pre.getItemList().get(i);
                    TCenterHisYpmlmx mx = mxMap.get(item.getYpmlHis() + "-" + item.getYpdmHis());
                    if (mx == null) {
                        return ResEntity.entity(false, item.getYpmcHis() + "不存在！", null);
                    }

                    if(StringUtils.isNotBlank(mx.getToxicityOverdoseMultiple()) ){
                        //mx.getToxicityOverdoseMultiple() 转成BigDecimal 比较大小 需要比1 大
                        if(new BigDecimal(mx.getToxicityOverdoseMultiple()).compareTo(new BigDecimal("1"))>0){
                            Map<String, Object> map = new HashMap<>();
                            map.put("pres", StringUtils.isBlank(item.getMatId()) ? item.getYpdmHis() : item.getMatId());
                            map.put("disid", "");
                            map.put("symid","");

                            ResEntity resEntity = knowRestTemplate.postKnow("check/pres", map);
                            if (resEntity.getStatus()) {
                                List<Map> list = JSONArray.parseArray(JSON.toJSONString(resEntity.getData()), Map.class);
                                if(!list.isEmpty()){
                                    for (Map ma: list) {
                                        //倍数
                                        BigDecimal multiple =    new BigDecimal(mx.getToxicityOverdoseMultiple());
                                        //剂量
                                        BigDecimal matDose = item.getMatDose();
                                        //推荐最大剂量
                                        if(ma.containsKey("matmaxdosage")){
                                            if(StringUtils.isNotBlank(ma.get("matmaxdosage").toString())){
                                                BigDecimal  maxDose =  new BigDecimal( ma.get("matmaxdosage").toString() );
                                                BigDecimal multiply = maxDose.multiply(multiple);

                                                int i1 = matDose.compareTo(multiply);
                                                if(i1>0){
                                                    Map<String,List<Map<String,String>>> mapList = new HashMap<>();
                                                    List<Map<String,String>> listmp = new ArrayList<>();
                                                    //毒药超倍数
                                                    Map<String,String> cbsmap = new HashMap<>();
                                                    cbsmap.put("matId",ma.get("matid").toString());
                                                    cbsmap.put("matName",ma.get("matname").toString());
//                                                    sb.append(ma.get("matname").toString()+"(超毒性最大用量，最大剂量："+ma.get("matmaxdosage").toString()
//                                                            +ma.get("unit").toString()+")").append("\r\n");
                                                    sb.append(ma.get("matname").toString()).append("(超毒性最大用量，最大剂量：").append(ma.get("matmaxdosage").toString()).append(ma.get("unit").toString()).append(")").append("\r\n");
                                                    listmp.add(cbsmap);
                                                    mapList.put(item.getMatId(),listmp);
                                                    item.setMap(mapList);
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }

                    }


                    item.setMx(mx);
                }
                pre.getPreSafetyEvaluation().setEvaDycjl(sb.toString());

                //医保限制提示
                validRecordCommon.validMatInsurance(pre);
                //炮制品提醒
                validRecordCommon.validCategoryMat(pre);
                //多规格同时开提醒
                validRecordCommon.validMultiSpec(pre);
                //知识库安全用药检测
                validRecordCommon.validSafetyEvaluation(record, pre,false);
                //特殊药品使用权限检测
                validRecordCommon.validWarnMats(pre);
                //对膏方（12）或者配方（10）进行库存的校验
                if (Constant.BASIC_STRING_TWELVE.equals(pre.getPreOrigin()) || Constant.BASIC_STRING_TEN.equals(pre.getPreOrigin())){
                    //检验膏方和配方的库存
                    String withholdSwitch = pre.getWithholdSwitch();
                    if (StringUtils.isBlank(withholdSwitch) || Constant.BASIC_STRING_ONE.equals(withholdSwitch)) {
                        ResEntity resultStock = withholdStockService.checkStock(record);
                        if (!resultStock.getStatus()){
                            resultStock.setCode(0);
                            return resultStock;
                        }
                    }
                }

                //处方保存接口校验库存
                String preStockSwitch = pre.getPreStockSwitch();
                if (!StringUtils.isBlank(preStockSwitch) && Constant.BASIC_STRING_ONE.equals(preStockSwitch)){

                    if (Constant.BASIC_STRING_TWELVE.equals(pre.getPreOrigin()) || Constant.BASIC_STRING_TEN.equals(pre.getPreOrigin())){
                        //制膏，不检查药品明细库存。
                    }else {
                        String storeId = pre.getStoreId();
                        List<CheckMatStock> list = pre.getItemList().stream().map(e -> new CheckMatStock(e.getYpdmHis(), storeId, e.getMatDose(), e.getYpmcHis(),pre.getPreNum()+"",e.getMatSeqn())).collect(Collectors.toList());
                        ResEntity resEntity = interfaceRestTemplate.checkMatStock(list);
                        if (!resEntity.getStatus()){
                            Object data = resEntity.getData();
                            HashMap<String, Object> map = new HashMap<>(16);
                            map.put("preType",pre.getPreType());
                            map.put("matList",data);
                            resEntity.setData(map);
                            return resEntity;
                        }
                    }
                }
            } else if (Constant.BASIC_STRING_FIVE.equals(pre.getPreType())) {
                //中药制剂医保限制
                validRecordCommon.validPreparationInsurance(pre);

            }

        }
        return ResEntity.success(record);
    }



    /**
     * 解析违禁药品
     *
     * @param evaStr evaStr
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021/10/9
     */
    private String appendValidWarnMats(String evaStr) {
        List<String> matNameList = Arrays.asList(evaStr.split("\r\n"));
        StringBuilder advice = new StringBuilder();
        advice.append(matNameList.get(0));
        for (int i = 1; i < matNameList.size(); i++) {
            advice.append(";");
            advice.append(matNameList.get(i));
        }
        advice.append("。");
        return advice.toString();
    }



    /**
     * 校验中药制剂日最大开药量
     *
     * @param preparationItemList preparationItemList
     * @return com.jiuzhekan.cbkj.beans.sysBeans.ResEntity
     * <AUTHOR>
     * @date 2021/6/9
     */
    public ResEntity validPreparationItemDailyMaxNum(List<TPrescriptionPreparationItem> preparationItemList) {

        TRegister register = AdminUtils.getCurrentRegister();

        for (TPrescriptionPreparationItem item : preparationItemList) {

            if (StringUtils.isBlank(item.getYpmlHis()) || StringUtils.isBlank(item.getYpdmHis()) || StringUtils.isBlank(item.getYpmlCenter()) || StringUtils.isBlank(item.getYpdmCenter())) {
                break;
            }
//            TCenterHisYpmlmx mx = tCenterHisYpmlmxMapper.getYpmx(item.getYpmlCenter(), item.getYpdmCenter());
//            if (mx != null) {
//                item.setZhuanhuanxs(mx.getZhuanhuanxs());
//            }

            JSONObject jsonObject = new JSONObject();
            JSONObject jsonObject2 = new JSONObject();
            JSONArray jsonArray = new JSONArray();
            jsonObject.put("phaId", item.getCenterStoreId());
            jsonObject.put("drugId", item.getYpmlCenter());
            jsonObject2.put("matPriceId", item.getYpdmCenter());
            jsonArray.add(jsonObject2);
            jsonObject.put("matPriceIdList", jsonArray);
            ResEntity post = drugsRestTemplate.post("mat/searchMatInfoById", jsonObject);
            if (200 == post.getCode() && post.getStatus()) {
                List<LinkedHashMap> data = (List<LinkedHashMap>) post.getData();
                List<MatVo> matVos = JSON.parseArray(JSON.toJSONString(data), MatVo.class);
                if (matVos.size() == 1) {
                    item.setCenterKucunsl(matVos.get(0).getStockNum());
                    item.setZhuanhuanxs(null == matVos.get(0).getConversionFactorHis() ? null : Double.parseDouble(matVos.get(0).getConversionFactorHis()));
                }
            }

            //医保支付条件限制 0）不限制（所有人不提醒） 1）限制门特病人和住院病人 2）限制医保病人 3）限制所有人
            if (tSysParamService.insuranceLimitObject()) {

                item.setPatientId(register.getPatientId());
                //BigDecimal dailyMaxNum = tCenterHisYpmlmxMapper.getDailyMaxNumPrep(item);
                BigDecimal dailyMaxNum = null;
                JSONObject j = new JSONObject();
                JSONObject j2 = new JSONObject();
                JSONArray jsonArray2 = new JSONArray();
                j.put("drugId", item.getYpmlHis());
                j2.put("matPriceIdHis", item.getYpdmHis());
                jsonArray2.add(j2);
                j.put("matPriceIdList", jsonArray2);
                ResEntity post2 = drugsRestTemplate.post("mat/searchMatInfoById", j);
                if (200 == post2.getCode() && post2.getStatus()) {
                    List<LinkedHashMap> data = (List<LinkedHashMap>) post2.getData();
                    List<MatVo> matVos = JSON.parseArray(JSON.toJSONString(data), MatVo.class);
                    if (matVos.size() == 1) {
                        //医保支付条件限制 0）不限制（所有人不提醒） 1）限制门特病人和住院病人 2）限制医保病人 3）限制所有人
                        item.setPatientId(register.getPatientId());
                        dailyMaxNum = new BigDecimal(matVos.get(0).getDailyMaxNumPrepHis());
                        item.setDailyMaxNum(dailyMaxNum);
                        BigDecimal usedNum = tPrescriptionPreparationItemMapper.getPreparationDailyNumSum(item);
                        item.setDailyUsedNum(usedNum);
                    }
                }
                if (dailyMaxNum == null) {
                    continue;
                }

//                BigDecimal usedNum = tPrescriptionPreparationItemMapper.getPreparationDailyNumSum(item);
//
//                //item.setDailyMaxNum(dailyMaxNum.stripTrailingZeros());
//                item.setDailyUsedNum(usedNum);
            }
        }

        return new ResEntity(true, Constant.SUCCESS_DX, preparationItemList);
    }




    public ResEntity findObj(String ypmlId, String yaopindm) {
        TCenterHisYpmlmx tCenterHisYpmlmx = new TCenterHisYpmlmx();
        JSONObject jsonObject = new JSONObject();
        JSONObject jsonObject2 = new JSONObject();
        JSONArray jsonArray = new JSONArray();
        jsonObject.put("drugId", ypmlId);
        jsonObject2.put("matPriceIdHis", yaopindm);
        jsonArray.add(jsonObject2);
        jsonObject.put("matPriceIdList", jsonArray);
        ResEntity post = drugsRestTemplate.post("mat/searchMatInfoById", jsonObject);
        if (200 == post.getCode() && post.getStatus()) {
            List<LinkedHashMap> data = (List<LinkedHashMap>) post.getData();
            List<MatVo> matVos = JSON.parseArray(JSON.toJSONString(data), MatVo.class);
            if (matVos.size() == 1) {
                tCenterHisYpmlmx = TCenterHisYpmlmxConvertUtil.transferMatBean(matVos.get(0), new TCenterHisYpmlmx());
            }
        }
        return new ResEntity(true, Constant.SUCCESS_DX, tCenterHisYpmlmx);
    }
}
