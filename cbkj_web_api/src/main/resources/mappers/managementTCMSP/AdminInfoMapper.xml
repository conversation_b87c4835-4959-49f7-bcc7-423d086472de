<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuzhekan.managementTCMSP.mapper.AdminInfoMapper">
    <resultMap id="BaseResultMap2" type="com.jiuzhekan.managementTCMSP.bean.AppsInfo">
    </resultMap>
    <select id="getPageDatas" parameterType="com.jiuzhekan.managementTCMSP.bean.TCMSPAdminInfoReq" resultMap="BaseResultMap2">
        SELECT
        a.user_id as userId,
        a.user_name as userName,
        a.password,
        a.sex,
        a.status,
        a.professional_pic_path,
        DATE_FORMAT(
        a.create_date,
        '%Y-%m-%d %H:%i:%s'
        ) createDate ,
        a.create_user createUser,
        a.update_user updateUser,
        DATE_FORMAT(
        a.update_date,
        '%Y-%m-%d %H:%i:%s'
        ) updateDate,
        a.last_ip as lastIp,
        a.phone,
        a.address,
        a.name_zh AS nameZh,
        a.user_heand,
        a.email,a.certificate,
        a.app_id appId,
        a.ins_code insCode,
        a.dept_id deptId,
        a.dept_id_his deptIdHis,
        a.dept_name_his deptNameHis,
        a.staff_type as staffType,
        a.work_status as workStatus,
        ap.employee_id as employeeId,
        group_concat(i.ins_name) insNames,

        group_concat(concat_ws('/',p.app_name,i.ins_name,ap.dept_name)) as doctorMultipoint
        FROM cbkj_web_parameter.`sys_admin_info` a

        LEFT JOIN cbkj_web_parameter.sys_doctor_multipoint ap ON ap.user_id = a.user_id and ap.status='0'
        left join cbkj_web_parameter.sys_app p on ap.app_id = p.app_id
        left join cbkj_web_parameter.sys_institution i on ap.app_id = i.APP_ID and ap.ins_code = i.INS_CODE
        WHERE a.status in ('0','2','3') and a.is_qualifier = '1'
        <if test="userName != null and userName != ''">
            and (a.user_name like concat('%',trim(#{userName}),'%') or a.name_zh like concat('%',trim(#{userName}),'%')
            )
        </if>
        <if test="appId != null and appId != '' and appId != '000000'">
            and ap.app_id = #{appId}
        </if>
        <if test="insCode != null and insCode != '' and insCode != '000000'">
            and ap.ins_code = #{insCode}
        </if>
        <if test="insName != null and insName != ''">
            and i.ins_name like concat('%',#{insName},'%')
        </if>
        <if test="deptName != null and deptName != ''">
            and ap.dept_name like concat('%',#{deptName},'%')
        </if>
          <if test="null != employeeId and employeeId != ''">
              and ap.employee_id = #{employeeId}
          </if>
        <if test="sex != null and sex != ''">
            and a.sex = #{sex}
        </if>
        <if test="userId != null and userId != ''">
            and a.user_id = #{userId}
        </if>
        <if test="staffType != null">
            and a.staff_type = #{staffType}
        </if>
        <if test="workStatus != null">
            and a.work_status = #{workStatus}
        </if>
        <if test="staffType != null">
            and a.staff_type = #{staffType}
        </if>
     <if test="null != professional and professional != ''">
         and a.professional = #{professional}
     </if>
        GROUP BY
        a.user_id
        order by ifnull(a.update_date,a.create_date) DESC
    </select>
    <select id="getPageDatasNum" parameterType="com.jiuzhekan.managementTCMSP.bean.TCMSPAdminInfoReq" resultType="Integer">
        SELECT
        count(*)
        FROM cbkj_web_parameter.`sys_admin_info` a
        LEFT JOIN cbkj_web_parameter.sys_doctor_multipoint ap ON ap.user_id = a.user_id and ap.status='0'
        left join cbkj_web_parameter.sys_app p on ap.app_id = p.app_id
        left join cbkj_web_parameter.sys_institution i on ap.app_id = i.APP_ID and ap.ins_code = i.INS_CODE
        WHERE a.status in ('0','2','3') and a.is_qualifier = '1'
        <if test="userName != null and userName != ''">
            and (a.user_name like concat('%',trim(#{userName}),'%') or a.name_zh like concat('%',trim(#{userName}),'%')
            )
        </if>
        <if test="appId != null and appId != '' and appId != '000000'">
            and ap.app_id = #{appId}
        </if>
        <if test="insCode != null and insCode != '' and insCode != '000000'">
            and ap.ins_code = #{insCode}
        </if>
        <if test="null != employeeId and employeeId != ''">
            and ap.employee_id = #{employeeId}
        </if>
        <if test="insName != null and insName != ''">
            and i.ins_name like concat('%',#{insName},'%')
        </if>
        <if test="deptName != null and deptName != ''">
            and ap.dept_name like concat('%',#{deptName},'%')
        </if>
        <if test="sex != null and sex != ''">
            and a.sex = #{sex}
        </if>
        <if test="userId != null and userId != ''">
            and a.user_id = #{userId}
        </if>
        <if test="staffType != null">
            and a.staff_type = #{staffType}
        </if>
        <if test="null != professional and professional != ''">
            and a.professional = #{professional}
        </if>
        <if test="workStatus != null">
            and a.work_status = #{workStatus}
        </if>
        GROUP BY
        a.user_id
        order by ifnull(a.update_date,a.create_date) DESC
    </select>

</mapper>