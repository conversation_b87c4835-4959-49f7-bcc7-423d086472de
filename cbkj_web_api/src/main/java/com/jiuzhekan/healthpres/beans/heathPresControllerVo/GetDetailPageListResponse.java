package com.jiuzhekan.healthpres.beans.heathPresControllerVo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/5/27 11:13
 * @Version 1.0
 */
@ApiModel
@Data
public class GetDetailPageListResponse {


    @ApiModelProperty(value = "是否可删除")
    private Boolean canDelete;
    @ApiModelProperty(value = "是否可修改")
    private Boolean canUpdate;

    @ApiModelProperty(value = "创建医生id")
    private String createDoctorId;

    @ApiModelProperty(value = "创建医生名称")
    private String createDoctorName;

    @ApiModelProperty(value = "患者id")
    private String patientId;

    @ApiModelProperty(value = "患者出生日期")
    private Date patientBirthDay;
    @ApiModelProperty(value = "结果id")
    private String resultId;

    @ApiModelProperty(value = "患者姓名")
    private String patientName;

    @ApiModelProperty(value = "患者性别")
    private String patientSex;

    @ApiModelProperty(value = "患者年龄")
    private String patientAge;

    @ApiModelProperty(value = "结果报告名称")
    private String resultReportName;



    @ApiModelProperty(value = "结果报告时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date resultReportTime;

    @ApiModelProperty(value = "1.儿童保健2.慢病调理")
    private Integer questionnaireMainType;

    @ApiModelProperty(value = "子分类 1-1(6、12月龄儿童)1-2(18、24月龄儿童)1-3(30、36月龄儿童) 等名儿")
    private String questionnaireMainSecondTypeName;

    @ApiModelProperty(value = "子分类 1-1(6、12月龄儿童)1-2(18、24月龄儿童)1-3(30、36月龄儿童) 等代码")
    private String questionnaireMainSecondTypeCode;
    private String resultReportTypeInfo;


//    private Date birthDay;1
}
