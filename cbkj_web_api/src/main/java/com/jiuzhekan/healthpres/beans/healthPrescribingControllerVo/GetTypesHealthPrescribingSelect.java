package com.jiuzhekan.healthpres.beans.healthPrescribingControllerVo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025/5/28 14:12
 * @Version 1.0
 */
@Data
@ApiModel
public class GetTypesHealthPrescribingSelect {
    @ApiModelProperty(value = "右上角的下拉类型名称")
    private String typeName;

    @ApiModelProperty(value = "右上角的下拉类型code")
    private String typeCode;
    @ApiModelProperty(value = "是否是推荐")
    private boolean recommend;


//    @ApiModelProperty(value = "报告名称")
//    private String resultReportName;
//
//
//    @ApiModelProperty(value = "报告类型信息")
//    private String resultReportTypeInfo;

    @ApiModelProperty(value = "标题")
    private String questionnaireMainTitle;

    @ApiModelProperty(value = "内容首行文本")
    private String questionnaireMainFirstLine;

    @ApiModelProperty(value = "内容第二行提示。")
    private String questionnaireMainTips;

    @ApiModelProperty(value = "1.儿童保健2.慢病调理")
    private Integer questionnaireMainType;

    @ApiModelProperty(value = "子分类：1-1(6、12月龄儿童)1-2(18、24月龄儿童)1-3(30、36月龄儿童) ")
    private String questionnaireMainSecondTypeCode;

    @ApiModelProperty(value = "子类型名称")
    private String questionnaireMainSecondTypeName;
    @ApiModelProperty(value = "模板内容id")
    private String questionnaireMainId;

    @ApiModelProperty(value = "处方內容（多选内容）和每个内容的默认信息")
    private List<GetTypesHealthPrescribingContent> contents;
}
