package com.jiuzhekan.cbkj.mapper.business.record;

import com.jiuzhekan.cbkj.beans.business.record.TBusinessQuestion;
import com.jiuzhekan.cbkj.beans.business.setting.TBusinessQuestionEx;
import com.jiuzhekan.cbkj.mapper.baseMapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public interface TBusinessQuestionMapper extends BaseMapper<TBusinessQuestion> {

    long deleteByQuestionS(@Param("list") List<String> split, @Param("obj") TBusinessQuestion businessQuestion);

    long insertUpdateEx(TBusinessQuestionEx tBusinessQuestionEx);

    TBusinessQuestion findObj(TBusinessQuestionEx id);

    long updateSeeTimes(TBusinessQuestionEx questionEx);
}